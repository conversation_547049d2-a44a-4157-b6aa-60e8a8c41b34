<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="iconGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="circleGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#64B5F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#42A5F5;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景六边形 -->
  <path d="M16,2 L28,9 L28,23 L16,30 L4,23 L4,9 Z" 
        fill="url(#iconGrad)" 
        stroke="#1565C0" 
        stroke-width="1"/>
  
  <!-- 中心圆圈 -->
  <circle cx="16" cy="16" r="6" 
          fill="url(#circleGrad)" 
          stroke="#1565C0" 
          stroke-width="1"/>
  
  <!-- 装饰线条 -->
  <g stroke="#90CAF9" stroke-width="1">
    <line x1="16" y1="4" x2="16" y2="10" />
    <line x1="16" y1="22" x2="16" y2="28" />
    <line x1="26" y1="16" x2="22" y2="16" />
    <line x1="10" y1="16" x2="6" y2="16" />
  </g>
  
  <!-- 点缀小圆点 -->
  <g fill="#E3F2FD">
    <circle cx="16" cy="4" r="1" />
    <circle cx="16" cy="28" r="1" />
    <circle cx="26" cy="16" r="1" />
    <circle cx="6" cy="16" r="1" />
  </g>
  
  <!-- 装饰性脉冲圆环 -->
  <circle cx="16" cy="16" r="9" 
          fill="none" 
          stroke="#64B5F6" 
          stroke-width="0.5"
          stroke-dasharray="2,2"/>
</svg> 