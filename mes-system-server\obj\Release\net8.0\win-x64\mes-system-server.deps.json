{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/win-x64": {"mes-system-server/1.0.0": {"dependencies": {"BCrypt.Net-Next": "4.0.3", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.2", "Microsoft.AspNetCore.OpenApi": "8.0.8", "Microsoft.EntityFrameworkCore.SqlServer": "7.0.15", "Microsoft.EntityFrameworkCore.Tools": "7.0.15", "Swashbuckle.AspNetCore": "6.4.0", "System.Linq.Dynamic.Core": "1.3.5", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "8.0.8", "runtimepack.Microsoft.AspNetCore.App.Runtime.win-x64": "8.0.8"}, "runtime": {"mes-system-server.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.8": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.824.36612"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "8.0.824.36612"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.40.33810.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "8.0.824.36612"}, "clretwrc.dll": {"fileVersion": "8.0.824.36612"}, "clrgc.dll": {"fileVersion": "8.0.824.36612"}, "clrjit.dll": {"fileVersion": "8.0.824.36612"}, "coreclr.dll": {"fileVersion": "8.0.824.36612"}, "createdump.exe": {"fileVersion": "8.0.824.36612"}, "hostfxr.dll": {"fileVersion": "8.0.824.36612"}, "hostpolicy.dll": {"fileVersion": "8.0.824.36612"}, "mscordaccore.dll": {"fileVersion": "8.0.824.36612"}, "mscordaccore_amd64_amd64_8.0.824.36612.dll": {"fileVersion": "8.0.824.36612"}, "mscordbi.dll": {"fileVersion": "8.0.824.36612"}, "mscorrc.dll": {"fileVersion": "8.0.824.36612"}, "msquic.dll": {"fileVersion": "*******"}}}, "runtimepack.Microsoft.AspNetCore.App.Runtime.win-x64/8.0.8": {"runtime": {"Microsoft.AspNetCore.Antiforgery.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authentication.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authentication.BearerToken.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authentication.Cookies.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authentication.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authentication.OAuth.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authentication.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authorization.Policy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Components.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Components.Endpoints.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Components.Server.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.CookiePolicy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Cors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.DataProtection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.HostFiltering.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Html.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Http.Connections.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Http.Connections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Http.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Http.Results.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.HttpLogging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.HttpOverrides.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.HttpsPolicy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Identity.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Localization.Routing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.Cors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.Razor.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.RazorPages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.TagHelpers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.OutputCaching.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Razor.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Razor.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.RequestDecompression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.ResponseCaching.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.ResponseCompression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Rewrite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Routing.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Routing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.HttpSys.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.IIS.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.IISIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.Kestrel.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.Kestrel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Session.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.SignalR.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.SignalR.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.StaticFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.WebUtilities.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.724.31311"}, "Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.724.31311"}, "Microsoft.Extensions.Configuration.Ini.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.KeyPerFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Diagnostics.HealthChecks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Features.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.FileProviders.Composite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Logging.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Options.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.224.6711"}, "Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.WebEncoders.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.JSInterop.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "native": {"aspnetcorev2_inprocess.dll": {"fileVersion": "18.0.24201.8"}}}, "Azure.Core/1.25.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net5.0/Azure.Core.dll": {"assemblyVersion": "********", "fileVersion": "1.2500.22.33004"}}}, "Azure.Identity/1.7.0": {"dependencies": {"Azure.Core": "1.25.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.Identity.Client.Extensions.Msal": "2.19.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "6.0.0", "System.Text.Json": "7.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.700.22.46903"}}}, "BCrypt.Net-Next/4.0.3": {"runtime": {"lib/net6.0/BCrypt.Net-Next.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.0.3.0"}}}, "Humanizer.Core/2.14.1": {}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.2": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.224.6804"}}}, "Microsoft.AspNetCore.OpenApi/8.0.8": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "8.0.8.0", "fileVersion": "8.0.824.36908"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.Data.SqlClient/5.1.1": {"dependencies": {"Azure.Identity": "1.7.0", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "7.0.0"}, "runtime": {"runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"native": {"runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/7.0.15": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "7.0.15", "Microsoft.EntityFrameworkCore.Analyzers": "7.0.15", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "7.0.1523.57109"}}}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.15": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "7.0.1523.57109"}}}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.15": {}, "Microsoft.EntityFrameworkCore.Design/7.0.15": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.EntityFrameworkCore.Relational": "7.0.15", "Microsoft.Extensions.DependencyModel": "7.0.0", "Mono.TextTemplating": "2.2.1"}}, "Microsoft.EntityFrameworkCore.Relational/7.0.15": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.15", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "********", "fileVersion": "7.0.1523.57109"}}}, "Microsoft.EntityFrameworkCore.SqlServer/7.0.15": {"dependencies": {"Microsoft.Data.SqlClient": "5.1.1", "Microsoft.EntityFrameworkCore.Relational": "7.0.15"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "********", "fileVersion": "7.0.1523.57109"}}}, "Microsoft.EntityFrameworkCore.Tools/7.0.15": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "7.0.15"}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {}, "Microsoft.Extensions.DependencyModel/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {}, "Microsoft.Extensions.Options/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Primitives/7.0.0": {}, "Microsoft.Identity.Client/4.47.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.47.2.0", "fileVersion": "4.47.2.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"dependencies": {"Microsoft.Identity.Client": "4.47.2", "System.Security.Cryptography.ProtectedData": "6.0.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "2.19.3.0", "fileVersion": "2.19.3.0"}}}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Logging/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Protocols/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.1.2", "System.IdentityModel.Tokens.Jwt": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Tokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.OpenApi/1.4.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Mono.TextTemplating/2.2.1": {"dependencies": {"System.CodeDom": "4.4.0"}}, "Swashbuckle.AspNetCore/6.4.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.4.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.4.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.4.0"}}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.4.0"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.CodeDom/4.4.0": {}, "System.Configuration.ConfigurationManager/6.0.1": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41905"}}}, "System.Diagnostics.DiagnosticSource/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Formats.Asn1/5.0.0": {}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "System.Linq.Dynamic.Core/1.3.5": {"runtime": {"lib/net7.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "1.3.5.0", "fileVersion": "1.3.5.0"}}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Runtime.Caching/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web/7.0.0": {}, "System.Text.Json/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}}}, "libraries": {"mes-system-server/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.8": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.AspNetCore.App.Runtime.win-x64/8.0.8": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Azure.Core/1.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-X8Dd4sAggS84KScWIjEbFAdt2U1KDolQopTPoHVubG2y3CM54f9l6asVrP5Uy384NWXjsspPYaJgz5xHc+KvTA==", "path": "azure.core/1.25.0", "hashPath": "azure.core.1.25.0.nupkg.sha512"}, "Azure.Identity/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-eHEiCO/8+MfNc9nH5dVew/+FvxdaGrkRL4OMNwIz0W79+wtJyEoeRlXJ3SrXhoy9XR58geBYKmzMR83VO7bcAw==", "path": "azure.identity/1.7.0", "hashPath": "azure.identity.1.7.0.nupkg.sha512"}, "BCrypt.Net-Next/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-W+U9WvmZQgi5cX6FS5GDtDoPzUCV4LkBLkywq/kRZhuDwcbavOzcDAr3LXJFqHUi952Yj3LEYoWW0jbEUQChsA==", "path": "bcrypt.net-next/4.0.3", "hashPath": "bcrypt.net-next.4.0.3.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-7qJkk5k5jabATZZrMIQgpUB9yjDNAAApSqw+8d0FEyK1AJ4j+wv1qOMl2byUr837xbK+MjehtPnQ32yZ5Gtzlw==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.2", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-wNHhohqP8rmsQ4UhKbd6jZMD6l+2Q/+DvRBT0Cgqeuglr13aF6sSJWicZKCIhZAUXzuhkdwtHVc95MlPlFk0dA==", "path": "microsoft.aspnetcore.openapi/8.0.8", "hashPath": "microsoft.aspnetcore.openapi.8.0.8.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MW5E9HFvCaV069o8b6YpuRDPBux8s96qDnOJ+4N9QNUCs7c5W3KxwQ+ftpAjbMUlImL+c9WR+l+f5hzjkqhu2g==", "path": "microsoft.data.sqlclient/5.1.1", "hashPath": "microsoft.data.sqlclient.5.1.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-jVsElisM5sfBzaaV9kdq2NXZLwIbytetnsOIlJ0cQGgQP4zFNBmkfHBnpwtmKrtBJBEV9+9PVQPVrcCVhDgcIg==", "path": "microsoft.data.sqlclient.sni.runtime/5.1.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/7.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-grjnH94+hXgTqRFdfl88otvlqHTG1QEtlLm6ADI3vtZ1h+C8xNhvKRLNNw1RMD7CKADpoEEPNgqTXKCg+Ki8OQ==", "path": "microsoft.entityframeworkcore/7.0.15", "hashPath": "microsoft.entityframeworkcore.7.0.15.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-YZYw1g0EGGkWdC+ymHRccQryerGyh7XlcL8nRo3r7kkPurPJgVCCtC+PK5pBwJGPDdwCYMrwHSoAtfEWcqqQjQ==", "path": "microsoft.entityframeworkcore.abstractions/7.0.15", "hashPath": "microsoft.entityframeworkcore.abstractions.7.0.15.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-zscMrRQQR1O4U5PhZ98ASBNdU/mAsSpqTwkE8RrguZEox31jYBK65LWTlFPPBWycEiC6U9feBZZ3bkkrmpWtbQ==", "path": "microsoft.entityframeworkcore.analyzers/7.0.15", "hashPath": "microsoft.entityframeworkcore.analyzers.7.0.15.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/7.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-knFwFoPpYqpV09xqZxowqA3Sbxg8cAKmJFuQDl43ZqgtCcMAEnVeH8aBLzSmpggauzC28/MWVtP88N/C+ko4dA==", "path": "microsoft.entityframeworkcore.design/7.0.15", "hashPath": "microsoft.entityframeworkcore.design.7.0.15.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/7.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-D03N/DF3lmtHHVd0RqHxZg+rtd3r8C4h7RJoy/ShxQA0r6rpJ/99ZA6HY9WeEgqsRbm2M8Lmj9MwJomcsJ+4GQ==", "path": "microsoft.entityframeworkcore.relational/7.0.15", "hashPath": "microsoft.entityframeworkcore.relational.7.0.15.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/7.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-FFJDo55Xgf3K3iDQbxVeYANi/43ghahMuudDzYmkDOHmQXNZDCj7ov97+FLlVIftbSUAJzBS250+hQqII389YA==", "path": "microsoft.entityframeworkcore.sqlserver/7.0.15", "hashPath": "microsoft.entityframeworkcore.sqlserver.7.0.15.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/7.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-7/gt7XfvQ1Sb7+kk6HkU8s6MlCkXRoEHKcJ0fXjGhLgDPq6e1G9/WJZvCxuLJzOFjxn4MIJMiEbdN8KVrIQ9CA==", "path": "microsoft.entityframeworkcore.tools/7.0.15", "hashPath": "microsoft.entityframeworkcore.tools.7.0.15.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "path": "microsoft.extensions.caching.abstractions/7.0.0", "hashPath": "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "path": "microsoft.extensions.caching.memory/7.0.0", "hashPath": "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oONNYd71J3LzkWc4fUHl3SvMfiQMYUCo/mDHDEu76hYYxdhdrPYv6fvGv9nnKVyhE9P0h20AU8RZB5OOWQcAXg==", "path": "microsoft.extensions.dependencymodel/7.0.0", "hashPath": "microsoft.extensions.dependencymodel.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "path": "microsoft.extensions.logging.abstractions/7.0.0", "hashPath": "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "path": "microsoft.extensions.options/7.0.0", "hashPath": "microsoft.extensions.options.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.47.2": {"type": "package", "serviceable": true, "sha512": "sha512-SPgesZRbXoDxg8Vv7k5Ou0ee7uupVw0E8ZCc4GKw25HANRLz1d5OSr0fvTVQRnEswo5Obk8qD4LOapYB+n5kzQ==", "path": "microsoft.identity.client/4.47.2", "hashPath": "microsoft.identity.client.4.47.2.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"type": "package", "serviceable": true, "sha512": "sha512-zVVZjn8aW7W79rC1crioDgdOwaFTQorsSO6RgVlDDjc7MvbEGz071wSNrjVhzR0CdQn6Sefx7Abf1o7vasmrLg==", "path": "microsoft.identity.client.extensions.msal/2.19.3", "hashPath": "microsoft.identity.client.extensions.msal.2.19.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-33eTIA2uO/L9utJjZWbKsMSVsQf7F8vtd6q5mQX7ZJzNvCpci5fleD6AeANGlbbb7WX7XKxq9+Dkb5e3GNDrmQ==", "path": "microsoft.identitymodel.abstractions/7.1.2", "hashPath": "microsoft.identitymodel.abstractions.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-cloLGeZolXbCJhJBc5OC05uhrdhdPL6MWHuVUnkkUvPDeK7HkwThBaLZ1XjBQVk9YhxXE2OvHXnKi0PLleXxDg==", "path": "microsoft.identitymodel.jsonwebtokens/7.1.2", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-YCxBt2EeJP8fcXk9desChkWI+0vFqFLvBwrz5hBMsoh0KJE6BC66DnzkdzkJNqMltLromc52dkdT206jJ38cTw==", "path": "microsoft.identitymodel.logging/7.1.2", "hashPath": "microsoft.identitymodel.logging.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-SydLwMRFx6EHPWJ+N6+MVaoArN1Htt92b935O3RUWPY1yUF63zEjvd3lBu79eWdZUwedP8TN2I5V9T3nackvIQ==", "path": "microsoft.identitymodel.protocols/7.1.2", "hashPath": "microsoft.identitymodel.protocols.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-6lHQoLXhnMQ42mGrfDkzbIOR3rzKM1W1tgTeMPLgLCqwwGw0d96xFi/UiX/fYsu7d6cD5MJiL3+4HuI8VU+sVQ==", "path": "microsoft.identitymodel.protocols.openidconnect/7.1.2", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-oICJMqr3aNEDZOwnH5SK49bR6Z4aX0zEAnOLuhloumOSuqnNq+GWBdQyrgILnlcT5xj09xKCP/7Y7gJYB+ls/g==", "path": "microsoft.identitymodel.tokens/7.1.2", "hashPath": "microsoft.identitymodel.tokens.7.1.2.nupkg.sha512"}, "Microsoft.OpenApi/1.4.3": {"type": "package", "serviceable": true, "sha512": "sha512-rURwggB+QZYcSVbDr7HSdhw/FELvMlriW10OeOzjPT7pstefMo7IThhtNtDudxbXhW+lj0NfX72Ka5EDsG8x6w==", "path": "microsoft.openapi/1.4.3", "hashPath": "microsoft.openapi.1.4.3.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUBr4TW0up6oKDA5Xwkul289uqSMgY0xGN4pnbOIBqCcN9VKGGaPvHX3vWaG/hvocfGDP+MGzMA0bBBKz2fkmQ==", "path": "swashbuckle.aspnetcore/6.4.0", "hashPath": "swashbuckle.aspnetcore.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-nl4SBgGM+cmthUcpwO/w1lUjevdDHAqRvfUoe4Xp/Uvuzt9mzGUwyFCqa3ODBAcZYBiFoKvrYwz0rabslJvSmQ==", "path": "swashbuckle.aspnetcore.swagger/6.4.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-lXhcUBVqKrPFAQF7e/ZeDfb5PMgE8n5t6L5B6/BQSpiwxgHzmBcx8Msu42zLYFTvR5PIqE9Q9lZvSQAcwCxJjw==", "path": "swashbuckle.aspnetcore.swaggergen/6.4.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Hh3atb3pi8c+v7n4/3N80Jj8RvLOXgWxzix6w3OZhB7zBGRwsy7FWr4e3hwgPweSBpwfElqj4V4nkjYabH9nQ==", "path": "swashbuckle.aspnetcore.swaggerui/6.4.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.4.0.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "path": "system.configuration.configurationmanager/6.0.1", "hashPath": "system.configuration.configurationmanager.6.0.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-frQDfv0rl209cKm1lnwTgFPzNigy2EKk1BS3uAvHvlBVKe5cymGyHO+Sj+NLv5VF/AhHsqPIUUwya5oV4CHMUw==", "path": "system.diagnostics.diagnosticsource/6.0.0", "hashPath": "system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Formats.Asn1/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "path": "system.formats.asn1/5.0.0", "hashPath": "system.formats.asn1.5.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-Thhbe1peAmtSBFaV/ohtykXiZSOkx59Da44hvtWfIMFofDA3M3LaVyjstACf2rKGn4dEDR2cUpRAZ0Xs/zB+7Q==", "path": "system.identitymodel.tokens.jwt/7.1.2", "hashPath": "system.identitymodel.tokens.jwt.7.1.2.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.3.5": {"type": "package", "serviceable": true, "sha512": "sha512-G8aRAVHSQ/Jmt6dekX+z4K3L6ecX3CizaIYSQoDUolPRIPBYmBCabT+/pq5fMAFM57aOchFn8PUufuVfLjvEZQ==", "path": "system.linq.dynamic.core/1.3.5", "hashPath": "system.linq.dynamic.core.1.3.5.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime.Caching/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "path": "system.runtime.caching/6.0.0", "hashPath": "system.runtime.caching.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "path": "system.text.encodings.web/7.0.0", "hashPath": "system.text.encodings.web.7.0.0.nupkg.sha512"}, "System.Text.Json/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DaGSsVqKsn/ia6RG8frjwmJonfos0srquhw09TlT8KRw5I43E+4gs+/bZj4K0vShJ5H9imCuXupb4RmS+dBy3w==", "path": "system.text.json/7.0.0", "hashPath": "system.text.json.7.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"]}}