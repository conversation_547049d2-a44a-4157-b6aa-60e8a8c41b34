import request from '@/utils/request'

// 获取用户列表
export function getUserList(params) {
  return request({
    url: '/api/users',
    method: 'get',
    params
  })
}

// 添加用户
export function addUser(data) {
  return request({
    url: '/api/users',
    method: 'post',
    data
  })
}

// 更新用户
export function updateUser(id, data) {
  return request({
    url: `/api/Users/<USER>
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `/api/users/${id}`,
    method: 'delete'
  })
}

// 重置用户密码
export function resetUserPassword(id) {
  return request({
    url: `/api/users/${id}/reset-password`,
    method: 'post'
  })
}

// 切换用户状态
export function toggleUserStatus(id, status) {
  return request({
    url: `/api/Users/<USER>/status`,
    method: 'put',
    data: status,
    headers: {
      'Content-Type': 'application/json'
    }
  })
} 