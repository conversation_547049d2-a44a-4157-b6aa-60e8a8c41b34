/**
 * 菜单工具函数
 * 用于处理菜单数据转换和树形结构操作
 */

/**
 * 转换后端菜单数据为前端树形结构
 * @param {Array} menuData - 后端菜单数据
 * @returns {Array} 前端树形菜单结构
 */
export function convertMenuDataToTree(menuData) {
  if (!menuData || !Array.isArray(menuData)) {
    return []
  }

  return menuData.map(item => {
    const treeNode = {
      id: item.menuId, // 使用menuId作为标识符
      name: item.name,
      menuId: item.menuId,
      path: item.path || '',
      type: item.type || 'menu-item',
      parentId: item.parentId,
      sortOrder: item.sortOrder || 0,
      status: item.status !== false, // 默认为true
      description: item.description || ''
    }
    
    if (item.children && item.children.length > 0) {
      treeNode.children = convertMenuDataToTree(item.children)
    }
    
    return treeNode
  })
}

/**
 * 从树形结构中获取所有菜单ID
 * @param {Array} menuTree - 菜单树
 * @returns {Array} 菜单ID数组
 */
export function getAllMenuIds(menuTree) {
  const ids = []
  
  function traverse(nodes) {
    nodes.forEach(node => {
      ids.push(node.id)
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  
  traverse(menuTree)
  return ids
}

/**
 * 检查菜单ID是否在树中存在
 * @param {Array} menuTree - 菜单树
 * @param {string} menuId - 菜单ID
 * @returns {boolean} 是否存在
 */
export function isMenuIdExists(menuTree, menuId) {
  function findMenu(nodes, id) {
    for (const node of nodes) {
      if (node.id === id) {
        return true
      }
      if (node.children && node.children.length > 0) {
        if (findMenu(node.children, id)) {
          return true
        }
      }
    }
    return false
  }
  
  return findMenu(menuTree, menuId)
}

/**
 * 过滤无效的菜单ID
 * @param {Array} menuTree - 菜单树
 * @param {Array} menuIds - 要检查的菜单ID数组
 * @returns {Array} 有效的菜单ID数组
 */
export function filterValidMenuIds(menuTree, menuIds) {
  if (!Array.isArray(menuIds)) {
    return []
  }
  
  return menuIds.filter(id => isMenuIdExists(menuTree, id))
}

/**
 * 将扁平的菜单列表转换为树形结构
 * @param {Array} flatMenus - 扁平菜单列表
 * @returns {Array} 树形菜单结构
 */
export function buildMenuTree(flatMenus) {
  const menuMap = {}
  const result = []
  
  // 创建菜单映射
  flatMenus.forEach(menu => {
    menuMap[menu.id] = {
      ...menu,
      children: []
    }
  })
  
  // 构建树形结构
  flatMenus.forEach(menu => {
    if (menu.parentId && menuMap[menu.parentId]) {
      menuMap[menu.parentId].children.push(menuMap[menu.id])
    } else {
      result.push(menuMap[menu.id])
    }
  })
  
  return result
}

export default {
  convertMenuDataToTree,
  getAllMenuIds,
  isMenuIdExists,
  filterValidMenuIds,
  buildMenuTree
} 