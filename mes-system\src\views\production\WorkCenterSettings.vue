<template>
  <div class="work-center-settings">
    <!-- 顶部工具栏 -->
    <el-card class="toolbar-card" shadow="never">
      <div class="toolbar">
        <div class="production-line-selector">
          <span class="label">选择产线：</span>
          <el-select 
            v-model="selectedProductionLine" 
            placeholder="请选择产线"
            @change="debouncedHandleProductionLineChange"
            style="width: 200px;"
          >
            <el-option
              v-for="line in productionLines"
              :key="line.id"
              :label="line.name"
              :value="line.id"
            />
          </el-select>
          <el-button type="primary" :icon="Plus" @click="handleAddProductionLine" style="margin-left: 10px;">添加产线</el-button>
        </div>
        <div class="toolbar-actions">
          <el-button type="primary" :icon="Plus" @click="handleAdd" :disabled="!canAdd">增加按钮</el-button>
          <el-button type="warning" :icon="Edit" @click="handleEdit" :disabled="!canEdit">编辑按钮</el-button>
          <el-button type="danger" :icon="Delete" @click="handleDelete" :disabled="!canDelete">删除按钮</el-button>
          <el-button type="success" :icon="Check" @click="handleSave" :loading="saving">保存按钮</el-button>
        </div>
      </div>
    </el-card>

    <!-- 工作中心层级结构图 -->
    <el-card class="work-center-tree-card" v-loading="loading">
      <div class="work-center-content" v-if="selectedProductionLine">
        <!-- 骨架屏 -->
        <div v-if="loading" class="skeleton-container">
          <div class="skeleton-legend"></div>
          <div class="skeleton-production-line"></div>
          <div class="skeleton-work-centers">
            <div v-for="i in 6" :key="i" class="skeleton-work-center"></div>
          </div>
          <div class="skeleton-devices">
            <div v-for="i in 12" :key="i" class="skeleton-device"></div>
          </div>
        </div>
        
        <!-- 实际内容 -->
        <div v-else>
          <!-- 颜色图例 -->
          <div class="color-legend">
            <div class="legend-title">层级说明：</div>
            <div class="legend-items">
              <div class="legend-item">
                <div class="legend-color production-line-color"></div>
                <span class="legend-label">产线层</span>
              </div>
              <div class="legend-item">
                <div class="legend-color work-center-color"></div>
                <span class="legend-label">工作中心层</span>
              </div>
              <div class="legend-item">
                <div class="legend-color device-color"></div>
                <span class="legend-label">设备层</span>
              </div>
            </div>
          </div>
          
          <!-- 层级结构图 -->
          <div class="hierarchy-container" :style="{ '--grid-columns': gridColumns }">
            <!-- 顶层节点 -->
            <div class="top-level-node">
              <div 
                class="node production-line-node"
                :class="{ 'selected': selectedNode?.type === 'production-line' }"
                @click="handleNodeSelect({ id: selectedProductionLine, name: getCurrentProductionLineName(), code: getCurrentProductionLineCode() }, 'production-line')"
              >
                <div class="node-content">
                  <span class="node-title">{{ getCurrentProductionLineName() }}</span>
                  <span class="node-code">{{ getCurrentProductionLineCode() }}</span>
                </div>
              </div>
            </div>

            <!-- 工作中心层级 -->
            <div class="work-center-levels">
              <!-- 使用Grid布局确保垂直对齐 -->
              <div class="hierarchy-grid">
                <!-- 第一层：工作中心 -->
                <div class="level level-1">
                  <div 
                    v-for="center in workCenters" 
                    :key="center.id"
                    class="work-center-column"
                  >
                    <div 
                      class="node work-center-node"
                      :class="{ 
                        'selected': selectedNode?.id === center.id && selectedNode?.type === 'work-center',
                        'long-text': center.name.length > 5 || center.name.includes('Breaker') || center.name.includes('装配中心') || center.name.includes('面壳') || center.name.includes('底座')
                      }"
                      @click="handleNodeSelect(center, 'work-center')"
                    >
                      <div class="node-content">
                        <span class="node-title">{{ center.name }}</span>
                        <span class="node-code">{{ center.code }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 第二层：设备层 -->
                <div class="level level-2">
                  <div 
                    v-for="center in workCenters" 
                    :key="`devices-${center.id}`"
                    class="work-center-column"
                  >
                    <div class="device-group">
                      <!-- 显示设备 -->
                      <div 
                        v-for="equipment in center.equipment" 
                        :key="equipment.id"
                        class="node device-node"
                        :class="{ 'selected': selectedNode?.id === equipment.id && selectedNode?.type === 'equipment' }"
                        @click="handleNodeSelect(equipment, 'equipment')"
                      >
                        <div class="node-content">
                          <span class="node-title">{{ equipment.name }}</span>
                          <span class="node-code">{{ equipment.code }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <el-empty v-else description="请先选择产线" />
    </el-card>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="getDialogTitle()"
      width="500px"
    >
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="名称">
          <el-input v-model="editForm.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="编码">
          <el-input v-model="editForm.code" placeholder="请输入编码" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="editForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmEdit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 产线添加对话框 -->
    <el-dialog
      v-model="productionLineDialogVisible"
      title="添加产线"
      width="500px"
    >
      <el-form :model="productionLineForm" label-width="100px">
        <el-form-item label="产线名称">
          <el-input v-model="productionLineForm.name" placeholder="请输入产线名称" />
        </el-form-item>
        <el-form-item label="产线编码">
          <el-input v-model="productionLineForm.code" placeholder="请输入产线编码" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="productionLineForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="productionLineDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmAddProductionLine">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, Check } from '@element-plus/icons-vue'
import { 
  getProductionLines, 
  getWorkCentersByProductionLine,
  createProductionLine,
  updateProductionLine,
  deleteProductionLine,
  createWorkCenter,
  updateWorkCenter,
  deleteWorkCenter,
  createEquipment,
  updateEquipment,
  deleteEquipment
} from '@/api/production'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const selectedProductionLine = ref('')
const productionLines = ref([])
const workCenters = ref([])
const selectedNode = ref(null)
const editDialogVisible = ref(false)
const editType = ref('')
const editMode = ref('')
const productionLineDialogVisible = ref(false)

// 编辑表单
const editForm = ref({
  name: '',
  code: '',
  description: ''
})

// 产线表单
const productionLineForm = ref({
  name: '',
  code: '',
  description: ''
})

// 计算属性
const hasSelectedNode = computed(() => selectedNode.value !== null)
const canAdd = computed(() => {
  return selectedNode.value && 
         (selectedNode.value.type === 'production-line' || selectedNode.value.type === 'work-center')
})
const canDelete = computed(() => {
  return selectedNode.value && selectedNode.value.type !== 'production-line'
})
const canEdit = computed(() => {
  return selectedNode.value && 
         (selectedNode.value.type === 'work-center' || selectedNode.value.type === 'equipment')
})
const gridColumns = computed(() => Math.max(workCenters.value.length, 1))

// 初始化数据 - 从API获取
const initializeData = async () => {
  try {
    loading.value = true
    
    // 获取产线列表 - 使用分页参数
    const response = await getProductionLines(1, 10, false)
    if (response.data?.code === 200 && response.data?.data?.list) {
      productionLines.value = response.data.data.list
      console.log('获取到产线列表:', productionLines.value)
      
      // 预加载前3个产线的工作中心数据到缓存
      preloadWorkCenterData()
    } else {
      console.warn('获取产线列表失败:', response.data)
      ElMessage.warning('获取产线列表失败')
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 预加载工作中心数据
const preloadWorkCenterData = async () => {
  // 只预加载前3个产线的数据，避免过多的并发请求
  const linesToPreload = productionLines.value.slice(0, 3)
  
  console.log('开始预加载工作中心数据...')
  const preloadPromises = linesToPreload.map(async (line) => {
    try {
      const cacheKey = `line_${line.id}`
      if (!workCenterCache.value.has(cacheKey)) {
        console.log('预加载产线数据:', line.name)
        const response = await getWorkCentersByProductionLine(line.id, true)
        if (response.data?.code === 200 && response.data?.data?.list) {
          const formattedData = response.data.data.list.map(center => ({
            id: center.id.toString(),
            name: center.name,
            code: center.code,
            description: center.description,
            productionLineId: center.productionLineId,
            sortOrder: center.sortOrder,
            isActive: center.isActive,
            equipment: center.equipment ? center.equipment.map(eq => ({
              id: eq.id.toString(),
              name: eq.name,
              code: eq.code,
              description: eq.description,
              workCenterId: eq.workCenterId,
              sortOrder: eq.sortOrder,
              isActive: eq.isActive
            })) : []
          }))
          workCenterCache.value.set(cacheKey, formattedData)
          console.log(`预加载完成: ${line.name}`)
        }
      }
    } catch (error) {
      console.warn(`预加载产线 ${line.name} 失败:`, error)
    }
  })
  
  // 并发执行但不阻塞主流程
  Promise.all(preloadPromises).then(() => {
    console.log('工作中心数据预加载完成')
  })
}

// 方法
const getCurrentProductionLineName = () => {
  const line = productionLines.value.find(l => l.id === selectedProductionLine.value)
  return line ? line.name : ''
}

const getCurrentProductionLineCode = () => {
  const line = productionLines.value.find(l => l.id === selectedProductionLine.value)
  return line ? line.code : ''
}

// 添加缓存机制
const workCenterCache = ref(new Map())

// 清除缓存的辅助函数
const clearCache = (lineId = null) => {
  if (lineId) {
    const cacheKey = `line_${lineId}`
    workCenterCache.value.delete(cacheKey)
    console.log('清除产线缓存:', lineId)
  } else {
    workCenterCache.value.clear()
    console.log('清除所有缓存')
  }
}

// 防抖机制
let loadingTimer = null
const debouncedHandleProductionLineChange = (lineId) => {
  if (loadingTimer) {
    clearTimeout(loadingTimer)
  }
  loadingTimer = setTimeout(() => {
    handleProductionLineChange(lineId)
  }, 100) // 100ms 防抖延迟
}

const handleProductionLineChange = async (lineId) => {
  if (!lineId) {
    workCenters.value = []
    selectedNode.value = null
    return
  }

  try {
    loading.value = true
    selectedNode.value = null
    
    // 检查缓存
    const cacheKey = `line_${lineId}`
    if (workCenterCache.value.has(cacheKey)) {
      console.log('从缓存获取工作中心数据:', lineId)
      workCenters.value = workCenterCache.value.get(cacheKey)
      loading.value = false
      return
    }
    
    // 根据产线ID获取工作中心数据
    console.time(`获取产线${lineId}数据`)
    const response = await getWorkCentersByProductionLine(lineId, true)
    console.timeEnd(`获取产线${lineId}数据`)
    
    if (response.data?.code === 200 && response.data?.data?.list) {
      // 将API返回的数据格式化为前端需要的格式
      const formattedData = response.data.data.list.map(center => ({
        id: center.id.toString(),
        name: center.name,
        code: center.code,
        description: center.description,
        productionLineId: center.productionLineId,
        sortOrder: center.sortOrder,
        isActive: center.isActive,
        equipment: center.equipment ? center.equipment.map(eq => ({
          id: eq.id.toString(),
          name: eq.name,
          code: eq.code,
          description: eq.description,
          workCenterId: eq.workCenterId,
          sortOrder: eq.sortOrder,
          isActive: eq.isActive
        })) : []
      }))
      
      // 存入缓存
      workCenterCache.value.set(cacheKey, formattedData)
      workCenters.value = formattedData
      console.log('获取到工作中心数据:', workCenters.value)
    } else {
      console.warn('获取工作中心失败:', response.data)
      workCenters.value = []
      ElMessage.warning('获取工作中心数据失败')
    }
  } catch (error) {
    console.error('获取工作中心失败:', error)
    workCenters.value = []
    ElMessage.error('获取工作中心数据失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

const handleNodeSelect = (node, type) => {
  selectedNode.value = { ...node, type }
}

const handleAdd = () => {
  if (!selectedProductionLine.value) {
    ElMessage.warning('请先选择产线')
    return
  }

  // 根据选中的节点类型决定要添加的类型
  if (!selectedNode.value) {
    // 如果没有选择节点，但已选择产线，则默认为产线添加工作中心
    if (selectedProductionLine.value) {
      // 检查工作中心数量限制
      if (workCenters.value.length >= 12) {
        ElMessage.warning('每条产线最多只能添加12个工作中心')
        return
      }
      editMode.value = 'add'
      editType.value = 'work-center'
      editForm.value = {
        name: '',
        code: '',
        description: ''
      }
      editDialogVisible.value = true
      return
    }
    ElMessage.warning('请先选择要添加子项的节点')
    return
  }

  if (selectedNode.value.type === 'production-line') {
    // 选中产线时，添加工作中心
    // 检查工作中心数量限制
    if (workCenters.value.length >= 12) {
      ElMessage.warning('每条产线最多只能添加12个工作中心')
      return
    }
    editMode.value = 'add'
    editType.value = 'work-center'
    editForm.value = {
      name: '',
      code: '',
      description: ''
    }
    editDialogVisible.value = true
  } else if (selectedNode.value.type === 'work-center') {
    // 选中工作中心时，添加设备
    console.log('为工作中心添加设备:', selectedNode.value)
    editMode.value = 'add'
    editType.value = 'device'
    editForm.value = {
      name: '',
      code: '',
      description: ''
    }
    editDialogVisible.value = true
  } else {
    ElMessage.warning('请选择产线或工作中心来添加子项')
  }
}

const handleEdit = () => {
  if (!selectedNode.value) {
    ElMessage.warning('请先选择要编辑的节点')
    return
  }

  if (selectedNode.value.type === 'work-center') {
    // 编辑工作中心
    editMode.value = 'edit'
    editType.value = 'work-center'
    editForm.value = {
      name: selectedNode.value.name,
      code: selectedNode.value.code,
      description: selectedNode.value.description || ''
    }
    editDialogVisible.value = true
  } else if (selectedNode.value.type === 'equipment') {
    // 编辑设备
    editMode.value = 'edit'
    editType.value = 'equipment'
    editForm.value = {
      name: selectedNode.value.name,
      code: selectedNode.value.code,
      description: selectedNode.value.description || ''
    }
    editDialogVisible.value = true
  } else {
    ElMessage.warning('只能编辑工作中心或设备')
  }
}

const handleAddProductionLine = () => {
  // 打开产线添加对话框
  productionLineForm.value = {
    name: '',
    code: '',
    description: ''
  }
  productionLineDialogVisible.value = true
}

const handleConfirmAddProductionLine = async () => {
  if (!productionLineForm.value.name.trim()) {
    ElMessage.warning('请输入产线名称')
    return
  }
  if (!productionLineForm.value.code.trim()) {
    ElMessage.warning('请输入产线编码')
    return
  }

  // 检查产线编码是否已存在
  const existingLine = productionLines.value.find(line => 
    line.code === productionLineForm.value.code || line.name === productionLineForm.value.name
  )
  if (existingLine) {
    ElMessage.warning('产线名称或编码已存在，请使用其他名称和编码')
    return
  }

  try {
    loading.value = true
    
    // 调用API创建新产线
    const response = await createProductionLine({
      name: productionLineForm.value.name,
      code: productionLineForm.value.code,
      description: productionLineForm.value.description || '',
      isActive: true,
      sortOrder: productionLines.value.length + 1
    })
    
    if (response.data?.code === 200 && response.data?.data) {
      // 重新获取产线列表以确保数据同步
      await initializeData()
      
      // 自动选择新添加的产线（通过名称查找）
      const newLine = productionLines.value.find(line => 
        line.name === productionLineForm.value.name
      )
      if (newLine) {
        selectedProductionLine.value = newLine.id
        // 加载新产线的工作中心数据
        await handleProductionLineChange(newLine.id)
      }
      
      productionLineDialogVisible.value = false
      ElMessage.success('产线添加成功')
    } else {
      ElMessage.error('添加产线失败: ' + (response.data?.message || '未知错误'))
    }
  } catch (error) {
    console.error('添加产线失败:', error)
    ElMessage.error('添加产线失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

const handleDelete = async () => {
  if (!selectedNode.value) {
    ElMessage.warning('请先选择要删除的节点')
    return
  }

  if (selectedNode.value.type === 'production-line') {
    ElMessage.warning('不能删除产线')
    return
  }

  // 构建删除确认信息
  let confirmMessage = `确定要删除 "${selectedNode.value.name}" 吗？`

  try {
    await ElMessageBox.confirm(
      confirmMessage,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    loading.value = true

    if (selectedNode.value.type === 'work-center') {
      // 调用API删除工作中心
      const response = await deleteWorkCenter(selectedNode.value.id)
      if (response.data?.code === 200) {
        // 清除当前产线的缓存
        clearCache(selectedProductionLine.value)
        // 重新获取当前产线的工作中心数据以确保数据同步
        await handleProductionLineChange(selectedProductionLine.value)
        ElMessage.success('工作中心删除成功')
      } else {
        ElMessage.error('删除工作中心失败: ' + (response.data?.message || '未知错误'))
      }
    } else if (selectedNode.value.type === 'equipment') {
      // 调用API删除设备
      const response = await deleteEquipment(selectedNode.value.id)
      if (response.data?.code === 200) {
        // 清除当前产线的缓存
        clearCache(selectedProductionLine.value)
        // 重新获取当前产线的工作中心数据以确保数据同步
        await handleProductionLineChange(selectedProductionLine.value)
        ElMessage.success('设备删除成功')
      } else {
        ElMessage.error('删除设备失败: ' + (response.data?.message || '未知错误'))
      }
    }
    selectedNode.value = null
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请检查网络连接')
    }
  } finally {
    loading.value = false
  }
}

const handleSave = () => {
  saving.value = true
  setTimeout(() => {
    saving.value = false
    ElMessage.success('保存成功')
  }, 1000)
}

const getDialogTitle = () => {
  const typeMap = {
    'production-line': '产线',
    'work-center': '工作中心',
    'equipment': '设备',
    'device': '设备'
  }
  const modeMap = {
    'add': '新增',
    'edit': '编辑'
  }
  return `${modeMap[editMode.value]}${typeMap[editType.value]}`
}

const handleConfirmEdit = async () => {
  if (!editForm.value.name.trim()) {
    ElMessage.warning('请输入名称')
    return
  }
  if (!editForm.value.code.trim()) {
    ElMessage.warning('请输入编码')
    return
  }

  try {
    loading.value = true

    if (editMode.value === 'add') {
      if (editType.value === 'work-center') {
        // 调用API添加新的工作中心
        const workCenterData = {
          name: editForm.value.name,
          code: editForm.value.code,
          description: editForm.value.description || '',
          sortOrder: workCenters.value.length + 1
        }
        console.log('正在创建工作中心:', {
          productionLineId: selectedProductionLine.value,
          data: workCenterData
        })
        const response = await createWorkCenter(selectedProductionLine.value, workCenterData)
        
        if ((response.data?.code === 200 || response.data?.code === 201) && response.data?.data) {
          // 清除当前产线的缓存
          clearCache(selectedProductionLine.value)
          // 重新获取当前产线的工作中心数据以确保数据同步
          await handleProductionLineChange(selectedProductionLine.value)
          ElMessage.success('工作中心添加成功')
          console.log('工作中心创建成功:', response.data.data)
        } else {
          console.error('创建工作中心失败:', response.data)
          ElMessage.error('添加工作中心失败: ' + (response.data?.message || '未知错误'))
        }
      } else if (editType.value === 'device') {
        // 调用API添加新的设备到选中的工作中心
        const targetCenter = workCenters.value.find(center => center.id === selectedNode.value.id)
        if (targetCenter) {
          const equipmentData = {
            name: editForm.value.name,
            code: editForm.value.code,
            description: editForm.value.description || '',
            sortOrder: targetCenter.equipment.length + 1
          }
          console.log('正在创建设备:', {
            workCenterId: selectedNode.value.id,
            data: equipmentData
          })
          const response = await createEquipment(selectedNode.value.id, equipmentData)
          
          if ((response.data?.code === 200 || response.data?.code === 201) && response.data?.data) {
            // 清除当前产线的缓存
            clearCache(selectedProductionLine.value)
            // 重新获取当前产线的工作中心数据以确保数据同步
            await handleProductionLineChange(selectedProductionLine.value)
            ElMessage.success('设备添加成功')
            console.log('设备创建成功:', response.data.data)
          } else {
            console.error('创建设备失败:', response.data)
            ElMessage.error('添加设备失败: ' + (response.data?.message || '未知错误'))
          }
        }
      }
    } else if (editMode.value === 'edit') {
      if (editType.value === 'work-center') {
        // 调用API编辑工作中心
        const response = await updateWorkCenter(selectedNode.value.id, {
          name: editForm.value.name,
          code: editForm.value.code,
          description: editForm.value.description || '',
          productionLineId: parseInt(selectedProductionLine.value),
          isActive: true,
          sortOrder: 1
        })
        
        if (response.data?.code === 200) {
          // 清除当前产线的缓存
          clearCache(selectedProductionLine.value)
          // 重新获取当前产线的工作中心数据以确保数据同步
          await handleProductionLineChange(selectedProductionLine.value)
          ElMessage.success('工作中心编辑成功')
          console.log('工作中心编辑成功:', response.data.data)
        } else {
          console.error('编辑工作中心失败:', response.data)
          ElMessage.error('编辑工作中心失败: ' + (response.data?.message || '未知错误'))
        }
      } else if (editType.value === 'equipment') {
        // 调用API编辑设备
        const equipmentUpdateData = {
          name: editForm.value.name,
          code: editForm.value.code,
          description: editForm.value.description || '',
          sortOrder: 1
        }
        console.log('正在编辑设备:', {
          equipmentId: selectedNode.value.id,
          data: equipmentUpdateData
        })
        const response = await updateEquipment(selectedNode.value.id, equipmentUpdateData)
        
        if (response.data?.code === 200) {
          // 清除当前产线的缓存
          clearCache(selectedProductionLine.value)
          // 重新获取当前产线的工作中心数据以确保数据同步
          await handleProductionLineChange(selectedProductionLine.value)
          ElMessage.success('设备编辑成功')
          console.log('设备编辑成功:', response.data.data)
        } else {
          console.error('编辑设备失败:', response.data)
          ElMessage.error('编辑设备失败: ' + (response.data?.message || '未知错误'))
        }
      }
    }

    editDialogVisible.value = false
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  await initializeData()
  // 如果有产线数据，自动选择第一个产线并加载工作中心数据
  if (productionLines.value && productionLines.value.length > 0) {
    selectedProductionLine.value = productionLines.value[0].id
    await handleProductionLineChange(selectedProductionLine.value)
  }
})
</script>

<style scoped>
.work-center-settings {
  padding: 20px;
}

.toolbar-card {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.production-line-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.label {
  font-weight: 500;
  color: #303133;
}

.toolbar-actions {
  display: flex;
  gap: 10px;
}

.work-center-tree-card {
  min-height: 600px;
}

.work-center-content {
  position: relative;
  min-height: 500px;
}

.color-legend {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #E4E7ED;
  border-radius: 6px;
  padding: 8px 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  backdrop-filter: blur(5px);
}

.legend-title {
  font-size: 11px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 6px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 16px;
  height: 12px;
  border-radius: 3px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.legend-label {
  font-size: 10px;
  color: #606266;
  font-weight: 500;
}

.production-line-color {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.work-center-color {
  background: linear-gradient(135deg, #4FC3F7 0%, #0288D1 100%);
}

.device-color {
  background: linear-gradient(135deg, #9E9E9E 0%, #757575 100%);
}

.hierarchy-container {
  position: relative;
  width: 100%;
  height: auto;
  overflow: auto;
  padding: 20px;
}

.top-level-node {
  display: flex;
  justify-content: center;
  margin-bottom: 50px;
}

.work-center-levels {
  display: flex;
  flex-direction: column;
}

.level-1 {
  margin-bottom: 30px;
}

.hierarchy-grid {
  display: grid;
  grid-template-rows: auto auto;
  row-gap: 30px;
  width: 100%;
  transition: all 0.3s ease;
}

.level {
  display: grid;
  gap: 20px;
  justify-items: center;
  align-items: start;
}

.level-1, .level-2 {
  grid-template-columns: repeat(var(--grid-columns, 1), 1fr);
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

.work-center-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 40px;
  width: 100%;
  transition: all 0.3s ease;
}

.device-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

/* 统一设备节点间距 - 使用flex gap确保一致性 */
.device-group {
  gap: 8px;
}

/* 清除所有设备节点的默认margin */
.device-node {
  margin: 0 !important;
}

/* 确保嵌套div也不影响间距 */
.device-group > div {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.node {
  position: relative;
  min-width: 90px;
  min-height: 60px;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 设备节点特殊宽度覆盖 */
.device-node.node {
  min-width: 65px;
  padding: 5px;
}

.production-line-node {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  min-width: 160px;
  min-height: 70px;
  font-size: 16px;
  font-weight: bold;
}

.production-line-node .node-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px;
  height: 100%;
}

.production-line-node .node-title {
  font-size: 14px;
  font-weight: 700;
  line-height: 1.2;
  text-align: center;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.production-line-node .node-code {
  font-size: 11px;
  font-weight: 500;
  line-height: 1.1;
  text-align: center;
  color: #E8E3F3;
  opacity: 0.9;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.work-center-node {
  background: linear-gradient(135deg, #4FC3F7 0%, #0288D1 100%);
  color: #FFFFFF;
  min-width: 90px;
  min-height: 60px;
  max-height: 60px;
  height: 60px;
  overflow: hidden;
  position: relative;
  border: 1px solid #01579B;
  box-shadow: 0 3px 10px rgba(2, 136, 209, 0.3);
}

.work-center-node .node-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 4px 2px;
  box-sizing: border-box;
}

.work-center-node .node-title {
  font-size: 10px;
  line-height: 1.1;
  word-break: break-word;
  hyphens: auto;
  text-align: center;
  width: 100%;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
  max-height: 44px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  color: #FFFFFF;
}

/* 长文本节点的字体大小调整 */
.work-center-node.long-text .node-title {
  font-size: 9px;
  line-height: 1;
  -webkit-line-clamp: 5;
  line-clamp: 5;
  max-height: 45px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.25);
  color: #FFFFFF;
}

.work-center-node.long-text .node-code {
  font-size: 9px;
  line-height: 1;
  font-weight: 500;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.15);
  color: #E3F2FD;
}

.work-center-node .node-code {
  font-size: 10px;
  line-height: 1.1;
  margin-top: 0px;
  opacity: 0.95;
  font-weight: 500;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.15);
  color: #E3F2FD;
}

.device-node {
  background: linear-gradient(135deg, #9E9E9E 0%, #757575 100%);
  color: white;
  min-width: 65px;
  min-height: 40px;
}

.device-node .node-title {
  font-size: 10px;
  line-height: 1.1;
  word-break: break-word;
  text-align: center;
}

.device-node .node-code {
  font-size: 8px;
  line-height: 1.1;
  text-align: center;
}

.device-node .node-content {
  gap: 3px;
}

.node:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.work-center-node:hover {
  background: linear-gradient(135deg, #29B6F6 0%, #0277BD 100%);
  box-shadow: 0 5px 15px rgba(2, 136, 209, 0.4);
  transform: translateY(-2px);
}

.node.selected {
  border: 3px solid #FFD60A;
  box-shadow: 0 0 0 2px rgba(255, 214, 10, 0.3), 0 4px 15px rgba(255, 214, 10, 0.2);
  transform: translateY(-1px);
}

.work-center-node.selected {
  background: linear-gradient(135deg, #42A5F5 0%, #1976D2 100%);
  border: 3px solid #FFD60A;
  box-shadow: 0 0 0 2px rgba(255, 214, 10, 0.4), 0 5px 20px rgba(2, 136, 209, 0.4);
}

.node-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.node-title {
  font-weight: 600;
  font-size: 14px;
  line-height: 1.2;
  text-align: center;
  word-break: break-all;
}

.node-code {
  font-size: 12px;
  opacity: 0.9;
  font-weight: 400;
  line-height: 1.1;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 15px;
  }
  
  .production-line-selector {
    width: 100%;
    justify-content: center;
  }
  
  .toolbar-actions {
    width: 100%;
    justify-content: center;
  }
  
  .level-1, .level-2 {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
  
  .work-center-column {
    min-height: 35px;
  }
  
  .node {
    min-width: 75px;
    min-height: 35px;
    padding: 5px;
  }
  
  .device-node.node {
    min-width: 55px;
    padding: 4px;
  }
  
  .node-title {
    font-size: 12px;
  }
  
  .node-code {
    font-size: 10px;
  }
  
  .production-line-node {
    min-width: 120px;
    min-height: 55px;
    max-height: 55px;
    height: 55px;
    font-size: 14px;
  }
  
  .production-line-node .node-title {
    font-size: 12px;
  }
  
  .production-line-node .node-code {
    font-size: 9px;
  }
  
  .work-center-node {
    min-height: 50px;
    max-height: 50px;
    height: 50px;
  }
  
  .work-center-node .node-title {
    font-size: 10px;
    -webkit-line-clamp: 4;
    line-clamp: 4;
    max-height: 40px;
    line-height: 1;
  }
  
  .work-center-node .node-code {
    font-size: 8px;
    line-height: 1;
  }
  
  .work-center-node.long-text .node-title {
    font-size: 8px;
    -webkit-line-clamp: 5;
    line-clamp: 5;
    max-height: 40px;
    line-height: 1;
  }
  
  .work-center-node.long-text .node-code {
    font-size: 7px;
    line-height: 1;
  }
  
  .hierarchy-container {
    padding: 10px;
    --grid-columns: 2 !important;
  }
  
  .level-1 {
    margin-bottom: 20px;
  }
  
  .color-legend {
    top: 5px;
    left: 5px;
    padding: 6px 8px;
    border-radius: 4px;
  }
  
  .legend-title {
    font-size: 9px;
    margin-bottom: 4px;
  }
  
  .legend-items {
    gap: 3px;
  }
  
  .legend-item {
    gap: 4px;
  }
  
  .legend-color {
    width: 12px;
    height: 10px;
    border-radius: 2px;
  }
  
  .legend-label {
    font-size: 8px;
  }
}

/* 骨架屏样式 */
.skeleton-container {
  width: 100%;
  padding: 20px;
}

.skeleton-legend {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 120px;
  height: 80px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6px;
}

.skeleton-production-line {
  width: 160px;
  height: 70px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8px;
  margin: 0 auto 50px;
}

.skeleton-work-centers {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
  margin-bottom: 30px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.skeleton-work-center {
  width: 90px;
  height: 60px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8px;
  margin: 0 auto;
}

.skeleton-devices {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.skeleton-device {
  width: 65px;
  height: 40px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8px;
  margin: 0 auto;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style> 