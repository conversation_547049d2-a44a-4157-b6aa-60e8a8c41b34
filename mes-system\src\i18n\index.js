import { createI18n } from 'vue-i18n'

// 获取用户保存的语言设置或默认语言
const getDefaultLanguage = () => {
  const savedLanguage = localStorage.getItem('language')
  if (savedLanguage && ['zh', 'en'].includes(savedLanguage)) {
    return savedLanguage
  }
  // 检查浏览器语言设置
  const browserLanguage = navigator.language || navigator.userLanguage
  if (browserLanguage.startsWith('zh')) {
    return 'zh'
  }
  return 'zh' // 默认中文
}

const messages = {
  zh: {
    login: {
      cancelmessage: '取消',
      loginlogin: '登录成功',
      loginloginsystemsystem: '登录系统',
      msguseruser: '登录失败，请检查用户名和密码',
      passwordpassword: '密码',
      phpasswordpassword: '请输入密码',
      phuseruser: '请输入用户名',
      textsystemsystem: 'MES系统登录',
      useruser: '用户名',
      welcome: '欢迎登录',
      welcomeText: 'MES智能制造执行系统',
      slogan: '智能制造执行系统',
      subSlogan: '打造智能工厂，引领工业4.0',
      rememberMe: '记住我',
      forgotPassword: '忘记密码？',
      copyright: '© 2024 MES智能制造执行系统. All rights reserved.',
      loginSuccess: '登录成功',
      loginButton: '登录',
      usernameRequired: '请输入用户名',
      passwordRequired: '请输入密码'
    },
    common: {
      add: '新增',
      edit: '编辑',
      delete: '删除',
      save: '保存',
      cancel: '取消',
      confirm: '确认',
      close: '关闭',
      search: '搜索',
      query: '查询',
      retry: '重试',
      refresh: '刷新',
      export: '导出',
      import: '导入',
      loading: '加载中...',
      saving: '保存中...',
      success: '操作成功',
      error: '操作失败',
      warning: '警告',
      info: '提示'
    },
    system: {
      title: '系统管理',
      userManagement: '用户管理',
      rolePermission: '角色权限',
      menuManagement: '菜单管理',
      i18nManagement: '多语言管理',
      dataDict: '数据字典'
    },
    i18n: {
      management: '多语言管理',
      addTranslation: '新增翻译',
      editTranslation: '编辑翻译',
      deleteTranslation: '删除翻译',
      extractText: '提取文本',
      importExport: '导入/导出',
      translationKey: '翻译键',
      module: '模块',
      description: '描述',
      status: {
        completed: '已完成',
        incomplete: '未完成', 
        missing: '缺失翻译'
      },
      stats: {
        totalKeys: '总翻译键',
        supportedLanguages: '支持语言',
        completedTranslations: '已完成翻译',
        completionRate: '完成度'
      },
      extract: {
        selectPage: '选择页面',
        extractRules: '提取规则',
        modulePrefix: '模块前缀',
        startExtract: '开始提取',
        extractSuccess: '提取成功',
        batchAdd: '批量添加到翻译列表'
      }
    },
    dataDict: {
      // 页面标题和基本操作
      management: '数据字典管理',
      addDict: '新增字典',
      import: '导入',
      export: '导出',
      refresh: '刷新',
      search: '搜索',
      reset: '重置',
      
      // 搜索表单
      dictName: '字典名称',
      dictCode: '字典编码',
      dictType: '字典类型',
      status: '状态',
      searchNamePlaceholder: '请输入字典名称',
      searchCodePlaceholder: '请输入字典编码',
      selectTypePlaceholder: '请选择字典类型',
      selectStatusPlaceholder: '请选择状态',
      
      // 字典类型选项
      systemConfig: '系统配置',
      businessConfig: '业务配置',
      otherConfig: '其他配置',
      
      // 状态选项
      enabled: '启用',
      disabled: '禁用',
      
      // 批量操作
      selectedItems: '已选择 {count} 项',
      batchEnable: '批量启用',
      batchDisable: '批量禁用',
      batchDelete: '批量删除',
      
      // 表格列标题
      id: 'ID',
      name: '字典名称',
      code: '字典编码',
      type: '字典类型',
      description: '描述',
      itemCount: '字典项数量',
      createTime: '创建时间',
      updateTime: '更新时间',
      operation: '操作',
      
      // 操作按钮
      edit: '编辑',
      dictItems: '字典项',
      copy: '复制',
      delete: '删除',
      
      // 对话框标题
      addDictDialog: '新增字典',
      editDictDialog: '编辑字典',
      dictItemsDialog: '字典项管理',
      addItemDialog: '新增字典项',
      editItemDialog: '编辑字典项',
      importDialog: '导入数据字典',
      
      // 表单标签
      namePlaceholder: '请输入字典名称',
      codePlaceholder: '请输入字典编码（英文大写+下划线）',
      descriptionPlaceholder: '请输入字典描述',
      sort: '排序',
      remark: '备注',
      remarkPlaceholder: '请输入备注信息',
      
      // 字典项相关
      addItem: '新增字典项',
      itemsSort: '排序',
      itemLabel: '字典标签',
      itemValue: '字典值',
      itemLabelPlaceholder: '请输入字典标签',
      itemValuePlaceholder: '请输入字典值',
      itemDescriptionPlaceholder: '请输入字典项描述',
      extProperty1: '扩展属性1',
      extProperty2: '扩展属性2',
      extProperty3: '扩展属性3',
      extProperty1Placeholder: '请输入扩展属性1',
      extProperty2Placeholder: '请输入扩展属性2',
      extProperty3Placeholder: '请输入扩展属性3',
      
      // 上传相关
      uploadText: '将文件拖到此处，或',
      clickUpload: '点击上传',
      uploadTip: '只能上传 excel 文件，且不超过 5MB',
      
      // 按钮文本
      cancel: '取消',
      confirm: '确定',
      confirmImport: '确定导入',
      
      // 提示信息
      itemsCount: '{count} 项',
      enabledText: '启用',
      disabledText: '禁用',
      
      // 操作消息
      addSuccess: '新增成功',
      editSuccess: '编辑成功',
      deleteSuccess: '删除成功',
      addFailed: '新增失败',
      editFailed: '编辑失败',
      deleteFailed: '删除失败',
      loadFailed: '加载数据失败',
      operationFailed: '操作失败',
      deleteConfirm: '确定要删除字典"{name}"吗？删除后字典项也会一并删除！',
      deleteTitle: '删除确认',
      unknown: '未知'
    },
    menu: {
      dashboard: '仪表盘',
      engineering: '工程管理',
      production: '生产管理',
      quality: '质量管理',
      warehouse: '仓储管理',
      equipment: '设备管理',
      hr: '人事管理',
      system: '系统管理',
      // 详细菜单翻译
      home: '首页',
      // 工程管理
      modelManagement: '物料主数据',
      // 生产管理
      workCenterSettings: '工作中心设置',
      // 人事管理
      employeeManagement: '员工管理',
      companySettings: '公司设置',
      attendanceManagement: '考勤管理',
      // 系统管理
      userManagement: '用户管理',
      roleManagement: '角色管理',
      menuManagement: '菜单管理',
      i18nManagement: '多语言管理',
      dataDictionary: '数据字典',
      // WIS相关
      wisQuery: 'WIS查询',
      wisPDFViewer: 'WIS PDF查看器'
    },
    engineering: {
      // 物料主数据管理
      materialManagement: '物料主数据管理',
      productModel: '成品型号',
      componentModel: '组件型号', 
      partModel: '零件型号',
      auxiliaryModel: '辅料包材型号',
      hardwarePlasticModel: '五金塑胶型号',
      
      // 动态物料分类（从API获取）
      finishedProductModel: '成品型号',
      subAssemblyModel: '组件型号',
      componentPartModel: '零件型号',
      auxiliaryPackagingModel: '辅料包材型号',
      plasticMetalModel: '塑胶五金型号',
      administrativeMaterial: '行政物料',
      virtualMaterial: '虚拟物料型号',
      
      // 物料分类描述
      finishedProductDesc: '最终制成品',
      subAssemblyDesc: '产品组成部件',
      componentPartDesc: '最小粒度的零件',
      auxiliaryPackagingDesc: '辅助和包装材料',
      plasticMetalDesc: '塑胶五金制品',
      administrativeDesc: '行政后勤办公用品',
      virtualDesc: '虚拟物料类型',
      
      // 型号类型描述
      productModelDesc: '🏭 最终制成品',
      componentModelDesc: '⚙️ 产品组成部件',
      partModelDesc: '🔩 最小粒度零件',
      auxiliaryModelDesc: '📋 辅助和包装材料',
      hardwarePlasticModelDesc: '🔧 五金塑胶制品',
      
      // 动态分类相关
      loadingCategories: '正在加载物料分类...',
      loadingModels: '正在加载型号数据...',
      noModelsFound: '未找到型号数据',
      categoriesLoadedSuccess: '成功加载 {count} 个物料分类',
      usingDefaultCategories: '未配置物料分类字典，使用默认分类',
      loadCategoriesError: '加载物料分类失败',
      noCategoriesFound: '未找到物料分类数据',
      retryLoad: '重新加载',
      
      // 配置提示相关
      configMaterialCategories: '配置物料分类',
      configHintTitle: '如何配置物料分类字典',
      configHintContent: '为了让物料管理模块更好地适应您的业务需求，请按照以下步骤配置物料分类字典：',
      configStep1: '进入【系统管理】-【数据字典】页面',
      configStep2: '创建字典编码为"MATERIAL_CATEGORY"的字典',
      configStep3: '为该字典添加相应的字典项，每个字典项代表一个物料分类',
      goToConfig: '去配置',
      
      // 通用操作
      addModel: '新增型号',
      editModel: '编辑型号',
      viewModel: '查看型号',
      deleteModel: '删除型号',
      searchModel: '搜索型号',
      view: '查看',
      
      // 视图标签页
      basicInfo: '基本信息',
      salesView: '销售视图',
      warehouseView: '仓库视图',
      financeView: '财务视图',
      productionView: '生产视图',
      purchaseView: '采购视图',
      importExportView: '进出口视图',
      mrpView: 'MRP计划视图',
      
      // 基本信息字段
      modelName: '型号名称',
      modelCode: '型号编码',
      category: '分类',
      specification: '规格参数',
      status: '状态',
      image: '图片',
      unit: '单位',
      material: '材质',
      size: '尺寸',
      imageSaveToCloudServer: '图片保存到云服务器',
      
      // 销售信息字段
      salesPrice: '销售价格',
      salesUnit: '销售单位',
      minOrderQty: '最小订单量',
      maxOrderQty: '最大订单量',
      leadTime: '交货期',
      warranty: '保修期',
      
      // 仓库信息字段
      storageLocation: '存储位置',
      safetyStock: '安全库存',
      maxStock: '最大库存',
      reorderPoint: '再订货点',
      storageCondition: '存储条件',
      shelfLife: '保质期',
      
      // 财务信息字段
      standardCost: '标准成本',
      averageCost: '平均成本',
      valuationMethod: '估价方法',
      taxRate: '税率',
      accountSubject: '会计科目',
      costCenter: '成本中心',
      
      // 生产信息字段
      productionType: '生产类型',
      productionLeadTime: '生产周期',
      setupTime: '准备时间',
      cycleTime: '循环时间',
      batchSize: '批量大小',
      workCenter: '工作中心',
      qualityStandard: '质量标准',
      
      // 采购信息字段
      supplier: '供应商',
      purchasePrice: '采购价格',
      purchaseUnit: '采购单位',
      minPurchaseQty: '最小采购量',
      purchaseLeadTime: '采购周期',
      qualityLevel: '质量等级',
      purchaseNote: '采购备注',
      
      // 进出口信息字段
      hsCode: '海关编码',
      originCountry: '原产地',
      importTaxRate: '进口税率',
      exportTaxRefund: '出口退税率',
      isDangerous: '是否危险品',
      transportMode: '运输方式',
      packingRequirement: '包装要求',
      inspectionRequired: '需要检验检疫',
      licenseRequirement: '许可证要求',
      
      // MRP计划信息字段
      planningStrategy: '计划策略',
      planningCycle: '计划周期',
      forecastMethod: '预测方法',
      abcCategory: 'ABC分类',
      demandSource: '需求来源',
      planner: '计划员',
      safetyStockDays: '安全库存天数',
      lotSizeRule: '批量规则',
      demandTimeFence: '需求时界',
      supplyTimeFence: '供应时界',
      planningNote: '计划备注',
      
      // 占位符和提示
      selectCategory: '请选择分类',
      selectUnit: '请选择单位',
      
      // 状态消息
      addSuccess: '新增型号成功',
      editSuccess: '编辑型号成功',
      deleteSuccess: '删除型号成功',
      deleteConfirm: '确定要删除该型号吗？',
      
      // 错误消息
      loadError: '加载数据失败',
      saveError: '保存失败',
      deleteError: '删除失败'
    },
    user: {
      // 页面标题和基本操作
      management: '用户管理',
      addUser: '添加用户',
      editUser: '编辑用户',
      selectEmployee: '选择员工',
      // 搜索和筛选
      search: '搜索',
      searchPlaceholder: '请输入',
      username: '用户名',
      name: '姓名',
      department: '部门',
      role: '角色',
      email: '邮箱',
      phone: '电话',
      password: '密码',
      status: '状态',
      createTime: '创建时间',
      operation: '操作',
      // 状态
      active: '启用',
      inactive: '禁用',
      all: '全部',
      // 操作按钮
      edit: '编辑',
      resetPassword: '重置密码',
      enable: '启用',
      disable: '禁用',
      import: '导入',
      export: '导出',
      confirm: '确定',
      cancel: '取消',
      // 对话框和提示
      addUserDialog: '新增用户',
      editUserDialog: '编辑用户',
      selectEmployeeDialog: '选择员工',
      // 占位符
      selectDepartment: '请选择部门',
      selectRole: '请选择角色',
      // 验证消息
      usernameRequired: '请输入用户名',
      usernameLength: '长度在 3 到 20 个字符',
      nameRequired: '请输入姓名',
      departmentRequired: '请选择部门',
      roleRequired: '请选择角色',
      emailFormat: '请输入正确的邮箱地址',
      phoneFormat: '请输入正确的手机号码',
      passwordRequired: '请输入密码',
      passwordLength: '长度在 6 到 20 个字符',
      // 操作提示
      confirmResetPassword: '确定要重置用户 {name} 的密码吗？',
      confirmDisable: '确定要禁用用户 {name} 吗？',
      confirmEnable: '确定要启用用户 {name} 吗？',
      warning: '警告',
      // 成功消息
      addSuccess: '添加用户成功',
      updateSuccess: '更新用户成功',
      resetPasswordSuccess: '密码重置成功',
      enableSuccess: '启用成功',
      disableSuccess: '禁用成功',
      // 错误消息
      fetchListError: '获取用户列表失败，请稍后重试',
      operationError: '操作失败，请稍后重试',
      userIdNotFound: '用户ID不存在',
      enableError: '启用用户失败',
      disableError: '禁用用户失败',
      retryLater: '请重试',
      // 功能开发中
      importInDevelopment: '导入功能开发中',
      exportInDevelopment: '导出功能开发中'
    },
    role: {
      // 页面标题和基本操作
      management: '角色权限',
      roleDefinition: '角色定义',
      roleUsers: '角色用户设置',
      roleMenus: '角色菜单设置',
      addRole: '添加角色',
      editRole: '编辑角色',
      // 搜索和筛选
      search: '搜索',
      searchPlaceholder: '请输入角色名称',
      roleName: '角色名称',
      roleCode: '角色编码',
      roleDescription: '角色描述',
      status: '状态',
      createTime: '创建时间',
      operation: '操作',
      // 状态
      active: '启用',
      inactive: '禁用',
      // 操作按钮
      edit: '编辑',
      enable: '启用',
      disable: '禁用',
      delete: '删除',
      save: '保存',
      reset: '重置',
      confirm: '确定',
      cancel: '取消',
      // 对话框和提示
      addRoleDialog: '新增角色',
      editRoleDialog: '编辑角色',
      deleteConfirm: '删除确认',
      confirmDelete: '确定要删除角色"{name}"吗？',
      // 表单验证
      roleNameRequired: '请输入角色名称',
      roleNameLength: '长度在 2 到 20 个字符',
      roleCodeRequired: '请输入角色编码',
      roleCodeLength: '长度在 2 到 30 个字符',
      // 权限相关
      permissions: '权限',
      add: '新增',
      editPermission: '编辑',
      deletePermission: '删除',
      verify: '审核',
      import: '导入',
      export: '导出',
      query: '查询',
      report: '报表预览',
      // 用户相关
      username: '用户名',
      name: '姓名',
      department: '部门',
      position: '职称',
      email: '邮箱',
      phone: '电话',
      // 角色用户设置
      roleUsersTable: '角色关联的用户信息表',
      noUsersForRole: '该角色暂无关联用户',
      saveRolePermissions: '保存角色权限',
      resetRolePermissions: '重置',
      // 角色菜单设置
      selectRole: '请选择角色',
      initializePermissions: '初始化角色权限',
      initializingPermissions: '初始化中...',
      initializeConfirmTitle: '确认初始化',
      initializeConfirmMessage: '此操作将初始化系统默认的角色菜单权限数据，如果已存在权限数据将不会重复初始化。是否继续？',
      saveMenuPermissions: '保存菜单权限',
      resetMenuPermissions: '重置',
      noRoleSelected: '请先选择一个角色',
      loadingMenus: '正在加载菜单数据...',
      loadingUsers: '正在加载用户数据...',
      // 成功消息
      addRoleSuccess: '添加角色成功',
      updateRoleSuccess: '更新角色成功',
      deleteRoleSuccess: '删除角色成功',
      enableRoleSuccess: '启用角色成功',
      disableRoleSuccess: '禁用角色成功',
      savePermissionsSuccess: '保存角色权限成功',
      saveMenusSuccess: '保存菜单权限成功',
      resetPermissionsSuccess: '已重置角色权限',
      initializeSuccess: '初始化角色权限成功',
      // 错误消息
      fetchRoleListError: '获取角色列表失败，请稍后重试',
      saveRoleError: '保存角色失败，请稍后重试',
      deleteRoleError: '删除角色失败，请稍后重试',
      toggleStatusError: '切换角色状态失败，请稍后重试',
      getRoleUsersError: '获取角色用户列表失败',
      loadRoleUsersError: '加载角色用户失败，请稍后重试',
      getMenuTreeError: '获取菜单树失败，请稍后重试',
      getRoleMenusError: '获取角色菜单失败，请稍后重试',
      saveMenusError: '保存角色菜单权限失败，请稍后重试',
      initializeError: '初始化角色权限失败，请稍后重试',
      // 提示消息
      selectRoleFirst: '请先选择一个角色',
      operationSuccess: '操作成功',
      operationError: '操作失败',
      retryLater: '请稍后重试'
    },
    menuManagement: {
      // 页面标题和基本操作
      management: '菜单管理',
      addMenu: '新增菜单',
      initializeMenus: '初始化菜单',
      initializing: '初始化中...',
      refresh: '刷新',
      // 表格列标题
      menuName: '菜单名称',
      menuId: '菜单ID',
      path: '路径',
      type: '类型',
      sortOrder: '排序',
      status: '状态',
      operation: '操作',
      // 菜单类型
      menuItem: '菜单项',
      subMenu: '子菜单',
      // 状态
      enabled: '启用',
      disabled: '禁用',
      // 操作按钮
      edit: '编辑',
      addChild: '添加子菜单',
      delete: '删除',
      cancel: '取消',
      confirm: '确定',
      saving: '保存中...',
      // 对话框标题
      addMenuDialog: '新增菜单',
      editMenuDialog: '编辑菜单',
      addChildMenuDialog: '添加子菜单',
      menuSettings: '菜单设置',
      // 表单字段
      parentMenu: '父级菜单',
      menuType: '菜单类型',
      menuPath: '菜单路径',
      menuIcon: '菜单图标',
      sortNumber: '排序序号',
      menuStatus: '菜单状态',
      menuDescription: '菜单描述',
      pageGeneration: '页面生成',
      // 占位符文本
      enterMenuName: '请输入菜单名称',
      enterMenuId: '请输入菜单ID，如: system-users',
      pathPlaceholder: '如：/home/<USER>/menus',
      selectIcon: '选择图标',
      enterDescription: '请输入菜单描述（可选）',
      // 验证消息
      menuNameRequired: '请输入菜单名称',
      menuNameLength: '长度在 2 到 50 个字符',
      menuIdRequired: '请输入菜单ID',
      menuIdPattern: '菜单ID必须以字母开头，只能包含字母、数字、短横线和下划线',
      menuTypeRequired: '请选择菜单类型',
      menuPathRequired: '菜单项必须设置路径',
      menuIconRequired: '请选择菜单图标',
      sortOrderRequired: '请输入排序序号',
      // 页面生成相关
      autoGeneratePageFile: '自动生成页面文件',
      noGeneratePageFile: '不生成页面文件',
      generateTip: '开启后将自动生成基于PageTemplate的标准页面文件',
      pagePreview: '页面预览',
      pageGenerationDetails: '页面生成详情预览',
      viewDetails: '查看详情',
      // 预览相关
      basicInfo: '基本信息',
      pageName: '页面名称',
      componentName: '组件名称',
      vueFile: 'Vue文件',
      apiFile: 'API文件',
      pageFeatures: '页面功能特性',
      apiEndpoints: 'API接口清单',
      usageInstructions: '使用说明',
      generatedFilesResult: '生成文件结果',
      close: '关闭',
      downloadVueFile: '下载Vue文件',
      downloadApiFile: '下载API文件',
      copyVueCode: '复制Vue代码',
      copyApiCode: '复制API代码',
      copyRouteCode: '复制路由代码',
      // 确认对话框
      deleteConfirm: '删除确认',
      confirmDeleteMenu: '确定要删除菜单"{name}"吗？如果该菜单有子菜单，子菜单也会被删除。',
      confirmInitializeMenus: '确定要初始化菜单数据吗？这将会添加系统默认的菜单结构。',
      initializeConfirm: '初始化确认',
      // 成功消息
      loadMenuListSuccess: '菜单列表加载成功',
      deleteSuccess: '删除成功',
      addMenuSuccess: '新增菜单成功',
      editMenuSuccess: '编辑菜单成功',
      initializeMenusSuccess: '菜单数据初始化成功',
      generatePageSuccess: '页面模板生成成功！您可以下载文件或复制代码。',
      downloadSuccess: '下载成功',
      copySuccess: '代码已复制到剪贴板',
      routeCodeCopied: '路由配置代码已复制到剪贴板',
      // 错误消息
      loadMenuListError: '获取菜单列表失败',
      deleteError: '删除失败',
      operationError: '操作失败',
      initializeMenusError: '初始化菜单失败',
      networkError: '网络错误，请稍后重试',
      generatePageError: '页面模板生成失败',
      downloadError: '下载失败',
      copyError: '复制失败，请手动复制',
      menuIdNotFound: '无法找到菜单ID',
      generatePageFileError: '页面文件生成失败，请手动创建',
      // 其他文本
      featureCount: '等{count}项功能',
      pageWithCrudFeatures: '生成后的页面包含完整的CRUD功能',
      featureDescriptions: {
        template: '基于PageTemplate通用组件，具有统一的页面布局',
        crud: '包含搜索、分页、新增、编辑、删除等标准功能',
        api: '自动生成对应的API接口文件',
        validation: '支持表单验证和状态管理',
        responsive: '响应式设计，适配移动端',
        customizable: '可根据业务需求自定义扩展'
      }
    }
  },
  en: {
    login: {
      cancelmessage: 'Cancel',
      loginlogin: 'Login Successfully!',
      loginloginsystemsystem: 'Login System',
      msguseruser: 'Login Fail,pls kindly check the user name &pwd',
      passwordpassword: 'Pwd',
      phpasswordpassword: 'Pls key the pwd',
      phuseruser: 'Pls key the user name',
      textsystemsystem: 'MES System Login',
      useruser: 'User',
      welcome: 'Welcome',
      welcomeText: 'MES Smart Manufacturing System',
      slogan: 'Smart Manufacturing System',
      subSlogan: 'Building Smart Factory, Leading Industry 4.0',
      rememberMe: 'Remember me',
      forgotPassword: 'Forgot password?',
      copyright: '© 2024 MES Smart Manufacturing System. All rights reserved.',
      loginSuccess: 'Login Successfully!',
      loginButton: 'Login',
      usernameRequired: 'Please enter username',
      passwordRequired: 'Please enter password'
    },
    common: {
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      save: 'Save',
      cancel: 'Cancel',
      confirm: 'Confirm',
      close: 'Close',
      search: 'Search',
      query: 'Query',
      retry: 'Retry',
      refresh: 'Refresh',
      export: 'Export',
      import: 'Import',
      loading: 'Loading...',
      saving: 'Saving...',
      success: 'Success',
      error: 'Error',
      warning: 'Warning',
      info: 'Info'
    },
    system: {
      title: 'System Management',
      userManagement: 'User Management',
      rolePermission: 'Role Permission',
      menuManagement: 'Menu Management',
      i18nManagement: 'I18n Management',
      dataDict: 'Data Dictionary'
    },
    i18n: {
      management: 'I18n Management',
      addTranslation: 'Add Translation',
      editTranslation: 'Edit Translation',
      deleteTranslation: 'Delete Translation',
      extractText: 'Extract Text',
      importExport: 'Import/Export',
      translationKey: 'Translation Key',
      module: 'Module',
      description: 'Description',
      status: {
        completed: 'Completed',
        incomplete: 'Incomplete',
        missing: 'Missing Translation'
      },
      stats: {
        totalKeys: 'Total Keys',
        supportedLanguages: 'Supported Languages',
        completedTranslations: 'Completed Translations',
        completionRate: 'Completion Rate'
      },
      extract: {
        selectPage: 'Select Page',
        extractRules: 'Extract Rules',
        modulePrefix: 'Module Prefix',
        startExtract: 'Start Extract',
        extractSuccess: 'Extract Success',
        batchAdd: 'Batch Add to Translation List'
      }
    },
    menu: {
      dashboard: 'Dashboard',
      engineering: 'Engineering',
      production: 'Production',
      quality: 'Quality',
      warehouse: 'Warehouse',
      equipment: 'Equipment',
      hr: 'Human Resources',
      system: 'System',
      // 详细菜单翻译
      home: 'Home',
      // 工程管理
      modelManagement: 'MaterialMasterData',
      // 生产管理
      workCenterSettings: 'Work Center Settings',
      // 人事管理
      employeeManagement: 'Employee Management',
      companySettings: 'Company Settings',
      attendanceManagement: 'Attendance Management',
      // 系统管理
      userManagement: 'User Management',
      roleManagement: 'Role Management',
      menuManagement: 'Menu Management',
      i18nManagement: 'I18n Management',
      dataDictionary: 'Data Dictionary',
      // WIS相关
      wisQuery: 'WIS Query',
      wisPDFViewer: 'WIS PDF Viewer'
    },
    dataDict: {
      // 页面标题和基本操作
      management: 'Data Dictionary Management',
      addDict: 'Add Dictionary',
      import: 'Import',
      export: 'Export',
      refresh: 'Refresh',
      search: 'Search',
      reset: 'Reset',
      
      // 搜索表单
      dictName: 'Dictionary Name',
      dictCode: 'Dictionary Code',
      dictType: 'Dictionary Type',
      status: 'Status',
      searchNamePlaceholder: 'Please enter dictionary name',
      searchCodePlaceholder: 'Please enter dictionary code',
      selectTypePlaceholder: 'Please select dictionary type',
      selectStatusPlaceholder: 'Please select status',
      
      // 字典类型选项
      systemConfig: 'System Config',
      businessConfig: 'Business Config',
      otherConfig: 'Other Config',
      
      // 状态选项
      enabled: 'Enabled',
      disabled: 'Disabled',
      
      // 批量操作
      selectedItems: '{count} items selected',
      batchEnable: 'Batch Enable',
      batchDisable: 'Batch Disable',
      batchDelete: 'Batch Delete',
      
      // 表格列标题
      id: 'ID',
      name: 'Dictionary Name',
      code: 'Dictionary Code',
      type: 'Dictionary Type',
      description: 'Description',
      itemCount: 'Item Count',
      createTime: 'Create Time',
      updateTime: 'Update Time',
      operation: 'Operation',
      
      // 操作按钮
      edit: 'Edit',
      dictItems: 'Dict Items',
      copy: 'Copy',
      delete: 'Delete',
      
      // 对话框标题
      addDictDialog: 'Add Dictionary',
      editDictDialog: 'Edit Dictionary',
      dictItemsDialog: 'Dictionary Items Management',
      addItemDialog: 'Add Dictionary Item',
      editItemDialog: 'Edit Dictionary Item',
      importDialog: 'Import Data Dictionary',
      
      // 表单标签
      namePlaceholder: 'Please enter dictionary name',
      codePlaceholder: 'Please enter dictionary code (uppercase + underscore)',
      descriptionPlaceholder: 'Please enter dictionary description',
      sort: 'Sort',
      remark: 'Remark',
      remarkPlaceholder: 'Please enter remark information',
      
      // 字典项相关
      addItem: 'Add Item',
      itemsSort: 'Sort',
      itemLabel: 'Item Label',
      itemValue: 'Item Value',
      itemLabelPlaceholder: 'Please enter item label',
      itemValuePlaceholder: 'Please enter item value',
      itemDescriptionPlaceholder: 'Please enter item description',
      extProperty1: 'Extended Property 1',
      extProperty2: 'Extended Property 2',
      extProperty3: 'Extended Property 3',
      extProperty1Placeholder: 'Please enter extended property 1',
      extProperty2Placeholder: 'Please enter extended property 2',
      extProperty3Placeholder: 'Please enter extended property 3',
      
      // 上传相关
      uploadText: 'Drop file here, or ',
      clickUpload: 'click to upload',
      uploadTip: 'Only Excel files are supported, and the file size cannot exceed 5MB',
      
      // 按钮文本
      cancel: 'Cancel',
      confirm: 'Confirm',
      confirmImport: 'Confirm Import',
      
      // 提示信息
      itemsCount: '{count} items',
      enabledText: 'Enabled',
      disabledText: 'Disabled',
      
      // 操作消息
      addSuccess: 'Added successfully',
      editSuccess: 'Updated successfully',
      deleteSuccess: 'Deleted successfully',
      addFailed: 'Add failed',
      editFailed: 'Update failed',
      deleteFailed: 'Delete failed',
      loadFailed: 'Failed to load data',
      operationFailed: 'Operation failed',
      deleteConfirm: 'Are you sure you want to delete dictionary "{name}"? Dictionary items will also be deleted!',
      deleteTitle: 'Delete Confirmation',
      unknown: 'Unknown'
    },
    engineering: {
      // Material Master Data Management
      materialManagement: 'Material Master Data Management',
      productModel: 'Finished Product Model',
      componentModel: 'Component Model',
      partModel: 'Part Model',
      auxiliaryModel: 'Auxiliary & Packaging Model',
      hardwarePlasticModel: 'Hardware & Plastic Model',
      
      // Dynamic Material Categories (From API)
      finishedProductModel: 'FG Product',
      subAssemblyModel: 'Subset',
      componentPartModel: 'Component',
      auxiliaryPackagingModel: 'Accessories',
      plasticMetalModel: 'Plastic&Metal',
      administrativeMaterial: 'Admin Material',
      virtualMaterial: 'Virtual Material',
      
      // Material Category Descriptions
      finishedProductDesc: 'Final finished products',
      subAssemblyDesc: 'Product assembly parts',
      componentPartDesc: 'Smallest granular parts',
      auxiliaryPackagingDesc: 'Auxiliary and packaging materials',
      plasticMetalDesc: 'Plastic and metal products',
      administrativeDesc: 'Administrative office supplies',
      virtualDesc: 'Virtual material types',
      
      // Model Type Descriptions
      productModelDesc: '🏭 Final finished products',
      componentModelDesc: '⚙️ Product assembly parts',
      partModelDesc: '🔩 Smallest granular parts',
      auxiliaryModelDesc: '📋 Auxiliary and packaging materials',
      hardwarePlasticModelDesc: '🔧 Hardware and plastic products',
      
      // Dynamic Categories
      loadingCategories: 'Loading material categories...',
      loadingModels: 'Loading model data...',
      noModelsFound: 'No model data found',
      categoriesLoadedSuccess: 'Successfully loaded {count} material categories',
      usingDefaultCategories: 'No material category dictionary configured, using default categories',
      loadCategoriesError: 'Failed to load material categories',
      noCategoriesFound: 'No material category data found',
      retryLoad: 'Retry',
      
      // Configuration Hints
      configMaterialCategories: 'Configure Material Categories',
      configHintTitle: 'How to Configure Material Category Dictionary',
      configHintContent: 'To better adapt the material management module to your business needs, please follow these steps to configure the material category dictionary:',
      configStep1: 'Go to [System Management] - [Data Dictionary] page',
      configStep2: 'Create a dictionary with code "MATERIAL_CATEGORY"',
      configStep3: 'Add corresponding dictionary items to the dictionary, each item represents a material category',
      goToConfig: 'Go to Configure',
      
      // Common Operations
      addModel: 'New Add Reference',
      editModel: 'Edit Ref',
      viewModel: 'View Ref',
      deleteModel: 'Delete Ref',
      searchModel: 'Search Ref',
      view: 'View',
      
      // View Tabs
      basicInfo: 'Basic Info',
      salesView: 'Sales View',
      warehouseView: 'Warehouse View',
      financeView: 'Finance View',
      productionView: 'Production View',
      purchaseView: 'Purchase View',
      importExportView: 'Import/Export View',
      mrpView: 'MRP Planning View',
      
      // Basic Info Fields
      modelName: 'Ref. Name',
      modelCode: 'Ref. Code',
      category: 'Category',
      specification: 'Specification',
      status: 'Status',
      image: 'Image',
      unit: 'Unit',
      material: 'Material',
      size: 'Size',
      imageSaveToCloudServer: 'Image Save to Cloud Server',
      
      // Sales Info Fields
      salesPrice: 'Sales Price',
      salesUnit: 'Sales Unit',
      minOrderQty: 'Min Order Qty',
      maxOrderQty: 'Max Order Qty',
      leadTime: 'Lead Time',
      warranty: 'Warranty',
      
      // Warehouse Info Fields
      storageLocation: 'Storage Location',
      safetyStock: 'Safety Stock',
      maxStock: 'Max Stock',
      reorderPoint: 'Reorder Point',
      storageCondition: 'Storage Condition',
      shelfLife: 'Shelf Life',
      
      // Finance Info Fields
      standardCost: 'Standard Cost',
      averageCost: 'Average Cost',
      valuationMethod: 'Valuation Method',
      taxRate: 'Tax Rate',
      accountSubject: 'Account Subject',
      costCenter: 'Cost Center',
      
      // Production Info Fields
      productionType: 'Production Type',
      productionLeadTime: 'Production Lead Time',
      setupTime: 'Setup Time',
      cycleTime: 'Cycle Time',
      batchSize: 'Batch Size',
      workCenter: 'Work Center',
      qualityStandard: 'Quality Standard',
      
      // Purchase Info Fields
      supplier: 'Supplier',
      purchasePrice: 'Purchase Price',
      purchaseUnit: 'Purchase Unit',
      minPurchaseQty: 'Min Purchase Qty',
      purchaseLeadTime: 'Purchase Lead Time',
      qualityLevel: 'Quality Level',
      purchaseNote: 'Purchase Note',
      
      // Import/Export Info Fields
      hsCode: 'HS Code',
      originCountry: 'Country of Origin',
      importTaxRate: 'Import Tax Rate',
      exportTaxRefund: 'Export Tax Refund',
      isDangerous: 'Dangerous Goods',
      transportMode: 'Transport Mode',
      packingRequirement: 'Packing Requirement',
      inspectionRequired: 'Inspection Required',
      licenseRequirement: 'License Requirement',
      
      // MRP Planning Info Fields
      planningStrategy: 'Planning Strategy',
      planningCycle: 'Planning Cycle',
      forecastMethod: 'Forecast Method',
      abcCategory: 'ABC Category',
      demandSource: 'Demand Source',
      planner: 'Planner',
      safetyStockDays: 'Safety Stock Days',
      lotSizeRule: 'Lot Size Rule',
      demandTimeFence: 'Demand Time Fence',
      supplyTimeFence: 'Supply Time Fence',
      planningNote: 'Planning Note',
      
      // Placeholders and Tips
      selectCategory: 'Please select category',
      selectUnit: 'Please select unit',
      
      // Status Messages
      addSuccess: 'Model added successfully',
      editSuccess: 'Model updated successfully',
      deleteSuccess: 'Model deleted successfully',
      deleteConfirm: 'Are you sure to delete this model?',
      
      // Error Messages
      loadError: 'Failed to load data',
      saveError: 'Save failed',
      deleteError: 'Delete failed'
    },
    user: {
      // 页面标题和基本操作
      management: 'User Management',
      addUser: 'Add User',
      editUser: 'Edit User',
      selectEmployee: 'Select Employee',
      // 搜索和筛选
      search: 'Search',
      searchPlaceholder: 'Please enter',
      username: 'Username',
      name: 'Name',
      department: 'Department',
      role: 'Role',
      email: 'Email',
      phone: 'Phone',
      password: 'Password',
      status: 'Status',
      createTime: 'Create Time',
      operation: 'Operation',
      // 状态
      active: 'Active',
      inactive: 'Inactive',
      all: 'All',
      // 操作按钮
      edit: 'Edit',
      resetPassword: 'Reset Password',
      enable: 'Enable',
      disable: 'Disable',
      import: 'Import',
      export: 'Export',
      confirm: 'Confirm',
      cancel: 'Cancel',
      // 对话框和提示
      addUserDialog: 'Add User',
      editUserDialog: 'Edit User',
      selectEmployeeDialog: 'Select Employee',
      // 占位符
      selectDepartment: 'Please select department',
      selectRole: 'Please select role',
      // 验证消息
      usernameRequired: 'Please enter username',
      usernameLength: 'Length should be 3 to 20 characters',
      nameRequired: 'Please enter name',
      departmentRequired: 'Please select department',
      roleRequired: 'Please select role',
      emailFormat: 'Please enter valid email address',
      phoneFormat: 'Please enter valid phone number',
      passwordRequired: 'Please enter password',
      passwordLength: 'Length should be 6 to 20 characters',
      // 操作提示
      confirmResetPassword: 'Are you sure to reset password for user {name}?',
      confirmDisable: 'Are you sure to disable user {name}?',
      confirmEnable: 'Are you sure to enable user {name}?',
      warning: 'Warning',
      // 成功消息
      addSuccess: 'User added successfully',
      updateSuccess: 'User updated successfully',
      resetPasswordSuccess: 'Password reset successfully',
      enableSuccess: 'User enabled successfully',
      disableSuccess: 'User disabled successfully',
      // 错误消息
      fetchListError: 'Failed to fetch user list, please try again later',
      operationError: 'Operation failed, please try again later',
      userIdNotFound: 'User ID not found',
      enableError: 'Failed to enable user',
      disableError: 'Failed to disable user',
      retryLater: 'Please try again',
      // 功能开发中
      importInDevelopment: 'Import feature is under development',
      exportInDevelopment: 'Export feature is under development'
    },
    role: {
      // 页面标题和基本操作
      management: 'Role Management',
      roleDefinition: 'Role Definition',
      roleUsers: 'Role Users',
      roleMenus: 'Role Menus',
      addRole: 'Add Role',
      editRole: 'Edit Role',
      // 搜索和筛选
      search: 'Search',
      searchPlaceholder: 'Please enter role name',
      roleName: 'Role Name',
      roleCode: 'Role Code',
      roleDescription: 'Role Description',
      status: 'Status',
      createTime: 'Create Time',
      operation: 'Operation',
      // 状态
      active: 'Active',
      inactive: 'Inactive',
      // 操作按钮
      edit: 'Edit',
      enable: 'Enable',
      disable: 'Disable',
      delete: 'Delete',
      save: 'Save',
      reset: 'Reset',
      confirm: 'Confirm',
      cancel: 'Cancel',
      // 对话框和提示
      addRoleDialog: 'Add Role',
      editRoleDialog: 'Edit Role',
      deleteConfirm: 'Delete Confirmation',
      confirmDelete: 'Are you sure to delete role "{name}"?',
      // 表单验证
      roleNameRequired: 'Please enter role name',
      roleNameLength: 'Length should be 2 to 20 characters',
      roleCodeRequired: 'Please enter role code',
      roleCodeLength: 'Length should be 2 to 30 characters',
      // 权限相关
      permissions: 'Permissions',
      add: 'Add',
      editPermission: 'Edit',
      deletePermission: 'Delete',
      verify: 'Verify',
      import: 'Import',
      export: 'Export',
      query: 'Query',
      report: 'Report Preview',
      // 用户相关
      username: 'Username',
      name: 'Name',
      department: 'Department',
      position: 'Position',
      email: 'Email',
      phone: 'Phone',
      // 角色用户设置
      roleUsersTable: 'Role Associated Users',
      noUsersForRole: 'No users associated with this role',
      saveRolePermissions: 'Save Role Permissions',
      resetRolePermissions: 'Reset',
      // 角色菜单设置
      selectRole: 'Please select a role',
      initializePermissions: 'Initialize Role Permissions',
      initializingPermissions: 'Initializing...',
      initializeConfirmTitle: 'Confirm Initialization',
      initializeConfirmMessage: 'This will initialize the default role menu permissions. If permission data already exists, it will not be duplicated. Continue?',
      saveMenuPermissions: 'Save Menu Permissions',
      resetMenuPermissions: 'Reset',
      noRoleSelected: 'Please select a role first',
      loadingMenus: 'Loading menu data...',
      loadingUsers: 'Loading user data...',
      // 成功消息
      addRoleSuccess: 'Role added successfully',
      updateRoleSuccess: 'Role updated successfully',
      deleteRoleSuccess: 'Role deleted successfully',
      enableRoleSuccess: 'Role enabled successfully',
      disableRoleSuccess: 'Role disabled successfully',
      savePermissionsSuccess: 'Role permissions saved successfully',
      saveMenusSuccess: 'Menu permissions saved successfully',
      resetPermissionsSuccess: 'Role permissions reset',
      initializeSuccess: 'Role permissions initialized successfully',
      // 错误消息
      fetchRoleListError: 'Failed to fetch role list, please try again later',
      saveRoleError: 'Failed to save role, please try again later',
      deleteRoleError: 'Failed to delete role, please try again later',
      toggleStatusError: 'Failed to toggle role status, please try again later',
      getRoleUsersError: 'Failed to get role users',
      loadRoleUsersError: 'Failed to load role users, please try again later',
      getMenuTreeError: 'Failed to get menu tree, please try again later',
      getRoleMenusError: 'Failed to get role menus, please try again later',
      saveMenusError: 'Failed to save role menu permissions, please try again later',
      initializeError: 'Failed to initialize role permissions, please try again later',
      // 提示消息
      selectRoleFirst: 'Please select a role first',
      operationSuccess: 'Operation successful',
      operationError: 'Operation failed',
      retryLater: 'Please try again later'
    },
    menuManagement: {
      // 页面标题和基本操作
      management: 'Menu Management',
      addMenu: 'Add Menu',
      initializeMenus: 'Initialize Menus',
      initializing: 'Initializing...',
      refresh: 'Refresh',
      // 表格列标题
      menuName: 'Menu Name',
      menuId: 'Menu ID',
      path: 'Path',
      type: 'Type',
      sortOrder: 'Sort Order',
      status: 'Status',
      operation: 'Operation',
      // 菜单类型
      menuItem: 'Menu Item',
      subMenu: 'Sub Menu',
      // 状态
      enabled: 'Enabled',
      disabled: 'Disabled',
      // 操作按钮
      edit: 'Edit',
      addChild: 'Add Child',
      delete: 'Delete',
      cancel: 'Cancel',
      confirm: 'Confirm',
      saving: 'Saving...',
      // 对话框标题
      addMenuDialog: 'Add Menu',
      editMenuDialog: 'Edit Menu',
      addChildMenuDialog: 'Add Child Menu',
      menuSettings: 'Menu Settings',
      // 表单字段
      parentMenu: 'Parent Menu',
      menuType: 'Menu Type',
      menuPath: 'Menu Path',
      menuIcon: 'Menu Icon',
      sortNumber: 'Sort Number',
      menuStatus: 'Menu Status',
      menuDescription: 'Menu Description',
      pageGeneration: 'Page Generation',
      // 占位符文本
      enterMenuName: 'Please enter menu name',
      enterMenuId: 'Please enter menu ID, e.g: system-users',
      pathPlaceholder: 'e.g: /home/<USER>/menus',
      selectIcon: 'Select Icon',
      enterDescription: 'Please enter menu description (optional)',
      // 验证消息
      menuNameRequired: 'Please enter menu name',
      menuNameLength: 'Length should be 2 to 50 characters',
      menuIdRequired: 'Please enter menu ID',
      menuIdPattern: 'Menu ID must start with a letter and contain only letters, numbers, hyphens and underscores',
      menuTypeRequired: 'Please select menu type',
      menuPathRequired: 'Menu item must have a path',
      menuIconRequired: 'Please select menu icon',
      sortOrderRequired: 'Please enter sort order',
      // 页面生成相关
      autoGeneratePageFile: 'Auto Generate Page File',
      noGeneratePageFile: 'No Generate Page File',
      generateTip: 'When enabled, it will automatically generate standard page files based on PageTemplate',
      pagePreview: 'Page Preview',
      pageGenerationDetails: 'Page Generation Details Preview',
      viewDetails: 'View Details',
      // 预览相关
      basicInfo: 'Basic Info',
      pageName: 'Page Name',
      componentName: 'Component Name',
      vueFile: 'Vue File',
      apiFile: 'API File',
      pageFeatures: 'Page Features',
      apiEndpoints: 'API Endpoints',
      usageInstructions: 'Usage Instructions',
      generatedFilesResult: 'Generated Files Result',
      close: 'Close',
      downloadVueFile: 'Download Vue File',
      downloadApiFile: 'Download API File',
      copyVueCode: 'Copy Vue Code',
      copyApiCode: 'Copy API Code',
      copyRouteCode: 'Copy Route Code',
      // 确认对话框
      deleteConfirm: 'Delete Confirmation',
      confirmDeleteMenu: 'Are you sure to delete menu "{name}"? If this menu has sub-menus, they will also be deleted.',
      confirmInitializeMenus: 'Are you sure to initialize menu data? This will add the system default menu structure.',
      initializeConfirm: 'Initialize Confirmation',
      // 成功消息
      loadMenuListSuccess: 'Menu list loaded successfully',
      deleteSuccess: 'Deleted successfully',
      addMenuSuccess: 'Menu added successfully',
      editMenuSuccess: 'Menu updated successfully',
      initializeMenusSuccess: 'Menu data initialized successfully',
      generatePageSuccess: 'Page template generated successfully! You can download files or copy code.',
      downloadSuccess: 'Download successful',
      copySuccess: 'Code copied to clipboard',
      routeCodeCopied: 'Route configuration code copied to clipboard',
      // 错误消息
      loadMenuListError: 'Failed to load menu list',
      deleteError: 'Delete failed',
      operationError: 'Operation failed',
      initializeMenusError: 'Failed to initialize menus',
      networkError: 'Network error, please try again later',
      generatePageError: 'Failed to generate page template',
      downloadError: 'Download failed',
      copyError: 'Copy failed, please copy manually',
      menuIdNotFound: 'Menu ID not found',
      generatePageFileError: 'Failed to generate page file, please create manually',
      // 其他文本
      featureCount: 'and {count} more features',
      pageWithCrudFeatures: 'Generated page contains complete CRUD functionality',
      featureDescriptions: {
        template: 'Based on PageTemplate component with unified page layout',
        crud: 'Contains standard functions like search, pagination, add, edit, delete',
        api: 'Automatically generates corresponding API interface files',
        validation: 'Supports form validation and state management',
        responsive: 'Responsive design, compatible with mobile devices',
        customizable: 'Can be customized and extended according to business needs'
      }
    }
  }
}

const i18n = createI18n({
  legacy: false,
  locale: getDefaultLanguage(),
  fallbackLocale: 'zh',
  messages
})

export default i18n 