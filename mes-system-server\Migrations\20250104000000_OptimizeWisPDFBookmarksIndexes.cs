using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace mes_system_server.Migrations
{
    /// <inheritdoc />
    public partial class OptimizeWisPDFBookmarksIndexes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // 添加状态索引 - 用于快速过滤有效记录
            migrationBuilder.CreateIndex(
                name: "IX_WisPDFBookmarks_Status",
                table: "WisPDFBookmarks",
                column: "Status");

            // 添加复合索引 - 支持按文件名和状态查询，按页码排序
            migrationBuilder.CreateIndex(
                name: "IX_WisPDFBookmarks_FileName_Status_PageNumber",
                table: "WisPDFBookmarks",
                columns: new[] { "FileName", "Status", "PageNumber" });

            // 添加书签名称搜索索引
            migrationBuilder.CreateIndex(
                name: "IX_WisPDFBookmarks_OriginalBookmarkName_Status",
                table: "WisPDFBookmarks",
                columns: new[] { "OriginalBookmarkName", "Status" });

            // 添加时间范围查询索引
            migrationBuilder.CreateIndex(
                name: "IX_WisPDFBookmarks_CreateTime_Status",
                table: "WisPDFBookmarks",
                columns: new[] { "CreateTime", "Status" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // 删除添加的索引
            migrationBuilder.DropIndex(
                name: "IX_WisPDFBookmarks_Status",
                table: "WisPDFBookmarks");

            migrationBuilder.DropIndex(
                name: "IX_WisPDFBookmarks_FileName_Status_PageNumber",
                table: "WisPDFBookmarks");

            migrationBuilder.DropIndex(
                name: "IX_WisPDFBookmarks_OriginalBookmarkName_Status",
                table: "WisPDFBookmarks");

            migrationBuilder.DropIndex(
                name: "IX_WisPDFBookmarks_CreateTime_Status",
                table: "WisPDFBookmarks");
        }
    }
} 