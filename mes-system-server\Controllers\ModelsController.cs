using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using mes_system_server.Data;
using mes_system_server.DTOs;
using mes_system_server.Models;
using Microsoft.AspNetCore.Authorization;
using System.ComponentModel.DataAnnotations;

namespace mes_system_server.Controllers
{
    [ApiController]
    [Route("api/models")]
    [AllowAnonymous]
    public class ModelsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ModelsController> _logger;

        public ModelsController(ApplicationDbContext context, ILogger<ModelsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取物料型号列表
        /// </summary>
        /// <param name="type">物料类型</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="search">搜索关键词</param>
        /// <returns>物料型号列表</returns>
        [HttpGet("{type}")]
        public async Task<ActionResult<ApiResponseDto<PagedMaterialModelResultDto>>> GetMaterialModels(
            string type,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 12,
            [FromQuery] string search = "")
        {
            try
            {
                if (!IsValidType(type))
                {
                    return BadRequest(ApiResponseDto<string>.Fail("无效的物料类型"));
                }

                var query = _context.MaterialModels
                    .Where(m => m.Type == type && m.IsActive);

                // 搜索过滤
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(m => 
                        m.Name.Contains(search) || 
                        m.Code.Contains(search) ||
                        m.Specification.Contains(search));
                }

                var total = await query.CountAsync();
                var totalPages = (int)Math.Ceiling(total / (double)pageSize);

                var models = await query
                    .OrderByDescending(m => m.CreateTime)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(m => new MaterialModelListDto
                    {
                        Id = m.Id,
                        Name = m.Name,
                        Code = m.Code,
                        Type = m.Type,
                        Category = m.Category,
                        Specification = m.Specification,
                        Status = m.Status,
                        Image = m.Image,
                        Unit = m.Unit,
                        Material = m.Material,
                        Size = m.Size,
                        CreateTime = m.CreateTime,
                        UpdateTime = m.UpdateTime
                    })
                    .ToListAsync();

                var result = new PagedMaterialModelResultDto
                {
                    List = models,
                    Total = total,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = totalPages
                };

                return Ok(ApiResponseDto<PagedMaterialModelResultDto>.Success(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取物料型号列表失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取物料型号列表失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 根据ID获取物料型号详情
        /// </summary>
        /// <param name="type">物料类型</param>
        /// <param name="id">物料型号ID</param>
        /// <returns>物料型号详情</returns>
        [HttpGet("{type}/{id}")]
        public async Task<ActionResult<ApiResponseDto<MaterialModelDetailDto>>> GetMaterialModel(string type, int id)
        {
            try
            {
                if (!IsValidType(type))
                {
                    return BadRequest(ApiResponseDto<string>.Fail("无效的物料类型"));
                }

                var model = await _context.MaterialModels
                    .FirstOrDefaultAsync(m => m.Id == id && m.Type == type && m.IsActive);

                if (model == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("物料型号不存在", 404));
                }

                var result = new MaterialModelDetailDto
                {
                    Id = model.Id,
                    Name = model.Name,
                    Code = model.Code,
                    Type = model.Type,
                    Category = model.Category,
                    Specification = model.Specification,
                    Status = model.Status,
                    Image = model.Image,
                    Unit = model.Unit,
                    Material = model.Material,
                    Size = model.Size,
                    
                    // 销售信息
                    SalesPrice = model.SalesPrice,
                    SalesUnit = model.SalesUnit,
                    MinOrderQty = model.MinOrderQty,
                    MaxOrderQty = model.MaxOrderQty,
                    LeadTime = model.LeadTime,
                    Warranty = model.Warranty,
                    
                    // 仓库信息
                    StorageLocation = model.StorageLocation,
                    SafetyStock = model.SafetyStock,
                    MaxStock = model.MaxStock,
                    ReorderPoint = model.ReorderPoint,
                    StorageCondition = model.StorageCondition,
                    ShelfLife = model.ShelfLife,
                    
                    // 财务信息
                    StandardCost = model.StandardCost,
                    AverageCost = model.AverageCost,
                    ValuationMethod = model.ValuationMethod,
                    TaxRate = model.TaxRate,
                    AccountSubject = model.AccountSubject,
                    CostCenter = model.CostCenter,
                    
                    // 生产信息
                    ProductionType = model.ProductionType,
                    ProductionLeadTime = model.ProductionLeadTime,
                    SetupTime = model.SetupTime,
                    CycleTime = model.CycleTime,
                    BatchSize = model.BatchSize,
                    WorkCenter = model.WorkCenter,
                    QualityStandard = model.QualityStandard,
                    
                    // 采购信息
                    Supplier = model.Supplier,
                    PurchasePrice = model.PurchasePrice,
                    PurchaseUnit = model.PurchaseUnit,
                    MinPurchaseQty = model.MinPurchaseQty,
                    PurchaseLeadTime = model.PurchaseLeadTime,
                    QualityLevel = model.QualityLevel,
                    PurchaseNote = model.PurchaseNote,
                    
                    // 进出口信息
                    HsCode = model.HsCode,
                    OriginCountry = model.OriginCountry,
                    ImportTaxRate = model.ImportTaxRate,
                    ExportTaxRefund = model.ExportTaxRefund,
                    IsDangerous = model.IsDangerous,
                    TransportMode = model.TransportMode,
                    PackingRequirement = model.PackingRequirement,
                    InspectionRequired = model.InspectionRequired,
                    LicenseRequirement = model.LicenseRequirement,
                    
                    // MRP计划信息
                    PlanningStrategy = model.PlanningStrategy,
                    PlanningCycle = model.PlanningCycle,
                    ForecastMethod = model.ForecastMethod,
                    AbcCategory = model.AbcCategory,
                    DemandSource = model.DemandSource,
                    Planner = model.Planner,
                    SafetyStockDays = model.SafetyStockDays,
                    LotSizeRule = model.LotSizeRule,
                    DemandTimeFence = model.DemandTimeFence,
                    SupplyTimeFence = model.SupplyTimeFence,
                    PlanningNote = model.PlanningNote,
                    
                    CreateTime = model.CreateTime,
                    UpdateTime = model.UpdateTime,
                    CreateBy = model.CreateBy,
                    UpdateBy = model.UpdateBy
                };

                return Ok(ApiResponseDto<MaterialModelDetailDto>.Success(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取物料型号详情失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取物料型号详情失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 创建物料型号
        /// </summary>
        /// <param name="type">物料类型</param>
        /// <param name="createDto">创建物料型号DTO</param>
        /// <returns>创建结果</returns>
        [HttpPost("{type}")]
        public async Task<ActionResult<ApiResponseDto<MaterialModelDetailDto>>> CreateMaterialModel(
            string type,
            [FromBody] CreateMaterialModelDto createDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                if (!IsValidType(type))
                {
                    return BadRequest(ApiResponseDto<string>.Fail("无效的物料类型"));
                }

                // 检查型号编码是否已存在
                var existingModel = await _context.MaterialModels
                    .FirstOrDefaultAsync(m => m.Code == createDto.Code && m.Type == type && m.IsActive);

                if (existingModel != null)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("型号编码已存在"));
                }

                var model = new MaterialModel
                {
                    Name = createDto.Name,
                    Code = createDto.Code,
                    Type = type,
                    Category = createDto.Category,
                    Specification = createDto.Specification,
                    Status = createDto.Status,
                    Image = createDto.Image,
                    Unit = createDto.Unit,
                    Material = createDto.Material,
                    Size = createDto.Size,
                    
                    // 销售信息
                    SalesPrice = createDto.SalesPrice,
                    SalesUnit = createDto.SalesUnit,
                    MinOrderQty = createDto.MinOrderQty,
                    MaxOrderQty = createDto.MaxOrderQty,
                    LeadTime = createDto.LeadTime,
                    Warranty = createDto.Warranty,
                    
                    // 仓库信息
                    StorageLocation = createDto.StorageLocation,
                    SafetyStock = createDto.SafetyStock,
                    MaxStock = createDto.MaxStock,
                    ReorderPoint = createDto.ReorderPoint,
                    StorageCondition = createDto.StorageCondition,
                    ShelfLife = createDto.ShelfLife,
                    
                    // 财务信息
                    StandardCost = createDto.StandardCost,
                    AverageCost = createDto.AverageCost,
                    ValuationMethod = createDto.ValuationMethod,
                    TaxRate = createDto.TaxRate,
                    AccountSubject = createDto.AccountSubject,
                    CostCenter = createDto.CostCenter,
                    
                    // 生产信息
                    ProductionType = createDto.ProductionType,
                    ProductionLeadTime = createDto.ProductionLeadTime,
                    SetupTime = createDto.SetupTime,
                    CycleTime = createDto.CycleTime,
                    BatchSize = createDto.BatchSize,
                    WorkCenter = createDto.WorkCenter,
                    QualityStandard = createDto.QualityStandard,
                    
                    // 采购信息
                    Supplier = createDto.Supplier,
                    PurchasePrice = createDto.PurchasePrice,
                    PurchaseUnit = createDto.PurchaseUnit,
                    MinPurchaseQty = createDto.MinPurchaseQty,
                    PurchaseLeadTime = createDto.PurchaseLeadTime,
                    QualityLevel = createDto.QualityLevel,
                    PurchaseNote = createDto.PurchaseNote,
                    
                    // 进出口信息
                    HsCode = createDto.HsCode,
                    OriginCountry = createDto.OriginCountry,
                    ImportTaxRate = createDto.ImportTaxRate,
                    ExportTaxRefund = createDto.ExportTaxRefund,
                    IsDangerous = createDto.IsDangerous,
                    TransportMode = createDto.TransportMode,
                    PackingRequirement = createDto.PackingRequirement,
                    InspectionRequired = createDto.InspectionRequired,
                    LicenseRequirement = createDto.LicenseRequirement,
                    
                    // MRP计划信息
                    PlanningStrategy = createDto.PlanningStrategy,
                    PlanningCycle = createDto.PlanningCycle,
                    ForecastMethod = createDto.ForecastMethod,
                    AbcCategory = createDto.AbcCategory,
                    DemandSource = createDto.DemandSource,
                    Planner = createDto.Planner,
                    SafetyStockDays = createDto.SafetyStockDays,
                    LotSizeRule = createDto.LotSizeRule,
                    DemandTimeFence = createDto.DemandTimeFence,
                    SupplyTimeFence = createDto.SupplyTimeFence,
                    PlanningNote = createDto.PlanningNote,
                    
                    CreateTime = DateTime.UtcNow,
                    UpdateTime = DateTime.UtcNow,
                    CreateBy = 1, // TODO: 从当前用户上下文获取
                    IsActive = true
                };

                _context.MaterialModels.Add(model);
                await _context.SaveChangesAsync();

                // 返回创建的物料型号详情
                var result = new MaterialModelDetailDto
                {
                    Id = model.Id,
                    Name = model.Name,
                    Code = model.Code,
                    Type = model.Type,
                    Category = model.Category,
                    Specification = model.Specification,
                    Status = model.Status,
                    Image = model.Image,
                    Unit = model.Unit,
                    Material = model.Material,
                    Size = model.Size,
                    
                    // 销售信息
                    SalesPrice = model.SalesPrice,
                    SalesUnit = model.SalesUnit,
                    MinOrderQty = model.MinOrderQty,
                    MaxOrderQty = model.MaxOrderQty,
                    LeadTime = model.LeadTime,
                    Warranty = model.Warranty,
                    
                    // 仓库信息
                    StorageLocation = model.StorageLocation,
                    SafetyStock = model.SafetyStock,
                    MaxStock = model.MaxStock,
                    ReorderPoint = model.ReorderPoint,
                    StorageCondition = model.StorageCondition,
                    ShelfLife = model.ShelfLife,
                    
                    // 财务信息
                    StandardCost = model.StandardCost,
                    AverageCost = model.AverageCost,
                    ValuationMethod = model.ValuationMethod,
                    TaxRate = model.TaxRate,
                    AccountSubject = model.AccountSubject,
                    CostCenter = model.CostCenter,
                    
                    // 生产信息
                    ProductionType = model.ProductionType,
                    ProductionLeadTime = model.ProductionLeadTime,
                    SetupTime = model.SetupTime,
                    CycleTime = model.CycleTime,
                    BatchSize = model.BatchSize,
                    WorkCenter = model.WorkCenter,
                    QualityStandard = model.QualityStandard,
                    
                    // 采购信息
                    Supplier = model.Supplier,
                    PurchasePrice = model.PurchasePrice,
                    PurchaseUnit = model.PurchaseUnit,
                    MinPurchaseQty = model.MinPurchaseQty,
                    PurchaseLeadTime = model.PurchaseLeadTime,
                    QualityLevel = model.QualityLevel,
                    PurchaseNote = model.PurchaseNote,
                    
                    // 进出口信息
                    HsCode = model.HsCode,
                    OriginCountry = model.OriginCountry,
                    ImportTaxRate = model.ImportTaxRate,
                    ExportTaxRefund = model.ExportTaxRefund,
                    IsDangerous = model.IsDangerous,
                    TransportMode = model.TransportMode,
                    PackingRequirement = model.PackingRequirement,
                    InspectionRequired = model.InspectionRequired,
                    LicenseRequirement = model.LicenseRequirement,
                    
                    // MRP计划信息
                    PlanningStrategy = model.PlanningStrategy,
                    PlanningCycle = model.PlanningCycle,
                    ForecastMethod = model.ForecastMethod,
                    AbcCategory = model.AbcCategory,
                    DemandSource = model.DemandSource,
                    Planner = model.Planner,
                    SafetyStockDays = model.SafetyStockDays,
                    LotSizeRule = model.LotSizeRule,
                    DemandTimeFence = model.DemandTimeFence,
                    SupplyTimeFence = model.SupplyTimeFence,
                    PlanningNote = model.PlanningNote,
                    
                    CreateTime = model.CreateTime,
                    UpdateTime = model.UpdateTime,
                    CreateBy = model.CreateBy,
                    UpdateBy = model.UpdateBy
                };

                return CreatedAtAction(nameof(GetMaterialModel), new { type = type, id = model.Id }, 
                    ApiResponseDto<MaterialModelDetailDto>.Success(result, "创建成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建物料型号失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"创建物料型号失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 更新物料型号
        /// </summary>
        /// <param name="type">物料类型</param>
        /// <param name="id">物料型号ID</param>
        /// <param name="updateDto">更新物料型号DTO</param>
        /// <returns>更新结果</returns>
        [HttpPut("{type}/{id}")]
        public async Task<ActionResult<ApiResponseDto<MaterialModelDetailDto>>> UpdateMaterialModel(
            string type,
            int id,
            [FromBody] UpdateMaterialModelDto updateDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                if (!IsValidType(type))
                {
                    return BadRequest(ApiResponseDto<string>.Fail("无效的物料类型"));
                }

                var model = await _context.MaterialModels
                    .FirstOrDefaultAsync(m => m.Id == id && m.Type == type && m.IsActive);

                if (model == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("物料型号不存在", 404));
                }

                // 检查型号编码是否已存在（排除当前记录）
                var existingModel = await _context.MaterialModels
                    .FirstOrDefaultAsync(m => m.Code == updateDto.Code && m.Type == type && m.Id != id && m.IsActive);

                if (existingModel != null)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("型号编码已存在"));
                }

                // 更新字段
                model.Name = updateDto.Name;
                model.Code = updateDto.Code;
                model.Category = updateDto.Category;
                model.Specification = updateDto.Specification;
                model.Status = updateDto.Status;
                model.Image = updateDto.Image;
                model.Unit = updateDto.Unit;
                model.Material = updateDto.Material;
                model.Size = updateDto.Size;
                
                // 销售信息
                model.SalesPrice = updateDto.SalesPrice;
                model.SalesUnit = updateDto.SalesUnit;
                model.MinOrderQty = updateDto.MinOrderQty;
                model.MaxOrderQty = updateDto.MaxOrderQty;
                model.LeadTime = updateDto.LeadTime;
                model.Warranty = updateDto.Warranty;
                
                // 仓库信息
                model.StorageLocation = updateDto.StorageLocation;
                model.SafetyStock = updateDto.SafetyStock;
                model.MaxStock = updateDto.MaxStock;
                model.ReorderPoint = updateDto.ReorderPoint;
                model.StorageCondition = updateDto.StorageCondition;
                model.ShelfLife = updateDto.ShelfLife;
                
                // 财务信息
                model.StandardCost = updateDto.StandardCost;
                model.AverageCost = updateDto.AverageCost;
                model.ValuationMethod = updateDto.ValuationMethod;
                model.TaxRate = updateDto.TaxRate;
                model.AccountSubject = updateDto.AccountSubject;
                model.CostCenter = updateDto.CostCenter;
                
                // 生产信息
                model.ProductionType = updateDto.ProductionType;
                model.ProductionLeadTime = updateDto.ProductionLeadTime;
                model.SetupTime = updateDto.SetupTime;
                model.CycleTime = updateDto.CycleTime;
                model.BatchSize = updateDto.BatchSize;
                model.WorkCenter = updateDto.WorkCenter;
                model.QualityStandard = updateDto.QualityStandard;
                
                // 采购信息
                model.Supplier = updateDto.Supplier;
                model.PurchasePrice = updateDto.PurchasePrice;
                model.PurchaseUnit = updateDto.PurchaseUnit;
                model.MinPurchaseQty = updateDto.MinPurchaseQty;
                model.PurchaseLeadTime = updateDto.PurchaseLeadTime;
                model.QualityLevel = updateDto.QualityLevel;
                model.PurchaseNote = updateDto.PurchaseNote;
                
                // 进出口信息
                model.HsCode = updateDto.HsCode;
                model.OriginCountry = updateDto.OriginCountry;
                model.ImportTaxRate = updateDto.ImportTaxRate;
                model.ExportTaxRefund = updateDto.ExportTaxRefund;
                model.IsDangerous = updateDto.IsDangerous;
                model.TransportMode = updateDto.TransportMode;
                model.PackingRequirement = updateDto.PackingRequirement;
                model.InspectionRequired = updateDto.InspectionRequired;
                model.LicenseRequirement = updateDto.LicenseRequirement;
                
                // MRP计划信息
                model.PlanningStrategy = updateDto.PlanningStrategy;
                model.PlanningCycle = updateDto.PlanningCycle;
                model.ForecastMethod = updateDto.ForecastMethod;
                model.AbcCategory = updateDto.AbcCategory;
                model.DemandSource = updateDto.DemandSource;
                model.Planner = updateDto.Planner;
                model.SafetyStockDays = updateDto.SafetyStockDays;
                model.LotSizeRule = updateDto.LotSizeRule;
                model.DemandTimeFence = updateDto.DemandTimeFence;
                model.SupplyTimeFence = updateDto.SupplyTimeFence;
                model.PlanningNote = updateDto.PlanningNote;
                
                model.UpdateTime = DateTime.UtcNow;
                model.UpdateBy = 1; // TODO: 从当前用户上下文获取

                await _context.SaveChangesAsync();

                // 返回更新后的物料型号详情
                var result = new MaterialModelDetailDto
                {
                    Id = model.Id,
                    Name = model.Name,
                    Code = model.Code,
                    Type = model.Type,
                    Category = model.Category,
                    Specification = model.Specification,
                    Status = model.Status,
                    Image = model.Image,
                    Unit = model.Unit,
                    Material = model.Material,
                    Size = model.Size,
                    
                    // 销售信息
                    SalesPrice = model.SalesPrice,
                    SalesUnit = model.SalesUnit,
                    MinOrderQty = model.MinOrderQty,
                    MaxOrderQty = model.MaxOrderQty,
                    LeadTime = model.LeadTime,
                    Warranty = model.Warranty,
                    
                    // 仓库信息
                    StorageLocation = model.StorageLocation,
                    SafetyStock = model.SafetyStock,
                    MaxStock = model.MaxStock,
                    ReorderPoint = model.ReorderPoint,
                    StorageCondition = model.StorageCondition,
                    ShelfLife = model.ShelfLife,
                    
                    // 财务信息
                    StandardCost = model.StandardCost,
                    AverageCost = model.AverageCost,
                    ValuationMethod = model.ValuationMethod,
                    TaxRate = model.TaxRate,
                    AccountSubject = model.AccountSubject,
                    CostCenter = model.CostCenter,
                    
                    // 生产信息
                    ProductionType = model.ProductionType,
                    ProductionLeadTime = model.ProductionLeadTime,
                    SetupTime = model.SetupTime,
                    CycleTime = model.CycleTime,
                    BatchSize = model.BatchSize,
                    WorkCenter = model.WorkCenter,
                    QualityStandard = model.QualityStandard,
                    
                    // 采购信息
                    Supplier = model.Supplier,
                    PurchasePrice = model.PurchasePrice,
                    PurchaseUnit = model.PurchaseUnit,
                    MinPurchaseQty = model.MinPurchaseQty,
                    PurchaseLeadTime = model.PurchaseLeadTime,
                    QualityLevel = model.QualityLevel,
                    PurchaseNote = model.PurchaseNote,
                    
                    // 进出口信息
                    HsCode = model.HsCode,
                    OriginCountry = model.OriginCountry,
                    ImportTaxRate = model.ImportTaxRate,
                    ExportTaxRefund = model.ExportTaxRefund,
                    IsDangerous = model.IsDangerous,
                    TransportMode = model.TransportMode,
                    PackingRequirement = model.PackingRequirement,
                    InspectionRequired = model.InspectionRequired,
                    LicenseRequirement = model.LicenseRequirement,
                    
                    // MRP计划信息
                    PlanningStrategy = model.PlanningStrategy,
                    PlanningCycle = model.PlanningCycle,
                    ForecastMethod = model.ForecastMethod,
                    AbcCategory = model.AbcCategory,
                    DemandSource = model.DemandSource,
                    Planner = model.Planner,
                    SafetyStockDays = model.SafetyStockDays,
                    LotSizeRule = model.LotSizeRule,
                    DemandTimeFence = model.DemandTimeFence,
                    SupplyTimeFence = model.SupplyTimeFence,
                    PlanningNote = model.PlanningNote,
                    
                    CreateTime = model.CreateTime,
                    UpdateTime = model.UpdateTime,
                    CreateBy = model.CreateBy,
                    UpdateBy = model.UpdateBy
                };

                return Ok(ApiResponseDto<MaterialModelDetailDto>.Success(result, "更新成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新物料型号失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"更新物料型号失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 删除物料型号
        /// </summary>
        /// <param name="type">物料类型</param>
        /// <param name="id">物料型号ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{type}/{id}")]
        public async Task<ActionResult<ApiResponseDto<string>>> DeleteMaterialModel(string type, int id)
        {
            try
            {
                if (!IsValidType(type))
                {
                    return BadRequest(ApiResponseDto<string>.Fail("无效的物料类型"));
                }

                var model = await _context.MaterialModels
                    .FirstOrDefaultAsync(m => m.Id == id && m.Type == type && m.IsActive);

                if (model == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("物料型号不存在", 404));
                }

                // 软删除
                model.IsActive = false;
                model.UpdateTime = DateTime.UtcNow;
                model.UpdateBy = 1; // TODO: 从当前用户上下文获取

                await _context.SaveChangesAsync();

                return Ok(ApiResponseDto<string>.Success("删除成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除物料型号失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"删除物料型号失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 验证物料类型是否有效
        /// </summary>
        /// <param name="type">物料类型</param>
        /// <returns>是否有效</returns>
        private bool IsValidType(string type)
        {
            var validTypes = new[] { "products", "components", "parts", "auxiliary", "hardware-plastic" };
            return validTypes.Contains(type.ToLower());
        }
    }
} 