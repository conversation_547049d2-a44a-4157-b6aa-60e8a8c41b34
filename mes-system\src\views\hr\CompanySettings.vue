<template>
  <div class="company-settings">
    <div class="page-header">
      <h2 class="page-title">公司设置</h2>
      <div class="page-actions">
        <el-button-group class="action-group">
          <el-button type="primary" @click="handleExportData">
            <el-icon><Download /></el-icon>
            <span>导出数据</span>
          </el-button>
          <el-button type="success" @click="handleRefreshData">
            <el-icon><RefreshRight /></el-icon>
            <span>刷新数据</span>
          </el-button>
        </el-button-group>
      </div>
    </div>

    <el-tabs v-model="activeTab" class="settings-tabs" type="border-card">
      <!-- 组织架构图标签页 -->
      <el-tab-pane label="组织架构图" name="orgChart">
        <div class="org-chart-container">
          <div class="org-chart-actions">
            <el-button-group>
              <el-button @click="zoomIn"><el-icon><ZoomIn /></el-icon></el-button>
              <el-button @click="zoomOut"><el-icon><ZoomOut /></el-icon></el-button>
              <el-button @click="resetZoom"><el-icon><RefreshLeft /></el-icon></el-button>
            </el-button-group>
          </div>
          
          <div class="org-chart" ref="orgChartRef">
            <!-- 此处将用于渲染组织架构图 -->
            <div class="chart-placeholder" v-if="!orgChartLoaded">
              <div class="loading-chart">组织架构图加载中...</div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 部门管理标签页 -->
      <el-tab-pane label="部门管理" name="departments">
        <div class="tab-header">
          <el-button type="primary" :icon="Plus" @click="handleAddDepartment">添加部门</el-button>
          <el-input
            v-model="departmentSearch"
            placeholder="搜索部门"
            clearable
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        
        <el-table 
          :data="filteredDepartments" 
          style="width: 100%" 
          border 
          stripe
          highlight-current-row
          v-loading="tableLoading.departments"
        >
          <el-table-column prop="id" label="部门ID" width="80" sortable />
          <el-table-column prop="code" label="部门代码" width="100" sortable />
          <el-table-column prop="name" label="部门名称" sortable />
          <el-table-column prop="manager" label="部门主管" />
          <el-table-column label="上级部门" sortable>
            <template #default="scope">
              {{ getParentDepartmentName(scope.row) }}
            </template>
          </el-table-column>
          <el-table-column label="上级部门主管">
            <template #default="scope">
              {{ getParentDepartmentManager(scope.row) }}
            </template>
          </el-table-column>
          <el-table-column label="层级" width="100" sortable>
            <template #default="scope">
              <el-tag :type="getDepartmentLevelType(scope.row)">
                {{ getDepartmentLevelName(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="employeeCount" label="员工数量" width="120" sortable />
          <el-table-column prop="createTime" label="创建时间" width="180" sortable />
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleEditDepartment(scope.row)">
                <el-icon><Edit /></el-icon>
                <span>编辑</span>
              </el-button>
              <el-button type="danger" size="small" @click="handleDeleteDepartment(scope.row)">
                <el-icon><Delete /></el-icon>
                <span>删除</span>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="departmentPagination.currentPage"
            v-model:page-size="departmentPagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="departmentPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="(val) => handleSizeChange('departments', val)"
            @current-change="(val) => handleCurrentChange('departments', val)"
          />
        </div>
      </el-tab-pane>

      <!-- 职位管理标签页 -->
      <el-tab-pane label="职位管理" name="positions">
        <div class="tab-header">
          <el-button type="primary" :icon="Plus" @click="handleAddPosition">添加职位</el-button>
          <el-input
            v-model="positionSearch"
            placeholder="搜索职位"
            clearable
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        
        <el-table 
          :data="filteredPositions" 
          style="width: 100%" 
          border 
          stripe
          highlight-current-row
          v-loading="tableLoading.positions"
        >
          <el-table-column prop="id" label="职位ID" width="120" sortable />
          <el-table-column prop="name" label="职位名称" sortable />
          <el-table-column prop="departmentName" label="所属部门" sortable />
          <el-table-column label="职级" width="120" sortable>
            <template #default="scope">
              <el-tag 
                :type="getPositionLevelType(scope.row.level)" 
                effect="plain"
              >
                {{ getPositionLevelName(scope.row.level) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" sortable />
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleEditPosition(scope.row)">
                <el-icon><Edit /></el-icon>
                <span>编辑</span>
              </el-button>
              <el-button type="danger" size="small" @click="handleDeletePosition(scope.row)">
                <el-icon><Delete /></el-icon>
                <span>删除</span>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="positionPagination.currentPage"
            v-model:page-size="positionPagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="positionPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="(val) => handleSizeChange('positions', val)"
            @current-change="(val) => handleCurrentChange('positions', val)"
          />
        </div>
      </el-tab-pane>

      <!-- 公司信息标签页 -->
      <el-tab-pane label="公司信息" name="company">
        <el-card class="company-info-card">
          <template #header>
            <div class="card-header">
              <h3>基本信息</h3>
            </div>
          </template>

          <el-form :model="companyInfo" label-width="150px" label-position="right">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="公司名称">
                  <el-input v-model="companyInfo.name" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="统一社会信用代码">
                  <el-input v-model="companyInfo.creditCode" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="法定代表人">
                  <el-input v-model="companyInfo.legalRepresentative" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册资本">
                  <el-input v-model="companyInfo.registeredCapital" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="注册日期">
                  <el-date-picker 
                    v-model="companyInfo.registrationDate" 
                    type="date" 
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="企业类型">
                  <el-select v-model="companyInfo.companyType" style="width: 100%">
                    <el-option label="有限责任公司" value="LLC" />
                    <el-option label="股份有限公司" value="JSC" />
                    <el-option label="个人独资企业" value="SP" />
                    <el-option label="合伙企业" value="Partnership" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="公司地址">
              <el-input v-model="companyInfo.address" />
            </el-form-item>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="联系电话">
                  <el-input v-model="companyInfo.phone" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="电子邮箱">
                  <el-input v-model="companyInfo.email" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="公司简介">
              <el-input
                v-model="companyInfo.description"
                type="textarea"
                rows="4"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveCompanyInfo">保存公司信息</el-button>
              <el-button @click="resetCompanyInfo">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 部门对话框 -->
    <el-dialog
      v-model="departmentDialogVisible"
      :title="dialogType === 'add' ? '添加部门' : '编辑部门'"
      width="500px"
      destroy-on-close
    >
      <el-form :model="currentDepartment" label-width="100px" :rules="departmentRules" ref="departmentFormRef">
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="currentDepartment.name" placeholder="请输入部门名称" />
        </el-form-item>
        <el-form-item label="部门代码" prop="code">
          <el-input v-model="currentDepartment.code" placeholder="请输入部门代码" />
        </el-form-item>
        <el-form-item label="部门主管" prop="manager">
          <el-select v-model="currentDepartment.manager" placeholder="请选择部门主管" filterable>
            <el-option
              v-for="employee in employeeOptions"
              :key="employee.id"
              :label="employee.name"
              :value="employee.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="员工数量" prop="employeeCount">
          <el-input v-model="currentDepartment.employeeCount" type="number" placeholder="请输入员工数量" />
        </el-form-item>
        <el-form-item label="上级部门">
          <el-select v-model="currentDepartment.parentId" filterable clearable placeholder="请选择上级部门" @change="handleParentChange">
            <el-option
              v-for="dept in departments"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
              :disabled="dept.id === currentDepartment.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="上级部门主管">
          <el-input v-model="parentManager" placeholder="上级部门主管" disabled />
        </el-form-item>
        <el-form-item label="部门层级" prop="level">
          <el-select v-model="currentDepartment.level" placeholder="请选择部门层级">
            <el-option label="一级部门" value="1" />
            <el-option label="二级部门" value="2" />
            <el-option label="三级部门" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="部门描述">
          <el-input
            v-model="currentDepartment.description"
            type="textarea"
            placeholder="请输入部门描述"
            rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="departmentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveDepartment">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 职位对话框 -->
    <el-dialog
      v-model="positionDialogVisible"
      :title="dialogType === 'add' ? '添加职位' : '编辑职位'"
      width="500px"
      destroy-on-close
    >
      <el-form :model="currentPosition" label-width="100px" :rules="positionRules" ref="positionFormRef">
        <el-form-item label="职位名称" prop="name">
          <el-input v-model="currentPosition.name" placeholder="请输入职位名称" />
        </el-form-item>
        <el-form-item label="所属部门" prop="departmentId">
          <el-select v-model="currentPosition.departmentId" placeholder="请选择所属部门">
            <el-option
              v-for="department in departments"
              :key="department.id"
              :label="department.name"
              :value="department.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="职级" prop="level">
          <el-select v-model="currentPosition.level" placeholder="请选择职级">
            <el-option label="初级" value="1" />
            <el-option label="中级" value="2" />
            <el-option label="高级" value="3" />
            <el-option label="专家" value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="职位描述">
          <el-input
            v-model="currentPosition.description"
            type="textarea"
            placeholder="请输入职位描述"
            rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="positionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePosition">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch, nextTick } from 'vue'
import { 
  Plus, 
  Edit, 
  Delete, 
  Search, 
  Download, 
  RefreshRight, 
  ZoomIn, 
  ZoomOut, 
  RefreshLeft 
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCompanyInfo, updateCompanyInfo } from '@/api/company'
import { getDepartments, getDepartment, createDepartment, updateDepartment, deleteDepartment } from '@/api/department'
import { getPositions, getPosition, createPosition, updatePosition, deletePosition } from '@/api/position'

// 标签页
const activeTab = ref('orgChart')
const orgChartRef = ref(null)
const orgChartLoaded = ref(false)
const orgChartRendering = ref(false)
const orgChartDataReady = ref(false)
const departmentFormRef = ref(null)
const positionFormRef = ref(null)

// 监听标签页变化
watch(activeTab, (newValue) => {
  if (newValue === 'orgChart') {
    // 如果数据已经准备好，直接初始化图表
    if (orgChartDataReady.value && !orgChartRendering.value) {
      nextTick(() => {
        initOrgChart();
      });
    } 
    // 否则加载数据
    else if (!orgChartDataReady.value) {
      loadAllDepartmentsForChart();
    }
  }
});

// 监听部门数据变化
watch(() => [...allDepartments.value], () => {
  // 如果当前是组织架构图标签页，则更新
  if (activeTab.value === 'orgChart' && orgChartRef.value) {
    initOrgChart();
  }
}, { deep: true });

// 监听公司信息变化
watch(() => companyInfo.value.name, () => {
  // 如果当前是组织架构图标签页，则更新
  if (activeTab.value === 'orgChart' && orgChartRef.value) {
    initOrgChart();
  }
});

// 加载状态
const tableLoading = reactive({
  departments: false,
  positions: false
})

// 表单验证规则
const departmentRules = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入部门代码', trigger: 'blur' },
    { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' }
  ],
  manager: [
    { required: true, message: '请选择部门主管', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择部门层级', trigger: 'change' }
  ],
  employeeCount: [
    { type: 'number', transform: (value) => Number(value), message: '员工数量必须为数字', trigger: 'blur' }
  ]
}

const positionRules = {
  name: [
    { required: true, message: '请输入职位名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  departmentId: [
    { required: true, message: '请选择所属部门', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择职级', trigger: 'change' }
  ]
}

// 部门列表
const departments = ref([])
// 所有部门列表（用于组织架构图）
const allDepartments = ref([])

// 职位列表
const positions = ref([])

// 员工选项
const employeeOptions = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' },
  { id: 4, name: '赵六' },
  { id: 5, name: '钱七' },
  { id: 6, name: '孙八' },
  { id: 7, name: '周九' },
  { id: 8, name: '吴十' },
])

// 公司信息
const companyInfo = ref({
  name: '',
  creditCode: '',
  legalRepresentative: '',
  registeredCapital: '',
  address: '',
  phone: '',
  email: '',
  companyType: '',
  registrationDate: null,
  description: ''
})

// 原始公司信息（用于重置）
const originalCompanyInfo = ref({})

// 搜索
const departmentSearch = ref('')
const positionSearch = ref('')

// 分页
const departmentPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: departments.value.length
})

const positionPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: positions.value.length
})

// 组织架构图缩放
const currentZoom = ref(1)

// 搜索结果过滤
const filteredDepartments = computed(() => {
  // 直接返回从后端获取的数据，不再进行前端分页处理
  return departments.value;
})

const filteredPositions = computed(() => {
  // 直接返回从后端获取的数据，已经进行了过滤和分页
  return positions.value;
})

// 获取职级名称
const getPositionLevelName = (level) => {
  const levelMap = {
    '1': '初级',
    '2': '中级',
    '3': '高级',
    '4': '专家'
  }
  return levelMap[level] || '未知'
}

// 获取职级标签类型
const getPositionLevelType = (level) => {
  const typeMap = {
    '1': 'info',
    '2': 'success',
    '3': 'warning',
    '4': 'danger'
  }
  return typeMap[level] || ''
}

// 获取部门层级名称
const getDepartmentLevelName = (department) => {
  if (!department.level) {
    // 如果没有明确设置层级，根据parentId判断
    return department.parentId === null ? '一级部门' : 
           departments.value.find(d => d.id === department.parentId)?.parentId === null ? '二级部门' : '三级部门'
  }
  
  const levelMap = {
    '1': '一级部门',
    '2': '二级部门',
    '3': '三级部门'
  }
  return levelMap[department.level] || '未知层级'
}

// 获取部门层级标签类型
const getDepartmentLevelType = (department) => {
  const level = department.level || (department.parentId === null ? '1' : 
                departments.value.find(d => d.id === department.parentId)?.parentId === null ? '2' : '3')
  
  const typeMap = {
    '1': 'primary',
    '2': 'success',
    '3': 'warning'
  }
  return typeMap[level] || 'info'
}

// 获取上级部门名称
const getParentDepartmentName = (department) => {
  if (!department.parentId) {
    return '-'
  }
  
  const parentDept = departments.value.find(d => d.id === department.parentId)
  return parentDept ? parentDept.name : '-'
}

// 获取上级部门主管
const getParentDepartmentManager = (department) => {
  if (!department.parentId) {
    return '-'
  }
  
  const parentDept = departments.value.find(d => d.id === department.parentId)
  return parentDept ? parentDept.manager : '-'
}

// 对话框相关
const departmentDialogVisible = ref(false)
const positionDialogVisible = ref(false)
const dialogType = ref('add') // 'add' 或 'edit'
const parentManager = ref('-') // 上级部门主管
const currentDepartment = ref({
  id: null,
  name: '',
  code: '',
  manager: '',
  parentId: null,
  level: '',
  employeeCount: 0,
  description: ''
})
const currentPosition = ref({
  id: null,
  name: '',
  departmentId: null,
  departmentName: '',
  level: '',
  description: ''
})

// 组织架构图操作
const zoomIn = () => {
  currentZoom.value += 0.1
  if (orgChartRef.value) {
    orgChartRef.value.style.transform = `scale(${currentZoom.value})`
  }
}

const zoomOut = () => {
  currentZoom.value -= 0.1
  if (currentZoom.value < 0.5) currentZoom.value = 0.5
  if (orgChartRef.value) {
    orgChartRef.value.style.transform = `scale(${currentZoom.value})`
  }
}

const resetZoom = () => {
  currentZoom.value = 1
  if (orgChartRef.value) {
    orgChartRef.value.style.transform = 'scale(1)'
  }
}

// 加载所有部门数据用于组织架构图
const loadAllDepartmentsForChart = async () => {
  if (orgChartRendering.value) return; // 如果正在渲染，不要重复请求
  
  orgChartLoaded.value = false;
  orgChartRendering.value = true;
  
  try {
    const response = await getDepartments({
      page: 1,
      pageSize: 1000, // 一次性获取足够多的数据
      search: ''
    });
    
    if (response.data && response.data.code === 200 && response.data.data) {
      allDepartments.value = response.data.data.list;
      orgChartDataReady.value = true; // 标记数据已准备好
      
      // 如果当前在组织架构图标签页，初始化图表
      if (activeTab.value === 'orgChart') {
        nextTick(() => {
          initOrgChart();
        });
      }
    } else {
      ElMessage.warning('获取组织架构图数据失败');
      orgChartLoaded.value = true; // 即使失败也标记为已加载，防止无限加载
    }
  } catch (error) {
    console.error('获取组织架构图数据失败:', error);
    ElMessage.error('获取组织架构图数据失败，请稍后重试');
    orgChartLoaded.value = true; // 即使失败也标记为已加载，防止无限加载
  } finally {
    orgChartRendering.value = false;
  }
};

// 数据变更后更新组织架构图
const updateOrgChart = debounce(() => {
  if (activeTab.value === 'orgChart' && orgChartRef.value && orgChartDataReady.value) {
    loadAllDepartmentsForChart();
  }
}, 500);

// 监听部门数据变化
watch(departments, () => {
  // 如果有新增或修改部门，可能需要更新所有部门数据
  if (activeTab.value === 'orgChart') {
    updateOrgChart();
  }
}, { deep: true });

// 监听公司信息变化
watch(() => companyInfo.value.name, () => {
  // 如果当前是组织架构图标签页，则更新
  if (activeTab.value === 'orgChart' && orgChartRef.value) {
    initOrgChart();
  }
});

// 分页相关
const handleSizeChange = (type, val) => {
  if (type === 'departments') {
    departmentPagination.pageSize = val
    departmentPagination.currentPage = 1
    fetchDepartments()
  } else if (type === 'positions') {
    positionPagination.pageSize = val
    positionPagination.currentPage = 1
    fetchPositions()
  }
}

const handleCurrentChange = (type, val) => {
  if (type === 'departments') {
    departmentPagination.currentPage = val
    fetchDepartments()
  } else if (type === 'positions') {
    positionPagination.currentPage = val
    fetchPositions()
  }
}

// 监听搜索关键词变化
watch(departmentSearch, () => {
  departmentPagination.currentPage = 1
  fetchDepartments()
})

watch(positionSearch, () => {
  positionPagination.currentPage = 1
  fetchPositions()
})

// 导出数据
const handleExportData = () => {
  ElMessage.success('数据导出功能将在后续版本中实现')
}

// 刷新数据
const handleRefreshData = () => {
  tableLoading.departments = true
  tableLoading.positions = true
  
  // 获取部门数据
  fetchDepartments()
  
  // 获取职位数据
  fetchPositions()
  
  ElMessage.success('数据已刷新')
}

// 获取部门列表
const fetchDepartments = async (loadAll = false) => {
  tableLoading.departments = true
  try {
    // 获取当前页的部门数据
    const response = await getDepartments({
      page: departmentPagination.currentPage,
      pageSize: departmentPagination.pageSize,
      search: departmentSearch.value
    })
    
    if (response.data && response.data.code === 200 && response.data.data) {
      departments.value = response.data.data.list
      departmentPagination.total = response.data.data.total
      
      // 如果需要加载所有部门数据（用于组织架构图）
      if (loadAll) {
        await loadAllDepartmentsForChart();
      }
    } else {
      ElMessage.warning(response.data?.message || '获取部门列表失败')
    }
  } catch (error) {
    console.error('获取部门列表失败:', error.response?.data?.message || error.message)
    ElMessage.error('获取部门列表失败，请稍后重试')
  } finally {
    tableLoading.departments = false
  }
}

// 获取职位列表
const fetchPositions = async (loadAll = false) => {
  try {
    tableLoading.positions = true
    
    const params = {
      page: positionPagination.currentPage,
      pageSize: positionPagination.pageSize
    }
    
    if (positionSearch.value) {
      params.search = positionSearch.value
    }
    
    const response = await getPositions(params)
    
    if (response.data && response.data.code === 200 && response.data.data) {
      positions.value = response.data.data.list || []
      positionPagination.total = response.data.data.total || 0
    } else {
      ElMessage.warning('获取职位列表失败')
    }
  } catch (error) {
    console.error('获取职位列表失败:', error)
    ElMessage.error('获取职位列表失败，请稍后重试')
  } finally {
    tableLoading.positions = false
  }
}

// 部门操作
const handleAddDepartment = () => {
  dialogType.value = 'add'
  currentDepartment.value = {
    id: null,
    name: '',
    code: '',
    manager: '',
    parentId: null,
    level: '1',
    employeeCount: 0,
    description: ''
  }
  parentManager.value = '-'
  departmentDialogVisible.value = true
}

const handleEditDepartment = (row) => {
  dialogType.value = 'edit'
  currentDepartment.value = { ...row }
  // 设置上级部门主管
  if (row.parentId) {
    const parent = departments.value.find(dept => dept.id === row.parentId)
    parentManager.value = parent ? parent.manager : '-'
  } else {
    parentManager.value = '-'
  }
  departmentDialogVisible.value = true
}

const handleDeleteDepartment = (row) => {
  ElMessageBox.confirm(
    `确认删除部门 "${row.name}" 吗？删除后将无法恢复。`,
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const response = await deleteDepartment(row.id)
      if (response.data && response.data.code === 200) {
        ElMessage.success(response.data.message || '删除成功')
        // 重新获取部门列表
        fetchDepartments()
      } else {
        ElMessage.error(response.data?.message || '删除失败')
      }
    } catch (error) {
      console.error('删除部门失败:', error.response?.data?.message || error.message)
      ElMessage.error(error.response?.data?.message || '删除失败，请稍后重试')
    }
  }).catch(() => {})
}

const saveDepartment = async () => {
  if (!currentDepartment.value.name) {
    ElMessage.warning('部门名称不能为空')
    return
  }

  if (!currentDepartment.value.code) {
    ElMessage.warning('部门代码不能为空')
    return
  }

  try {
    if (dialogType.value === 'add') {
      // 创建新部门
      const response = await createDepartment({
        name: currentDepartment.value.name,
        code: currentDepartment.value.code,
        manager: currentDepartment.value.manager,
        parentId: currentDepartment.value.parentId,
        level: currentDepartment.value.level,
        employeeCount: Number(currentDepartment.value.employeeCount) || 0,
        description: currentDepartment.value.description
      })
      
      if (response.data && response.data.code === 200) {
        ElMessage.success(response.data.message || '添加成功')
        // 重新获取部门列表
        fetchDepartments()
        departmentDialogVisible.value = false
      } else {
        ElMessage.error(response.data?.message || '添加失败')
      }
    } else {
      // 更新部门
      const response = await updateDepartment(currentDepartment.value.id, {
        name: currentDepartment.value.name,
        code: currentDepartment.value.code,
        manager: currentDepartment.value.manager,
        parentId: currentDepartment.value.parentId,
        level: currentDepartment.value.level,
        employeeCount: Number(currentDepartment.value.employeeCount) || 0,
        description: currentDepartment.value.description
      })
      
      if (response.data && response.data.code === 200) {
        ElMessage.success(response.data.message || '编辑成功')
        // 重新获取部门列表
        fetchDepartments()
        departmentDialogVisible.value = false
      } else {
        ElMessage.error(response.data?.message || '编辑失败')
      }
    }
  } catch (error) {
    console.error('保存部门失败:', error.response?.data?.message || error.message)
    ElMessage.error(error.response?.data?.message || '保存失败，请稍后重试')
  }
}

// 处理上级部门变更
const handleParentChange = (parentId) => {
  if (parentId) {
    const parent = departments.value.find(dept => dept.id === parentId)
    parentManager.value = parent ? parent.manager : '-'
    
    // 自动设置层级
    if (parent && !currentDepartment.value.level) {
      if (parent.level === '1') {
        currentDepartment.value.level = '2'
      } else if (parent.level === '2') {
        currentDepartment.value.level = '3'
      }
    }
  } else {
    parentManager.value = '-'
    // 如果没有上级部门，默认为一级部门
    if (!currentDepartment.value.level) {
      currentDepartment.value.level = '1'
    }
  }
}

// 职位操作
const handleAddPosition = () => {
  dialogType.value = 'add'
  currentPosition.value = {
    id: null,
    name: '',
    departmentId: null,
    level: '',
    description: ''
  }
  positionDialogVisible.value = true
}

const handleEditPosition = (row) => {
  dialogType.value = 'edit'
  currentPosition.value = { ...row }
  positionDialogVisible.value = true
}

const handleDeletePosition = (row) => {
  ElMessageBox.confirm(
    `确认删除职位 "${row.name}" 吗？删除后将无法恢复。`,
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      tableLoading.positions = true
      const response = await deletePosition(row.id)
      if (response.data && response.data.code === 200) {
        ElMessage.success(response.data.data || '删除成功')
        // 重新获取职位列表
        fetchPositions()
      } else {
        ElMessage.error(response.data?.message || '删除失败')
      }
    } catch (error) {
      console.error('删除职位失败:', error)
      ElMessage.error('删除职位失败，请稍后重试')
    } finally {
      tableLoading.positions = false
    }
  }).catch(() => {})
}

const savePosition = async () => {
  if (!currentPosition.value.name) {
    ElMessage.warning('职位名称不能为空')
    return
  }
  
  if (!currentPosition.value.departmentId) {
    ElMessage.warning('请选择所属部门')
    return
  }

  try {
    tableLoading.positions = true
    
    if (dialogType.value === 'add') {
      // 创建职位
      const response = await createPosition({
        name: currentPosition.value.name,
        departmentId: currentPosition.value.departmentId,
        level: currentPosition.value.level,
        description: currentPosition.value.description
      })
      
      if (response.data && response.data.code === 200) {
        ElMessage.success(response.data.message || '添加成功')
        // 重新获取职位列表
        fetchPositions()
        positionDialogVisible.value = false
      } else {
        ElMessage.error(response.data?.message || '添加失败')
      }
    } else {
      // 更新职位
      const response = await updatePosition(currentPosition.value.id, {
        name: currentPosition.value.name,
        departmentId: currentPosition.value.departmentId,
        level: currentPosition.value.level,
        description: currentPosition.value.description
      })
      
      if (response.data && response.data.code === 200) {
        ElMessage.success(response.data.message || '更新成功')
        // 重新获取职位列表
        fetchPositions()
        positionDialogVisible.value = false
      } else {
        ElMessage.error(response.data?.message || '更新失败')
      }
    }
  } catch (error) {
    console.error('保存职位失败:', error)
    ElMessage.error('保存职位失败，请稍后重试')
  } finally {
    tableLoading.positions = false
  }
}

// 保存公司信息
const saveCompanyInfo = async () => {
  try {
    const response = await updateCompanyInfo({
      name: companyInfo.value.name,
      creditCode: companyInfo.value.creditCode,
      legalRepresentative: companyInfo.value.legalRepresentative,
      registeredCapital: companyInfo.value.registeredCapital,
      address: companyInfo.value.address,
      phone: companyInfo.value.phone,
      email: companyInfo.value.email,
      companyType: companyInfo.value.companyType,
      registrationDate: companyInfo.value.registrationDate,
      description: companyInfo.value.description
    })
    
    if (response.data && response.data.code === 200) {
      ElMessage.success('公司信息保存成功')
      originalCompanyInfo.value = { ...companyInfo.value }
    } else {
      ElMessage.error(response.data?.message || '保存失败')
    }
  } catch (error) {
    console.error('保存公司信息失败:', error)
    ElMessage.error('保存公司信息失败，请稍后重试')
  }
}

// 重置公司信息
const resetCompanyInfo = () => {
  companyInfo.value = { ...originalCompanyInfo.value }
  ElMessage.info('已重置为原始数据')
}

// 获取公司信息
const fetchCompanyInfo = async () => {
  try {
    const response = await getCompanyInfo()
    if (response.data && response.data.code === 200 && response.data.data) {
      const data = response.data.data
      companyInfo.value = {
        name: data.name,
        creditCode: data.creditCode,
        legalRepresentative: data.legalRepresentative,
        registeredCapital: data.registeredCapital,
        address: data.address,
        phone: data.phone,
        email: data.email,
        companyType: data.companyType,
        registrationDate: data.registrationDate ? new Date(data.registrationDate) : null,
        description: data.description
      }
      // 保存原始数据用于重置
      originalCompanyInfo.value = { ...companyInfo.value }
    } else {
      ElMessage.warning('获取公司信息失败')
    }
  } catch (error) {
    console.error('获取公司信息失败:', error)
    ElMessage.error('获取公司信息失败，请稍后重试')
  }
}

// 组件挂载时获取数据
onMounted(async () => {
  // 获取公司信息
  await fetchCompanyInfo();
  
  // 获取部门信息（同时加载所有部门数据）
  await fetchDepartments(true);
  
  // 获取职位列表
  await fetchPositions();
})

// 定义防抖函数
function debounce(fn, delay) {
  let timer = null;
  return function() {
    let context = this;
    let args = arguments;
    clearTimeout(timer);
    timer = setTimeout(function() {
      fn.apply(context, args);
    }, delay);
  };
}

const initOrgChart = () => {
  if (!orgChartRef.value) return;
  if (orgChartRendering.value) return; // 防止重复渲染
  
  orgChartRendering.value = true;
  orgChartLoaded.value = false; // 显示加载状态

  // 清空之前的内容
  orgChartRef.value.innerHTML = '<div class="chart-placeholder"><div class="loading-chart">组织架构图加载中...</div></div>';
  
  // 添加一些延迟，让浏览器有时间显示加载状态
  setTimeout(() => {
    try {
      if (!orgChartRef.value) {
        orgChartRendering.value = false;
        return;
      }
      
      // 获取公司名称
      const companyName = companyInfo.value.name || '丰信科技有限公司';
      
      // 处理部门数据前，确保数据存在
      if (!allDepartments.value || allDepartments.value.length === 0) {
        orgChartRef.value.innerHTML = '<div class="chart-placeholder">暂无部门数据</div>';
        orgChartLoaded.value = true;
        orgChartRendering.value = false;
        // 尝试再次加载部门数据
        if (!orgChartDataReady.value) {
          loadAllDepartmentsForChart();
        }
        return;
      }
      
      // 获取一级部门
      const level1Departments = allDepartments.value.filter(dept => dept.level === '1' || (!dept.parentId && dept.level !== '2' && dept.level !== '3'));
      
      // 对一级部门按名称排序，使其排列更美观
      level1Departments.sort((a, b) => a.name.localeCompare(b.name));
      
      // 获取二级部门
      const level2Departments = allDepartments.value.filter(dept => dept.level === '2' || 
        (dept.parentId && level1Departments.some(l1 => l1.id === dept.parentId)));
      
      // 对二级部门进行排序，按照其父部门在一级部门中的顺序
      level2Departments.sort((a, b) => {
        // 找到父部门在一级部门中的索引
        const parentIndexA = level1Departments.findIndex(dept => dept.id === a.parentId);
        const parentIndexB = level1Departments.findIndex(dept => dept.id === b.parentId);
        
        // 如果父部门索引不同，则按照父部门索引排序
        if (parentIndexA !== parentIndexB) {
          return parentIndexA - parentIndexB;
        }
        
        // 如果父部门相同，则按照部门名称排序
        return a.name.localeCompare(b.name);
      });
      
      // 为了在布局中保持清晰的视觉结构，创建一个父部门ID到其在二级部门中位置的映射
      const parentPositionMap = {};
      level2Departments.forEach((dept, index) => {
        if (!parentPositionMap[dept.id]) {
          parentPositionMap[dept.id] = index;
        }
      });
      
      // 获取三级部门
      const level3Departments = allDepartments.value.filter(dept => dept.level === '3' || 
        (dept.parentId && level2Departments.some(l2 => l2.id === dept.parentId)));
      
      // 对三级部门进行排序，先按照其父部门在二级部门中的顺序，再按名称
      level3Departments.sort((a, b) => {
        // 获取父部门在二级部门列表中的位置
        const parentPositionA = parentPositionMap[a.parentId] !== undefined ? parentPositionMap[a.parentId] : Number.MAX_SAFE_INTEGER;
        const parentPositionB = parentPositionMap[b.parentId] !== undefined ? parentPositionMap[b.parentId] : Number.MAX_SAFE_INTEGER;
        
        // 如果父部门顺序不同，按父部门顺序排列
        if (parentPositionA !== parentPositionB) {
          return parentPositionA - parentPositionB;
        }
        
        // 如果父部门相同，按名称排序
        return a.name.localeCompare(b.name);
      });
      
      // 构建组织架构图HTML结构
      const chartHTML = `
        <div class="dynamic-org-chart">
          <div class="org-title" id="company-node">${companyName}</div>
          
          <div class="level-container level1-container">
            ${level1Departments.map(dept => `
              <div class="org-item dept" data-id="${dept.id}" id="dept-${dept.id}">
                ${dept.name}
              </div>
            `).join('')}
          </div>
          
          <div class="level-container level2-container">
            ${level2Departments.map(dept => `
              <div class="org-item subdept" data-id="${dept.id}" data-parent="${dept.parentId || ''}" id="dept-${dept.id}">
                ${dept.name}
              </div>
            `).join('')}
          </div>
          
          <div class="level-container level3-container">
            ${level3Departments.map(dept => `
              <div class="org-item subteam" data-id="${dept.id}" data-parent="${dept.parentId || ''}" id="dept-${dept.id}">
                ${dept.name}
              </div>
            `).join('')}
          </div>
        </div>
      `;
      
      // 设置HTML内容
      orgChartRef.value.innerHTML = chartHTML;
      
      // 添加SVG容器
      const svgContainer = document.createElement('div');
      svgContainer.style.cssText = 'position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none;';
      
      // 创建SVG元素
      const svgElement = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svgElement.setAttribute('width', '100%');
      svgElement.setAttribute('height', '100%');
      svgElement.setAttribute('class', 'connection-lines');
      svgElement.style.cssText = 'position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1; overflow: visible;';
      
      // 添加SVG到容器
      svgContainer.appendChild(svgElement);
      
      // 检查是否已经加载了组织架构图
      if (orgChartRef.value.querySelector('.dynamic-org-chart')) {
        orgChartRef.value.querySelector('.dynamic-org-chart').appendChild(svgContainer);
        
        // 添加样式
        addOrgChartStyles();
        
        // 在DOM更新后绘制连接线
        nextTick(() => {
          drawOrgChartLines(svgElement, level1Departments, level2Departments, level3Departments);
          
          // 添加点击交互
          addOrgChartInteractions();
          
          // 标记为已加载完成
          orgChartLoaded.value = true;
          orgChartRendering.value = false;
        });
      }
    } catch (error) {
      console.error('初始化组织架构图失败:', error);
      orgChartRef.value.innerHTML = '<div class="chart-placeholder">组织架构图加载失败: ' + error.message + '</div>';
      orgChartLoaded.value = true;
      orgChartRendering.value = false;
    }
  }, 100);
}

// 添加组织架构图样式
const addOrgChartStyles = () => {
  const style = document.createElement('style');
  style.textContent = `
    .dynamic-org-chart {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      padding: 20px;
      min-height: 600px;
    }
    
    .org-title {
      background-color: #2861c1;
      color: white;
      padding: 10px 30px;
      border-radius: 5px;
      margin-bottom: 60px;
      font-weight: bold;
      text-align: center;
      position: relative;
      font-size: 18px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      z-index: 2;
    }
    
    .level-container {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 30px;
      margin-bottom: 60px;
      position: relative;
      width: 90%;
      z-index: 2;
    }
    
    .org-item {
      padding: 8px 15px;
      border-radius: 5px;
      text-align: center;
      position: relative;
      min-width: 120px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      z-index: 2;
    }
    
    .org-item:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .dept {
      background-color: #d1e5ff;
      border: 1px solid #b3d8ff;
    }
    
    .subdept {
      background-color: #e1f0ff;
      border: 1px solid #c6e2ff;
    }
    
    .subteam {
      background-color: #edf5ff;
      border: 1px solid #d9ecff;
    }
    
    .connection-lines {
      overflow: visible;
    }
    
    .connection-line {
      stroke: #aaa;
      stroke-width: 2;
      fill: none;
    }
    
    .loading-chart {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #909399;
    }
    
    @media screen and (max-width: 768px) {
      .level-container {
        flex-direction: column;
        align-items: center;
      }
    }
  `;
  orgChartRef.value.appendChild(style);
}

// 绘制组织架构图连接线
const drawOrgChartLines = (svg, level1Departments, level2Departments, level3Departments) => {
  if (!orgChartRef.value) return;
  
  try {
    const companyNode = document.getElementById('company-node');
    
    if (companyNode && svg) {
      // 获取组织架构图的位置信息
      const chartRect = orgChartRef.value.querySelector('.dynamic-org-chart').getBoundingClientRect();
      
      // 绘制从公司节点到一级部门的连接线
      const companyRect = companyNode.getBoundingClientRect();
      const companyX = companyRect.left + companyRect.width / 2 - chartRect.left;
      const companyY = companyRect.bottom - chartRect.top;
      
      // 绘制公司到一级部门的连接线
      level1Departments.forEach(dept => {
        const deptNode = document.getElementById(`dept-${dept.id}`);
        if (deptNode) {
          const deptRect = deptNode.getBoundingClientRect();
          const deptX = deptRect.left + deptRect.width / 2 - chartRect.left;
          const deptY = deptRect.top - chartRect.top;
          
          // 创建贝塞尔曲线连接线
          const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
          const bezierControlY = companyY + (deptY - companyY) / 2;
          const pathData = `M${companyX},${companyY} C${companyX},${bezierControlY} ${deptX},${bezierControlY} ${deptX},${deptY}`;
          
          path.setAttribute('d', pathData);
          path.setAttribute('class', 'connection-line');
          path.style.strokeWidth = '2';
          path.style.stroke = '#aaa';
          svg.appendChild(path);
        }
      });
      
      // 绘制从一级部门到二级部门的连接线
      level2Departments.forEach(dept => {
        if (dept.parentId) {
          const parentNode = document.getElementById(`dept-${dept.parentId}`);
          const deptNode = document.getElementById(`dept-${dept.id}`);
          
          if (parentNode && deptNode) {
            const parentRect = parentNode.getBoundingClientRect();
            const deptRect = deptNode.getBoundingClientRect();
            
            const parentX = parentRect.left + parentRect.width / 2 - chartRect.left;
            const parentY = parentRect.bottom - chartRect.top;
            const deptX = deptRect.left + deptRect.width / 2 - chartRect.left;
            const deptY = deptRect.top - chartRect.top;
            
            // 创建贝塞尔曲线连接线
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            const bezierControlY = parentY + (deptY - parentY) / 2;
            const pathData = `M${parentX},${parentY} C${parentX},${bezierControlY} ${deptX},${bezierControlY} ${deptX},${deptY}`;
            
            path.setAttribute('d', pathData);
            path.setAttribute('class', 'connection-line');
            path.style.strokeWidth = '2';
            path.style.stroke = '#aaa';
            svg.appendChild(path);
          }
        }
      });
      
      // 绘制从二级部门到三级部门的连接线
      level3Departments.forEach(dept => {
        if (dept.parentId) {
          const parentNode = document.getElementById(`dept-${dept.parentId}`);
          const deptNode = document.getElementById(`dept-${dept.id}`);
          
          if (parentNode && deptNode) {
            const parentRect = parentNode.getBoundingClientRect();
            const deptRect = deptNode.getBoundingClientRect();
            
            const parentX = parentRect.left + parentRect.width / 2 - chartRect.left;
            const parentY = parentRect.bottom - chartRect.top;
            const deptX = deptRect.left + deptRect.width / 2 - chartRect.left;
            const deptY = deptRect.top - chartRect.top;
            
            // 创建贝塞尔曲线连接线
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            const bezierControlY = parentY + (deptY - parentY) / 2;
            const pathData = `M${parentX},${parentY} C${parentX},${bezierControlY} ${deptX},${bezierControlY} ${deptX},${deptY}`;
            
            path.setAttribute('d', pathData);
            path.setAttribute('class', 'connection-line');
            path.style.strokeWidth = '2';
            path.style.stroke = '#aaa';
            svg.appendChild(path);
          }
        }
      });
    }
  } catch (error) {
    console.error('绘制组织架构图连接线失败:', error);
  }
}

// 添加组织架构图交互
const addOrgChartInteractions = () => {
  const departmentNodes = orgChartRef.value.querySelectorAll('.org-item');
  departmentNodes.forEach(node => {
    node.addEventListener('click', () => {
      const deptId = parseInt(node.getAttribute('data-id'));
      // 查找对应部门
      const dept = allDepartments.value.find(d => d.id === deptId);
      if (dept) {
        // 显示部门详情
        ElMessage({
          message: `${dept.name} - 部门主管: ${dept.manager}, 员工数量: ${dept.employeeCount}`,
          type: 'info'
        });
      }
    });
  });
}
</script>

<style scoped>
.company-settings {
  padding: 20px;
  height: 100%;
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.action-group {
  display: flex;
}

.settings-tabs {
  width: 100%;
}

.tab-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-input {
  width: 250px;
}

.settings-card {
  margin-bottom: 20px;
  border-radius: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.company-info-card {
  margin-bottom: 20px;
}

/* 组织架构图样式 */
.org-chart-container {
  padding: 20px;
  width: 100%;
  height: 700px;
  overflow: auto;
  position: relative;
  background-color: #fff;
  border-radius: 4px;
}

.org-chart-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 5px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.org-chart {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.3s;
  transform-origin: center top;
  display: flex;
  justify-content: center;
  padding-top: 20px;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

/* 组织结构图节点样式 */
.org-chart-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 90%;
  position: relative;
}

.org-level {
  display: flex;
  justify-content: center;
  width: 100%;
  position: relative;
}

.level-1 {
  justify-content: space-between;
  width: 80%;
  margin-top: 20px;
}

.level-2 {
  justify-content: space-between;
  width: 100%;
  margin-top: 40px;
}

.level-3 {
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
  width: 100%;
}

.org-branch {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 45%;
}

.left-branch {
  align-items: flex-start;
}

.right-branch {
  align-items: flex-end;
}

.org-departments {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 10px;
}

.org-sub-departments {
  margin-top: 20px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.org-sub-group {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}

.org-connection-line {
  width: 2px;
  height: 20px;
  background-color: #aaa;
  margin: 5px 0;
}

.main-line {
  width: 80%;
  height: 2px;
  background-color: #aaa;
  margin: 10px 0;
  position: relative;
}

.main-line::before, .main-line::after {
  content: '';
  position: absolute;
  width: 2px;
  height: 20px;
  background-color: #aaa;
}

.main-line::before {
  left: 0;
  top: 0;
}

.main-line::after {
  right: 0;
  top: 0;
}

.sub-line {
  position: relative;
}

.org-node {
  padding: 10px 20px;
  min-width: 120px;
  border-radius: 4px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  z-index: 1;
}

.company-node {
  background-color: #2861c1;
  color: white;
  border-radius: 8px;
  padding: 10px 30px;
  font-weight: bold;
}

.director-node {
  background-color: #4088db;
  color: white;
  border-radius: 8px;
}

.department-node {
  background-color: #d1e5ff;
  color: #333;
  border: 1px solid #b3d8ff;
  border-radius: 8px;
}

.sub-dept-node {
  background-color: #e1f0ff;
  color: #333;
  border: 1px solid #c6e2ff;
  border-radius: 8px;
}

.team-node {
  background-color: #edf5ff;
  color: #333;
  border: 1px solid #d9ecff;
  border-radius: 8px;
}

.node-content {
  text-align: center;
}

.node-title {
  font-weight: 600;
  font-size: 14px;
}

.manufacturing {
  position: absolute;
  top: 0;
  left: 10%;
}

.engineering {
  position: absolute;
  top: 0;
  left: 30%;
}

.planning {
  position: absolute;
  top: 0;
  right: 30%;
}

.purchase {
  position: absolute;
  top: 0;
  right: 10%;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .tab-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style> 