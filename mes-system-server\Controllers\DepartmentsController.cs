using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using mes_system_server.Data;
using mes_system_server.DTOs;
using mes_system_server.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace mes_system_server.Controllers
{
    [Route("api/departments")]
    [ApiController]
    [AllowAnonymous]
    public class DepartmentsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public DepartmentsController(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取部门列表
        /// </summary>
        /// <param name="page">页码，默认为1</param>
        /// <param name="pageSize">每页记录数，默认为10</param>
        /// <param name="search">搜索关键词</param>
        /// <returns>部门列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetDepartments([FromQuery] int page = 1, [FromQuery] int pageSize = 10, [FromQuery] string search = "")
        {
            try
            {
                var query = _context.Departments.AsQueryable();

                // 应用搜索条件
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(d => 
                        d.Name.Contains(search) || 
                        d.Code.Contains(search) || 
                        d.Manager.Contains(search)
                    );
                }

                // 计算总记录数
                var total = await query.CountAsync();

                // 应用分页
                var departments = await query
                    .OrderBy(d => d.Id)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // 构建响应数据
                var departmentDtos = departments.Select(d => new DepartmentDto
                {
                    Id = d.Id,
                    Name = d.Name,
                    Code = d.Code,
                    Manager = d.Manager,
                    ParentId = d.ParentId,
                    Level = d.Level,
                    EmployeeCount = d.EmployeeCount,
                    Description = d.Description,
                    CreateTime = d.CreateTime
                }).ToList();

                var response = new DepartmentResponseDto
                {
                    Total = total,
                    List = departmentDtos
                };

                return Ok(ApiResponseDto<DepartmentResponseDto>.Success(response));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取部门列表失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 根据ID获取部门详情
        /// </summary>
        /// <param name="id">部门ID</param>
        /// <returns>部门详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetDepartment(int id)
        {
            try
            {
                var department = await _context.Departments.FindAsync(id);

                if (department == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("部门不存在", 404));
                }

                var departmentDto = new DepartmentDto
                {
                    Id = department.Id,
                    Name = department.Name,
                    Code = department.Code,
                    Manager = department.Manager,
                    ParentId = department.ParentId,
                    Level = department.Level,
                    EmployeeCount = department.EmployeeCount,
                    Description = department.Description,
                    CreateTime = department.CreateTime
                };

                return Ok(ApiResponseDto<DepartmentDto>.Success(departmentDto));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取部门详情失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 创建部门
        /// </summary>
        /// <param name="departmentDto">部门信息</param>
        /// <returns>创建的部门</returns>
        [HttpPost]
        public async Task<IActionResult> CreateDepartment([FromBody] CreateDepartmentDto departmentDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                // 检查部门代码是否已存在
                var existingDepartment = await _context.Departments
                    .FirstOrDefaultAsync(d => d.Code == departmentDto.Code);

                if (existingDepartment != null)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("部门代码已存在"));
                }

                // 如果有父部门ID，检查父部门是否存在
                if (departmentDto.ParentId.HasValue)
                {
                    var parentDepartment = await _context.Departments.FindAsync(departmentDto.ParentId.Value);
                    if (parentDepartment == null)
                    {
                        return BadRequest(ApiResponseDto<string>.Fail("父部门不存在"));
                    }
                }

                var department = new Department
                {
                    Name = departmentDto.Name,
                    Code = departmentDto.Code,
                    Manager = departmentDto.Manager,
                    ParentId = departmentDto.ParentId,
                    Level = departmentDto.Level,
                    EmployeeCount = departmentDto.EmployeeCount,
                    Description = departmentDto.Description,
                    CreateTime = DateTime.UtcNow,
                    UpdateTime = DateTime.UtcNow
                };

                _context.Departments.Add(department);
                await _context.SaveChangesAsync();

                var createdDepartmentDto = new DepartmentDto
                {
                    Id = department.Id,
                    Name = department.Name,
                    Code = department.Code,
                    Manager = department.Manager,
                    ParentId = department.ParentId,
                    Level = department.Level,
                    EmployeeCount = department.EmployeeCount,
                    Description = department.Description,
                    CreateTime = department.CreateTime
                };

                return CreatedAtAction(nameof(GetDepartment), new { id = department.Id }, 
                    ApiResponseDto<DepartmentDto>.Success(createdDepartmentDto, "添加成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"创建部门失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 更新部门
        /// </summary>
        /// <param name="id">部门ID</param>
        /// <param name="departmentDto">部门信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateDepartment(int id, [FromBody] UpdateDepartmentDto departmentDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                var department = await _context.Departments.FindAsync(id);

                if (department == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("部门不存在", 404));
                }

                // 检查部门代码是否已存在（排除当前部门）
                var existingDepartment = await _context.Departments
                    .FirstOrDefaultAsync(d => d.Code == departmentDto.Code && d.Id != id);

                if (existingDepartment != null)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("部门代码已存在"));
                }

                // 如果有父部门ID，检查父部门是否存在且不是自己
                if (departmentDto.ParentId.HasValue)
                {
                    if (departmentDto.ParentId.Value == id)
                    {
                        return BadRequest(ApiResponseDto<string>.Fail("部门不能将自己设为父部门"));
                    }

                    var parentDepartment = await _context.Departments.FindAsync(departmentDto.ParentId.Value);
                    if (parentDepartment == null)
                    {
                        return BadRequest(ApiResponseDto<string>.Fail("父部门不存在"));
                    }

                    // 检查是否会形成循环引用
                    if (await HasCircularReference(id, departmentDto.ParentId.Value))
                    {
                        return BadRequest(ApiResponseDto<string>.Fail("设置的父部门会导致循环引用"));
                    }
                }

                // 更新部门信息
                department.Name = departmentDto.Name;
                department.Code = departmentDto.Code;
                department.Manager = departmentDto.Manager;
                department.ParentId = departmentDto.ParentId;
                department.Level = departmentDto.Level;
                department.EmployeeCount = departmentDto.EmployeeCount;
                department.Description = departmentDto.Description;
                department.UpdateTime = DateTime.UtcNow;

                _context.Departments.Update(department);
                await _context.SaveChangesAsync();

                return Ok(ApiResponseDto<object>.Success(null, "更新成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"更新部门失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 删除部门
        /// </summary>
        /// <param name="id">部门ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteDepartment(int id)
        {
            try
            {
                var department = await _context.Departments.FindAsync(id);

                if (department == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("部门不存在", 404));
                }

                // 检查是否有子部门
                var hasChildren = await _context.Departments
                    .AnyAsync(d => d.ParentId == id);

                if (hasChildren)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("该部门下存在子部门，无法删除"));
                }

                // TODO: 检查是否有关联的职位
                // 需要在添加职位模型后实现

                _context.Departments.Remove(department);
                await _context.SaveChangesAsync();

                return Ok(ApiResponseDto<object>.Success(null, "删除成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"删除部门失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 检查是否会形成循环引用
        /// </summary>
        /// <param name="departmentId">当前部门ID</param>
        /// <param name="parentId">父部门ID</param>
        /// <returns>是否会形成循环引用</returns>
        private async Task<bool> HasCircularReference(int departmentId, int parentId)
        {
            if (departmentId == parentId)
            {
                return true;
            }

            // 逐级向上查找
            var currentParentId = parentId;
            var visitedParents = new HashSet<int>();

            while (currentParentId != null)
            {
                if (visitedParents.Contains(currentParentId))
                {
                    // 已经访问过该父部门，说明存在循环
                    return true;
                }

                visitedParents.Add(currentParentId);

                if (currentParentId == departmentId)
                {
                    // 如果在父链上找到了当前部门，说明会形成循环
                    return true;
                }

                // 获取当前父部门的上级部门
                var parent = await _context.Departments.FindAsync(currentParentId);
                if (parent == null || parent.ParentId == null)
                {
                    // 已经到达顶级部门，没有形成循环
                    break;
                }

                currentParentId = parent.ParentId.Value;
            }

            return false;
        }
    }
} 