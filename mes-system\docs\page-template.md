# PageTemplate 通用页面模板使用说明

## 概述

PageTemplate 是一个通用的Vue组件，用于快速创建标准的CRUD（增删改查）页面。该组件提供了统一的页面布局、搜索功能、数据表格、分页、表单对话框等功能，大大减少了重复代码的编写。

## 功能特性

- 🎨 **统一的页面布局**：标准的页面头部、内容区域和操作按钮
- 🔍 **灵活的搜索功能**：支持输入框、下拉选择、日期选择等多种搜索类型
- 📊 **可配置的数据表格**：支持排序、固定列、自定义列渲染
- 📄 **智能分页**：自动处理分页逻辑，支持页码和页数选择
- 📝 **动态表单**：根据配置自动生成表单，支持多种表单控件
- ✅ **表单验证**：内置表单验证机制
- 📱 **响应式设计**：自适应不同屏幕尺寸
- 🔧 **高度可扩展**：支持插槽自定义内容

## 在菜单管理中的应用

### 自动生成页面

当在菜单管理中添加新的菜单项时，可以启用"自动生成页面文件"选项：

1. **创建菜单项**：在菜单管理中点击"新增菜单"
2. **填写菜单信息**：输入菜单名称、ID、路径等基本信息
3. **启用页面生成**：勾选"自动生成页面文件"开关
4. **预览页面结构**：系统会自动显示将要生成的页面预览
5. **保存菜单**：提交后系统自动生成标准页面文件

### 生成的文件结构

```
src/
├── views/
│   └── [模块名]/
│       └── [页面名].vue     # Vue组件文件
└── api/
    └── [api名].js          # API接口文件
```

## 基本用法

### 1. 导入组件

```vue
<script setup>
import PageTemplate from '@/components/PageTemplate.vue'
</script>
```

### 2. 使用组件

```vue
<template>
  <PageTemplate
    ref="pageTemplateRef"
    page-title="数据管理"
    entity-name="数据"
    :search-fields="searchFields"
    :table-columns="tableColumns"
    :form-fields="formFields"
    :form-rules="formRules"
    @search="handleSearch"
    @submit="handleSubmit"
    @refresh="loadData"
  />
</template>
```

### 3. 配置搜索字段

```javascript
const searchFields = [
  {
    prop: 'name',           // 字段名
    label: '名称',          // 显示标签
    type: 'input',          // 控件类型
    placeholder: '请输入名称'
  },
  {
    prop: 'status',
    label: '状态',
    type: 'select',
    options: [
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ]
  },
  {
    prop: 'createTime',
    label: '创建时间',
    type: 'daterange'
  }
]
```

#### 支持的搜索字段类型

- `input`：文本输入框
- `select`：下拉选择框
- `date`：日期选择器
- `daterange`：日期范围选择器

### 4. 配置表格列

```javascript
const tableColumns = [
  {
    prop: 'id',
    label: 'ID',
    width: '80'
  },
  {
    prop: 'name',
    label: '名称',
    minWidth: '150'
  },
  {
    prop: 'status',
    label: '状态',
    width: '100',
    slot: 'status'        // 使用插槽自定义渲染
  }
]
```

### 5. 配置表单字段

```javascript
const formFields = [
  {
    prop: 'name',
    label: '名称',
    type: 'input',
    placeholder: '请输入名称'
  },
  {
    prop: 'type',
    label: '类型',
    type: 'select',
    options: [
      { label: '类型A', value: 'A' },
      { label: '类型B', value: 'B' }
    ]
  },
  {
    prop: 'status',
    label: '状态',
    type: 'switch',
    defaultValue: true
  },
  {
    prop: 'description',
    label: '描述',
    type: 'textarea',
    rows: 3
  }
]
```

#### 支持的表单字段类型

- `input`：文本输入框
- `textarea`：多行文本框
- `select`：下拉选择框
- `switch`：开关控件
- `date`：日期选择器
- `number`：数字输入框

### 6. 配置表单验证

```javascript
const formRules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}
```

## 事件处理

### 必需事件

```javascript
// 搜索事件
const handleSearch = (searchParams) => {
  console.log('搜索参数:', searchParams)
  // 重新加载数据
  loadData(searchParams)
}

// 表单提交事件
const handleSubmit = async ({ type, data }) => {
  try {
    if (type === 'add') {
      // 新增逻辑
      await createData(data)
    } else {
      // 编辑逻辑
      await updateData(data.id, data)
    }
    
    pageTemplateRef.value?.showSuccess('操作成功')
    pageTemplateRef.value?.closeDialog()
    loadData()
  } catch (error) {
    pageTemplateRef.value?.showError('操作失败')
  } finally {
    pageTemplateRef.value?.setSubmitting(false)
  }
}

// 刷新事件
const loadData = async (params = {}) => {
  try {
    pageTemplateRef.value?.setLoading(true)
    
    const response = await getDataList(params)
    
    pageTemplateRef.value?.setDataList(response.data.list)
    pageTemplateRef.value?.setPagination(
      response.data.page,
      response.data.size,
      response.data.total
    )
  } catch (error) {
    pageTemplateRef.value?.showError('加载数据失败')
  } finally {
    pageTemplateRef.value?.setLoading(false)
  }
}
```

### 可选事件

```javascript
// 新增事件
const handleAdd = () => {
  console.log('新增按钮被点击')
}

// 编辑事件
const handleEdit = (row) => {
  console.log('编辑行:', row)
}

// 查看事件
const handleView = (row) => {
  console.log('查看行:', row)
}

// 删除事件
const handleDelete = async (row) => {
  try {
    await deleteData(row.id)
    pageTemplateRef.value?.showSuccess('删除成功')
    loadData()
  } catch (error) {
    pageTemplateRef.value?.showError('删除失败')
  }
}
```

## 插槽使用

### 自定义表格列

```vue
<template>
  <PageTemplate>
    <!-- 状态列自定义渲染 -->
    <template #status="{ row }">
      <el-tag :type="row.status ? 'success' : 'danger'">
        {{ row.status ? '启用' : '禁用' }}
      </el-tag>
    </template>
  </PageTemplate>
</template>
```

### 额外操作按钮

```vue
<template>
  <PageTemplate>
    <!-- 额外的页面操作按钮 -->
    <template #extra-actions>
      <el-button type="warning" @click="handleExport">导出</el-button>
      <el-button type="info" @click="handleImport">导入</el-button>
    </template>
  </PageTemplate>
</template>
```

### 自定义操作列

```vue
<template>
  <PageTemplate>
    <!-- 表格操作列额外按钮 -->
    <template #custom-actions="{ row }">
      <el-button type="warning" size="small" @click="handleCopy(row)">
        复制
      </el-button>
    </template>
  </PageTemplate>
</template>
```

### 自定义表单项

```vue
<template>
  <PageTemplate>
    <!-- 自定义表单项 -->
    <template #form-customField="{ field, form }">
      <el-upload
        action="/upload"
        :show-file-list="false"
        @success="handleUploadSuccess"
      >
        <el-button>上传文件</el-button>
      </el-upload>
    </template>
  </PageTemplate>
</template>
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| pageTitle | String | '数据管理' | 页面标题 |
| entityName | String | '数据' | 实体名称，用于对话框标题 |
| addButtonText | String | '新增' | 新增按钮文本 |
| showSearch | Boolean | true | 是否显示搜索区域 |
| searchFields | Array | [] | 搜索字段配置 |
| tableColumns | Array | [] | 表格列配置 |
| formFields | Array | [] | 表单字段配置 |
| formRules | Object | {} | 表单验证规则 |
| showSelection | Boolean | false | 是否显示选择列 |
| showIndex | Boolean | true | 是否显示序号列 |
| showActions | Boolean | true | 是否显示操作列 |
| actionWidth | String | '200' | 操作列宽度 |
| actions | Array | ['edit', 'delete'] | 可用操作 |
| showPagination | Boolean | true | 是否显示分页 |
| pageSizes | Array | [10, 20, 50, 100] | 分页大小选项 |
| dialogWidth | String | '600px' | 对话框宽度 |

## 组件方法

通过 ref 可以调用以下方法：

```javascript
const pageTemplateRef = ref(null)

// 设置加载状态
pageTemplateRef.value?.setLoading(true)

// 设置提交状态
pageTemplateRef.value?.setSubmitting(true)

// 设置数据列表
pageTemplateRef.value?.setDataList(dataList)

// 设置分页信息
pageTemplateRef.value?.setPagination(page, size, total)

// 关闭对话框
pageTemplateRef.value?.closeDialog()

// 显示成功消息
pageTemplateRef.value?.showSuccess('操作成功')

// 显示错误消息
pageTemplateRef.value?.showError('操作失败')
```

## 最佳实践

### 1. 数据加载

```javascript
const loadData = async (params = {}) => {
  try {
    pageTemplateRef.value?.setLoading(true)
    
    // 构建查询参数
    const queryParams = {
      page: pageTemplateRef.value?.pagination.page || 1,
      size: pageTemplateRef.value?.pagination.size || 20,
      ...params
    }
    
    const response = await api.getList(queryParams)
    
    pageTemplateRef.value?.setDataList(response.data.list)
    pageTemplateRef.value?.setPagination(
      response.data.page,
      response.data.size,
      response.data.total
    )
  } catch (error) {
    pageTemplateRef.value?.showError('加载数据失败')
  } finally {
    pageTemplateRef.value?.setLoading(false)
  }
}
```

### 2. 表单处理

```javascript
const handleSubmit = async ({ type, data }) => {
  try {
    pageTemplateRef.value?.setSubmitting(true)
    
    if (type === 'add') {
      await api.create(data)
      pageTemplateRef.value?.showSuccess('新增成功')
    } else {
      await api.update(data.id, data)
      pageTemplateRef.value?.showSuccess('编辑成功')
    }
    
    pageTemplateRef.value?.closeDialog()
    loadData()
  } catch (error) {
    pageTemplateRef.value?.showError('保存失败')
  } finally {
    pageTemplateRef.value?.setSubmitting(false)
  }
}
```

### 3. 删除确认

```javascript
const handleDelete = async (row) => {
  try {
    await api.delete(row.id)
    pageTemplateRef.value?.showSuccess('删除成功')
    loadData()
  } catch (error) {
    pageTemplateRef.value?.showError('删除失败')
  }
}
```

## 示例页面

参考 `src/views/example/PageTemplateExample.vue` 查看完整的使用示例。

## 注意事项

1. **组件引用**：需要通过 ref 获取组件实例才能调用组件方法
2. **事件处理**：确保正确处理 loading 和 submitting 状态
3. **数据格式**：确保 API 返回的数据格式符合组件要求
4. **表单验证**：合理配置表单验证规则，提升用户体验
5. **插槽使用**：善用插槽扩展组件功能，避免修改组件源码

## 总结

PageTemplate 通用页面模板极大地简化了标准CRUD页面的开发工作，通过配置化的方式快速生成功能完整的页面。结合菜单管理系统的自动生成功能，可以实现从菜单配置到页面实现的一站式开发体验。 