<template>
  <div class="i18n-management">
    <div class="page-header">
      <h2 class="page-title">多语言管理</h2>
      <div class="page-actions">
        <el-button type="primary" :icon="Plus" @click="handleAddI18nKey">新增翻译</el-button>
        <el-button type="warning" :icon="Download" @click="handleExtractTexts">提取文本</el-button>
        <el-button type="success" :icon="RefreshRight" @click="refreshI18nList">刷新</el-button>
        <el-button type="info" :icon="Upload" @click="handleImportExport">导入/导出</el-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-cards">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ totalKeys }}</div>
              <div class="stats-label">总翻译键</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ supportedLanguages.length }}</div>
              <div class="stats-label">支持语言</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ completedTranslations }}</div>
              <div class="stats-label">已完成翻译</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ Math.round((completedTranslations / totalKeys) * 100) || 0 }}%</div>
              <div class="stats-label">完成度</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="搜索键名">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入键名或翻译内容"
            clearable
            style="width: 300px;"
            @input="handleSearch"
          />
        </el-form-item>
        <el-form-item label="模块筛选">
          <el-select v-model="searchForm.module" placeholder="选择模块" clearable style="width: 150px;">
            <el-option
              v-for="module in moduleList"
              :key="module"
              :label="module"
              :value="module"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="翻译状态">
          <el-select v-model="searchForm.status" placeholder="翻译状态" clearable style="width: 150px;">
            <el-option label="已完成" value="completed" />
            <el-option label="未完成" value="incomplete" />
            <el-option label="缺失翻译" value="missing" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <!-- 多语言数据表格 -->
    <el-table
      v-loading="loading"
      :data="filteredI18nList"
      border
      style="width: 100%"
      :max-height="600"
    >
      <el-table-column prop="key" label="翻译键" width="200" fixed="left">
        <template #default="{ row }">
          <div class="key-cell">
            <el-tag v-if="row.module" size="small" type="primary">{{ row.module }}</el-tag>
            <div class="key-text">{{ row.key }}</div>
          </div>
        </template>
      </el-table-column>
      
      <!-- 动态生成语言列 -->
      <el-table-column 
        v-for="lang in supportedLanguages" 
        :key="lang.code"
        :label="lang.name"
        :min-width="200"
      >
        <template #header>
          <div class="language-header">
            <span>{{ lang.flag }}</span>
            <span>{{ lang.name }}</span>
          </div>
        </template>
        <template #default="{ row, $index }">
          <div class="translation-cell">
            <el-input
              v-model="row.translations[lang.code]"
              :placeholder="`请输入${lang.name}翻译`"
              type="textarea"
              :rows="2"
              resize="none"
              @blur="handleTranslationChange(row, lang.code, $index)"
              :class="{ 'missing-translation': !row.translations[lang.code] }"
            />
            <div class="translation-status">
              <el-tag 
                v-if="row.translations[lang.code]" 
                size="small" 
                type="success"
              >
                已翻译
              </el-tag>
              <el-tag 
                v-else 
                size="small" 
                type="danger"
              >
                待翻译
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row, $index }">
          <el-button-group>
            <el-button
              type="primary"
              size="small"
              @click="handleEditI18nKey(row, $index)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDeleteI18nKey(row, $index)"
            >
              删除
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalItems"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑翻译对话框 -->
    <el-dialog
      v-model="i18nDialogVisible"
      :title="i18nDialogType === 'add' ? '新增翻译' : '编辑翻译'"
      width="600px"
      @closed="resetI18nForm"
    >
      <el-form
        ref="i18nFormRef"
        :model="i18nForm"
        :rules="i18nRules"
        label-width="100px"
      >
        <el-form-item label="翻译键" prop="key">
          <el-input 
            v-model="i18nForm.key" 
            :disabled="i18nDialogType === 'edit'"
            placeholder="如：login.title" 
          />
        </el-form-item>
        <el-form-item label="模块" prop="module">
          <el-select v-model="i18nForm.module" placeholder="选择所属模块" filterable allow-create>
            <el-option
              v-for="module in moduleList"
              :key="module"
              :label="module"
              :value="module"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="i18nForm.description" 
            type="textarea"
            :rows="2"
            placeholder="翻译内容的描述说明（可选）" 
          />
        </el-form-item>
        
        <!-- 各语言翻译输入 -->
        <div class="language-inputs">
          <h4>翻译内容</h4>
          <el-form-item 
            v-for="lang in supportedLanguages" 
            :key="lang.code"
            :label="lang.name"
            :prop="`translations.${lang.code}`"
          >
            <el-input
              v-model="i18nForm.translations[lang.code]"
              :placeholder="`请输入${lang.name}翻译`"
              type="textarea"
              :rows="2"
            />
          </el-form-item>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="i18nDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveI18nKey" :loading="saving">
            {{ saving ? '保存中...' : '确定' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文本提取对话框 -->
    <el-dialog
      v-model="extractDialogVisible"
      title="文本提取"
      width="800px"
      @closed="resetExtractForm"
    >
      <div class="extract-container">
        <el-form :model="extractForm" label-width="100px">
          <el-form-item label="选择页面">
            <el-select v-model="extractForm.selectedPage" placeholder="选择要提取文本的页面" style="width: 100%;">
              <el-option
                v-for="page in pageList"
                :key="page.path"
                :label="`${page.name} (${page.path})`"
                :value="page.path"
              />
            </el-select>
          </el-form-item>
          <el-alert 
            title="自动提取说明" 
            type="info" 
            :closable="false"
            style="margin: 16px 0;"
          >
            <p>系统将自动从选择的页面中提取所有中文文本，包括：</p>
            <ul style="margin: 8px 0; padding-left: 20px;">
              <li>页面标题和子标题</li>
              <li>按钮文本</li>
              <li>表单标签和占位符</li>
              <li>消息提示文本</li>
              <li>Element Plus组件文本</li>
            </ul>
            <p>并自动生成对应的翻译键名，无需手动配置。</p>
          </el-alert>
        </el-form>

        <div class="extract-actions">
          <el-button type="primary" @click="performTextExtraction" :loading="extracting">
            {{ extracting ? '提取中...' : '开始提取' }}
          </el-button>
          <el-button @click="extractDialogVisible = false">取消</el-button>
        </div>

        <!-- 提取结果预览 -->
        <div v-if="extractedTexts.length > 0" class="extract-results">
          <el-divider>提取结果预览</el-divider>
          <div class="results-header">
            <span>共提取到 {{ extractedTexts.length }} 个文本项</span>
            <el-button type="success" size="small" @click="handleBatchAddTexts">
              批量添加到翻译列表
            </el-button>
          </div>
          <el-table :data="extractedTexts" border max-height="300">
            <el-table-column prop="key" label="生成的键名" width="200" />
            <el-table-column prop="text" label="提取的文本" min-width="150" />
            <el-table-column prop="type" label="文本类型" width="100">
              <template #default="{ row }">
                <el-tag size="small" :type="getTypeTagColor(row.type)">{{ getTypeDisplayName(row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="context" label="上下文" width="200" show-overflow-tooltip />
            <el-table-column label="操作" width="100">
              <template #default="{ row, $index }">
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="extractedTexts.splice($index, 1)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 导入/导出对话框 -->
    <el-dialog
      v-model="importExportDialogVisible"
      title="导入/导出"
      width="600px"
    >
      <el-tabs v-model="importExportTab">
        <el-tab-pane label="导出" name="export">
          <div class="export-container">
            <el-form label-width="100px">
              <el-form-item label="导出格式">
                <el-radio-group v-model="exportFormat">
                  <el-radio label="json">JSON格式</el-radio>
                  <el-radio label="excel">Excel格式</el-radio>
                  <el-radio label="csv">CSV格式</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="导出语言">
                <el-checkbox-group v-model="exportLanguages">
                  <el-checkbox 
                    v-for="lang in supportedLanguages" 
                    :key="lang.code"
                    :label="lang.code"
                  >
                    {{ lang.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
            <el-button type="primary" @click="handleExport" :loading="exporting">
              {{ exporting ? '导出中...' : '开始导出' }}
            </el-button>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="导入" name="import">
          <div class="import-container">
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :show-file-list="false"
              accept=".json,.xlsx,.csv"
              :on-change="handleFileChange"
            >
              <el-button type="primary">选择文件</el-button>
            </el-upload>
            <div v-if="importFile" class="import-preview">
              <p>已选择文件：{{ importFile.name }}</p>
              <el-button type="success" @click="handleImport" :loading="importing">
                {{ importing ? '导入中...' : '开始导入' }}
              </el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download, RefreshRight, Upload } from '@element-plus/icons-vue'
import i18n from '@/i18n'
import { extractChineseTexts, fetchVueFileContent } from '@/utils/textExtractor'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const extracting = ref(false)
const exporting = ref(false)
const importing = ref(false)

// 多语言数据
const i18nList = ref([])
const filteredI18nList = ref([])
const totalItems = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 支持的语言列表
const supportedLanguages = ref([
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' }
])

// 搜索筛选
const searchForm = ref({
  keyword: '',
  module: '',
  status: ''
})

// 对话框控制
const i18nDialogVisible = ref(false)
const i18nDialogType = ref('add')
const i18nFormRef = ref()

// 表单数据
const i18nForm = ref({
  key: '',
  module: '',
  description: '',
  translations: {}
})

// 表单验证规则
const i18nRules = {
  key: [
    { required: true, message: '请输入翻译键', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9._-]+$/, message: '翻译键只能包含字母、数字、点号、下划线和横线', trigger: 'blur' }
  ]
}

// 文本提取相关
const extractDialogVisible = ref(false)
const extractForm = ref({
  selectedPage: ''
})
const extractedTexts = ref([])

// 导入导出相关
const importExportDialogVisible = ref(false)
const importExportTab = ref('export')
const exportFormat = ref('json')
const exportLanguages = ref(['zh', 'en'])
const importFile = ref(null)

// 页面列表（用于文本提取）
const pageList = ref([
  { name: '登录页', path: '/views/Login.vue' },
  { name: '首页', path: '/views/Home.vue' },
  // 系统管理
  { name: '用户管理', path: '/views/system/UserManagement.vue' },
  { name: '角色权限', path: '/views/system/RolePermission.vue' },
  { name: '菜单管理', path: '/views/system/MenuManagement.vue' },
  { name: '数据字典', path: '/views/system/DataDictionary.vue' },
  { name: '多语言管理', path: '/views/system/I18nManagement.vue' },
  // 人事管理
  { name: '员工管理', path: '/views/hr/EmployeeManagement.vue' },
  { name: '考勤管理', path: '/views/hr/AttendanceManagement.vue' },
  { name: '薪资管理', path: '/views/hr/SalaryManagement.vue' },
  { name: '公司设置', path: '/views/hr/CompanySettings.vue' },
  // 工程管理
  { name: '型号管理', path: '/views/engineering/ModelManagement.vue' },
  { name: 'WIS管理', path: '/views/wis/WisPDFViewer.vue' },
  { name: 'WIS查询', path: '/views/wis/WisQuery.vue' },
  // 生产管理
  { name: '工作中心设置', path: '/views/production/WorkCenterSettings.vue' }
])

// 计算属性
const totalKeys = computed(() => i18nList.value.length)
const moduleList = computed(() => {
  const modules = [...new Set(i18nList.value.map(item => item.module).filter(Boolean))]
  return modules.sort()
})

const completedTranslations = computed(() => {
  return i18nList.value.filter(item => {
    return supportedLanguages.value.every(lang => item.translations[lang.code])
  }).length
})

// 初始化数据
onMounted(() => {
  loadI18nData()
})

// 加载多语言数据
const loadI18nData = () => {
  loading.value = true
  
  // 从当前i18n配置中加载数据
  const messages = i18n.global.messages
  const i18nData = []
  
  // 递归解析嵌套的翻译对象
  const parseMessages = (obj, prefix = '', module = '', langCode = '') => {
    Object.keys(obj).forEach(key => {
      const fullKey = prefix ? `${prefix}.${key}` : key
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        const currentModule = prefix ? module : key
        parseMessages(obj[key], fullKey, currentModule, langCode)
      } else {
        // 查找是否已存在该key的记录
        let existingItem = i18nData.find(item => item.key === fullKey)
        if (!existingItem) {
          existingItem = {
            key: fullKey,
            module: module || prefix.split('.')[0] || 'common',
            translations: {}
          }
          i18nData.push(existingItem)
        }
        // 设置当前语言的翻译
        existingItem.translations[langCode] = obj[key]
      }
    })
  }
  
  // 解析每种语言的消息
  supportedLanguages.value.forEach(lang => {
    if (messages[lang.code]) {
      parseMessages(messages[lang.code], '', '', lang.code)
    }
  })
  
  i18nList.value = i18nData
  applyFilters()
  loading.value = false
}

// 应用搜索和筛选
const applyFilters = () => {
  let filtered = [...i18nList.value]
  
  // 关键词搜索
  if (searchForm.value.keyword) {
    const keyword = searchForm.value.keyword.toLowerCase()
    filtered = filtered.filter(item => {
      const keyMatch = item.key.toLowerCase().includes(keyword)
      const translationMatch = Object.values(item.translations).some(text => 
        text && text.toLowerCase().includes(keyword)
      )
      return keyMatch || translationMatch
    })
  }
  
  // 模块筛选
  if (searchForm.value.module) {
    filtered = filtered.filter(item => item.module === searchForm.value.module)
  }
  
  // 状态筛选
  if (searchForm.value.status) {
    filtered = filtered.filter(item => {
      const hasAllTranslations = supportedLanguages.value.every(lang => item.translations[lang.code])
      const hasSomeTranslations = supportedLanguages.value.some(lang => item.translations[lang.code])
      
      switch (searchForm.value.status) {
        case 'completed':
          return hasAllTranslations
        case 'incomplete':
          return hasSomeTranslations && !hasAllTranslations
        case 'missing':
          return !hasSomeTranslations
        default:
          return true
      }
    })
  }
  
  totalItems.value = filtered.length
  
  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  filteredI18nList.value = filtered.slice(start, end)
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  applyFilters()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  applyFilters()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  applyFilters()
}

// 刷新列表
const refreshI18nList = () => {
  loadI18nData()
  ElMessage.success('刷新成功')
}

// 新增翻译
const handleAddI18nKey = () => {
  i18nDialogType.value = 'add'
  i18nDialogVisible.value = true
}

// 编辑翻译
const handleEditI18nKey = (row) => {
  i18nDialogType.value = 'edit'
  i18nForm.value = {
    key: row.key,
    module: row.module,
    description: row.description || '',
    translations: { ...row.translations }
  }
  i18nDialogVisible.value = true
}

// 删除翻译
const handleDeleteI18nKey = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除翻译键 "${row.key}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 从列表中移除
    const index = i18nList.value.findIndex(item => item.key === row.key)
    if (index > -1) {
      i18nList.value.splice(index, 1)
      applyFilters()
      
      // 更新i18n配置
      updateI18nConfig()
      
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

// 保存翻译
const handleSaveI18nKey = async () => {
  if (!i18nFormRef.value) return
  
  try {
    await i18nFormRef.value.validate()
    saving.value = true
    
    if (i18nDialogType.value === 'add') {
      // 检查键名是否已存在
      if (i18nList.value.some(item => item.key === i18nForm.value.key)) {
        ElMessage.error('翻译键已存在')
        saving.value = false
        return
      }
      
      // 添加新翻译
      i18nList.value.push({
        key: i18nForm.value.key,
        module: i18nForm.value.module || 'common',
        description: i18nForm.value.description,
        translations: { ...i18nForm.value.translations }
      })
    } else {
      // 更新现有翻译
      const index = i18nList.value.findIndex(item => item.key === i18nForm.value.key)
      if (index > -1) {
        i18nList.value[index] = {
          ...i18nList.value[index],
          module: i18nForm.value.module || 'common',
          description: i18nForm.value.description,
          translations: { ...i18nForm.value.translations }
        }
      }
    }
    
    // 更新i18n配置
    updateI18nConfig()
    
    applyFilters()
    i18nDialogVisible.value = false
    ElMessage.success(i18nDialogType.value === 'add' ? '添加成功' : '更新成功')
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

// 翻译内容变更处理
const handleTranslationChange = (row, langCode, index) => {
  // 实时更新翻译内容
  updateI18nConfig()
}

// 更新i18n配置
const updateI18nConfig = () => {
  const newMessages = {}
  
  supportedLanguages.value.forEach(lang => {
    newMessages[lang.code] = {}
  })
  
  i18nList.value.forEach(item => {
    supportedLanguages.value.forEach(lang => {
      if (item.translations[lang.code]) {
        setNestedValue(newMessages[lang.code], item.key, item.translations[lang.code])
      }
    })
  })
  
  // 更新全局i18n配置
  supportedLanguages.value.forEach(lang => {
    i18n.global.setLocaleMessage(lang.code, newMessages[lang.code])
  })
}

// 设置嵌套对象值的辅助函数
const setNestedValue = (obj, path, value) => {
  const keys = path.split('.')
  let current = obj
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i]
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {}
    }
    current = current[key]
  }
  
  current[keys[keys.length - 1]] = value
}

// 重置表单
const resetI18nForm = () => {
  i18nForm.value = {
    key: '',
    module: '',
    description: '',
    translations: {}
  }
  
  // 初始化所有语言的翻译字段
  supportedLanguages.value.forEach(lang => {
    i18nForm.value.translations[lang.code] = ''
  })
  
  if (i18nFormRef.value) {
    i18nFormRef.value.clearValidate()
  }
}

// 文本提取功能
const handleExtractTexts = () => {
  extractDialogVisible.value = true
}

const performTextExtraction = async () => {
  if (!extractForm.value.selectedPage) {
    ElMessage.warning('请选择要提取文本的页面')
    return
  }
  
  extracting.value = true
  
  try {
    // 获取Vue文件内容
    const fileContent = await fetchVueFileContent(extractForm.value.selectedPage)
    
    // 提取中文文本
    const extractedResults = extractChineseTexts(fileContent, extractForm.value.selectedPage)
    
    // 格式化提取结果
    extractedTexts.value = extractedResults.map(item => ({
      key: item.key,
      text: item.text,
      type: item.type,
      context: item.context || ''
    }))
    
    if (extractedTexts.value.length > 0) {
      ElMessage.success(`成功提取 ${extractedTexts.value.length} 个中文文本项`)
    } else {
      ElMessage.warning('未在该页面中找到中文文本内容')
    }
  } catch (error) {
    console.error('文本提取失败:', error)
    ElMessage.error('文本提取失败：' + error.message)
  } finally {
    extracting.value = false
  }
}

// 批量添加提取的文本
const handleBatchAddTexts = () => {
  let addedCount = 0
  let existingCount = 0
  
  extractedTexts.value.forEach(item => {
    // 检查是否已存在
    if (!i18nList.value.some(existing => existing.key === item.key)) {
      // 从翻译键中提取模块名
      const moduleName = item.key.split('.')[0] || 'page'
      
      i18nList.value.push({
        key: item.key,
        module: moduleName,
        translations: {
          zh: item.text,
          en: '' // 默认英文为空，待翻译
        }
      })
      addedCount++
    } else {
      existingCount++
    }
  })
  
  updateI18nConfig()
  applyFilters()
  extractDialogVisible.value = false
  
  if (addedCount > 0) {
    ElMessage.success(`成功添加 ${addedCount} 个新翻译项${existingCount > 0 ? `，跳过 ${existingCount} 个已存在的项目` : ''}`)
  } else {
    ElMessage.info('所有提取的文本都已存在，未添加新项目')
  }
}

// 重置提取表单
const resetExtractForm = () => {
  extractForm.value = {
    selectedPage: ''
  }
  extractedTexts.value = []
}

// 导入导出功能
const handleImportExport = () => {
  importExportDialogVisible.value = true
}

// 导出功能
const handleExport = () => {
  if (exportLanguages.value.length === 0) {
    ElMessage.warning('请至少选择一种语言进行导出')
    return
  }
  
  exporting.value = true
  
  try {
    const exportData = {}
    
    exportLanguages.value.forEach(langCode => {
      exportData[langCode] = {}
      i18nList.value.forEach(item => {
        if (item.translations[langCode]) {
          setNestedValue(exportData[langCode], item.key, item.translations[langCode])
        }
      })
    })
    
    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = `i18n-export-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败：' + error.message)
  } finally {
    exporting.value = false
  }
}

// 文件选择处理
const handleFileChange = (file) => {
  importFile.value = file
}

// 导入功能
const handleImport = () => {
  if (!importFile.value) {
    ElMessage.warning('请选择要导入的文件')
    return
  }
  
  importing.value = true
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = JSON.parse(e.target.result)
      
      // 解析导入的数据
      let importedCount = 0
      Object.keys(data).forEach(langCode => {
        if (supportedLanguages.value.some(lang => lang.code === langCode)) {
          const parseImportData = (obj, prefix = '') => {
            Object.keys(obj).forEach(key => {
              const fullKey = prefix ? `${prefix}.${key}` : key
              if (typeof obj[key] === 'object' && obj[key] !== null) {
                parseImportData(obj[key], fullKey)
              } else {
                // 查找或创建翻译项
                let existingItem = i18nList.value.find(item => item.key === fullKey)
                if (!existingItem) {
                  existingItem = {
                    key: fullKey,
                    module: fullKey.split('.')[0] || 'imported',
                    translations: {}
                  }
                  i18nList.value.push(existingItem)
                  importedCount++
                }
                existingItem.translations[langCode] = obj[key]
              }
            })
          }
          
          parseImportData(data[langCode])
        }
      })
      
      updateI18nConfig()
      applyFilters()
      importExportDialogVisible.value = false
      
      ElMessage.success(`成功导入 ${importedCount} 个翻译项`)
    } catch (error) {
      ElMessage.error('导入失败：文件格式不正确')
    } finally {
      importing.value = false
    }
  }
  
  reader.readAsText(importFile.value.raw)
}

// 获取类型标签颜色
const getTypeTagColor = (type) => {
  const colorMap = {
    'title': 'primary',
    'button': 'success',
    'label': 'info',
    'placeholder': 'warning',
    'message': 'danger',
    'confirm': 'danger',
    'error': 'danger',
    'success': 'success',
    'warning': 'warning',
    'text': 'default',
    'script': 'info',
    'tag': 'primary',
    'component': 'primary'
  }
  return colorMap[type] || 'default'
}

// 获取类型显示名称
const getTypeDisplayName = (type) => {
  const nameMap = {
    'title': '标题',
    'button': '按钮',
    'label': '标签',
    'placeholder': '占位符',
    'message': '消息',
    'confirm': '确认',
    'error': '错误',
    'success': '成功',
    'warning': '警告',
    'text': '文本',
    'script': '脚本',
    'tag': '标签',
    'component': '组件'
  }
  return nameMap[type] || type
}

// 初始化表单数据
resetI18nForm()
</script>

<style scoped>
.i18n-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  color: #303133;
}

.page-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  text-align: center;
}

.stats-content {
  padding: 10px;
}

.stats-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  line-height: 1;
}

.stats-label {
  margin-top: 8px;
  color: #606266;
  font-size: 14px;
}

.search-filters {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.search-form {
  margin: 0;
}

.key-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.key-text {
  font-family: monospace;
  font-size: 12px;
  color: #606266;
}

.language-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.translation-cell {
  position: relative;
}

.translation-status {
  margin-top: 4px;
  text-align: right;
}

.missing-translation {
  border-color: #f56c6c !important;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.language-inputs {
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
  margin-top: 20px;
}

.language-inputs h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.extract-container {
  max-height: 600px;
  overflow-y: auto;
}

.extract-actions {
  margin: 20px 0;
  text-align: center;
}

.extract-results {
  margin-top: 20px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.export-container,
.import-container {
  padding: 20px 0;
}

.import-preview {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
}
</style> 