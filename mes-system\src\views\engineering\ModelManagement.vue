<template>
  <div class="material-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>{{ $t('engineering.materialManagement') }}</span>
          <div class="header-actions">
            <el-button 
              type="primary" 
              size="small" 
              @click="refreshCategories"
              :loading="categoriesLoading"
            >
              {{ $t('common.refresh') }}
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 加载状态 -->
      <div v-if="categoriesLoading" class="loading-container">
        <el-skeleton :rows="3" animated />
        <div class="loading-text">{{ $t('engineering.loadingCategories') }}</div>
      </div>
      
      <!-- 动态生成的标签页 -->
      <el-tabs 
        v-else-if="materialCategories.length > 0"
        v-model="activeTab" 
        type="border-card" 
        class="material-tabs"
        @tab-click="handleTabClick"
      >
        <el-tab-pane 
          v-for="category in materialCategories"
          :key="category.code"
          :label="$t(getCategoryTitle(category.code))"
          :name="category.code"
        >
          <template #label>
            <span class="tab-label">
              <span class="tab-icon">{{ getCategoryIcon(category.code) }}</span>
              <span class="tab-text">{{ $t(getCategoryTitle(category.code)) }}</span>
              <el-tooltip v-if="category.description" :content="category.description" placement="top">
                <el-icon class="tab-help"><QuestionFilled /></el-icon>
              </el-tooltip>
            </span>
          </template>
          
          <MaterialModelTab 
            :key="`material-tab-${category.code}-${locale}`"
            :model-type="category.code" 
            :api="getMaterialCategoryApi(category.code)"
            :categories="getCategoriesForType(category.code)"
            :category-config="category"
          />
        </el-tab-pane>
      </el-tabs>
      
      <!-- 无分类数据时的提示 -->
      <el-empty 
        v-else
        :description="$t('engineering.noCategoriesFound')"
      >
        <template #image>
          <el-icon size="60"><FolderOpened /></el-icon>
        </template>
        <el-button type="primary" @click="refreshCategories">
          {{ $t('engineering.retryLoad') }}
        </el-button>
      </el-empty>
    </el-card>
    
    <!-- 配置数据字典提示对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      :title="$t('engineering.configMaterialCategories')"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="config-hint">
        <el-alert
          :title="$t('engineering.configHintTitle')"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>{{ $t('engineering.configHintContent') }}</p>
            <ol>
              <li>{{ $t('engineering.configStep1') }}</li>
              <li>{{ $t('engineering.configStep2') }}</li>
              <li>{{ $t('engineering.configStep3') }}</li>
            </ol>
          </template>
        </el-alert>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="configDialogVisible = false">
            {{ $t('common.close') }}
          </el-button>
          <el-button type="primary" @click="goToDataDictionary">
            {{ $t('engineering.goToConfig') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElNotification } from 'element-plus'
import MaterialModelTab from '@/components/MaterialModelTab.vue'
import { 
  getMaterialCategories, 
  getDefaultMaterialCategories,
  getMaterialCategoryApi 
} from '@/api/dataDictionary'

const router = useRouter()
const { t, locale } = useI18n()

// 响应式数据
const activeTab = ref('')
const materialCategories = ref([])
const categoriesLoading = ref(false)
const configDialogVisible = ref(false)

// 计算属性
const defaultActiveTab = computed(() => {
  return materialCategories.value.length > 0 ? materialCategories.value[0].code : ''
})

// 获取分类图标
const getCategoryIcon = (categoryCode) => {
  const iconMap = {
    'REF_FG': '🏭',        // 成品型号
    'REF_SUB': '⚙️',       // 组件型号
    'REF_COMP': '🔩',      // 零件型号
    'REF_ACC': '📋',       // 辅料包材型号
    'REF_MET': '🔧',       // 塑胶五金型号
    'REF_ADMIN': '📎',     // 行政物料 - 使用回形针图标，更符合办公用品特征
    'REF_VIRTUAL': '✨',   // 虚拟物料型号 - 使用闪光图标，体现虚拟特性
    // 兼容旧编码（回退）
    'product': '🏭',
    'component': '⚙️', 
    'part': '🔩',
    'auxiliary': '📋',
    'hardwarePlastic': '🔧',
    'rawMaterial': '🧱',
    'chemical': '⚗️',
    'electronic': '💻',
    'mechanical': '🔧',
    'packaging': '📦'
  }
  return iconMap[categoryCode] || '📁'
}

// 获取特定类型的分类数据
const getCategoriesForType = (categoryCode) => {
  // 这里可以根据需要为每个物料类型返回相应的子分类
  // 现在先返回空数组，后续可以扩展
  return []
}

// 根据分类编码获取国际化标题
const getCategoryTitle = (categoryCode) => {
  const titleMap = {
    'REF_FG': 'engineering.finishedProductModel',
    'REF_SUB': 'engineering.subAssemblyModel',
    'REF_COMP': 'engineering.componentPartModel',
    'REF_ACC': 'engineering.auxiliaryPackagingModel',
    'REF_MET': 'engineering.plasticMetalModel',
    'REF_ADMIN': 'engineering.administrativeMaterial',
    'REF_VIRTUAL': 'engineering.virtualMaterial',
    // 兼容旧编码
    'product': 'engineering.productModel',
    'component': 'engineering.componentModel',
    'part': 'engineering.partModel',
    'auxiliary': 'engineering.auxiliaryModel',
    'hardwarePlastic': 'engineering.hardwarePlasticModel'
  }
  
  return titleMap[categoryCode] || categoryCode
}

// 处理标签页点击事件
const handleTabClick = (tab) => {
  console.info('标签页点击:', tab.props.name)
  if (tab.props.name && tab.props.name !== activeTab.value) {
    activeTab.value = tab.props.name
  }
}

// 监听 activeTab 变化
watch(activeTab, (newTab, oldTab) => {
  if (newTab && newTab !== oldTab) {
    console.info('活动标签页变化:', oldTab, '->', newTab)
    // 可以在这里添加额外的处理逻辑，比如记录用户操作等
  }
}, { immediate: false })

// 监听语言变化（仅用于调试）
watch(locale, (newLocale, oldLocale) => {
  if (newLocale !== oldLocale) {
    console.info('语言切换:', oldLocale, '->', newLocale)
  }
}, { immediate: false })

// 加载物料分类
const loadMaterialCategories = async () => {
  // 防止重复加载
  if (categoriesLoading.value) {
    return
  }
  
  categoriesLoading.value = true
  try {
    const categories = await getMaterialCategories()
    
    if (categories && categories.length > 0) {
      materialCategories.value = categories
      
      // 设置默认激活的标签页
      if (!activeTab.value || !categories.find(cat => cat.code === activeTab.value)) {
        activeTab.value = categories[0].code
        console.info('设置默认活动标签页:', categories[0].code)
      }
      
      // 判断是否从后端API成功加载（如果数据长度不等于默认分类数量，说明是从API获取）
      const defaultCategoriesLength = 6 // 默认分类有6个
      const isFromAPI = categories.length !== defaultCategoriesLength || 
                       !categories.every(cat => ['REF_FG', 'REF_SUB', 'REF_COMP', 'REF_ACC', 'REF_MET', 'REF_ADMIN'].includes(cat.code))
      
      if (isFromAPI) {
        ElNotification({
          title: t('common.success'),
          message: t('engineering.categoriesLoadedSuccess', { count: categories.length }),
          type: 'success',
          duration: 3000
        })
      } else {
        console.info('使用默认物料分类')
      }
    } else {
      // 使用默认分类
      materialCategories.value = getDefaultMaterialCategories()
      activeTab.value = materialCategories.value[0].code
      console.info('使用默认物料分类')
    }
  } catch (error) {
    console.error('加载物料分类失败:', error)
    
    // 出错时使用默认分类
    materialCategories.value = getDefaultMaterialCategories()
    activeTab.value = materialCategories.value[0].code
    
    // 只在确实发生严重错误时才显示错误消息
    if (error.response && error.response.status >= 500) {
      ElMessage.error(t('engineering.loadCategoriesError'))
    }
  } finally {
    categoriesLoading.value = false
  }
}

// 刷新分类数据
const refreshCategories = async () => {
  await loadMaterialCategories()
}

// 跳转到数据字典配置页面
const goToDataDictionary = () => {
  configDialogVisible.value = false
  router.push('/home/<USER>/dictionary')
}

// 组件挂载后初始化
onMounted(async () => {
  try {
    // 延迟一点再加载，避免与其他组件的初始化冲突
    await new Promise(resolve => setTimeout(resolve, 200))
    await loadMaterialCategories()
    
    // 确保标签页正确渲染
    await nextTick(() => {
      if (materialCategories.value.length > 0 && !activeTab.value) {
        activeTab.value = materialCategories.value[0].code
        console.info('强制设置活动标签页:', materialCategories.value[0].code)
      }
    })
  } catch (error) {
    console.error('组件初始化失败:', error)
  }
})

// 组件销毁时清理
onUnmounted(() => {
  categoriesLoading.value = false
})
</script>

<style scoped>
.material-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.loading-container {
  text-align: center;
  padding: 40px 20px;
}

.loading-text {
  margin-top: 16px;
  color: #909399;
  font-size: 14px;
}

.material-tabs {
  margin-top: 20px;
}

.material-tabs :deep(.el-tabs__content) {
  padding: 0;
}

.material-tabs :deep(.el-tab-pane) {
  padding: 20px;
}

.material-tabs :deep(.el-tabs__item) {
  font-weight: bold;
  font-size: 15px;
  position: relative;
  transition: all 0.3s ease;
}

.material-tabs :deep(.el-tabs__item.is-active) {
  color: #409EFF;
  background-color: #f0f9ff;
}

.material-tabs :deep(.el-tabs__item:hover) {
  color: #409EFF;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-icon {
  font-size: 16px;
}

.tab-text {
  font-size: 14px;
}

.tab-help {
  opacity: 0.6;
  font-size: 12px;
  transition: opacity 0.3s;
}

.tab-help:hover {
  opacity: 1;
}

.config-hint {
  margin-bottom: 20px;
}

.config-hint :deep(.el-alert__content) {
  text-align: left;
}

.config-hint ol {
  margin-top: 10px;
  padding-left: 20px;
}

.config-hint li {
  margin-bottom: 8px;
  line-height: 1.5;
}

:deep(.el-card) {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-empty) {
  padding: 60px 20px;
}

:deep(.el-skeleton) {
  margin-bottom: 20px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .material-management {
    padding: 10px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .material-tabs :deep(.el-tabs__item) {
    font-size: 13px;
  }
  
  .tab-icon {
    font-size: 14px;
  }
  
  .tab-text {
    font-size: 12px;
  }
}
</style> 