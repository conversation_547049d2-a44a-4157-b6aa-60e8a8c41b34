﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace mes_system_server.Migrations
{
    /// <inheritdoc />
    public partial class AddDataDictionary : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DataDictionaries",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Type = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Sort = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<bool>(type: "bit", nullable: false),
                    Remark = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreateBy = table.Column<int>(type: "int", nullable: true),
                    UpdateBy = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataDictionaries", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DataDictionaryItems",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DictId = table.Column<int>(type: "int", nullable: false),
                    Label = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Value = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Sort = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<bool>(type: "bit", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    ExtProperty1 = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ExtProperty2 = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ExtProperty3 = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreateBy = table.Column<int>(type: "int", nullable: true),
                    UpdateBy = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataDictionaryItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DataDictionaryItems_DataDictionaries_DictId",
                        column: x => x.DictId,
                        principalTable: "DataDictionaries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2561), new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2562) });

            migrationBuilder.InsertData(
                table: "DataDictionaries",
                columns: new[] { "Id", "Code", "CreateBy", "CreateTime", "Description", "Name", "Remark", "Sort", "Status", "Type", "UpdateBy", "UpdateTime" },
                values: new object[,]
                {
                    { 1, "USER_STATUS", null, new DateTime(2024, 1, 1, 8, 0, 0, 0, DateTimeKind.Unspecified), "用户账号状态配置", "用户状态", "系统核心配置", 1, true, "system", null, null },
                    { 2, "DEPT_TYPE", null, new DateTime(2024, 1, 1, 9, 0, 0, 0, DateTimeKind.Unspecified), "部门分类配置", "部门类型", "业务配置", 2, true, "business", null, null },
                    { 3, "GENDER", null, new DateTime(2024, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "性别分类", "性别", "基础数据", 3, true, "system", null, null },
                    { 4, "EDUCATION", null, new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified), "学历分类", "学历", "基础数据", 4, true, "system", null, null }
                });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2576));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2585));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2591));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2595));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2599));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2604));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2608));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2612));

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2656), new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2657) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2667), new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2667) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2676), new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2676) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2684), new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2684) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2693), new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2694) });

            migrationBuilder.InsertData(
                table: "DataDictionaryItems",
                columns: new[] { "Id", "CreateBy", "CreateTime", "Description", "DictId", "ExtProperty1", "ExtProperty2", "ExtProperty3", "Label", "Sort", "Status", "UpdateBy", "UpdateTime", "Value" },
                values: new object[,]
                {
                    { 1, null, new DateTime(2024, 1, 1, 8, 0, 0, 0, DateTimeKind.Unspecified), "用户正常状态", 1, null, null, null, "正常", 1, true, null, null, "1" },
                    { 2, null, new DateTime(2024, 1, 1, 8, 0, 0, 0, DateTimeKind.Unspecified), "用户被禁用", 1, null, null, null, "禁用", 2, true, null, null, "0" },
                    { 3, null, new DateTime(2024, 1, 1, 8, 0, 0, 0, DateTimeKind.Unspecified), "用户被锁定", 1, null, null, null, "锁定", 3, true, null, null, "2" },
                    { 4, null, new DateTime(2024, 1, 1, 9, 0, 0, 0, DateTimeKind.Unspecified), "技术研发部门", 2, null, null, null, "技术部门", 1, true, null, null, "tech" },
                    { 5, null, new DateTime(2024, 1, 1, 9, 0, 0, 0, DateTimeKind.Unspecified), "销售业务部门", 2, null, null, null, "销售部门", 2, true, null, null, "sales" },
                    { 6, null, new DateTime(2024, 1, 1, 9, 0, 0, 0, DateTimeKind.Unspecified), "人力资源部门", 2, null, null, null, "人事部门", 3, true, null, null, "hr" },
                    { 7, null, new DateTime(2024, 1, 1, 9, 0, 0, 0, DateTimeKind.Unspecified), "财务管理部门", 2, null, null, null, "财务部门", 4, true, null, null, "finance" },
                    { 8, null, new DateTime(2024, 1, 1, 9, 0, 0, 0, DateTimeKind.Unspecified), "行政管理部门", 2, null, null, null, "行政部门", 5, true, null, null, "admin" },
                    { 9, null, new DateTime(2024, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "男性", 3, null, null, null, "男", 1, true, null, null, "male" },
                    { 10, null, new DateTime(2024, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified), "女性", 3, null, null, null, "女", 2, true, null, null, "female" },
                    { 11, null, new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified), "小学学历", 4, null, null, null, "小学", 1, true, null, null, "primary" },
                    { 12, null, new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified), "初中学历", 4, null, null, null, "初中", 2, true, null, null, "junior" },
                    { 13, null, new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified), "高中学历", 4, null, null, null, "高中", 3, true, null, null, "senior" },
                    { 14, null, new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified), "大专学历", 4, null, null, null, "大专", 4, true, null, null, "college" },
                    { 15, null, new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified), "本科学历", 4, null, null, null, "本科", 5, true, null, null, "bachelor" },
                    { 16, null, new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified), "硕士学历", 4, null, null, null, "硕士", 6, true, null, null, "master" },
                    { 17, null, new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified), "博士学历", 4, null, null, null, "博士", 7, true, null, null, "doctor" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_DataDictionaries_Code",
                table: "DataDictionaries",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DataDictionaries_Status",
                table: "DataDictionaries",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_DataDictionaries_Type_Sort",
                table: "DataDictionaries",
                columns: new[] { "Type", "Sort" });

            migrationBuilder.CreateIndex(
                name: "IX_DataDictionaryItems_DictId_Sort",
                table: "DataDictionaryItems",
                columns: new[] { "DictId", "Sort" });

            migrationBuilder.CreateIndex(
                name: "IX_DataDictionaryItems_DictId_Status",
                table: "DataDictionaryItems",
                columns: new[] { "DictId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_DataDictionaryItems_DictId_Value",
                table: "DataDictionaryItems",
                columns: new[] { "DictId", "Value" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DataDictionaryItems");

            migrationBuilder.DropTable(
                name: "DataDictionaries");

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8027), new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8028) });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8052));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8067));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8075));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8082));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8089));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8096));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8103));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8110));

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8175), new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8176) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8193), new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8195) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8209), new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8210) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8224), new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8225) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8239), new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8241) });
        }
    }
}
