﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace mes_system_server.Migrations
{
    /// <inheritdoc />
    public partial class AddWorkCenterTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ProductionLines",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductionLines", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "WorkCenters",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    ProductionLineId = table.Column<int>(type: "int", nullable: false),
                    SortOrder = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkCenters", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkCenters_ProductionLines_ProductionLineId",
                        column: x => x.ProductionLineId,
                        principalTable: "ProductionLines",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Equipment",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    WorkCenterId = table.Column<int>(type: "int", nullable: false),
                    SortOrder = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Equipment", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Equipment_WorkCenters_WorkCenterId",
                        column: x => x.WorkCenterId,
                        principalTable: "WorkCenters",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8027), new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8028) });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8052));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8067));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8075));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8082));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8089));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8096));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8103));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8110));

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8175), new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8176) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8193), new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8195) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8209), new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8210) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8224), new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8225) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8239), new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8241) });

            migrationBuilder.CreateIndex(
                name: "IX_WisPDFBookmarks_CreateTime_Status",
                table: "WisPDFBookmarks",
                columns: new[] { "CreateTime", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_WisPDFBookmarks_FileName_Status_PageNumber",
                table: "WisPDFBookmarks",
                columns: new[] { "FileName", "Status", "PageNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_WisPDFBookmarks_OriginalBookmarkName_Status",
                table: "WisPDFBookmarks",
                columns: new[] { "OriginalBookmarkName", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_WisPDFBookmarks_Status",
                table: "WisPDFBookmarks",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Equipment_WorkCenterId_Code",
                table: "Equipment",
                columns: new[] { "WorkCenterId", "Code" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ProductionLines_Code",
                table: "ProductionLines",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_WorkCenters_ProductionLineId_Code",
                table: "WorkCenters",
                columns: new[] { "ProductionLineId", "Code" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Equipment");

            migrationBuilder.DropTable(
                name: "WorkCenters");

            migrationBuilder.DropTable(
                name: "ProductionLines");

            migrationBuilder.DropIndex(
                name: "IX_WisPDFBookmarks_CreateTime_Status",
                table: "WisPDFBookmarks");

            migrationBuilder.DropIndex(
                name: "IX_WisPDFBookmarks_FileName_Status_PageNumber",
                table: "WisPDFBookmarks");

            migrationBuilder.DropIndex(
                name: "IX_WisPDFBookmarks_OriginalBookmarkName_Status",
                table: "WisPDFBookmarks");

            migrationBuilder.DropIndex(
                name: "IX_WisPDFBookmarks_Status",
                table: "WisPDFBookmarks");

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3573), new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3573) });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3604));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3616));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3621));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3625));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3630));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3634));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3639));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3643));

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3698), new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3698) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3708), new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3709) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3717), new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3717) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3726), new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3726) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3734), new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3735) });
        }
    }
}
