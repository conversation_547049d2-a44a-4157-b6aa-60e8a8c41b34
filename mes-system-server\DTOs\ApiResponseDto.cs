namespace mes_system_server.DTOs
{
    public class ApiResponseDto<T>
    {
        public int Code { get; set; }
        public string Message { get; set; }
        public T? Data { get; set; }

        public ApiResponseDto(int code, string message, T? data = default)
        {
            Code = code;
            Message = message;
            Data = data;
        }

        public static ApiResponseDto<T> Success(T? data = default, string message = "success")
        {
            return new ApiResponseDto<T>(200, message, data);
        }

        public static ApiResponseDto<T> Fail(string message = "操作失败", int code = 400)
        {
            return new ApiResponseDto<T>(code, message);
        }
    }
} 