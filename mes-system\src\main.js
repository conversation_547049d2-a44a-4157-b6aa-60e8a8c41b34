import { createApp } from 'vue'
import './style.css'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'
import router from './router'
import i18n from './i18n'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(ElementPlus)
app.use(router)
app.use(i18n)

// 错误处理
app.config.errorHandler = (err, vm, info) => {
  console.warn('Vue组件错误已捕获:', err.message || err)
  console.info('错误详情:', info)
  // 防止错误向上传播导致页面崩溃
}

// 处理未捕获的Promise错误
window.addEventListener('unhandledrejection', (event) => {
  console.info('未处理的Promise已捕获:', event.reason?.message || event.reason)
  event.preventDefault() // 阻止默认的错误输出
})

// 挂载应用
app.mount('#app')
