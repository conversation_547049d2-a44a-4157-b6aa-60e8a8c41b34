﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace mes_system_server.Migrations
{
    /// <inheritdoc />
    public partial class AddCompanyEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Companies",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    CreditCode = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    LegalRepresentative = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    RegisteredCapital = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Address = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Phone = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    CompanyType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    RegistrationDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Companies", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "Companies",
                columns: new[] { "Id", "Address", "CompanyType", "CreatedAt", "CreditCode", "Description", "Email", "LegalRepresentative", "Name", "Phone", "RegisteredCapital", "RegistrationDate", "UpdatedAt" },
                values: new object[] { 1, "上海市浦东新区张江高科技园区", "LLC", new DateTime(2025, 4, 15, 12, 47, 47, 848, DateTimeKind.Utc).AddTicks(5541), "91310000XXXXXXXXXX", "丰信科技是一家专注于智能制造领域的高新技术企业，致力于为工业企业提供先进的智能制造解决方案和服务。", "<EMAIL>", "张三", "丰信科技有限公司", "021-12345678", "1000万元", new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2025, 4, 15, 12, 47, 47, 848, DateTimeKind.Utc).AddTicks(5541) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Companies");
        }
    }
}
