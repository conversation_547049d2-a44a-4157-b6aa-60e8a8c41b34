<template>
  <div class="language-switcher">
    <el-dropdown @command="handleLanguageChange" trigger="click">
      <div class="language-button">
        <span class="language-icon">{{ currentLanguageInfo.flag }}</span>
        <span class="language-text">{{ currentLanguageInfo.name }}</span>
        <el-icon class="arrow-icon">
          <ArrowDown />
        </el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item 
            v-for="lang in supportedLanguages" 
            :key="lang.code"
            :command="lang.code"
            :class="{ 'is-active': currentLanguage === lang.code }"
          >
            <div class="language-option">
              <span class="language-flag">{{ lang.flag }}</span>
              <span class="language-name">{{ lang.name }}</span>
              <el-icon v-if="currentLanguage === lang.code" class="check-icon">
                <Check />
              </el-icon>
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { ArrowDown, Check } from '@element-plus/icons-vue'

const { locale } = useI18n()

// 支持的语言列表
const supportedLanguages = ref([
  { 
    code: 'zh', 
    name: '中文', 
    flag: '🇨🇳',
    fullName: '简体中文'
  },
  { 
    code: 'en', 
    name: 'English', 
    flag: '🇺🇸',
    fullName: 'English (US)'
  }
])

// 当前语言
const currentLanguage = ref(locale.value)

// 当前语言信息
const currentLanguageInfo = computed(() => {
  return supportedLanguages.value.find(lang => lang.code === currentLanguage.value) || supportedLanguages.value[0]
})

// 切换语言处理
const handleLanguageChange = (langCode) => {
  if (langCode === currentLanguage.value) return
  
  try {
    // 更新i18n locale
    locale.value = langCode
    currentLanguage.value = langCode
    
    // 保存到localStorage
    localStorage.setItem('language', langCode)
    
    // 显示切换成功消息
    const langInfo = supportedLanguages.value.find(lang => lang.code === langCode)
    ElMessage.success(`语言已切换为 ${langInfo?.fullName || langCode}`)
    
    // 发出语言变更事件
    window.dispatchEvent(new CustomEvent('languageChanged', { 
      detail: { 
        language: langCode,
        languageInfo: langInfo
      } 
    }))
  } catch (error) {
    console.error('语言切换失败:', error)
    ElMessage.error('语言切换失败')
  }
}

// 初始化语言设置
onMounted(() => {
  const savedLanguage = localStorage.getItem('language')
  if (savedLanguage && supportedLanguages.value.some(lang => lang.code === savedLanguage)) {
    currentLanguage.value = savedLanguage
    locale.value = savedLanguage
  }
})
</script>

<style scoped>
.language-switcher {
  position: relative;
}

.language-button {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  color: #ffffff;
  font-size: 14px;
  min-width: 80px;
}

.language-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.language-icon {
  margin-right: 6px;
  font-size: 16px;
}

.language-text {
  margin-right: 6px;
  font-weight: 500;
}

.arrow-icon {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.language-button:hover .arrow-icon {
  transform: rotate(180deg);
}

.language-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-width: 120px;
}

.language-flag {
  margin-right: 8px;
  font-size: 16px;
}

.language-name {
  flex: 1;
  font-size: 14px;
}

.check-icon {
  color: #409eff;
  font-size: 14px;
}

.is-active {
  background-color: #f0f9ff;
}

.is-active .language-option {
  color: #409eff;
  font-weight: 500;
}

/* 适配深色主题的登录页面 */
.login-page .language-button {
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.login-page .language-button:hover {
  background: rgba(255, 255, 255, 1);
  border-color: #409eff;
}

/* 适配普通页面 */
.normal-page .language-button {
  background: #ffffff;
  color: #333;
  border: 1px solid #dcdfe6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.normal-page .language-button:hover {
  border-color: #409eff;
  color: #409eff;
}
</style> 