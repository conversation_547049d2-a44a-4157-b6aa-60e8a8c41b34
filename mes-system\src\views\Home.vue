<template>
  <div class="home-container">
    <el-container>
      <el-aside :width="isCollapse ? '64px' : '200px'" :class="{ 'is-collapse': isCollapse }">
        <div class="logo-container">
          <img src="../assets/logo.svg" alt="logo" class="logo" />
          <h1 class="logo-text" v-show="!isCollapse">丰信科技</h1>
        </div>
        
        <el-menu
          :default-active="activeMenu"
          class="el-menu-vertical"
          :collapse="isCollapse"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          router
        >
          <!-- 动态生成菜单项 -->
          <template v-for="menu in navigationMenus" :key="menu.id">
            <!-- 单层菜单项 -->
            <el-menu-item 
              v-if="menu.type === 'menu-item'" 
              :index="menu.index"
            >
              <el-icon><component :is="menu.icon" /></el-icon>
              <template #title>{{ getMenuDisplayName(menu) }}</template>
            </el-menu-item>
            
            <!-- 子菜单 -->
            <el-sub-menu 
              v-else-if="menu.type === 'sub-menu'" 
              :index="menu.index"
            >
              <template #title>
                <el-icon><component :is="menu.icon" /></el-icon>
                <span>{{ getMenuDisplayName(menu) }}</span>
              </template>
              
              <el-menu-item 
                v-for="child in menu.children" 
                :key="child.id"
                :index="child.index"
              >
                <el-icon><component :is="child.icon" /></el-icon>
                <span>{{ getMenuDisplayName(child) }}</span>
              </el-menu-item>
            </el-sub-menu>
          </template>
        </el-menu>
      </el-aside>
      
      <el-container :class="{ 'is-collapsed': isCollapse }">
        <el-header height="60px">
          <div class="header-left">
            <el-icon class="fold-icon" @click="toggleSidebar">
              <Fold v-if="!isCollapse" />
              <Expand v-else />
            </el-icon>
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item v-if="currentRoute.meta?.title">
                {{ currentRoute.meta.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="header-right">
            <el-tooltip content="全屏" placement="bottom">
              <el-icon class="action-icon" @click="toggleFullScreen">
                <FullScreen v-if="!isFullscreen" />
                <Aim v-else />
              </el-icon>
            </el-tooltip>
            <el-badge :is-dot="hasNotification" class="notification-badge">
              <el-icon class="action-icon" @click="showNotifications">
                <Bell />
              </el-icon>
            </el-badge>
            <!-- 语言切换按钮 -->
            <div class="language-switcher-container">
              <LanguageSwitcher />
            </div>
            <el-dropdown @command="handleCommand" trigger="click">
              <div class="user-info">
                <el-avatar :size="32" src="../assets/avatar.jpg" />
                <span class="username">{{ currentUserInfo?.username || '用户' }}</span>
                <el-icon><CaretBottom /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>个人信息
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><Setting /></el-icon>系统设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <el-main>
          <router-view v-if="route.path !== '/home'" v-loading="loading" />
          <Dashboard v-else v-loading="loading" />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  Monitor,
  Setting,
  Files,
  Operation,
  List,
  Odometer,
  Calendar,
  Document,
  VideoCamera,
  Aim,
  Check,
  DataAnalysis,
  Box,
  Goods,
  Collection,
  Tools,
  Platform,
  Service,
  UserFilled,
  User,
  Money,
  Avatar,
  Lock,
  Fold,
  Expand,
  FullScreen,
  Bell,
  CaretBottom,
  SwitchButton,
  OfficeBuilding,
  Notebook,
  Grid,
  Menu
} from '@element-plus/icons-vue'
import Dashboard from '../components/Dashboard.vue'
import LanguageSwitcher from '../components/LanguageSwitcher.vue'
import { getMenuTree } from '@/api/menu'
import { getCurrentUser, getCurrentUserMenuPermissions, filterMenusByPermissions, logout } from '@/utils/permission'
import eventBus from '@/utils/eventBus'
import { useI18n } from 'vue-i18n'
import { getTranslatedMenuName } from '@/utils/menuTranslations'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const isCollapse = ref(false)
const isFullscreen = ref(false)
const hasNotification = ref(true)
const loading = ref(false)

// 获取导航菜单配置
const navigationMenus = ref([])

// 当前用户信息
const currentUserInfo = ref(null)

const activeMenu = computed(() => route.path)
const currentRoute = computed(() => route)

// 获取翻译后的菜单名称
const getMenuDisplayName = (menu) => {
  return getTranslatedMenuName(t, menu.name, menu.path)
}

// 图标映射表
const iconMap = {
  'Monitor': Monitor,
  'Setting': Setting,
  'Files': Files,
  'Operation': Operation,
  'List': List,
  'Odometer': Odometer,
  'Calendar': Calendar,
  'Document': Document,
  'VideoCamera': VideoCamera,
  'Aim': Aim,
  'Check': Check,
  'DataAnalysis': DataAnalysis,
  'Box': Box,
  'Goods': Goods,
  'Collection': Collection,
  'Tools': Tools,
  'Platform': Platform,
  'Service': Service,
  'UserFilled': UserFilled,
  'User': User,
  'Money': Money,
  'Avatar': Avatar,
  'Lock': Lock,
  'OfficeBuilding': OfficeBuilding,
  'Notebook': Notebook,
  'Grid': Grid,
  'Menu': Menu
}

// 转换数据库菜单数据为导航菜单格式
const convertMenuData = (menus) => {
  return menus.map(menu => ({
    id: menu.menuId,
    name: menu.name,
    path: menu.path,
    icon: iconMap[menu.icon] || Setting, // 如果找不到图标，使用默认图标
    index: menu.path || menu.menuId, // 使用路径作为index，如果没有路径使用menuId
    type: menu.type,
    sortOrder: menu.sortOrder,
    status: menu.status,
    children: menu.children && menu.children.length > 0 ? convertMenuData(menu.children) : undefined
  }))
}

// 初始化用户信息
const initUserInfo = () => {
  const userInfo = getCurrentUser()
  if (userInfo) {
    currentUserInfo.value = userInfo
  }
}

// 初始化导航菜单 - 从数据库获取
const initNavigationMenu = async () => {
  try {
    loading.value = true
    
    // 获取用户信息
    const userInfo = getCurrentUser()
    if (!userInfo || !userInfo.roleId) {
      console.warn('用户信息不完整，无法获取菜单权限')
      navigationMenus.value = []
      return
    }
    
    // 获取菜单树和用户角色菜单权限
    const [menuResponse, userMenuIds] = await Promise.all([
      getMenuTree(),
      getCurrentUserMenuPermissions()
    ])
    
    if (menuResponse.data.code !== 200) {
      ElMessage.error('获取导航菜单失败: ' + menuResponse.data.message)
      navigationMenus.value = []
      return
    }
    
    const menuData = menuResponse.data.data || []
    
    // 只显示启用的菜单
    const enabledMenus = menuData.filter(menu => menu.status)
    
    // 转换菜单数据格式
    const convertedMenus = convertMenuData(enabledMenus)
    
    // 根据用户权限过滤菜单
    navigationMenus.value = filterMenusByPermissions(convertedMenus, userMenuIds)
    
  } catch (error) {
    console.error('获取导航菜单失败:', error)
    ElMessage.error('获取导航菜单失败，请检查网络连接')
    navigationMenus.value = []
  } finally {
    loading.value = false
  }
}

const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

const toggleFullScreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

const showNotifications = () => {
  // 实现通知功能
  hasNotification.value = false
}

const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/system/profile')
      break
    case 'settings':
      router.push('/system/settings')
      break
    case 'logout':
      logout()
      router.push('/login')
      break
  }
}

// 处理菜单点击
const handleMenuClick = (menu) => {
  if (menu.children) {
    // 如果有子菜单，展开/收起子菜单
    menu.isOpen = !menu.isOpen
  } else {
    // 如果是叶子节点，进行路由跳转
    router.push(menu.path)
    
    // 如果是员工管理页面，自动选中第一个员工
    if (menu.path === '/hr/employees') {
      // 等待组件挂载完成后执行
      nextTick(() => {
        const employeeManagement = document.querySelector('.employee-management')
        if (employeeManagement) {
          // 触发第一个员工的点击事件
          const firstEmployee = employeeManagement.querySelector('.employee-item')
          if (firstEmployee) {
            firstEmployee.click()
          }
        }
      })
    }
  }
}

// 组件挂载后初始化
onMounted(async () => {
  // 初始化用户信息
  initUserInfo()
  
  // 初始化导航菜单
  await initNavigationMenu()
  
  // 监听菜单更新事件
  eventBus.on('menuUpdated', () => {
    console.log('收到菜单更新事件，重新加载导航菜单')
    initNavigationMenu()
  })
  
  // 监听语言变化事件，强制更新菜单显示
  window.addEventListener('languageChanged', () => {
    // 强制重新渲染，确保菜单名称使用新语言
    navigationMenus.value = [...navigationMenus.value]
  })
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  eventBus.off('menuUpdated')
  window.removeEventListener('languageChanged', () => {
    navigationMenus.value = [...navigationMenus.value]
  })
})
</script>

<style scoped>
.home-container {
  height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
}

.el-container {
  height: 100%;
  width: 100%;
}

.el-aside {
  background-color: #304156;
  transition: all 0.3s;
  overflow-x: hidden;
  z-index: 1000;
}

.el-aside.is-collapse {
  width: 64px !important;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  background-color: #2b3648;
  overflow: hidden;
  transition: all 0.3s;
}

.logo {
  width: 32px;
  height: 32px;
  margin-right: 8px;
  transition: all 0.3s;
}

.logo-text {
  color: #fff;
  font-size: 18px;
  margin: 0;
  white-space: nowrap;
  opacity: 1;
  transition: all 0.3s;
}

.el-aside.is-collapse .logo-text {
  width: 0;
  opacity: 0;
}

.el-menu {
  border-right: none;
  transition: all 0.3s;
}

.el-header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  position: relative;
  z-index: 999;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.fold-icon {
  font-size: 20px;
  cursor: pointer;
  color: #606266;
  transition: all 0.3s;
}

.fold-icon:hover {
  color: #409EFF;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.action-icon {
  font-size: 20px;
  cursor: pointer;
  color: #606266;
  transition: all 0.3s;
}

.action-icon:hover {
  color: #409EFF;
}

.notification-badge {
  line-height: 1;
}

/* 语言切换按钮样式 */
.language-switcher-container {
  display: flex;
  align-items: center;
}

.language-switcher-container :deep(.language-button) {
  background: #ffffff;
  color: #606266;
  border: 1px solid #dcdfe6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  padding: 6px 12px;
  min-width: 80px;
}

.language-switcher-container :deep(.language-button:hover) {
  border-color: #409eff;
  color: #409eff;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 8px;
  padding: 0 8px;
  height: 40px;
  border-radius: 20px;
  transition: all 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #606266;
}

.el-main {
  background-color: #f0f2f5;
  padding: 20px;
  overflow-y: auto;
  position: relative;
  height: calc(100vh - 60px);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .el-aside {
    position: fixed;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-200px);
  }

  .el-aside.is-collapse {
    transform: translateX(0);
  }

  .el-main {
    margin-left: 0;
    width: 100vw;
    padding: 10px;
  }

  .header-left .el-breadcrumb {
    display: none;
  }

  .username {
    display: none;
  }

  .user-info {
    padding: 0;
  }

  /* 移动端语言切换按钮样式 */
  .language-switcher-container :deep(.language-button) {
    padding: 4px 8px;
    min-width: 60px;
    font-size: 12px;
  }
  
  .language-switcher-container :deep(.language-text) {
    display: none;
  }
}

/* 动画效果 */
.el-aside, .el-main {
  transition: all 0.3s ease-in-out;
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 200px;
}
</style> 