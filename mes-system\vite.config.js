import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  server: {
    https: false, // 开发环境使用 HTTP
    port: 5175,
    strictPort: false,
    host: true,
    proxy: {
      '/api': {
        target: 'http://***************:5221',
        changeOrigin: true,
        secure: false
      }
    },
    cors: true
  }
})
