using System;
using System.ComponentModel.DataAnnotations;

namespace mes_system_server.DTOs
{
    // 职位DTO
    public class PositionDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public int DepartmentId { get; set; }
        public string DepartmentName { get; set; }
        public string Level { get; set; }
        public string Description { get; set; }
        public DateTime CreateTime { get; set; }
    }

    // 创建职位请求DTO
    public class CreatePositionDto
    {
        [Required(ErrorMessage = "职位名称不能为空")]
        [StringLength(50, ErrorMessage = "职位名称不能超过50个字符")]
        public string Name { get; set; }

        [Required(ErrorMessage = "所属部门不能为空")]
        public int DepartmentId { get; set; }

        [Required(ErrorMessage = "职级不能为空")]
        [RegularExpression("^[1-4]$", ErrorMessage = "职级必须是1-4之间的数字")]
        public string Level { get; set; }

        [StringLength(500, ErrorMessage = "描述不能超过500个字符")]
        public string Description { get; set; }
    }

    // 更新职位请求DTO
    public class UpdatePositionDto
    {
        [Required(ErrorMessage = "职位名称不能为空")]
        [StringLength(50, ErrorMessage = "职位名称不能超过50个字符")]
        public string Name { get; set; }

        [Required(ErrorMessage = "所属部门不能为空")]
        public int DepartmentId { get; set; }

        [Required(ErrorMessage = "职级不能为空")]
        [RegularExpression("^[1-4]$", ErrorMessage = "职级必须是1-4之间的数字")]
        public string Level { get; set; }

        [StringLength(500, ErrorMessage = "描述不能超过500个字符")]
        public string Description { get; set; }
    }

    // 职位列表响应DTO
    public class PositionResponseDto
    {
        public int Total { get; set; }
        public List<PositionDto> List { get; set; }
    }
} 