/**
 * 权限管理工具类
 * 用于处理用户权限相关的逻辑
 */

import { getRoleMenus } from '@/api/role'

/**
 * 获取当前登录用户信息
 * @returns {Object|null} 用户信息对象或null
 */
export function getCurrentUser() {
  try {
    const userInfoStr = localStorage.getItem('userInfo')
    if (userInfoStr) {
      const userInfo = JSON.parse(userInfoStr)
      return userInfo
    }
  } catch (error) {
    console.error('解析用户信息失败:', error)
  }
  return null
}

/**
 * 获取当前用户的角色ID
 * @returns {number|null} 角色ID或null
 */
export function getCurrentUserRoleId() {
  const userInfo = getCurrentUser()
  return userInfo?.roleId || null
}

/**
 * 获取当前用户的菜单权限
 * @returns {Promise<Array>} 菜单ID数组
 */
export async function getCurrentUserMenuPermissions() {
  try {
    const roleId = getCurrentUserRoleId()
    if (!roleId) {
      console.warn('用户未登录或角色信息不完整')
      return []
    }

    const response = await getRoleMenus(roleId)
    if (response.data.code === 200) {
      return response.data.data || []
    } else {
      console.warn('获取用户菜单权限失败:', response.data.message)
      return []
    }
  } catch (error) {
    console.error('获取用户菜单权限异常:', error)
    return []
  }
}

/**
 * 检查用户是否有指定菜单权限
 * @param {string} menuId 菜单ID
 * @param {Array} userMenuIds 用户拥有的菜单ID列表
 * @returns {boolean} 是否有权限
 */
export function hasMenuPermission(menuId, userMenuIds = []) {
  return userMenuIds.includes(menuId)
}

/**
 * 根据用户权限过滤菜单
 * @param {Array} menus 菜单列表
 * @param {Array} userMenuIds 用户拥有的菜单ID列表
 * @returns {Array} 过滤后的菜单列表
 */
export function filterMenusByPermissions(menus, userMenuIds = []) {
  if (!userMenuIds || userMenuIds.length === 0) {
    return []
  }
  
  const filterMenu = (items) => {
    return items.filter(item => {
      // 检查当前菜单项是否在用户权限列表中
      if (!userMenuIds.includes(item.id)) {
        return false
      }
      
      // 如果有子菜单，递归过滤子菜单
      if (item.children && item.children.length > 0) {
        const filteredChildren = filterMenu(item.children)
        
        // 如果有有效的子菜单，保留父菜单并更新子菜单
        if (filteredChildren.length > 0) {
          item.children = filteredChildren
          return true
        } else {
          // 如果子菜单全部被过滤掉，但父菜单有权限且是菜单项，保留父菜单
          return item.type === 'menu-item'
        }
      }
      
      return true
    })
  }
  
  return filterMenu(menus)
}

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export function isUserLoggedIn() {
  return !!localStorage.getItem('token') && !!localStorage.getItem('userInfo')
}

/**
 * 检查用户是否是超级管理员
 * @returns {boolean} 是否是超级管理员
 */
export function isSuperAdmin() {
  const userInfo = getCurrentUser()
  return userInfo?.role === '超级管理员' || userInfo?.roleId === 1
}

/**
 * 检查用户是否是系统管理员
 * @returns {boolean} 是否是系统管理员
 */
export function isSystemAdmin() {
  const userInfo = getCurrentUser()
  return userInfo?.role === '系统管理员' || userInfo?.role === '超级管理员'
}

/**
 * 用户登出
 */
export function logout() {
  localStorage.removeItem('token')
  localStorage.removeItem('userInfo')
  localStorage.removeItem('isLoggedIn')
}

export default {
  getCurrentUser,
  getCurrentUserRoleId,
  getCurrentUserMenuPermissions,
  hasMenuPermission,
  filterMenusByPermissions,
  isUserLoggedIn,
  isSuperAdmin,
  isSystemAdmin,
  logout
} 