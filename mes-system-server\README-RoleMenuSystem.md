# 角色菜单权限系统 API 文档

## 概述

本系统实现了完整的角色菜单权限管理功能，包括菜单管理和角色菜单权限分配。

## 数据库表结构

### Menus 表
存储系统所有菜单信息：
- `Id`: 菜单主键ID
- `MenuId`: 菜单唯一标识（如：system-users）
- `Name`: 菜单名称
- `Path`: 菜单路径
- `Icon`: 菜单图标
- `Type`: 菜单类型（menu-item, sub-menu）
- `ParentId`: 父菜单ID
- `SortOrder`: 排序序号
- `Status`: 菜单状态
- `Description`: 菜单描述
- `CreateTime`, `UpdateTime`: 创建和更新时间

### RoleMenus 表
存储角色和菜单的多对多关系：
- `Id`: 关联表主键ID
- `RoleId`: 角色ID（外键）
- `MenuId`: 菜单ID（外键）
- `CreateTime`: 创建时间

## API 接口

### 角色菜单权限相关API

#### 1. 获取角色菜单权限
```
GET /api/roles/{roleId}/menus
```

**功能**: 获取指定角色的菜单权限列表

**路径参数**:
- `roleId`: 角色ID（整数）

**响应格式**:
```json
{
    "code": 200,
    "message": "获取角色菜单权限成功",
    "data": ["system-users", "system-roles", "production-plan", "quality-inspection"]
}
```

#### 2. 保存角色菜单权限
```
PUT /api/roles/{roleId}/menus
```

**功能**: 保存指定角色的菜单权限

**路径参数**:
- `roleId`: 角色ID（整数）

**请求体**:
```json
{
    "menuIds": ["system-users", "system-roles", "production-plan", "quality-inspection"]
}
```

**响应格式**:
```json
{
    "code": 200,
    "message": "保存角色菜单权限成功",
    "data": null
}
```

### 菜单管理相关API

#### 3. 获取菜单列表
```
GET /api/menus
```

**功能**: 获取所有菜单的平面列表

#### 4. 获取菜单树
```
GET /api/menus/tree
```

**功能**: 获取菜单的树形结构

#### 5. 获取菜单详情
```
GET /api/menus/{id}
```

#### 6. 创建菜单
```
POST /api/menus
```

#### 7. 更新菜单
```
PUT /api/menus/{id}
```

#### 8. 删除菜单
```
DELETE /api/menus/{id}
```

## 使用说明

### 1. 数据库初始化

系统已自动创建以下默认菜单：

**一级菜单：**
- 仓储管理 (warehouse)
- 生产管理 (production)
- 工程管理 (engineering)
- 生产线管理 (production-line)
- 质量管理 (quality)
- 设备管理 (equipment)
- 系统管理 (system)

**系统管理子菜单：**
- 用户管理 (system-users)
- 角色权限 (system-roles)
- 菜单管理 (system-menus)

### 2. 角色权限分配

**系统管理员**: 拥有所有菜单权限
**生产经理**: 拥有生产相关菜单权限
**质量检验员**: 拥有质量相关菜单权限

### 3. 手动插入角色菜单数据

如果需要手动插入角色菜单关联数据，可以执行：
```sql
-- 运行 Scripts/InsertRoleMenuData.sql 脚本
```

## 前端集成

前端通过以下API调用获取和设置角色菜单权限：

1. **获取角色菜单权限**：
```javascript
import { getRoleMenus } from '@/api/role'

const response = await getRoleMenus(roleId)
const menuIds = response.data.data
```

2. **保存角色菜单权限**：
```javascript
import { saveRoleMenus } from '@/api/role'

const result = await saveRoleMenus({
    roleId: roleId,
    menuIds: ['system-users', 'system-roles']
})
```

## 测试API

您可以使用以下URL进行测试：

1. **获取角色1的菜单权限**：
   ```
   GET http://***************:5221/api/roles/1/menus
   ```

2. **保存角色1的菜单权限**：
   ```
   PUT http://***************:5221/api/roles/1/menus
   Content-Type: application/json
   
   {
       "menuIds": ["system-users", "system-roles", "production-plan"]
   }
   ```

## 错误处理

- **404**: 角色不存在
- **400**: 请求参数错误
- **500**: 服务器内部错误

所有错误都返回统一格式：
```json
{
    "code": 404,
    "message": "角色不存在",
    "data": null
}
``` 