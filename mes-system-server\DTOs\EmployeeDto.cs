using System;
using System.ComponentModel.DataAnnotations;

namespace mes_system_server.DTOs
{
    public class EmployeeDto
    {
        public int Id { get; set; }
        public string EmployeeId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public int DepartmentId { get; set; }
        public string Department { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public DateTime EntryDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime? Birthday { get; set; }
        public string IdCard { get; set; } = string.Empty;
        public int? Age { get; set; }
        public DateTime? IdCardIssueDate { get; set; }
        public string Gender { get; set; } = string.Empty;
        public string IdCardIssuePlace { get; set; } = string.Empty;
        public string Education { get; set; } = string.Empty;
        public string ContractNo { get; set; } = string.Empty;
        public string Level { get; set; } = string.Empty;
        public string SkillLevel { get; set; } = string.Empty;
        public string PerformanceLevel { get; set; } = string.Empty;
        public FactoryInfoDto FactoryInfo { get; set; } = new FactoryInfoDto();
        public DateTime CreateTime { get; set; }
    }

    public class FactoryInfoDto
    {
        public string Name { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string Contact { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string SalaryType { get; set; } = string.Empty;
        public string Team { get; set; } = string.Empty;
        public string BaseSalary { get; set; } = string.Empty;
        public int? WorkYears { get; set; }
    }

    public class CreateEmployeeDto
    {
        [Required(ErrorMessage = "工号不能为空")]
        [StringLength(50, ErrorMessage = "工号最长为50个字符")]
        public string EmployeeId { get; set; } = string.Empty;

        [Required(ErrorMessage = "姓名不能为空")]
        [StringLength(50, ErrorMessage = "姓名最长为50个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "部门不能为空")]
        public int DepartmentId { get; set; }

        [Required(ErrorMessage = "职位不能为空")]
        [StringLength(50, ErrorMessage = "职位最长为50个字符")]
        public string Position { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "电话最长为20个字符")]
        public string Phone { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "邮箱最长为100个字符")]
        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        public string Email { get; set; } = string.Empty;

        public DateTime EntryDate { get; set; } = DateTime.Now;

        [Required(ErrorMessage = "状态不能为空")]
        public string Status { get; set; } = "active";

        public DateTime? Birthday { get; set; }

        [StringLength(18, ErrorMessage = "身份证号最长为18个字符")]
        public string IdCard { get; set; } = string.Empty;

        public string Gender { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "教育程度最长为50个字符")]
        public string Education { get; set; } = string.Empty;

        public CreateFactoryInfoDto FactoryInfo { get; set; } = new CreateFactoryInfoDto();
    }

    public class CreateFactoryInfoDto
    {
        [StringLength(100, ErrorMessage = "工厂名称最长为100个字符")]
        public string Name { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "工厂地址最长为200个字符")]
        public string Address { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "联系人最长为50个字符")]
        public string Contact { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "联系电话最长为20个字符")]
        public string Phone { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "薪资类型最长为20个字符")]
        public string SalaryType { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "班组最长为50个字符")]
        public string Team { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "基本薪资最长为20个字符")]
        public string BaseSalary { get; set; } = string.Empty;

        public int? WorkYears { get; set; }
    }

    public class UpdateEmployeeDto
    {
        [Required(ErrorMessage = "姓名不能为空")]
        [StringLength(50, ErrorMessage = "姓名最长为50个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "部门不能为空")]
        public int DepartmentId { get; set; }

        [Required(ErrorMessage = "职位不能为空")]
        [StringLength(50, ErrorMessage = "职位最长为50个字符")]
        public string Position { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "电话最长为20个字符")]
        public string Phone { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "邮箱最长为100个字符")]
        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        public string Email { get; set; } = string.Empty;

        public DateTime EntryDate { get; set; }

        [Required(ErrorMessage = "状态不能为空")]
        public string Status { get; set; } = string.Empty;

        public DateTime? Birthday { get; set; }

        [StringLength(18, ErrorMessage = "身份证号最长为18个字符")]
        public string IdCard { get; set; } = string.Empty;

        public string Gender { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "教育程度最长为50个字符")]
        public string Education { get; set; } = string.Empty;

        public UpdateFactoryInfoDto FactoryInfo { get; set; } = new UpdateFactoryInfoDto();
    }

    public class UpdateFactoryInfoDto
    {
        [StringLength(100, ErrorMessage = "工厂名称最长为100个字符")]
        public string Name { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "工厂地址最长为200个字符")]
        public string Address { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "联系人最长为50个字符")]
        public string Contact { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "联系电话最长为20个字符")]
        public string Phone { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "薪资类型最长为20个字符")]
        public string SalaryType { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "班组最长为50个字符")]
        public string Team { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "基本薪资最长为20个字符")]
        public string BaseSalary { get; set; } = string.Empty;

        public int? WorkYears { get; set; }
    }

    public class EmployeeResponseDto
    {
        public int Total { get; set; }
        public List<EmployeeDto> List { get; set; } = new List<EmployeeDto>();
    }
} 