using System.ComponentModel.DataAnnotations;

namespace mes_system_server.DTOs
{
    public class CreateUserDto
    {
        [Required(ErrorMessage = "用户名不能为空")]
        [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
        public string Username { get; set; } = string.Empty;

        [Required(ErrorMessage = "姓名不能为空")]
        [StringLength(50, ErrorMessage = "姓名长度不能超过50个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "部门不能为空")]
        [StringLength(50, ErrorMessage = "部门长度不能超过50个字符")]
        public string Department { get; set; } = string.Empty;

        [Required(ErrorMessage = "角色不能为空")]
        [StringLength(50, ErrorMessage = "角色长度不能超过50个字符")]
        public string Role { get; set; } = string.Empty;

        [Required(ErrorMessage = "邮箱不能为空")]
        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        [StringLength(100, ErrorMessage = "邮箱长度不能超过100个字符")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "手机号不能为空")]
        [Phone(ErrorMessage = "手机号格式不正确")]
        [StringLength(20, ErrorMessage = "手机号长度不能超过20个字符")]
        public string Phone { get; set; } = string.Empty;

        [Required(ErrorMessage = "密码不能为空")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
        public string Password { get; set; } = string.Empty;
    }

    public class UpdateUserDto
    {
        [Required(ErrorMessage = "姓名不能为空")]
        [StringLength(50, ErrorMessage = "姓名长度不能超过50个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "部门不能为空")]
        [StringLength(50, ErrorMessage = "部门长度不能超过50个字符")]
        public string Department { get; set; } = string.Empty;

        [Required(ErrorMessage = "角色不能为空")]
        [StringLength(50, ErrorMessage = "角色长度不能超过50个字符")]
        public string Role { get; set; } = string.Empty;

        [Required(ErrorMessage = "邮箱不能为空")]
        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        [StringLength(100, ErrorMessage = "邮箱长度不能超过100个字符")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "手机号不能为空")]
        [Phone(ErrorMessage = "手机号格式不正确")]
        [StringLength(20, ErrorMessage = "手机号长度不能超过20个字符")]
        public string Phone { get; set; } = string.Empty;
    }

    public class UserListDto
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public int? RoleId { get; set; }
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public bool Status { get; set; }
        public DateTime CreateTime { get; set; }
    }
} 