import request from '@/utils/request'

// 产品型号相关API
export const productModelApi = {
  // 获取产品型号列表
  getList(params) {
    return request({
      url: '/api/models/products',
      method: 'get',
      params
    })
  },
  
  // 创建产品型号
  create(data) {
    return request({
      url: '/api/models/products',
      method: 'post',
      data
    })
  },
  
  // 更新产品型号
  update(id, data) {
    return request({
      url: `/api/models/products/${id}`,
      method: 'put',
      data
    })
  },
  
  // 删除产品型号
  delete(id) {
    return request({
      url: `/api/models/products/${id}`,
      method: 'delete'
    })
  },
  
  // 获取产品型号详情
  getDetail(id) {
    return request({
      url: `/api/models/products/${id}`,
      method: 'get'
    })
  }
}

// 组件型号相关API
export const componentModelApi = {
  // 获取组件型号列表
  getList(params) {
    return request({
      url: '/api/models/components',
      method: 'get',
      params
    })
  },
  
  // 创建组件型号
  create(data) {
    return request({
      url: '/api/models/components',
      method: 'post',
      data
    })
  },
  
  // 更新组件型号
  update(id, data) {
    return request({
      url: `/api/models/components/${id}`,
      method: 'put',
      data
    })
  },
  
  // 删除组件型号
  delete(id) {
    return request({
      url: `/api/models/components/${id}`,
      method: 'delete'
    })
  },
  
  // 获取组件型号详情
  getDetail(id) {
    return request({
      url: `/api/models/components/${id}`,
      method: 'get'
    })
  }
}

// 零件型号相关API
export const partModelApi = {
  // 获取零件型号列表
  getList(params) {
    return request({
      url: '/api/models/parts',
      method: 'get',
      params
    })
  },
  
  // 创建零件型号
  create(data) {
    return request({
      url: '/api/models/parts',
      method: 'post',
      data
    })
  },
  
  // 更新零件型号
  update(id, data) {
    return request({
      url: `/api/models/parts/${id}`,
      method: 'put',
      data
    })
  },
  
  // 删除零件型号
  delete(id) {
    return request({
      url: `/api/models/parts/${id}`,
      method: 'delete'
    })
  },
  
  // 获取零件型号详情
  getDetail(id) {
    return request({
      url: `/api/models/parts/${id}`,
      method: 'get'
    })
  }
}

// 辅料包材型号相关API
export const auxiliaryModelApi = {
  // 获取辅料包材型号列表
  getList(params) {
    return request({
      url: '/api/models/auxiliary',
      method: 'get',
      params
    })
  },
  
  // 创建辅料包材型号
  create(data) {
    return request({
      url: '/api/models/auxiliary',
      method: 'post',
      data
    })
  },
  
  // 更新辅料包材型号
  update(id, data) {
    return request({
      url: `/api/models/auxiliary/${id}`,
      method: 'put',
      data
    })
  },
  
  // 删除辅料包材型号
  delete(id) {
    return request({
      url: `/api/models/auxiliary/${id}`,
      method: 'delete'
    })
  },
  
  // 获取辅料包材型号详情
  getDetail(id) {
    return request({
      url: `/api/models/auxiliary/${id}`,
      method: 'get'
    })
  }
}

// 五金塑胶型号相关API
export const hardwarePlasticModelApi = {
  // 获取五金塑胶型号列表
  getList(params) {
    return request({
      url: '/api/models/hardware-plastic',
      method: 'get',
      params
    })
  },
  
  // 创建五金塑胶型号
  create(data) {
    // 格式化数量类型字段为decimal(18,2)格式
    const formatDecimal = (value) => {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }
      // 转换为数字，然后格式化为2位小数
      const numValue = parseFloat(value) || 0
      return numValue.toFixed(2)
    }
    
    // 格式化字符串字段
    const formatString = (value) => {
      return value ? String(value).trim() : ''
    }
    
    // 格式化布尔值字段
    const formatBoolean = (value) => {
      if (value === true || value === 'true' || value === '是') {
        return true
      }
      if (value === false || value === 'false' || value === '否') {
        return false
      }
      // 默认返回false
      return false
    }
    
    // 构建请求数据
    const requestData = {
      name: data.name || '',
      code: data.code || '',
      type: 'hardware-plastic',
      category: 'REF_MET', // 使用正确的分类码
      status: 'True',
      specification: data.specification || '',
      unit: data.unit || '',
      image: data.image || '',
      material: data.material || '',
      size: data.size || '',
      // 销售视图字段
      salesPrice: formatDecimal(data.salesPrice),
      minOrderQty: formatDecimal(data.minOrderQty),
      maxOrderQty: formatDecimal(data.maxOrderQty),
      salesUnit: formatString(data.salesUnit),
      leadTime: formatString(data.leadTime),
      warranty: formatString(data.warranty),
      // 仓库视图字段
      storageLocation: formatString(data.storageLocation),
      safetyStock: formatDecimal(data.safetyStock),
      maxStock: formatDecimal(data.maxStock),
      reorderPoint: formatDecimal(data.reorderPoint),
      storageCondition: formatString(data.storageCondition),
      shelfLife: formatString(data.shelfLife),
      // 财务视图字段
      standardCost: formatDecimal(data.standardCost),
      averageCost: formatDecimal(data.averageCost),
      valuationMethod: formatString(data.valuationMethod),
      taxRate: formatDecimal(data.taxRate),
      accountSubject: formatString(data.accountSubject),
      costCenter: formatString(data.costCenter),
      // 生产视图字段
      productionType: formatString(data.productionType),
      productionLeadTime: formatString(data.productionLeadTime),
      setupTime: formatString(data.setupTime),
      cycleTime: formatString(data.cycleTime),
      batchSize: formatString(data.batchSize),
      workCenter: formatString(data.workCenter),
      qualityStandard: formatString(data.qualityStandard),
      // 采购视图字段
      supplier: formatString(data.supplier),
      purchasePrice: formatDecimal(data.purchasePrice),
      purchaseUnit: formatString(data.purchaseUnit),
      minPurchaseQty: formatDecimal(data.minPurchaseQty),
      purchaseLeadTime: formatString(data.purchaseLeadTime),
      qualityLevel: formatString(data.qualityLevel),
      purchaseNote: formatString(data.purchaseNote),
      // 进出口视图字段
      hsCode: formatString(data.hsCode),
      originCountry: formatString(data.originCountry),
      importTaxRate: formatDecimal(data.importTaxRate),
      exportTaxRefund: formatDecimal(data.exportTaxRefund),
      isDangerous: formatBoolean(data.isDangerous),
      transportMode: formatString(data.transportMode),
      packingRequirement: formatString(data.packingRequirement),
      inspectionRequired: formatBoolean(data.inspectionRequired),
      licenseRequirement: formatString(data.licenseRequirement),
      // MRP计划视图字段
      planningStrategy: formatString(data.planningStrategy),
      planningCycle: formatString(data.planningCycle),
      forecastMethod: formatString(data.forecastMethod),
      abcCategory: formatString(data.abcCategory),
      demandSource: formatString(data.demandSource),
      planner: formatString(data.planner),
      safetyStockDays: formatString(data.safetyStockDays),
      lotSizeRule: formatString(data.lotSizeRule),
      demandTimeFence: formatString(data.demandTimeFence),
      supplyTimeFence: formatString(data.supplyTimeFence),
      planningNote: formatString(data.planningNote)
    }

    return request({
      url: '/api/models/hardware-plastic',
      method: 'post',
      data: requestData
    })
  },
  
  // 更新五金塑胶型号
  update(id, data) {
    // 格式化数量类型字段为decimal(18,2)格式
    const formatDecimal = (value) => {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }
      // 转换为数字，然后格式化为2位小数
      const numValue = parseFloat(value) || 0
      return numValue.toFixed(2)
    }
    
    // 格式化字符串字段
    const formatString = (value) => {
      return value ? String(value).trim() : ''
    }
    
    // 格式化布尔值字段
    const formatBoolean = (value) => {
      if (value === true || value === 'true' || value === '是') {
        return true
      }
      if (value === false || value === 'false' || value === '否') {
        return false
      }
      // 默认返回false
      return false
    }
    
    // 构建请求数据
    const requestData = {
      name: data.name || '',
      code: data.code || '',
      type: 'hardware-plastic',
      category: 'REF_MET', // 使用正确的分类码
      status: data.status || 'True',
      specification: data.specification || '',
      unit: data.unit || '',
      image: data.image || '',
      material: data.material || '',
      size: data.size || '',
      // 销售视图字段
      salesPrice: formatDecimal(data.salesPrice),
      minOrderQty: formatDecimal(data.minOrderQty),
      maxOrderQty: formatDecimal(data.maxOrderQty),
      salesUnit: formatString(data.salesUnit),
      leadTime: formatString(data.leadTime),
      warranty: formatString(data.warranty),
      // 仓库视图字段
      storageLocation: formatString(data.storageLocation),
      safetyStock: formatDecimal(data.safetyStock),
      maxStock: formatDecimal(data.maxStock),
      reorderPoint: formatDecimal(data.reorderPoint),
      storageCondition: formatString(data.storageCondition),
      shelfLife: formatString(data.shelfLife),
      // 财务视图字段
      standardCost: formatDecimal(data.standardCost),
      averageCost: formatDecimal(data.averageCost),
      valuationMethod: formatString(data.valuationMethod),
      taxRate: formatDecimal(data.taxRate),
      accountSubject: formatString(data.accountSubject),
      costCenter: formatString(data.costCenter),
      // 生产视图字段
      productionType: formatString(data.productionType),
      productionLeadTime: formatString(data.productionLeadTime),
      setupTime: formatString(data.setupTime),
      cycleTime: formatString(data.cycleTime),
      batchSize: formatString(data.batchSize),
      workCenter: formatString(data.workCenter),
      qualityStandard: formatString(data.qualityStandard),
      // 采购视图字段
      supplier: formatString(data.supplier),
      purchasePrice: formatDecimal(data.purchasePrice),
      purchaseUnit: formatString(data.purchaseUnit),
      minPurchaseQty: formatDecimal(data.minPurchaseQty),
      purchaseLeadTime: formatString(data.purchaseLeadTime),
      qualityLevel: formatString(data.qualityLevel),
      purchaseNote: formatString(data.purchaseNote),
      // 进出口视图字段
      hsCode: formatString(data.hsCode),
      originCountry: formatString(data.originCountry),
      importTaxRate: formatDecimal(data.importTaxRate),
      exportTaxRefund: formatDecimal(data.exportTaxRefund),
      isDangerous: formatBoolean(data.isDangerous),
      transportMode: formatString(data.transportMode),
      packingRequirement: formatString(data.packingRequirement),
      inspectionRequired: formatBoolean(data.inspectionRequired),
      licenseRequirement: formatString(data.licenseRequirement),
      // MRP计划视图字段
      planningStrategy: formatString(data.planningStrategy),
      planningCycle: formatString(data.planningCycle),
      forecastMethod: formatString(data.forecastMethod),
      abcCategory: formatString(data.abcCategory),
      demandSource: formatString(data.demandSource),
      planner: formatString(data.planner),
      safetyStockDays: formatString(data.safetyStockDays),
      lotSizeRule: formatString(data.lotSizeRule),
      demandTimeFence: formatString(data.demandTimeFence),
      supplyTimeFence: formatString(data.supplyTimeFence),
      planningNote: formatString(data.planningNote)
    }

    return request({
      url: `/api/models/hardware-plastic/${id}`,
      method: 'put',
      data: requestData
    })
  },
  
  // 删除五金塑胶型号
  delete(id) {
    return request({
      url: `/api/models/hardware-plastic/${id}`,
      method: 'delete'
    })
  },
  
  // 获取五金塑胶型号详情
  getDetail(id) {
    return request({
      url: `/api/models/hardware-plastic/${id}`,
      method: 'get'
    })
  },
  
  // 获取五金塑胶型号列表（用于查询功能）
  search(params) {
    return request({
      url: '/api/models/hardware-plastic/search',
      method: 'get',
      params
    })
  }
}

// 成品型号专用API - 调用用户指定的后端API
export const productModelApiNew = {
  // 创建成品型号 - 调用指定的后端API
  create(data) {
    // 格式化数量类型字段为decimal(18,2)格式
    const formatDecimal = (value) => {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }
      // 转换为数字，然后格式化为2位小数
      const numValue = parseFloat(value) || 0
      return numValue.toFixed(2)
    }
    
    // 格式化字符串字段
    const formatString = (value) => {
      return value ? String(value).trim() : ''
    }
    
    // 格式化布尔值字段
    const formatBoolean = (value) => {
      if (value === true || value === 'true' || value === '是') {
        return true
      }
      if (value === false || value === 'false' || value === '否') {
        return false
      }
      // 默认返回false
      return false
    }
    
    // 构建简化的请求数据，只包含核心必需字段
    const requestData = {
      name: data.name || '',
      code: data.code || '',
      type: 'products',
      category: 'REF_FG',
      status: 'True',
      specification: data.specification || '',
      unit: data.unit || '',
      image: data.image || '',
      material: data.material || '',
      size: data.size || '',
      // 销售视图字段
      salesPrice: formatDecimal(data.salesPrice),
      minOrderQty: formatDecimal(data.minOrderQty),
      maxOrderQty: formatDecimal(data.maxOrderQty),
      salesUnit: formatString(data.salesUnit),
      leadTime: formatString(data.leadTime),
      warranty: formatString(data.warranty),
      // 仓库视图字段
      storageLocation: formatString(data.storageLocation),
      safetyStock: formatDecimal(data.safetyStock),
      maxStock: formatDecimal(data.maxStock),
      reorderPoint: formatDecimal(data.reorderPoint),
      storageCondition: formatString(data.storageCondition),
      shelfLife: formatString(data.shelfLife),
      // 财务视图字段
      standardCost: formatDecimal(data.standardCost),
      averageCost: formatDecimal(data.averageCost),
      valuationMethod: formatString(data.valuationMethod),
      taxRate: formatDecimal(data.taxRate),
      accountSubject: formatString(data.accountSubject),
      costCenter: formatString(data.costCenter),
      // 生产视图字段
      productionType: formatString(data.productionType),
      productionLeadTime: formatString(data.productionLeadTime),
      setupTime: formatString(data.setupTime),
      cycleTime: formatString(data.cycleTime),
      batchSize: formatString(data.batchSize),
      workCenter: formatString(data.workCenter),
      qualityStandard: formatString(data.qualityStandard),
      // 采购视图字段
      supplier: formatString(data.supplier),
      purchasePrice: formatDecimal(data.purchasePrice),
      purchaseUnit: formatString(data.purchaseUnit),
      minPurchaseQty: formatDecimal(data.minPurchaseQty),
      purchaseLeadTime: formatString(data.purchaseLeadTime),
      qualityLevel: formatString(data.qualityLevel),
      purchaseNote: formatString(data.purchaseNote),
      // 进出口视图字段
      hsCode: formatString(data.hsCode),
      originCountry: formatString(data.originCountry),
      importTaxRate: formatDecimal(data.importTaxRate),
      exportTaxRefund: formatDecimal(data.exportTaxRefund),
      isDangerous: formatBoolean(data.isDangerous),
      transportMode: formatString(data.transportMode),
      packingRequirement: formatString(data.packingRequirement),
      inspectionRequired: formatBoolean(data.inspectionRequired),
      licenseRequirement: formatString(data.licenseRequirement),
      // MRP计划视图字段
      planningStrategy: formatString(data.planningStrategy),
      planningCycle: formatString(data.planningCycle),
      forecastMethod: formatString(data.forecastMethod),
      abcCategory: formatString(data.abcCategory),
      demandSource: formatString(data.demandSource),
      planner: formatString(data.planner),
      safetyStockDays: formatString(data.safetyStockDays),
      lotSizeRule: formatString(data.lotSizeRule),
      demandTimeFence: formatString(data.demandTimeFence),
      supplyTimeFence: formatString(data.supplyTimeFence),
      planningNote: formatString(data.planningNote)
    }
    
    return request({
      url: '/api/models/products',
      method: 'post',
      data: requestData
    })
  },

  // 获取成品型号详情
  getDetail(id) {
    return request({
      url: `/api/models/products/${id}`,
      method: 'get'
    })
  },

  // 更新成品型号
  update(id, data) {
    // 使用相同的格式化函数
    const formatDecimal = (value) => {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }
      const numValue = parseFloat(value) || 0
      return numValue.toFixed(2)
    }
    
    const formatString = (value) => {
      return value ? String(value).trim() : ''
    }
    
    const formatBoolean = (value) => {
      if (value === true || value === 'true' || value === '是') {
        return true
      }
      if (value === false || value === 'false' || value === '否') {
        return false
      }
      return false
    }
    
    // 构建更新请求数据
    const requestData = {
      name: data.name || '',
      code: data.code || '',
      type: 'products',
      category: 'REF_FG',
      status: data.status || 'True',
      specification: data.specification || '',
      unit: data.unit || '',
      image: data.image || '',
      material: data.material || '',
      size: data.size || '',
      // 销售视图字段
      salesPrice: formatDecimal(data.salesPrice),
      minOrderQty: formatDecimal(data.minOrderQty),
      maxOrderQty: formatDecimal(data.maxOrderQty),
      salesUnit: formatString(data.salesUnit),
      leadTime: formatString(data.leadTime),
      warranty: formatString(data.warranty),
      // 仓库视图字段
      storageLocation: formatString(data.storageLocation),
      safetyStock: formatDecimal(data.safetyStock),
      maxStock: formatDecimal(data.maxStock),
      reorderPoint: formatDecimal(data.reorderPoint),
      storageCondition: formatString(data.storageCondition),
      shelfLife: formatString(data.shelfLife),
      // 财务视图字段
      standardCost: formatDecimal(data.standardCost),
      averageCost: formatDecimal(data.averageCost),
      valuationMethod: formatString(data.valuationMethod),
      taxRate: formatDecimal(data.taxRate),
      accountSubject: formatString(data.accountSubject),
      costCenter: formatString(data.costCenter),
      // 生产视图字段
      productionType: formatString(data.productionType),
      productionLeadTime: formatString(data.productionLeadTime),
      setupTime: formatString(data.setupTime),
      cycleTime: formatString(data.cycleTime),
      batchSize: formatString(data.batchSize),
      workCenter: formatString(data.workCenter),
      qualityStandard: formatString(data.qualityStandard),
      // 采购视图字段
      supplier: formatString(data.supplier),
      purchasePrice: formatDecimal(data.purchasePrice),
      purchaseUnit: formatString(data.purchaseUnit),
      minPurchaseQty: formatDecimal(data.minPurchaseQty),
      purchaseLeadTime: formatString(data.purchaseLeadTime),
      qualityLevel: formatString(data.qualityLevel),
      purchaseNote: formatString(data.purchaseNote),
      // 进出口视图字段
      hsCode: formatString(data.hsCode),
      originCountry: formatString(data.originCountry),
      importTaxRate: formatDecimal(data.importTaxRate),
      exportTaxRefund: formatDecimal(data.exportTaxRefund),
      isDangerous: formatBoolean(data.isDangerous),
      transportMode: formatString(data.transportMode),
      packingRequirement: formatString(data.packingRequirement),
      inspectionRequired: formatBoolean(data.inspectionRequired),
      licenseRequirement: formatString(data.licenseRequirement),
      // MRP计划视图字段
      planningStrategy: formatString(data.planningStrategy),
      planningCycle: formatString(data.planningCycle),
      forecastMethod: formatString(data.forecastMethod),
      abcCategory: formatString(data.abcCategory),
      demandSource: formatString(data.demandSource),
      planner: formatString(data.planner),
      safetyStockDays: formatString(data.safetyStockDays),
      lotSizeRule: formatString(data.lotSizeRule),
      demandTimeFence: formatString(data.demandTimeFence),
      supplyTimeFence: formatString(data.supplyTimeFence),
      planningNote: formatString(data.planningNote)
    }
    
    return request({
      url: `/api/models/products/${id}`,
      method: 'put',
      data: requestData
    })
  }
}

// 通用分类API
export const categoryApi = {
  // 获取产品分类
  getProductCategories() {
    return request({
      url: '/api/categories/products',
      method: 'get'
    })
  },
  
  // 获取组件分类
  getComponentCategories() {
    return request({
      url: '/api/categories/components',
      method: 'get'
    })
  },
  
  // 获取零件分类
  getPartCategories() {
    return request({
      url: '/api/categories/parts',
      method: 'get'
    })
  },
  
  // 获取辅料包材分类
  getAuxiliaryCategories() {
    return request({
      url: '/api/categories/auxiliary',
      method: 'get'
    })
  },
  
  // 获取五金塑胶分类
  getHardwarePlasticCategories() {
    return request({
      url: '/api/categories/hardware-plastic',
      method: 'get'
    })
  }
}

// 组件型号专用API - 调用用户指定的后端API
export const componentModelApiNew = {
  // 创建组件型号 - 调用指定的后端API
  create(data) {
    // 格式化数量类型字段为decimal(18,2)格式
    const formatDecimal = (value) => {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }
      // 转换为数字，然后格式化为2位小数
      const numValue = parseFloat(value) || 0
      return numValue.toFixed(2)
    }
    
    // 格式化字符串字段
    const formatString = (value) => {
      return value ? String(value).trim() : ''
    }
    
    // 格式化布尔值字段
    const formatBoolean = (value) => {
      if (value === true || value === 'true' || value === '是') {
        return true
      }
      if (value === false || value === 'false' || value === '否') {
        return false
      }
      // 默认返回false
      return false
    }
    
    // 构建简化的请求数据，只包含核心必需字段
    const requestData = {
      name: data.name || '',
      code: data.code || '',
      type: 'components',
      category: 'REF_SUB',
      status: 'True',
      specification: data.specification || '',
      unit: data.unit || '',
      image: data.image || '',
      material: data.material || '',
      size: data.size || '',
      // 销售视图字段
      salesPrice: formatDecimal(data.salesPrice),
      minOrderQty: formatDecimal(data.minOrderQty),
      maxOrderQty: formatDecimal(data.maxOrderQty),
      salesUnit: formatString(data.salesUnit),
      leadTime: formatString(data.leadTime),
      warranty: formatString(data.warranty),
      // 仓库视图字段
      storageLocation: formatString(data.storageLocation),
      safetyStock: formatDecimal(data.safetyStock),
      maxStock: formatDecimal(data.maxStock),
      reorderPoint: formatDecimal(data.reorderPoint),
      storageCondition: formatString(data.storageCondition),
      shelfLife: formatString(data.shelfLife),
      // 财务视图字段
      standardCost: formatDecimal(data.standardCost),
      averageCost: formatDecimal(data.averageCost),
      valuationMethod: formatString(data.valuationMethod),
      taxRate: formatDecimal(data.taxRate),
      accountSubject: formatString(data.accountSubject),
      costCenter: formatString(data.costCenter),
      // 生产视图字段
      productionType: formatString(data.productionType),
      productionLeadTime: formatString(data.productionLeadTime),
      setupTime: formatString(data.setupTime),
      cycleTime: formatString(data.cycleTime),
      batchSize: formatString(data.batchSize),
      workCenter: formatString(data.workCenter),
      qualityStandard: formatString(data.qualityStandard),
      // 采购视图字段
      supplier: formatString(data.supplier),
      purchasePrice: formatDecimal(data.purchasePrice),
      purchaseUnit: formatString(data.purchaseUnit),
      minPurchaseQty: formatDecimal(data.minPurchaseQty),
      purchaseLeadTime: formatString(data.purchaseLeadTime),
      qualityLevel: formatString(data.qualityLevel),
      purchaseNote: formatString(data.purchaseNote),
      // 进出口视图字段
      hsCode: formatString(data.hsCode),
      originCountry: formatString(data.originCountry),
      importTaxRate: formatDecimal(data.importTaxRate),
      exportTaxRefund: formatDecimal(data.exportTaxRefund),
      isDangerous: formatBoolean(data.isDangerous),
      transportMode: formatString(data.transportMode),
      packingRequirement: formatString(data.packingRequirement),
      inspectionRequired: formatBoolean(data.inspectionRequired),
      licenseRequirement: formatString(data.licenseRequirement),
      // MRP计划视图字段
      planningStrategy: formatString(data.planningStrategy),
      planningCycle: formatString(data.planningCycle),
      forecastMethod: formatString(data.forecastMethod),
      abcCategory: formatString(data.abcCategory),
      demandSource: formatString(data.demandSource),
      planner: formatString(data.planner),
      safetyStockDays: formatString(data.safetyStockDays),
      lotSizeRule: formatString(data.lotSizeRule),
      demandTimeFence: formatString(data.demandTimeFence),
      supplyTimeFence: formatString(data.supplyTimeFence),
      planningNote: formatString(data.planningNote)
    }
    
    return request({
      url: '/api/models/components',
      method: 'post',
      data: requestData
    })
  },

  // 获取组件型号详情
  getDetail(id) {
    return request({
      url: `/api/models/components/${id}`,
      method: 'get'
    })
  },

  // 更新组件型号
  update(id, data) {
    // 使用相同的格式化函数
    const formatDecimal = (value) => {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }
      const numValue = parseFloat(value) || 0
      return numValue.toFixed(2)
    }
    
    const formatString = (value) => {
      return value ? String(value).trim() : ''
    }
    
    const formatBoolean = (value) => {
      if (value === true || value === 'true' || value === '是') {
        return true
      }
      if (value === false || value === 'false' || value === '否') {
        return false
      }
      return false
    }
    
    // 构建更新请求数据
    const requestData = {
      name: data.name || '',
      code: data.code || '',
      type: 'components',
      category: 'REF_SUB',
      status: data.status || 'True',
      specification: data.specification || '',
      unit: data.unit || '',
      image: data.image || '',
      material: data.material || '',
      size: data.size || '',
      // 销售视图字段
      salesPrice: formatDecimal(data.salesPrice),
      minOrderQty: formatDecimal(data.minOrderQty),
      maxOrderQty: formatDecimal(data.maxOrderQty),
      salesUnit: formatString(data.salesUnit),
      leadTime: formatString(data.leadTime),
      warranty: formatString(data.warranty),
      // 仓库视图字段
      storageLocation: formatString(data.storageLocation),
      safetyStock: formatDecimal(data.safetyStock),
      maxStock: formatDecimal(data.maxStock),
      reorderPoint: formatDecimal(data.reorderPoint),
      storageCondition: formatString(data.storageCondition),
      shelfLife: formatString(data.shelfLife),
      // 财务视图字段
      standardCost: formatDecimal(data.standardCost),
      averageCost: formatDecimal(data.averageCost),
      valuationMethod: formatString(data.valuationMethod),
      taxRate: formatDecimal(data.taxRate),
      accountSubject: formatString(data.accountSubject),
      costCenter: formatString(data.costCenter),
      // 生产视图字段
      productionType: formatString(data.productionType),
      productionLeadTime: formatString(data.productionLeadTime),
      setupTime: formatString(data.setupTime),
      cycleTime: formatString(data.cycleTime),
      batchSize: formatString(data.batchSize),
      workCenter: formatString(data.workCenter),
      qualityStandard: formatString(data.qualityStandard),
      // 采购视图字段
      supplier: formatString(data.supplier),
      purchasePrice: formatDecimal(data.purchasePrice),
      purchaseUnit: formatString(data.purchaseUnit),
      minPurchaseQty: formatDecimal(data.minPurchaseQty),
      purchaseLeadTime: formatString(data.purchaseLeadTime),
      qualityLevel: formatString(data.qualityLevel),
      purchaseNote: formatString(data.purchaseNote),
      // 进出口视图字段
      hsCode: formatString(data.hsCode),
      originCountry: formatString(data.originCountry),
      importTaxRate: formatDecimal(data.importTaxRate),
      exportTaxRefund: formatDecimal(data.exportTaxRefund),
      isDangerous: formatBoolean(data.isDangerous),
      transportMode: formatString(data.transportMode),
      packingRequirement: formatString(data.packingRequirement),
      inspectionRequired: formatBoolean(data.inspectionRequired),
      licenseRequirement: formatString(data.licenseRequirement),
      // MRP计划视图字段
      planningStrategy: formatString(data.planningStrategy),
      planningCycle: formatString(data.planningCycle),
      forecastMethod: formatString(data.forecastMethod),
      abcCategory: formatString(data.abcCategory),
      demandSource: formatString(data.demandSource),
      planner: formatString(data.planner),
      safetyStockDays: formatString(data.safetyStockDays),
      lotSizeRule: formatString(data.lotSizeRule),
      demandTimeFence: formatString(data.demandTimeFence),
      supplyTimeFence: formatString(data.supplyTimeFence),
      planningNote: formatString(data.planningNote)
    }
    
    return request({
      url: `/api/models/components/${id}`,
      method: 'put',
      data: requestData
    })
  }
} 

// 零件型号专用API - 调用用户指定的后端API
export const partModelApiNew = {
  // 创建零件型号 - 调用指定的后端API
  create(data) {
    // 格式化数量类型字段为decimal(18,2)格式
    const formatDecimal = (value) => {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }
      // 转换为数字，然后格式化为2位小数
      const numValue = parseFloat(value) || 0
      return numValue.toFixed(2)
    }
    
    // 格式化字符串字段
    const formatString = (value) => {
      return value ? String(value).trim() : ''
    }
    
    // 格式化布尔值字段
    const formatBoolean = (value) => {
      if (value === true || value === 'true' || value === '是') {
        return true
      }
      if (value === false || value === 'false' || value === '否') {
        return false
      }
      // 默认返回false
      return false
    }
    
    const requestData = {
      // 基本信息视图字段
      name: formatString(data.name),
      code: formatString(data.code),
      category: formatString(data.category),
      specification: formatString(data.specification),
      status: formatString(data.status),
      image: formatString(data.image),
      unit: formatString(data.unit),
      material: formatString(data.material),
      size: formatString(data.size),
      
      // 销售信息视图字段
      salesPrice: formatDecimal(data.salesPrice),
      salesUnit: formatString(data.salesUnit),
      minOrderQty: formatDecimal(data.minOrderQty),
      maxOrderQty: formatDecimal(data.maxOrderQty),
      leadTime: formatString(data.leadTime),
      warranty: formatString(data.warranty),
      
      // 仓库信息视图字段
      storageLocation: formatString(data.storageLocation),
      safetyStock: formatDecimal(data.safetyStock),
      maxStock: formatDecimal(data.maxStock),
      reorderPoint: formatDecimal(data.reorderPoint),
      storageCondition: formatString(data.storageCondition),
      shelfLife: formatString(data.shelfLife),
      
      // 财务信息视图字段
      standardCost: formatDecimal(data.standardCost),
      averageCost: formatDecimal(data.averageCost),
      valuationMethod: formatString(data.valuationMethod),
      taxRate: formatDecimal(data.taxRate),
      accountSubject: formatString(data.accountSubject),
      costCenter: formatString(data.costCenter),
      
      // 生产信息视图字段
      productionType: formatString(data.productionType),
      productionLeadTime: formatString(data.productionLeadTime),
      setupTime: formatString(data.setupTime),
      cycleTime: formatString(data.cycleTime),
      batchSize: formatString(data.batchSize),
      workCenter: formatString(data.workCenter),
      qualityStandard: formatString(data.qualityStandard),
      
      // 采购信息视图字段
      supplier: formatString(data.supplier),
      purchasePrice: formatDecimal(data.purchasePrice),
      purchaseUnit: formatString(data.purchaseUnit),
      minPurchaseQty: formatDecimal(data.minPurchaseQty),
      purchaseLeadTime: formatString(data.purchaseLeadTime),
      qualityLevel: formatString(data.qualityLevel),
      purchaseNote: formatString(data.purchaseNote),
      
      // 进出口视图字段
      hsCode: formatString(data.hsCode),
      originCountry: formatString(data.originCountry),
      importTaxRate: formatDecimal(data.importTaxRate),
      exportTaxRefund: formatDecimal(data.exportTaxRefund),
      isDangerous: formatBoolean(data.isDangerous),
      transportMode: formatString(data.transportMode),
      packingRequirement: formatString(data.packingRequirement),
      inspectionRequired: formatBoolean(data.inspectionRequired),
      licenseRequirement: formatString(data.licenseRequirement),
      
      // MRP计划视图字段
      planningStrategy: formatString(data.planningStrategy),
      planningCycle: formatString(data.planningCycle),
      forecastMethod: formatString(data.forecastMethod),
      abcCategory: formatString(data.abcCategory),
      demandSource: formatString(data.demandSource),
      planner: formatString(data.planner),
      safetyStockDays: formatString(data.safetyStockDays),
      lotSizeRule: formatString(data.lotSizeRule),
      demandTimeFence: formatString(data.demandTimeFence),
      supplyTimeFence: formatString(data.supplyTimeFence),
      planningNote: formatString(data.planningNote)
    }
    
    return request({
      url: '/api/models/parts',
      method: 'post',
      data: requestData
    })
  },
  
  // 更新零件型号
  update(id, data) {
    // 格式化数量类型字段为decimal(18,2)格式
    const formatDecimal = (value) => {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }
      // 转换为数字，然后格式化为2位小数
      const numValue = parseFloat(value) || 0
      return numValue.toFixed(2)
    }
    
    // 格式化字符串字段
    const formatString = (value) => {
      return value ? String(value).trim() : ''
    }
    
    // 格式化布尔值字段
    const formatBoolean = (value) => {
      if (value === true || value === 'true' || value === '是') {
        return true
      }
      if (value === false || value === 'false' || value === '否') {
        return false
      }
      // 默认返回false
      return false
    }
    
    const requestData = {
      id,
      // 基本信息视图字段
      name: formatString(data.name),
      code: formatString(data.code),
      category: formatString(data.category),
      specification: formatString(data.specification),
      status: formatString(data.status),
      image: formatString(data.image),
      unit: formatString(data.unit),
      material: formatString(data.material),
      size: formatString(data.size),
      
      // 销售信息视图字段
      salesPrice: formatDecimal(data.salesPrice),
      salesUnit: formatString(data.salesUnit),
      minOrderQty: formatDecimal(data.minOrderQty),
      maxOrderQty: formatDecimal(data.maxOrderQty),
      leadTime: formatString(data.leadTime),
      warranty: formatString(data.warranty),
      
      // 仓库信息视图字段
      storageLocation: formatString(data.storageLocation),
      safetyStock: formatDecimal(data.safetyStock),
      maxStock: formatDecimal(data.maxStock),
      reorderPoint: formatDecimal(data.reorderPoint),
      storageCondition: formatString(data.storageCondition),
      shelfLife: formatString(data.shelfLife),
      
      // 财务信息视图字段
      standardCost: formatDecimal(data.standardCost),
      averageCost: formatDecimal(data.averageCost),
      valuationMethod: formatString(data.valuationMethod),
      taxRate: formatDecimal(data.taxRate),
      accountSubject: formatString(data.accountSubject),
      costCenter: formatString(data.costCenter),
      
      // 生产信息视图字段
      productionType: formatString(data.productionType),
      productionLeadTime: formatString(data.productionLeadTime),
      setupTime: formatString(data.setupTime),
      cycleTime: formatString(data.cycleTime),
      batchSize: formatString(data.batchSize),
      workCenter: formatString(data.workCenter),
      qualityStandard: formatString(data.qualityStandard),
      
      // 采购信息视图字段
      supplier: formatString(data.supplier),
      purchasePrice: formatDecimal(data.purchasePrice),
      purchaseUnit: formatString(data.purchaseUnit),
      minPurchaseQty: formatDecimal(data.minPurchaseQty),
      purchaseLeadTime: formatString(data.purchaseLeadTime),
      qualityLevel: formatString(data.qualityLevel),
      purchaseNote: formatString(data.purchaseNote),
      
      // 进出口视图字段
      hsCode: formatString(data.hsCode),
      originCountry: formatString(data.originCountry),
      importTaxRate: formatDecimal(data.importTaxRate),
      exportTaxRefund: formatDecimal(data.exportTaxRefund),
      isDangerous: formatBoolean(data.isDangerous),
      transportMode: formatString(data.transportMode),
      packingRequirement: formatString(data.packingRequirement),
      inspectionRequired: formatBoolean(data.inspectionRequired),
      licenseRequirement: formatString(data.licenseRequirement),
      
      // MRP计划视图字段
      planningStrategy: formatString(data.planningStrategy),
      planningCycle: formatString(data.planningCycle),
      forecastMethod: formatString(data.forecastMethod),
      abcCategory: formatString(data.abcCategory),
      demandSource: formatString(data.demandSource),
      planner: formatString(data.planner),
      safetyStockDays: formatString(data.safetyStockDays),
      lotSizeRule: formatString(data.lotSizeRule),
      demandTimeFence: formatString(data.demandTimeFence),
      supplyTimeFence: formatString(data.supplyTimeFence),
      planningNote: formatString(data.planningNote)
    }
    
    return request({
      url: `/api/models/parts/${id}`,
      method: 'put',
      data: requestData
    })
  },
  
  // 获取零件型号详情
  getDetail(id) {
    return request({
      url: `/api/models/parts/${id}`,
      method: 'get'
    })
  },
  
  // 获取零件型号列表
  getList(params) {
    return request({
      url: '/api/models/parts',
      method: 'get',
      params
    })
  },
  
  // 删除零件型号
  delete(id) {
    return request({
      url: `/api/models/parts/${id}`,
      method: 'delete'
    })
  }
} 

// 辅料包材型号专用API - 调用用户指定的后端API
export const auxiliaryModelApiNew = {
  // 创建辅料包材型号 - 调用指定的后端API
  create(data) {
    // 格式化数量类型字段为decimal(18,2)格式
    const formatDecimal = (value) => {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }
      // 转换为数字，然后格式化为2位小数
      const numValue = parseFloat(value) || 0
      return numValue.toFixed(2)
    }
    
    // 格式化字符串字段
    const formatString = (value) => {
      return value ? String(value).trim() : ''
    }
    
    // 格式化布尔值字段
    const formatBoolean = (value) => {
      if (value === true || value === 'true' || value === '是') {
        return true
      }
      if (value === false || value === 'false' || value === '否') {
        return false
      }
      // 默认返回false
      return false
    }
    
    const requestData = {
      // 基本信息视图字段
      name: formatString(data.name),
      code: formatString(data.code),
      category: formatString(data.category),
      specification: formatString(data.specification),
      status: formatString(data.status),
      image: formatString(data.image),
      unit: formatString(data.unit),
      material: formatString(data.material),
      size: formatString(data.size),
      
      // 销售信息视图字段
      salesPrice: formatDecimal(data.salesPrice),
      salesUnit: formatString(data.salesUnit),
      minOrderQty: formatDecimal(data.minOrderQty),
      maxOrderQty: formatDecimal(data.maxOrderQty),
      leadTime: formatString(data.leadTime),
      warranty: formatString(data.warranty),
      
      // 仓库信息视图字段
      storageLocation: formatString(data.storageLocation),
      safetyStock: formatDecimal(data.safetyStock),
      maxStock: formatDecimal(data.maxStock),
      reorderPoint: formatDecimal(data.reorderPoint),
      storageCondition: formatString(data.storageCondition),
      shelfLife: formatString(data.shelfLife),
      
      // 财务信息视图字段
      standardCost: formatDecimal(data.standardCost),
      averageCost: formatDecimal(data.averageCost),
      valuationMethod: formatString(data.valuationMethod),
      taxRate: formatDecimal(data.taxRate),
      accountSubject: formatString(data.accountSubject),
      costCenter: formatString(data.costCenter),
      
      // 生产信息视图字段
      productionType: formatString(data.productionType),
      productionLeadTime: formatString(data.productionLeadTime),
      setupTime: formatString(data.setupTime),
      cycleTime: formatString(data.cycleTime),
      batchSize: formatString(data.batchSize),
      workCenter: formatString(data.workCenter),
      qualityStandard: formatString(data.qualityStandard),
      
      // 采购信息视图字段
      supplier: formatString(data.supplier),
      purchasePrice: formatDecimal(data.purchasePrice),
      purchaseUnit: formatString(data.purchaseUnit),
      minPurchaseQty: formatDecimal(data.minPurchaseQty),
      purchaseLeadTime: formatString(data.purchaseLeadTime),
      qualityLevel: formatString(data.qualityLevel),
      purchaseNote: formatString(data.purchaseNote),
      
      // 进出口视图字段
      hsCode: formatString(data.hsCode),
      originCountry: formatString(data.originCountry),
      importTaxRate: formatDecimal(data.importTaxRate),
      exportTaxRefund: formatDecimal(data.exportTaxRefund),
      isDangerous: formatBoolean(data.isDangerous),
      transportMode: formatString(data.transportMode),
      packingRequirement: formatString(data.packingRequirement),
      inspectionRequired: formatBoolean(data.inspectionRequired),
      licenseRequirement: formatString(data.licenseRequirement),
      
      // MRP计划视图字段
      planningStrategy: formatString(data.planningStrategy),
      planningCycle: formatString(data.planningCycle),
      forecastMethod: formatString(data.forecastMethod),
      abcCategory: formatString(data.abcCategory),
      demandSource: formatString(data.demandSource),
      planner: formatString(data.planner),
      safetyStockDays: formatString(data.safetyStockDays),
      lotSizeRule: formatString(data.lotSizeRule),
      demandTimeFence: formatString(data.demandTimeFence),
      supplyTimeFence: formatString(data.supplyTimeFence),
      planningNote: formatString(data.planningNote)
    }
    
    return request({
      url: '/api/models/auxiliary',
      method: 'post',
      data: requestData
    })
  },
  
  // 更新辅料包材型号
  update(id, data) {
    // 格式化数量类型字段为decimal(18,2)格式
    const formatDecimal = (value) => {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }
      // 转换为数字，然后格式化为2位小数
      const numValue = parseFloat(value) || 0
      return numValue.toFixed(2)
    }
    
    // 格式化字符串字段
    const formatString = (value) => {
      return value ? String(value).trim() : ''
    }
    
    // 格式化布尔值字段
    const formatBoolean = (value) => {
      if (value === true || value === 'true' || value === '是') {
        return true
      }
      if (value === false || value === 'false' || value === '否') {
        return false
      }
      // 默认返回false
      return false
    }
    
    const requestData = {
      id,
      // 基本信息视图字段
      name: formatString(data.name),
      code: formatString(data.code),
      category: formatString(data.category),
      specification: formatString(data.specification),
      status: formatString(data.status),
      image: formatString(data.image),
      unit: formatString(data.unit),
      material: formatString(data.material),
      size: formatString(data.size),
      
      // 销售信息视图字段
      salesPrice: formatDecimal(data.salesPrice),
      salesUnit: formatString(data.salesUnit),
      minOrderQty: formatDecimal(data.minOrderQty),
      maxOrderQty: formatDecimal(data.maxOrderQty),
      leadTime: formatString(data.leadTime),
      warranty: formatString(data.warranty),
      
      // 仓库信息视图字段
      storageLocation: formatString(data.storageLocation),
      safetyStock: formatDecimal(data.safetyStock),
      maxStock: formatDecimal(data.maxStock),
      reorderPoint: formatDecimal(data.reorderPoint),
      storageCondition: formatString(data.storageCondition),
      shelfLife: formatString(data.shelfLife),
      
      // 财务信息视图字段
      standardCost: formatDecimal(data.standardCost),
      averageCost: formatDecimal(data.averageCost),
      valuationMethod: formatString(data.valuationMethod),
      taxRate: formatDecimal(data.taxRate),
      accountSubject: formatString(data.accountSubject),
      costCenter: formatString(data.costCenter),
      
      // 生产信息视图字段
      productionType: formatString(data.productionType),
      productionLeadTime: formatString(data.productionLeadTime),
      setupTime: formatString(data.setupTime),
      cycleTime: formatString(data.cycleTime),
      batchSize: formatString(data.batchSize),
      workCenter: formatString(data.workCenter),
      qualityStandard: formatString(data.qualityStandard),
      
      // 采购信息视图字段
      supplier: formatString(data.supplier),
      purchasePrice: formatDecimal(data.purchasePrice),
      purchaseUnit: formatString(data.purchaseUnit),
      minPurchaseQty: formatDecimal(data.minPurchaseQty),
      purchaseLeadTime: formatString(data.purchaseLeadTime),
      qualityLevel: formatString(data.qualityLevel),
      purchaseNote: formatString(data.purchaseNote),
      
      // 进出口视图字段
      hsCode: formatString(data.hsCode),
      originCountry: formatString(data.originCountry),
      importTaxRate: formatDecimal(data.importTaxRate),
      exportTaxRefund: formatDecimal(data.exportTaxRefund),
      isDangerous: formatBoolean(data.isDangerous),
      transportMode: formatString(data.transportMode),
      packingRequirement: formatString(data.packingRequirement),
      inspectionRequired: formatBoolean(data.inspectionRequired),
      licenseRequirement: formatString(data.licenseRequirement),
      
      // MRP计划视图字段
      planningStrategy: formatString(data.planningStrategy),
      planningCycle: formatString(data.planningCycle),
      forecastMethod: formatString(data.forecastMethod),
      abcCategory: formatString(data.abcCategory),
      demandSource: formatString(data.demandSource),
      planner: formatString(data.planner),
      safetyStockDays: formatString(data.safetyStockDays),
      lotSizeRule: formatString(data.lotSizeRule),
      demandTimeFence: formatString(data.demandTimeFence),
      supplyTimeFence: formatString(data.supplyTimeFence),
      planningNote: formatString(data.planningNote)
    }
    
    return request({
      url: `/api/models/auxiliary/${id}`,
      method: 'put',
      data: requestData
    })
  },
  
  // 获取辅料包材型号详情
  getDetail(id) {
    return request({
      url: `/api/models/auxiliary/${id}`,
      method: 'get'
    })
  },
  
  // 获取辅料包材型号列表
  getList(params) {
    return request({
      url: '/api/models/auxiliary',
      method: 'get',
      params
    })
  },
  
  // 删除辅料包材型号
  delete(id) {
    return request({
      url: `/api/models/auxiliary/${id}`,
      method: 'delete'
    })
  }
} 