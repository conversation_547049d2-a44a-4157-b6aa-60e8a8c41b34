-- 初始化工作中心设置测试数据
-- 清空现有数据（如果需要重新初始化）
-- DELETE FROM Equipment WHERE 1=1;
-- DELETE FROM WorkCenters WHERE 1=1;
-- DELETE FROM ProductionLines WHERE 1=1;

-- 插入产线数据
INSERT INTO ProductionLines (Name, Code, Description, IsActive, CreateTime, UpdateTime) 
VALUES 
    ('HW+装配1号线', 'HW001', '华为装配产线1号', 1, GETUTCDATE(), GETUTCDATE()),
    ('HW+装配2号线', 'HW002', '华为装配产线2号', 1, GETUTCDATE(), GETUTCDATE()),
    ('HW+测试1号线', 'HW003', '华为测试产线1号', 1, GETUTCDATE(), GETUTCDATE());

-- 获取产线ID用于后续插入
DECLARE @Line1Id INT, @Line2Id INT, @Line3Id INT;
SELECT @Line1Id = Id FROM ProductionLines WHERE Code = 'HW001';
SELECT @Line2Id = Id FROM ProductionLines WHERE Code = 'HW002';
SELECT @Line3Id = Id FROM ProductionLines WHERE Code = 'HW003';

-- 为第一条产线插入工作中心数据
INSERT INTO WorkCenters (Name, Code, Description, ProductionLineId, SortOrder, IsActive, CreateTime, UpdateTime)
VALUES
    ('动触头装配中心', '20910000', '动触头装配工作中心', @Line1Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('静触头装配中心', '20920000', '静触头装配工作中心', @Line1Id, 2, 1, GETUTCDATE(), GETUTCDATE()),
    ('框架装配中心', '20930000', '框架装配工作中心', @Line1Id, 3, 1, GETUTCDATE(), GETUTCDATE()),
    ('半头装配中心', '20940000', '半头装配工作中心', @Line1Id, 4, 1, GETUTCDATE(), GETUTCDATE()),
    ('Breaker面壳装配中心', '20950000', 'Breaker面壳装配工作中心', @Line1Id, 5, 1, GETUTCDATE(), GETUTCDATE()),
    ('Breaker机芯中心', '20960000', 'Breaker机芯工作中心', @Line1Id, 6, 1, GETUTCDATE(), GETUTCDATE()),
    ('Breaker底座装配中心', '20970000', 'Breaker底座装配工作中心', @Line1Id, 7, 1, GETUTCDATE(), GETUTCDATE()),
    ('HV高压装配中心', '20980000', 'HV高压装配工作中心', @Line1Id, 8, 1, GETUTCDATE(), GETUTCDATE()),
    ('LS型号装配中心', '20990000', 'LS型号装配工作中心', @Line1Id, 9, 1, GETUTCDATE(), GETUTCDATE()),
    ('PT感应中心', '20910100', 'PT感应工作中心', @Line1Id, 10, 1, GETUTCDATE(), GETUTCDATE()),
    ('静触头装配中心2', '20950500', '静触头装配工作中心2', @Line1Id, 11, 1, GETUTCDATE(), GETUTCDATE()),
    ('MCC芯部中心', '20960500', 'MCC芯部工作中心', @Line1Id, 12, 1, GETUTCDATE(), GETUTCDATE());

-- 获取工作中心ID
DECLARE @WorkCenter1Id INT, @WorkCenter2Id INT, @WorkCenter3Id INT, @WorkCenter4Id INT, @WorkCenter5Id INT, @WorkCenter6Id INT,
        @WorkCenter7Id INT, @WorkCenter8Id INT, @WorkCenter9Id INT, @WorkCenter10Id INT, @WorkCenter11Id INT, @WorkCenter12Id INT;

SELECT @WorkCenter1Id = Id FROM WorkCenters WHERE Code = '20910000';
SELECT @WorkCenter2Id = Id FROM WorkCenters WHERE Code = '20920000';
SELECT @WorkCenter3Id = Id FROM WorkCenters WHERE Code = '20930000';
SELECT @WorkCenter4Id = Id FROM WorkCenters WHERE Code = '20940000';
SELECT @WorkCenter5Id = Id FROM WorkCenters WHERE Code = '20950000';
SELECT @WorkCenter6Id = Id FROM WorkCenters WHERE Code = '20960000';
SELECT @WorkCenter7Id = Id FROM WorkCenters WHERE Code = '20970000';
SELECT @WorkCenter8Id = Id FROM WorkCenters WHERE Code = '20980000';
SELECT @WorkCenter9Id = Id FROM WorkCenters WHERE Code = '20990000';
SELECT @WorkCenter10Id = Id FROM WorkCenters WHERE Code = '20910100';
SELECT @WorkCenter11Id = Id FROM WorkCenters WHERE Code = '20950500';
SELECT @WorkCenter12Id = Id FROM WorkCenters WHERE Code = '20960500';

-- 插入设备数据
INSERT INTO Equipment (Name, Code, Description, WorkCenterId, SortOrder, IsActive, CreateTime, UpdateTime)
VALUES
    -- 动触头装配中心设备
    ('条码扫', '20910010', '动触头装配中心条码扫描设备', @WorkCenter1Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('作业显示器', '20910020', '动触头装配中心作业显示器', @WorkCenter1Id, 2, 1, GETUTCDATE(), GETUTCDATE()),
    ('测试机', '20910030', '动触头装配中心测试机', @WorkCenter1Id, 3, 1, GETUTCDATE(), GETUTCDATE()),
    
    -- 静触头装配中心设备
    ('作业显示器', '20920010', '静触头装配中心作业显示器', @WorkCenter2Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('点胶机', '20920020', '静触头装配中心点胶机', @WorkCenter2Id, 2, 1, GETUTCDATE(), GETUTCDATE()),
    ('电机', '20920030', '静触头装配中心电机', @WorkCenter2Id, 3, 1, GETUTCDATE(), GETUTCDATE()),
    
    -- 框架装配中心设备
    ('条码扫', '20930010', '框架装配中心条码扫描设备', @WorkCenter3Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('作业显示器', '20930030', '框架装配中心作业显示器', @WorkCenter3Id, 2, 1, GETUTCDATE(), GETUTCDATE()),
    
    -- 半头装配中心设备
    ('作业显示器', '20940010', '半头装配中心作业显示器', @WorkCenter4Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('测试机', '20940020', '半头装配中心测试机', @WorkCenter4Id, 2, 1, GETUTCDATE(), GETUTCDATE()),
    
    -- Breaker面壳装配中心设备
    ('条码扫', '20950010', 'Breaker面壳装配中心条码扫描设备', @WorkCenter5Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('打印机', '20950020', 'Breaker面壳装配中心打印机', @WorkCenter5Id, 2, 1, GETUTCDATE(), GETUTCDATE()),
    ('作业显示器', '20950030', 'Breaker面壳装配中心作业显示器', @WorkCenter5Id, 3, 1, GETUTCDATE(), GETUTCDATE()),
    
    -- Breaker机芯中心设备
    ('数字工位', '20960010', 'Breaker机芯中心数字工位', @WorkCenter6Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('数字设备', '20960020', 'Breaker机芯中心数字设备', @WorkCenter6Id, 2, 1, GETUTCDATE(), GETUTCDATE()),
    
    -- Breaker底座装配中心设备
    ('条码扫', '20970010', 'Breaker底座装配中心条码扫描设备', @WorkCenter7Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('作业显示器', '20970030', 'Breaker底座装配中心作业显示器', @WorkCenter7Id, 2, 1, GETUTCDATE(), GETUTCDATE()),
    ('点胶机', '20970040', 'Breaker底座装配中心点胶机', @WorkCenter7Id, 3, 1, GETUTCDATE(), GETUTCDATE()),
    ('电机', '20970050', 'Breaker底座装配中心电机', @WorkCenter7Id, 4, 1, GETUTCDATE(), GETUTCDATE()),
    
    -- HV高压装配中心设备
    ('数字工位', '20980010', 'HV高压装配中心数字工位', @WorkCenter8Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('数字设备', '20980020', 'HV高压装配中心数字设备', @WorkCenter8Id, 2, 1, GETUTCDATE(), GETUTCDATE()),
    
    -- LS型号装配中心设备
    ('条码扫', '20990010', 'LS型号装配中心条码扫描设备', @WorkCenter9Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('作业显示器', '20990020', 'LS型号装配中心作业显示器', @WorkCenter9Id, 2, 1, GETUTCDATE(), GETUTCDATE()),
    
    -- PT感应中心设备
    ('感应机', '20910110', 'PT感应中心感应机', @WorkCenter10Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('点胶机', '20910120', 'PT感应中心点胶机', @WorkCenter10Id, 2, 1, GETUTCDATE(), GETUTCDATE()),
    
    -- 静触头装配中心2设备
    ('数字工位', '20950510', '静触头装配中心2数字工位', @WorkCenter11Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('数字设备', '20950520', '静触头装配中心2数字设备', @WorkCenter11Id, 2, 1, GETUTCDATE(), GETUTCDATE()),
    
    -- MCC芯部中心设备
    ('条码扫', '20960510', 'MCC芯部中心条码扫描设备', @WorkCenter12Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('作业显示器', '20960520', 'MCC芯部中心作业显示器', @WorkCenter12Id, 2, 1, GETUTCDATE(), GETUTCDATE());

-- 为第二条产线插入一些示例工作中心
INSERT INTO WorkCenters (Name, Code, Description, ProductionLineId, SortOrder, IsActive, CreateTime, UpdateTime)
VALUES
    ('终端装配中心', '30910000', '终端装配工作中心', @Line2Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('测试验证中心', '30920000', '测试验证工作中心', @Line2Id, 2, 1, GETUTCDATE(), GETUTCDATE());

-- 获取第二条产线工作中心ID
DECLARE @Line2WorkCenter1Id INT, @Line2WorkCenter2Id INT;
SELECT @Line2WorkCenter1Id = Id FROM WorkCenters WHERE Code = '30910000';
SELECT @Line2WorkCenter2Id = Id FROM WorkCenters WHERE Code = '30920000';

-- 为第二条产线的工作中心插入设备
INSERT INTO Equipment (Name, Code, Description, WorkCenterId, SortOrder, IsActive, CreateTime, UpdateTime)
VALUES
    ('自动装配机', '30910010', '终端装配中心自动装配机', @Line2WorkCenter1Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('质检设备', '30910020', '终端装配中心质检设备', @Line2WorkCenter1Id, 2, 1, GETUTCDATE(), GETUTCDATE()),
    ('综合测试仪', '30920010', '测试验证中心综合测试仪', @Line2WorkCenter2Id, 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('数据采集器', '30920020', '测试验证中心数据采集器', @Line2WorkCenter2Id, 2, 1, GETUTCDATE(), GETUTCDATE());

PRINT '工作中心设置初始化数据插入完成！';

-- 查询验证数据
SELECT 
    pl.Name AS ProductionLineName,
    pl.Code AS ProductionLineCode,
    COUNT(DISTINCT wc.Id) AS WorkCenterCount,
    COUNT(eq.Id) AS EquipmentCount
FROM ProductionLines pl
LEFT JOIN WorkCenters wc ON pl.Id = wc.ProductionLineId AND wc.IsActive = 1
LEFT JOIN Equipment eq ON wc.Id = eq.WorkCenterId AND eq.IsActive = 1
WHERE pl.IsActive = 1
GROUP BY pl.Id, pl.Name, pl.Code
ORDER BY pl.Id; 