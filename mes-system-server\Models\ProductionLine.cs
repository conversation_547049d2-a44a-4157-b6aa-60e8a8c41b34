using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace mes_system_server.Models
{
    public class ProductionLine
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Code { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreateTime { get; set; } = DateTime.UtcNow;

        public DateTime UpdateTime { get; set; } = DateTime.UtcNow;

        // 导航属性
        public virtual ICollection<WorkCenter> WorkCenters { get; set; } = new List<WorkCenter>();
    }
} 