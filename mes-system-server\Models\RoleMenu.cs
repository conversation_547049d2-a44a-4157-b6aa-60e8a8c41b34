using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace mes_system_server.Models
{
    public class RoleMenu
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int RoleId { get; set; }

        [Required]
        public int MenuId { get; set; }

        public DateTime CreateTime { get; set; } = DateTime.Now;

        // 导航属性
        [ForeignKey("RoleId")]
        public virtual Role Role { get; set; } = null!;

        [ForeignKey("MenuId")]
        public virtual Menu Menu { get; set; } = null!;
    }
} 