# 安装和部署指南

本指南详细介绍如何安装、配置和部署MES智能制造执行系统。

## 📋 系统要求

### 硬件要求
- **CPU**: 双核2.0GHz或更高
- **内存**: 4GB RAM（推荐8GB）
- **存储**: 至少10GB可用空间
- **网络**: 稳定的网络连接

### 软件要求

#### 开发环境
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Node.js**: >= 16.0.0
- **.NET**: >= 8.0
- **数据库**: SQL Server 2017+ 或 MySQL 8.0+
- **Git**: 最新版本

#### 生产环境
- **Web服务器**: IIS, Nginx, Apache
- **应用服务器**: .NET Core Runtime
- **数据库服务器**: SQL Server 或 MySQL
- **反向代理**: Nginx (推荐)

## 🚀 快速安装

### 1. 获取源代码
```bash
# 克隆项目
git clone [项目地址]
cd AMPER

# 或下载发布包
wget [发布包地址]
unzip mes-system-v1.0.0.zip
```

### 2. 前端安装
```bash
# 进入前端目录
cd mes-system

# 安装Node.js依赖
npm install
# 或使用pnpm (推荐)
pnpm install

# 构建生产版本
npm run build
```

### 3. 后端安装
```bash
# 进入后端目录
cd mes-system-server

# 还原NuGet包
dotnet restore

# 配置数据库连接字符串
# 编辑 appsettings.json

# 运行数据库迁移
dotnet ef database update

# 构建项目
dotnet build --configuration Release
```

## ⚙️ 详细配置

### 前端配置

#### 1. 环境变量配置
创建 `.env.production` 文件：
```bash
# API基础地址
VITE_API_BASE_URL=https://your-api-domain.com

# 应用标题
VITE_APP_TITLE=MES智能制造执行系统

# 是否启用Mock数据
VITE_ENABLE_MOCK=false
```

#### 2. 构建配置
修改 `vite.config.js`：
```javascript
export default defineConfig({
  base: '/', // 部署路径
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false, // 生产环境关闭sourcemap
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]'
      }
    }
  }
})
```

### 后端配置

#### 1. 数据库配置
编辑 `appsettings.json`：
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=MESSystem;User Id=sa;Password=YourPassword;TrustServerCertificate=true;"
  },
  "JWT": {
    "SecretKey": "your-secret-key-here",
    "Issuer": "MESSystem",
    "Audience": "MESSystem",
    "ExpiryHours": 24
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

#### 2. CORS配置
```csharp
// Program.cs
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.WithOrigins("https://your-frontend-domain.com")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});
```

## 🐳 Docker部署

### 1. 前端Docker化
创建 `Dockerfile` (前端)：
```dockerfile
# 构建阶段
FROM node:18-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 2. 后端Docker化
创建 `Dockerfile` (后端)：
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["MESSystem.csproj", "."]
RUN dotnet restore
COPY . .
RUN dotnet build -c Release -o /app/build

FROM build AS publish
RUN dotnet publish -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "MESSystem.dll"]
```

### 3. Docker Compose配置
创建 `docker-compose.yml`：
```yaml
version: '3.8'
services:
  frontend:
    build:
      context: ./mes-system
      dockerfile: Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend

  backend:
    build:
      context: ./mes-system-server
      dockerfile: Dockerfile
    ports:
      - "5000:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
    depends_on:
      - database

  database:
    image: mcr.microsoft.com/mssql/server:2019-latest
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Password
    ports:
      - "1433:1433"
    volumes:
      - sqldata:/var/opt/mssql

volumes:
  sqldata:
```

## 🌐 Web服务器配置

### Nginx配置
创建 `nginx.conf`：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /usr/share/nginx/html;
    index index.html;

    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://backend:5000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
```

### IIS配置
创建 `web.config`：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="Angular Routes" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/index.html" />
        </rule>
      </rules>
    </rewrite>
    <staticContent>
      <mimeMap fileExtension=".json" mimeType="application/json" />
    </staticContent>
  </system.webServer>
</configuration>
```

## 📊 数据库部署

### SQL Server部署
```sql
-- 创建数据库
CREATE DATABASE MESSystem;
GO

-- 创建用户
CREATE LOGIN mesuser WITH PASSWORD = 'YourPassword';
GO

USE MESSystem;
GO

CREATE USER mesuser FOR LOGIN mesuser;
GO

-- 授予权限
ALTER ROLE db_owner ADD MEMBER mesuser;
GO
```

### MySQL部署
```sql
-- 创建数据库
CREATE DATABASE mes_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'mesuser'@'%' IDENTIFIED BY 'YourPassword';

-- 授予权限
GRANT ALL PRIVILEGES ON mes_system.* TO 'mesuser'@'%';
FLUSH PRIVILEGES;
```

## 🔧 生产环境优化

### 1. 性能优化
- 启用Gzip压缩
- 配置静态资源缓存
- 使用CDN加速
- 数据库索引优化

### 2. 安全配置
- 配置HTTPS证书
- 设置CSP策略
- 启用安全头
- 定期更新依赖

### 3. 监控配置
- 配置日志收集
- 设置性能监控
- 配置错误报告
- 设置健康检查

## 🔍 部署验证

### 1. 功能验证
- [ ] 登录功能正常
- [ ] 菜单加载正常
- [ ] API接口调用成功
- [ ] 数据库连接正常

### 2. 性能验证
- [ ] 页面加载时间 < 3秒
- [ ] API响应时间 < 1秒
- [ ] 并发用户支持测试
- [ ] 资源使用率监控

### 3. 安全验证
- [ ] HTTPS配置正确
- [ ] 身份验证正常
- [ ] 权限控制有效
- [ ] 敏感信息保护

## 📞 技术支持

如果在安装部署过程中遇到问题：

1. **查看日志**: 检查应用程序和Web服务器日志
2. **常见问题**: 参考[FAQ文档](./faq.md)
3. **技术支持**: 发送邮件至 <EMAIL>
4. **在线文档**: 访问[项目文档](./README.md)

---

*最后更新时间：2024-01-15* 