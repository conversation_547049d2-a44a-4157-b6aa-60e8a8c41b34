{"ConnectionStrings": {"DefaultConnection": "Server=***************;Database=MesSystem;User Id=sa;Password=***********;TrustServerCertificate=True;Encrypt=False"}, "Jwt": {"Key": "your-production-secure-key-here-minimum-32-characters", "Issuer": "mes-system-api", "Audience": "mes-system-client", "ExpirationDays": 7}, "Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning"}, "File": {"Path": ".\\Logs\\api-.log", "FileSizeLimitBytes": 5242880, "RetainedFileCountLimit": 30}}, "AllowedHosts": "*", "Cors": {"AllowedOrigins": ["https://localhost:5221", "http://localhost:5221", "http://localhost:5173", "http://***************:5221"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE"], "AllowedHeaders": ["Content-Type", "Authorization"], "ExposedHeaders": ["X-Total-Count", "X-Total-Pages"]}, "Security": {"RequireHttps": true, "HstsEnabled": true, "HstsMaxAge": 30}, "Swagger": {"Enabled": true, "Title": "MES System API", "Version": "v1", "Description": "MES System Production API Documentation", "Contact": {"Name": "MES System Team", "Email": "<EMAIL>"}, "License": {"Name": "Private License", "Url": "https://your-domain.com/license"}}}