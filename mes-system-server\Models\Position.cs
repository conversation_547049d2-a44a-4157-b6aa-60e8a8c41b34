using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace mes_system_server.Models
{
    public class Position
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string Name { get; set; }

        [Required]
        public int DepartmentId { get; set; }

        [ForeignKey("DepartmentId")]
        public Department Department { get; set; }

        [StringLength(10)]
        public string Level { get; set; } = "1"; // 1=初级, 2=中级, 3=高级, 4=专家

        [StringLength(500)]
        public string Description { get; set; } = "";

        public DateTime CreateTime { get; set; } = DateTime.Now;
        
        public DateTime? UpdateTime { get; set; }
    }
} 