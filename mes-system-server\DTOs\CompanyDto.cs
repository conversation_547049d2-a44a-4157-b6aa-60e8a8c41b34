using System;
using System.ComponentModel.DataAnnotations;

namespace mes_system_server.DTOs
{
    public class CompanyDto
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "公司名称不能为空")]
        [StringLength(100, ErrorMessage = "公司名称最长为100个字符")]
        public string Name { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "统一社会信用代码最长为50个字符")]
        public string CreditCode { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "法定代表人最长为50个字符")]
        public string LegalRepresentative { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "注册资本最长为100个字符")]
        public string RegisteredCapital { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "公司地址最长为200个字符")]
        public string Address { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "联系电话最长为20个字符")]
        public string Phone { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "电子邮箱最长为100个字符")]
        [EmailAddress(ErrorMessage = "电子邮箱格式不正确")]
        public string Email { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "公司类型最长为50个字符")]
        public string CompanyType { get; set; } = string.Empty;

        public DateTime? RegistrationDate { get; set; }

        [StringLength(500, ErrorMessage = "公司描述最长为500个字符")]
        public string Description { get; set; } = string.Empty;
    }

    public class UpdateCompanyDto
    {
        [Required(ErrorMessage = "公司名称不能为空")]
        [StringLength(100, ErrorMessage = "公司名称最长为100个字符")]
        public string Name { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "统一社会信用代码最长为50个字符")]
        public string CreditCode { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "法定代表人最长为50个字符")]
        public string LegalRepresentative { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "注册资本最长为100个字符")]
        public string RegisteredCapital { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "公司地址最长为200个字符")]
        public string Address { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "联系电话最长为20个字符")]
        public string Phone { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "电子邮箱最长为100个字符")]
        [EmailAddress(ErrorMessage = "电子邮箱格式不正确")]
        public string Email { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "公司类型最长为50个字符")]
        public string CompanyType { get; set; } = string.Empty;

        public DateTime? RegistrationDate { get; set; }

        [StringLength(500, ErrorMessage = "公司描述最长为500个字符")]
        public string Description { get; set; } = string.Empty;
    }
} 