﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace mes_system_server.Migrations
{
    /// <inheritdoc />
    public partial class AddRoleEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "RoleId",
                table: "Users",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Roles",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RoleName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    RoleCode = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Status = table.Column<bool>(type: "bit", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Roles", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1778), new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1778) });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1789));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1797));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1802));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1806));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1809));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1813));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1817));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1821));

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1854), new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1854) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1863), new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1864) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1901), new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1901) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1908), new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1909) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1916), new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1917) });

            migrationBuilder.InsertData(
                table: "Roles",
                columns: new[] { "Id", "CreateTime", "Description", "RoleCode", "RoleName", "Status", "UpdateTime" },
                values: new object[,]
                {
                    { 1, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "系统管理员，拥有所有权限", "admin", "系统管理员", true, null },
                    { 2, new DateTime(2024, 1, 2, 0, 0, 0, 0, DateTimeKind.Unspecified), "负责生产管理相关工作", "production_manager", "生产经理", true, null },
                    { 3, new DateTime(2024, 1, 3, 0, 0, 0, 0, DateTimeKind.Unspecified), "负责产品质量检验工作", "quality_inspector", "质量检验员", true, null }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Users_RoleId",
                table: "Users",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_Roles_RoleCode",
                table: "Roles",
                column: "RoleCode",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Users_Roles_RoleId",
                table: "Users",
                column: "RoleId",
                principalTable: "Roles",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Users_Roles_RoleId",
                table: "Users");

            migrationBuilder.DropTable(
                name: "Roles");

            migrationBuilder.DropIndex(
                name: "IX_Users_RoleId",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "RoleId",
                table: "Users");

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3222), new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3222) });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3329));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3437));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3444));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3449));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3454));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3459));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3510));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3516));

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3569), new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3570) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3581), new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3582) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3591), new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3592) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3601), new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3601) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3610), new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3611) });
        }
    }
}
