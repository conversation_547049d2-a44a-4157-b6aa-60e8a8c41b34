# 成品型号API集成说明

## 概述

本文档描述了前端物料主数据模块中成品型号保存功能的实现，该功能集成了用户指定的后端API接口。

## API集成详情

### 目标API
- **URL**: `http://111.230.239.197:5221/api/models/products`
- **方法**: POST
- **内容类型**: application/json

### 关键参数设置
根据业务需求，以下参数会被自动设置：
- `type`: 固定为 `"products"`
- `category`: 固定为 `"REF_FG"`
- `status`: 固定为 `"True"`

### 使用方法

1. **进入物料主数据模块**
   - 导航到 `/home/<USER>/material-management`
   - 选择"成品型号"标签页

2. **新增成品型号**
   - 点击"新增型号"按钮
   - 在弹出的对话框中填写相关信息
   - 支持8个视图的数据录入：
     - 基本信息
     - 销售视图
     - 仓库视图
     - 财务视图
     - 生产视图
     - 采购视图
     - 进出口视图
     - MRP计划视图

3. **保存数据**
   - 点击"保存"按钮
   - 系统会自动调用指定的后端API
   - 成功保存后显示"成品型号新增成功"提示

## 实现细节

### 前端文件修改

1. **API文件** (`mes-system/src/api/model.js`)
   - 添加了 `productModelApiNew` 对象
   - 包含完整的数据字段映射和类型转换

2. **组件文件** (`mes-system/src/components/MaterialModelTab.vue`)
   - 导入了新的API函数
   - 修改了 `handleSave` 方法，针对成品型号使用专用API

### 数据格式处理

系统会自动处理以下数据转换：
- 价格字段：转换为浮点数
- 数量字段：转换为整数
- 布尔字段：转换为布尔值
- 空值处理：提供合理的默认值

### 请求数据示例

```json
{
  "name": "示例成品",
  "code": "FG001",
  "type": "products",
  "category": "REF_FG",
  "status": "True",
  "specification": "产品规格说明",
  "unit": "个",
  "material": "材质信息",
  "size": "尺寸规格",
  "salesPrice": 100.0,
  "salesUnit": "个",
  "minOrderQty": 1,
  "maxOrderQty": 1000,
  "leadTime": "7天",
  "storageLocation": "A01",
  "safetyStock": 100,
  "maxStock": 1000,
  "reorderPoint": 200,
  "standardCost": 80.0,
  "averageCost": 85.0,
  "supplier": "供应商名称",
  "purchasePrice": 90.0,
  "hsCode": "1234567890",
  "importTaxRate": 0.13,
  "exportTaxRefund": 0.10,
  "isDangerous": false,
  "inspectionRequired": true,
  "planningStrategy": "按需生产",
  "planningCycle": "月度",
  "abcCategory": "A",
  "demandSource": "客户订单",
  "planner": "计划员姓名"
}
```

## 注意事项

1. **网络配置**
   - 确保前端可以访问指定的API地址
   - 检查跨域设置和防火墙配置

2. **数据验证**
   - 必填字段：名称、编码、分类、规格参数
   - 系统会自动验证表单数据

3. **错误处理**
   - API调用失败时会显示详细错误信息
   - 支持网络超时和服务器错误的处理

4. **日志记录**
   - 保存操作会在浏览器控制台中记录详细日志
   - 便于调试和问题排查

## 测试建议

1. **功能测试**
   - 验证所有视图数据是否正确传输
   - 确认API响应处理正确

2. **边界测试**
   - 测试必填字段验证
   - 测试特殊字符和长文本

3. **网络测试**
   - 测试网络中断情况
   - 测试API服务不可用情况

## 支持

如果在使用过程中遇到问题，请检查：
1. 网络连接是否正常
2. API服务是否可用
3. 浏览器控制台是否有错误信息
4. 数据格式是否符合要求 