import request from '@/utils/request'
import { mockDepartments } from '@/utils/mockData'

// 获取部门列表
export function getDepartments(params) {
  // 使用实际API调用
  return request({
    url: '/api/departments',
    method: 'get',
    params
  })
}

// 获取部门详情
export function getDepartment(id) {
  return request({
    url: `/api/departments/${id}`,
    method: 'get'
  })
}

// 创建部门
export function createDepartment(data) {
  return request({
    url: '/api/departments',
    method: 'post',
    data
  })
}

// 更新部门
export function updateDepartment(id, data) {
  return request({
    url: `/api/departments/${id}`,
    method: 'put',
    data
  })
}

// 删除部门
export function deleteDepartment(id) {
  return request({
    url: `/api/departments/${id}`,
    method: 'delete'
  })
} 