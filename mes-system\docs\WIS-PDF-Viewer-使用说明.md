# WIS PDF查看器使用说明

## 概述

WIS PDF查看器是一个功能强大的PDF文件处理和查看工具，支持PDF文件上传、书签提取、页面转图片以及在线预览等功能。

## 功能特性

### 🔧 核心功能
- **PDF文件上传**：支持拖拽或点击上传PDF文件（最大50MB）
- **书签提取**：自动提取PDF文件中的书签信息，如无书签则自动生成页面书签
- **页面转图片**：将PDF每一页转换为高质量JPEG图片
- **在线预览**：左侧书签列表，右侧图片预览，点击书签即可查看对应页面
- **图片缩放**：支持50%-300%的图片缩放功能
- **图片下载**：支持单页图片下载功能

### 🎨 界面特性
- **响应式设计**：适配不同屏幕尺寸
- **进度提示**：详细的处理进度显示
- **错误处理**：完善的错误提示和处理机制
- **现代化UI**：基于Element Plus的现代化界面设计

## 使用方法

### 1. 访问页面
在左侧导航菜单中点击 **工程管理** → **WIS管理** 进入页面。

### 2. 上传PDF文件
- 点击页面中的"上传PDF文件"按钮
- 选择本地PDF文件（支持.pdf格式，最大50MB）
- 系统会自动处理PDF文件并显示进度

### 3. 查看PDF内容
- 上传完成后，左侧会显示PDF的书签列表
- 点击任意书签，右侧会显示对应页面的图片
- 使用缩放控件调整图片大小
- 点击"下载图片"按钮可下载当前页面

### 4. 管理PDF文件
- 点击"清除PDF"按钮可清除当前加载的PDF文件
- 支持重新上传新的PDF文件

## 技术实现

### 前端技术栈
- **Vue 3**：响应式框架
- **Element Plus**：UI组件库
- **PDF.js**：PDF文件解析和渲染
- **Canvas API**：图片转换和处理

### 核心流程
1. **文件上传**：使用HTML5 File API读取PDF文件
2. **PDF解析**：通过PDF.js解析PDF文档结构
3. **书签提取**：获取PDF outline信息或生成默认书签
4. **页面渲染**：使用Canvas将PDF页面渲染为图片
5. **图片存储**：将图片以Base64格式存储在sessionStorage中
6. **交互展示**：通过Vue响应式数据实现书签和图片的联动

### 数据存储
- **临时存储**：使用sessionStorage存储转换后的图片数据
- **会话级别**：数据在浏览器会话期间有效，关闭页面后自动清除
- **扩展性**：预留了服务器存储的API接口，可轻松扩展为持久化存储

## API接口

### 前端API（预留）
```javascript
// 上传PDF图片到服务器
uploadPDFImage(formData)

// 获取PDF图片
getPDFImage(pageNumber, pdfName)

// 删除PDF相关图片
deletePDFImages(pdfName)

// 获取PDF文件列表
getPDFList()
```

### 后端扩展
如需实现服务器端存储，可以：
1. 创建文件上传接口
2. 实现图片存储服务
3. 添加文件管理功能
4. 支持多用户文件隔离

## 配置说明

### 文件限制
- **文件格式**：仅支持PDF格式（.pdf）
- **文件大小**：最大50MB
- **页面数量**：理论上无限制，但建议不超过500页以保证性能

### 图片质量
- **渲染比例**：2.0倍缩放（提高清晰度）
- **图片格式**：JPEG格式
- **压缩质量**：90%（平衡质量和大小）

### 性能优化
- **懒加载**：仅在点击书签时加载对应图片
- **内存管理**：组件卸载时自动清理PDF文档对象
- **进度提示**：实时显示处理进度，提升用户体验

## 故障排除

### 常见问题

#### 1. PDF文件无法上传
- **检查文件格式**：确保文件是.pdf格式
- **检查文件大小**：确保文件不超过50MB
- **检查网络连接**：确保能正常访问CDN资源

#### 2. PDF处理失败
- **文件损坏**：尝试使用其他PDF文件
- **浏览器兼容性**：建议使用Chrome、Firefox等现代浏览器
- **内存不足**：尝试处理较小的PDF文件

#### 3. 图片显示异常
- **清除缓存**：刷新页面或清除浏览器缓存
- **重新上传**：清除当前PDF后重新上传
- **检查控制台**：查看浏览器控制台的错误信息

#### 4. 性能问题
- **大文件处理慢**：这是正常现象，请耐心等待
- **内存占用高**：处理完成后会自动释放内存
- **页面卡顿**：建议处理较小的PDF文件

### 浏览器兼容性
- **Chrome 80+**：完全支持
- **Firefox 75+**：完全支持
- **Safari 13+**：完全支持
- **Edge 80+**：完全支持

## 更新日志

### v1.0.0 (2024-01-01)
- ✨ 初始版本发布
- 🔧 支持PDF文件上传和解析
- 📖 支持书签提取和显示
- 🖼️ 支持页面转图片功能
- 🔍 支持图片缩放和下载
- 📱 响应式界面设计

## 技术支持

如遇到问题或需要技术支持，请：
1. 检查浏览器控制台错误信息
2. 确认文件格式和大小符合要求
3. 尝试使用不同的PDF文件测试
4. 联系系统管理员获取帮助

## 开发计划

### 后续版本规划
- 🔄 支持服务器端图片存储
- 👥 支持多用户文件管理
- 🔍 支持PDF文本搜索功能
- 📝 支持PDF注释和标记
- 🔗 支持PDF文件分享功能
- 📊 支持批量PDF处理 