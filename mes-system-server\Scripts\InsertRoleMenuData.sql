-- 插入角色菜单权限数据
-- 确保角色和菜单表已经存在数据

-- 先删除现有的角色菜单关联数据（如果有的话）
DELETE FROM RoleMenus;

-- 系统管理员 (RoleId = 1) - 拥有所有权限
INSERT INTO RoleMenus (RoleId, MenuId, CreateTime) VALUES 
(1, 8, '2024-01-01 00:00:00'),   -- 入库管理
(1, 9, '2024-01-01 00:00:00'),   -- 出库管理
(1, 10, '2024-01-01 00:00:00'),  -- 库存调拨
(1, 11, '2024-01-01 00:00:00'),  -- 库存盘点
(1, 12, '2024-01-01 00:00:00'),  -- 生产计划
(1, 13, '2024-01-01 00:00:00'),  -- 生产订单
(1, 14, '2024-01-01 00:00:00'),  -- 排产管理
(1, 15, '2024-01-01 00:00:00'),  -- 工艺管理
(1, 16, '2024-01-01 00:00:00'),  -- BOM管理
(1, 17, '2024-01-01 00:00:00'),  -- 型号管理
(1, 18, '2024-01-01 00:00:00'),  -- 生产监控
(1, 19, '2024-01-01 00:00:00'),  -- 生产任务
(1, 20, '2024-01-01 00:00:00'),  -- 质量检验
(1, 21, '2024-01-01 00:00:00'),  -- 质量报告
(1, 22, '2024-01-01 00:00:00'),  -- 设备台账
(1, 23, '2024-01-01 00:00:00'),  -- 用户管理
(1, 24, '2024-01-01 00:00:00'),  -- 角色权限
(1, 25, '2024-01-01 00:00:00');  -- 菜单管理

-- 生产经理 (RoleId = 2) - 生产相关权限
INSERT INTO RoleMenus (RoleId, MenuId, CreateTime) VALUES 
(2, 12, '2024-01-01 00:00:00'),  -- 生产计划
(2, 13, '2024-01-01 00:00:00'),  -- 生产订单
(2, 14, '2024-01-01 00:00:00'),  -- 排产管理
(2, 18, '2024-01-01 00:00:00'),  -- 生产监控
(2, 19, '2024-01-01 00:00:00'),  -- 生产任务
(2, 20, '2024-01-01 00:00:00');  -- 质量检验

-- 质量检验员 (RoleId = 3) - 质量相关权限
INSERT INTO RoleMenus (RoleId, MenuId, CreateTime) VALUES 
(3, 20, '2024-01-01 00:00:00'),  -- 质量检验
(3, 21, '2024-01-01 00:00:00');  -- 质量报告

-- 查询插入结果
SELECT 
    rm.Id,
    r.RoleName,
    m.MenuId,
    m.Name as MenuName,
    rm.CreateTime
FROM RoleMenus rm
INNER JOIN Roles r ON rm.RoleId = r.Id
INNER JOIN Menus m ON rm.MenuId = m.Id
ORDER BY rm.RoleId, m.SortOrder; 