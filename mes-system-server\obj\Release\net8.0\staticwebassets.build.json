{"Version": 1, "Hash": "o78LrcB6FyR+yaaRelbWMUUIGUeGfuCRoyQrcu3mQJ0=", "Source": "mes-system-server", "BasePath": "_content/mes-system-server", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "mes-system-server\\wwwroot", "Source": "mes-system-server", "ContentRoot": "D:\\开发\\For Cursor\\AMPER\\mes-system-server\\wwwroot\\", "BasePath": "_content/mes-system-server", "Pattern": "**"}], "Assets": [{"Identity": "D:\\开发\\For Cursor\\AMPER\\mes-system-server\\wwwroot\\uploads\\wis-pdf-images\\README.md", "SourceId": "mes-system-server", "SourceType": "Discovered", "ContentRoot": "D:\\开发\\For Cursor\\AMPER\\mes-system-server\\wwwroot\\", "BasePath": "_content/mes-system-server", "RelativePath": "uploads/wis-pdf-images/README.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\wis-pdf-images\\README.md"}]}