using System;
using System.ComponentModel.DataAnnotations;

namespace mes_system_server.Models
{
    public class Company
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(50)]
        public string CreditCode { get; set; } = string.Empty;

        [StringLength(50)]
        public string LegalRepresentative { get; set; } = string.Empty;

        [StringLength(100)]
        public string RegisteredCapital { get; set; } = string.Empty;

        [StringLength(200)]
        public string Address { get; set; } = string.Empty;

        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        [StringLength(100)]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [StringLength(50)]
        public string CompanyType { get; set; } = string.Empty;

        public DateTime? RegistrationDate { get; set; }

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
} 