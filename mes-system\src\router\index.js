import { createRouter, createWebHistory } from 'vue-router'
import { isUserLoggedIn } from '@/utils/permission'
import Login from '../views/Login.vue'
import Home from '../views/Home.vue'
import ModelManagement from '../views/engineering/ModelManagement.vue'
import UserManagement from '../views/system/UserManagement.vue'
import RolePermission from '../views/system/RolePermission.vue'
import MenuManagement from '../views/system/MenuManagement.vue'
import DataDictionary from '../views/system/DataDictionary.vue'
import PageTemplateDiagnosis from '../views/system/PageTemplateDiagnosis.vue'
import I18nManagement from '../views/system/I18nManagement.vue'
import EmployeeManagement from '../views/hr/EmployeeManagement.vue'
import AttendanceManagement from '../views/hr/AttendanceManagement.vue'
import SalaryManagement from '../views/hr/SalaryManagement.vue'
import CompanySettings from '../views/hr/CompanySettings.vue'
import PermissionTest from '../views/system/PermissionTest.vue'
import PageTemplateDemo from '../views/example/PageTemplateDemo.vue'
import WisPDFViewer from '../views/wis/WisPDFViewer.vue'
import WisQuery from '../views/wis/WisQuery.vue'
import WorkCenterSettings from '../views/production/WorkCenterSettings.vue'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/home',
    name: 'Home',
    component: Home,
    meta: { requiresAuth: true },
    children: [
      {
        path: 'engineering/model-management',
        name: 'ModelManagement',
        component: ModelManagement,
        meta: { requiresAuth: true }
      },
      {
        path: 'production/workcenter',
        name: 'WorkCenterSettings',
        component: WorkCenterSettings,
        meta: { requiresAuth: true, title: '工作中心设置' }
      },
      {
        path: 'system/users',
        name: 'UserManagement',
        component: UserManagement,
        meta: { requiresAuth: true }
      },
      {
        path: 'system/roles',
        name: 'RolePermission',
        component: RolePermission,
        meta: { requiresAuth: true, title: '角色权限' }
      },
      {
        path: 'system/menus',
        name: 'MenuManagement',
        component: MenuManagement,
        meta: { requiresAuth: true, title: '菜单管理' }
      },
      {
        path: 'system/dictionary',
        name: 'DataDictionary',
        component: DataDictionary,
        meta: { requiresAuth: true, title: '数据字典' }
      },
      {
        path: 'system/page-template-diagnosis',
        name: 'PageTemplateDiagnosis',
        component: PageTemplateDiagnosis,
        meta: { requiresAuth: true, title: 'PageTemplate诊断' }
      },
      {
        path: 'system/language',
        name: 'LanguageManagement',
        component: I18nManagement,
        meta: { requiresAuth: true, title: '多语言管理' }
      },
      {
        path: 'hr/employees',
        name: 'EmployeeManagement',
        component: EmployeeManagement,
        meta: { requiresAuth: true, title: '员工管理' }
      },
      {
        path: 'hr/attendance',
        name: 'AttendanceManagement',
        component: AttendanceManagement,
        meta: { requiresAuth: true, title: '考勤管理' }
      },
      {
        path: 'hr/salary',
        name: 'SalaryManagement',
        component: SalaryManagement,
        meta: { requiresAuth: true, title: '薪资管理' }
      },
      {
        path: 'hr/company-settings',
        name: 'CompanySettings',
        component: CompanySettings,
        meta: { requiresAuth: true, title: '公司设置' }
      },
      {
        path: 'system/permission-test',
        name: 'PermissionTest',
        component: PermissionTest,
        meta: { requiresAuth: true, title: '权限测试' }
      },
      {
        path: 'example/page-template-demo',
        name: 'PageTemplateDemo',
        component: PageTemplateDemo,
        meta: { requiresAuth: true, title: 'PageTemplate演示' }
      },
      {
        path: 'engineering/wis-management',
        name: 'WisPDFViewer',
        component: WisPDFViewer,
        meta: { requiresAuth: true, title: 'WIS管理' }
      },
      {
        path: 'engineering/wis-query',
        name: 'WisQuery',
        component: WisQuery,
        meta: { requiresAuth: true, title: 'WIS查询' }
      },
      {
        path: 'engineering/Ebom',
        name: 'EngineeringBOM',
        component: () => import('@/views/engineering/EngineeringBOM.vue'),
        meta: { requiresAuth: true, title: '研发BOM' }
      }
    ]
  },
  // 捕获所有未匹配的路由
  {
    path: '/:pathMatch(.*)*',
    redirect: '/login'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 使用权限工具类检查登录状态
  const loggedIn = isUserLoggedIn()
  
  // 如果需要登录
  if (to.meta.requiresAuth) {
    if (!loggedIn) {
      // 未登录，重定向到登录页
      next({ path: '/login', query: { redirect: to.fullPath } })
    } else {
      // 已登录，允许访问
      next()
    }
  } else {
    // 不需要登录的页面（如登录页）
    if (to.path === '/login' && loggedIn) {
      // 已登录但访问登录页，重定向到首页
      next({ path: '/home' })
    } else {
      // 其他情况正常访问
      next()
    }
  }
})

export default router 