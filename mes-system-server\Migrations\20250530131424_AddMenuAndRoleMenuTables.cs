﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace mes_system_server.Migrations
{
    /// <inheritdoc />
    public partial class AddMenuAndRoleMenuTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "<PERSON><PERSON>",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MenuId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Path = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Icon = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Type = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    ParentId = table.Column<int>(type: "int", nullable: true),
                    SortOrder = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<bool>(type: "bit", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Menus", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Menus_Menus_ParentId",
                        column: x => x.ParentId,
                        principalTable: "Menus",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "RoleMenus",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RoleId = table.Column<int>(type: "int", nullable: false),
                    MenuId = table.Column<int>(type: "int", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RoleMenus", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RoleMenus_Menus_MenuId",
                        column: x => x.MenuId,
                        principalTable: "Menus",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RoleMenus_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8270), new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8270) });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8286));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8295));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8301));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8305));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8310));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8314));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8319));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8323));

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8362), new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8362) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8373), new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8373) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8382), new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8382) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8390), new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8391) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8400), new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8400) });

            migrationBuilder.InsertData(
                table: "Menus",
                columns: new[] { "Id", "CreateTime", "Description", "Icon", "MenuId", "Name", "ParentId", "Path", "SortOrder", "Status", "Type", "UpdateTime" },
                values: new object[,]
                {
                    { 1, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "仓储管理模块", null, "warehouse", "仓储管理", null, null, 1, true, "sub-menu", null },
                    { 2, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "生产管理模块", null, "production", "生产管理", null, null, 2, true, "sub-menu", null },
                    { 3, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "工程管理模块", null, "engineering", "工程管理", null, null, 3, true, "sub-menu", null },
                    { 4, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "生产线管理", null, "production-line", "生产管理", null, null, 4, true, "sub-menu", null },
                    { 5, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "质量管理模块", null, "quality", "质量管理", null, null, 5, true, "sub-menu", null },
                    { 6, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "设备管理模块", null, "equipment", "设备管理", null, null, 6, true, "sub-menu", null },
                    { 7, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "系统管理模块", null, "system", "系统管理", null, null, 7, true, "sub-menu", null },
                    { 8, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "warehouse-in", "入库管理", 1, "/home/<USER>/in", 1, true, "menu-item", null },
                    { 9, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "warehouse-out", "出库管理", 1, "/home/<USER>/out", 2, true, "menu-item", null },
                    { 10, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "warehouse-transfer", "库存调拨", 1, "/home/<USER>/transfer", 3, true, "menu-item", null },
                    { 11, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "warehouse-check", "库存盘点", 1, "/home/<USER>/check", 4, true, "menu-item", null },
                    { 12, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "production-plan", "生产计划", 2, "/home/<USER>/plan", 1, true, "menu-item", null },
                    { 13, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "production-order", "生产订单", 2, "/home/<USER>/order", 2, true, "menu-item", null },
                    { 14, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "production-schedule", "排产管理", 2, "/home/<USER>/schedule", 3, true, "menu-item", null },
                    { 15, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "engineering-process", "工艺管理", 3, "/home/<USER>/process", 1, true, "menu-item", null },
                    { 16, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "engineering-bom", "BOM管理", 3, "/home/<USER>/bom", 2, true, "menu-item", null },
                    { 17, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "engineering-model", "型号管理", 3, "/home/<USER>/models", 3, true, "menu-item", null },
                    { 18, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "production-monitor", "生产监控", 4, "/home/<USER>/monitor", 1, true, "menu-item", null },
                    { 19, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "production-task", "生产任务", 4, "/home/<USER>/task", 2, true, "menu-item", null },
                    { 20, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "quality-inspection", "质量检验", 5, "/home/<USER>/inspection", 1, true, "menu-item", null },
                    { 21, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "quality-report", "质量报告", 5, "/home/<USER>/report", 2, true, "menu-item", null },
                    { 22, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "equipment-list", "设备台账", 6, "/home/<USER>/list", 1, true, "menu-item", null },
                    { 23, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "system-users", "用户管理", 7, "/home/<USER>/users", 1, true, "menu-item", null },
                    { 24, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "system-roles", "角色权限", 7, "/home/<USER>/roles", 2, true, "menu-item", null },
                    { 25, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "system-menus", "菜单管理", 7, "/home/<USER>/menus", 3, true, "menu-item", null }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Menus_MenuId",
                table: "Menus",
                column: "MenuId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Menus_ParentId",
                table: "Menus",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "IX_RoleMenus_MenuId",
                table: "RoleMenus",
                column: "MenuId");

            migrationBuilder.CreateIndex(
                name: "IX_RoleMenus_RoleId_MenuId",
                table: "RoleMenus",
                columns: new[] { "RoleId", "MenuId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RoleMenus");

            migrationBuilder.DropTable(
                name: "Menus");

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1778), new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1778) });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1789));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1797));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1802));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1806));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1809));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1813));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1817));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 25, 12, 26, 8, 455, DateTimeKind.Utc).AddTicks(1821));

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1854), new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1854) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1863), new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1864) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1901), new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1901) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1908), new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1909) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1916), new DateTime(2025, 5, 25, 20, 26, 8, 455, DateTimeKind.Local).AddTicks(1917) });
        }
    }
}
