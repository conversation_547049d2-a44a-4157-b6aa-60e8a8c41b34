import request from '@/utils/request'

// 获取职位列表
export function getPositions(params) {
  return request({
    url: '/api/positions',
    method: 'get',
    params
  })
}

// 获取职位详情
export function getPosition(id) {
  return request({
    url: `/api/positions/${id}`,
    method: 'get'
  })
}

// 创建职位
export function createPosition(data) {
  return request({
    url: '/api/positions',
    method: 'post',
    data
  })
}

// 更新职位
export function updatePosition(id, data) {
  return request({
    url: `/api/positions/${id}`,
    method: 'put',
    data
  })
}

// 删除职位
export function deletePosition(id) {
  return request({
    url: `/api/positions/${id}`,
    method: 'delete'
  })
} 