using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using mes_system_server.Models;
using mes_system_server.Data;
using mes_system_server.DTOs;
using System.Linq.Dynamic.Core;

namespace mes_system_server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DataDictionaryController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public DataDictionaryController(ApplicationDbContext context)
        {
            _context = context;
        }

        #region 数据字典主表操作

        /// <summary>
        /// 获取数据字典列表（分页）
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ApiResponseDto<PagedResultDto<DataDictionaryListDto>>>> GetDataDictionaries([FromQuery] DataDictionaryQueryDto query)
        {
            try
            {
                var queryable = _context.DataDictionaries.AsQueryable();

                // 应用筛选条件
                if (!string.IsNullOrEmpty(query.Name))
                {
                    queryable = queryable.Where(d => d.Name.Contains(query.Name));
                }

                if (!string.IsNullOrEmpty(query.Code))
                {
                    queryable = queryable.Where(d => d.Code.Contains(query.Code.ToUpper()));
                }

                if (!string.IsNullOrEmpty(query.Type))
                {
                    queryable = queryable.Where(d => d.Type == query.Type);
                }

                if (query.Status.HasValue)
                {
                    queryable = queryable.Where(d => d.Status == query.Status.Value);
                }

                // 获取总数
                var total = await queryable.CountAsync();

                // 应用排序
                var sortField = string.IsNullOrEmpty(query.SortField) ? "Sort" : query.SortField;
                var sortOrder = string.IsNullOrEmpty(query.SortOrder) || query.SortOrder.ToLower() == "asc" ? "asc" : "desc";
                var orderBy = $"{sortField} {sortOrder}";

                // 分页查询
                var items = await queryable
                    .OrderBy(orderBy)
                    .Skip((query.Page - 1) * query.PageSize)
                    .Take(query.PageSize)
                    .Select(d => new DataDictionaryListDto
                    {
                        Id = d.Id,
                        Name = d.Name,
                        Code = d.Code,
                        Type = d.Type,
                        Description = d.Description,
                        Sort = d.Sort,
                        Status = d.Status,
                        Remark = d.Remark,
                        CreateTime = d.CreateTime,
                        UpdateTime = d.UpdateTime,
                        ItemCount = d.Items.Count
                    })
                    .ToListAsync();

                var result = new PagedResultDto<DataDictionaryListDto>
                {
                    Items = items,
                    TotalCount = total,
                    PageIndex = query.Page,
                    PageSize = query.PageSize
                };

                return Ok(new ApiResponseDto<PagedResultDto<DataDictionaryListDto>>(200, "获取数据字典列表成功", result));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"获取数据字典列表失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取数据字典详情
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponseDto<DataDictionaryDetailDto>>> GetDataDictionary(int id)
        {
            try
            {
                var dictionary = await _context.DataDictionaries
                    .Include(d => d.Items.Where(i => i.Status))
                    .FirstOrDefaultAsync(d => d.Id == id);

                if (dictionary == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "数据字典不存在"));
                }

                var result = new DataDictionaryDetailDto
                {
                    Id = dictionary.Id,
                    Name = dictionary.Name,
                    Code = dictionary.Code,
                    Type = dictionary.Type,
                    Description = dictionary.Description,
                    Sort = dictionary.Sort,
                    Status = dictionary.Status,
                    Remark = dictionary.Remark,
                    CreateTime = dictionary.CreateTime,
                    UpdateTime = dictionary.UpdateTime,
                    Items = dictionary.Items.Select(i => new DataDictionaryItemDto
                    {
                        Id = i.Id,
                        DictId = i.DictId,
                        Label = i.Label,
                        Value = i.Value,
                        Sort = i.Sort,
                        Status = i.Status,
                        Description = i.Description,
                        ExtProperty1 = i.ExtProperty1,
                        ExtProperty2 = i.ExtProperty2,
                        ExtProperty3 = i.ExtProperty3,
                        CreateTime = i.CreateTime,
                        UpdateTime = i.UpdateTime
                    }).OrderBy(i => i.Sort).ToList()
                };

                return Ok(new ApiResponseDto<DataDictionaryDetailDto>(200, "获取数据字典详情成功", result));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"获取数据字典详情失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 创建数据字典
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ApiResponseDto<DataDictionaryListDto>>> CreateDataDictionary(CreateDataDictionaryDto createDto)
        {
            try
            {
                // 检查编码是否已存在
                var existingDict = await _context.DataDictionaries
                    .FirstOrDefaultAsync(d => d.Code == createDto.Code.ToUpper());

                if (existingDict != null)
                {
                    return BadRequest(new ApiResponseDto<object>(400, "字典编码已存在"));
                }

                var dictionary = new DataDictionary
                {
                    Name = createDto.Name,
                    Code = createDto.Code.ToUpper(),
                    Type = createDto.Type,
                    Description = createDto.Description,
                    Sort = createDto.Sort,
                    Status = createDto.Status,
                    Remark = createDto.Remark,
                    CreateTime = DateTime.Now,
                    CreateBy = 1 // TODO: 从当前用户上下文获取
                };

                _context.DataDictionaries.Add(dictionary);
                await _context.SaveChangesAsync();

                var result = new DataDictionaryListDto
                {
                    Id = dictionary.Id,
                    Name = dictionary.Name,
                    Code = dictionary.Code,
                    Type = dictionary.Type,
                    Description = dictionary.Description,
                    Sort = dictionary.Sort,
                    Status = dictionary.Status,
                    Remark = dictionary.Remark,
                    CreateTime = dictionary.CreateTime,
                    UpdateTime = dictionary.UpdateTime,
                    ItemCount = 0
                };

                return CreatedAtAction(nameof(GetDataDictionary), new { id = dictionary.Id }, 
                    new ApiResponseDto<DataDictionaryListDto>(200, "创建数据字典成功", result));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"创建数据字典失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 更新数据字典
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<ApiResponseDto<DataDictionaryListDto>>> UpdateDataDictionary(int id, UpdateDataDictionaryDto updateDto)
        {
            try
            {
                var dictionary = await _context.DataDictionaries.FindAsync(id);

                if (dictionary == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "数据字典不存在"));
                }

                dictionary.Name = updateDto.Name;
                dictionary.Type = updateDto.Type;
                dictionary.Description = updateDto.Description;
                dictionary.Sort = updateDto.Sort;
                dictionary.Status = updateDto.Status;
                dictionary.Remark = updateDto.Remark;
                dictionary.UpdateTime = DateTime.Now;
                dictionary.UpdateBy = 1; // TODO: 从当前用户上下文获取

                await _context.SaveChangesAsync();

                var result = new DataDictionaryListDto
                {
                    Id = dictionary.Id,
                    Name = dictionary.Name,
                    Code = dictionary.Code,
                    Type = dictionary.Type,
                    Description = dictionary.Description,
                    Sort = dictionary.Sort,
                    Status = dictionary.Status,
                    Remark = dictionary.Remark,
                    CreateTime = dictionary.CreateTime,
                    UpdateTime = dictionary.UpdateTime,
                    ItemCount = await _context.DataDictionaryItems.CountAsync(i => i.DictId == id)
                };

                return Ok(new ApiResponseDto<DataDictionaryListDto>(200, "更新数据字典成功", result));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"更新数据字典失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 删除数据字典
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<ApiResponseDto<object>>> DeleteDataDictionary(int id)
        {
            try
            {
                var dictionary = await _context.DataDictionaries
                    .Include(d => d.Items)
                    .FirstOrDefaultAsync(d => d.Id == id);

                if (dictionary == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "数据字典不存在"));
                }

                // 删除字典会级联删除所有字典项
                _context.DataDictionaries.Remove(dictionary);
                await _context.SaveChangesAsync();

                return Ok(new ApiResponseDto<object>(200, $"删除数据字典成功，同时删除了 {dictionary.Items.Count} 个字典项"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"删除数据字典失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 批量删除数据字典
        /// </summary>
        [HttpDelete("batch")]
        public async Task<ActionResult<ApiResponseDto<object>>> BatchDeleteDataDictionaries(BatchOperationDto batchDto)
        {
            try
            {
                var dictionaries = await _context.DataDictionaries
                    .Include(d => d.Items)
                    .Where(d => batchDto.Ids.Contains(d.Id))
                    .ToListAsync();

                if (dictionaries.Count == 0)
                {
                    return NotFound(new ApiResponseDto<object>(404, "未找到要删除的数据字典"));
                }

                var totalItems = dictionaries.Sum(d => d.Items.Count);
                _context.DataDictionaries.RemoveRange(dictionaries);
                await _context.SaveChangesAsync();

                return Ok(new ApiResponseDto<object>(200, $"批量删除成功，删除了 {dictionaries.Count} 个数据字典和 {totalItems} 个字典项"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"批量删除数据字典失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 批量更新数据字典状态
        /// </summary>
        [HttpPut("batch/status")]
        public async Task<ActionResult<ApiResponseDto<object>>> BatchUpdateDataDictionaryStatus(BatchStatusUpdateDto batchDto)
        {
            try
            {
                var dictionaries = await _context.DataDictionaries
                    .Where(d => batchDto.Ids.Contains(d.Id))
                    .ToListAsync();

                if (dictionaries.Count == 0)
                {
                    return NotFound(new ApiResponseDto<object>(404, "未找到要更新的数据字典"));
                }

                foreach (var dictionary in dictionaries)
                {
                    dictionary.Status = batchDto.Status;
                    dictionary.UpdateTime = DateTime.Now;
                    dictionary.UpdateBy = 1; // TODO: 从当前用户上下文获取
                }

                await _context.SaveChangesAsync();

                var action = batchDto.Status ? "启用" : "禁用";
                return Ok(new ApiResponseDto<object>(200, $"批量{action}成功，共{action}了 {dictionaries.Count} 个数据字典"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"批量更新数据字典状态失败: {ex.Message}"));
            }
        }

        #endregion

        #region 数据字典项操作

        /// <summary>
        /// 获取字典项列表
        /// </summary>
        [HttpGet("{dictId}/items")]
        public async Task<ActionResult<ApiResponseDto<PagedResultDto<DataDictionaryItemDto>>>> GetDataDictionaryItems(int dictId, [FromQuery] DataDictionaryItemQueryDto query)
        {
            try
            {
                // 验证字典是否存在
                var dictExists = await _context.DataDictionaries.AnyAsync(d => d.Id == dictId);
                if (!dictExists)
                {
                    return NotFound(new ApiResponseDto<object>(404, "数据字典不存在"));
                }

                query.DictId = dictId;
                var queryable = _context.DataDictionaryItems.Where(i => i.DictId == dictId);

                // 应用筛选条件
                if (!string.IsNullOrEmpty(query.Label))
                {
                    queryable = queryable.Where(i => i.Label.Contains(query.Label));
                }

                if (!string.IsNullOrEmpty(query.Value))
                {
                    queryable = queryable.Where(i => i.Value.Contains(query.Value));
                }

                if (query.Status.HasValue)
                {
                    queryable = queryable.Where(i => i.Status == query.Status.Value);
                }

                // 获取总数
                var total = await queryable.CountAsync();

                // 应用排序
                var sortField = string.IsNullOrEmpty(query.SortField) ? "Sort" : query.SortField;
                var sortOrder = string.IsNullOrEmpty(query.SortOrder) || query.SortOrder.ToLower() == "asc" ? "asc" : "desc";
                var orderBy = $"{sortField} {sortOrder}";

                // 分页查询
                var items = await queryable
                    .OrderBy(orderBy)
                    .Skip((query.Page - 1) * query.PageSize)
                    .Take(query.PageSize)
                    .Select(i => new DataDictionaryItemDto
                    {
                        Id = i.Id,
                        DictId = i.DictId,
                        Label = i.Label,
                        Value = i.Value,
                        Sort = i.Sort,
                        Status = i.Status,
                        Description = i.Description,
                        ExtProperty1 = i.ExtProperty1,
                        ExtProperty2 = i.ExtProperty2,
                        ExtProperty3 = i.ExtProperty3,
                        CreateTime = i.CreateTime,
                        UpdateTime = i.UpdateTime
                    })
                    .ToListAsync();

                var result = new PagedResultDto<DataDictionaryItemDto>
                {
                    Items = items,
                    TotalCount = total,
                    PageIndex = query.Page,
                    PageSize = query.PageSize
                };

                return Ok(new ApiResponseDto<PagedResultDto<DataDictionaryItemDto>>(200, "获取字典项列表成功", result));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"获取字典项列表失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取字典项详情
        /// </summary>
        [HttpGet("items/{id}")]
        public async Task<ActionResult<ApiResponseDto<DataDictionaryItemDto>>> GetDataDictionaryItem(int id)
        {
            try
            {
                var item = await _context.DataDictionaryItems.FindAsync(id);

                if (item == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "字典项不存在"));
                }

                var result = new DataDictionaryItemDto
                {
                    Id = item.Id,
                    DictId = item.DictId,
                    Label = item.Label,
                    Value = item.Value,
                    Sort = item.Sort,
                    Status = item.Status,
                    Description = item.Description,
                    ExtProperty1 = item.ExtProperty1,
                    ExtProperty2 = item.ExtProperty2,
                    ExtProperty3 = item.ExtProperty3,
                    CreateTime = item.CreateTime,
                    UpdateTime = item.UpdateTime
                };

                return Ok(new ApiResponseDto<DataDictionaryItemDto>(200, "获取字典项详情成功", result));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"获取字典项详情失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 创建字典项
        /// </summary>
        [HttpPost("items")]
        public async Task<ActionResult<ApiResponseDto<DataDictionaryItemDto>>> CreateDataDictionaryItem(CreateDataDictionaryItemDto createDto)
        {
            try
            {
                // 验证字典是否存在
                var dictExists = await _context.DataDictionaries.AnyAsync(d => d.Id == createDto.DictId);
                if (!dictExists)
                {
                    return NotFound(new ApiResponseDto<object>(404, "数据字典不存在"));
                }

                // 检查同一字典下值是否已存在
                var existingItem = await _context.DataDictionaryItems
                    .FirstOrDefaultAsync(i => i.DictId == createDto.DictId && i.Value == createDto.Value);

                if (existingItem != null)
                {
                    return BadRequest(new ApiResponseDto<object>(400, "该字典下已存在相同的字典值"));
                }

                var item = new DataDictionaryItem
                {
                    DictId = createDto.DictId,
                    Label = createDto.Label,
                    Value = createDto.Value,
                    Sort = createDto.Sort,
                    Status = createDto.Status,
                    Description = createDto.Description,
                    ExtProperty1 = createDto.ExtProperty1,
                    ExtProperty2 = createDto.ExtProperty2,
                    ExtProperty3 = createDto.ExtProperty3,
                    CreateTime = DateTime.Now,
                    CreateBy = 1 // TODO: 从当前用户上下文获取
                };

                _context.DataDictionaryItems.Add(item);
                await _context.SaveChangesAsync();

                var result = new DataDictionaryItemDto
                {
                    Id = item.Id,
                    DictId = item.DictId,
                    Label = item.Label,
                    Value = item.Value,
                    Sort = item.Sort,
                    Status = item.Status,
                    Description = item.Description,
                    ExtProperty1 = item.ExtProperty1,
                    ExtProperty2 = item.ExtProperty2,
                    ExtProperty3 = item.ExtProperty3,
                    CreateTime = item.CreateTime,
                    UpdateTime = item.UpdateTime
                };

                return CreatedAtAction(nameof(GetDataDictionaryItem), new { id = item.Id }, 
                    new ApiResponseDto<DataDictionaryItemDto>(200, "创建字典项成功", result));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"创建字典项失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 更新字典项
        /// </summary>
        [HttpPut("items/{id}")]
        public async Task<ActionResult<ApiResponseDto<DataDictionaryItemDto>>> UpdateDataDictionaryItem(int id, UpdateDataDictionaryItemDto updateDto)
        {
            try
            {
                var item = await _context.DataDictionaryItems.FindAsync(id);

                if (item == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "字典项不存在"));
                }

                // 检查同一字典下值是否已存在（排除当前项）
                var existingItem = await _context.DataDictionaryItems
                    .FirstOrDefaultAsync(i => i.DictId == item.DictId && i.Value == updateDto.Value && i.Id != id);

                if (existingItem != null)
                {
                    return BadRequest(new ApiResponseDto<object>(400, "该字典下已存在相同的字典值"));
                }

                item.Label = updateDto.Label;
                item.Value = updateDto.Value;
                item.Sort = updateDto.Sort;
                item.Status = updateDto.Status;
                item.Description = updateDto.Description;
                item.ExtProperty1 = updateDto.ExtProperty1;
                item.ExtProperty2 = updateDto.ExtProperty2;
                item.ExtProperty3 = updateDto.ExtProperty3;
                item.UpdateTime = DateTime.Now;
                item.UpdateBy = 1; // TODO: 从当前用户上下文获取

                await _context.SaveChangesAsync();

                var result = new DataDictionaryItemDto
                {
                    Id = item.Id,
                    DictId = item.DictId,
                    Label = item.Label,
                    Value = item.Value,
                    Sort = item.Sort,
                    Status = item.Status,
                    Description = item.Description,
                    ExtProperty1 = item.ExtProperty1,
                    ExtProperty2 = item.ExtProperty2,
                    ExtProperty3 = item.ExtProperty3,
                    CreateTime = item.CreateTime,
                    UpdateTime = item.UpdateTime
                };

                return Ok(new ApiResponseDto<DataDictionaryItemDto>(200, "更新字典项成功", result));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"更新字典项失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 删除字典项
        /// </summary>
        [HttpDelete("items/{id}")]
        public async Task<ActionResult<ApiResponseDto<object>>> DeleteDataDictionaryItem(int id)
        {
            try
            {
                var item = await _context.DataDictionaryItems.FindAsync(id);

                if (item == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "字典项不存在"));
                }

                _context.DataDictionaryItems.Remove(item);
                await _context.SaveChangesAsync();

                return Ok(new ApiResponseDto<object>(200, "删除字典项成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"删除字典项失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 批量删除字典项
        /// </summary>
        [HttpDelete("items/batch")]
        public async Task<ActionResult<ApiResponseDto<object>>> BatchDeleteDataDictionaryItems(BatchOperationDto batchDto)
        {
            try
            {
                var items = await _context.DataDictionaryItems
                    .Where(i => batchDto.Ids.Contains(i.Id))
                    .ToListAsync();

                if (items.Count == 0)
                {
                    return NotFound(new ApiResponseDto<object>(404, "未找到要删除的字典项"));
                }

                _context.DataDictionaryItems.RemoveRange(items);
                await _context.SaveChangesAsync();

                return Ok(new ApiResponseDto<object>(200, $"批量删除成功，删除了 {items.Count} 个字典项"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"批量删除字典项失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 批量更新字典项状态
        /// </summary>
        [HttpPut("items/batch/status")]
        public async Task<ActionResult<ApiResponseDto<object>>> BatchUpdateDataDictionaryItemStatus(BatchStatusUpdateDto batchDto)
        {
            try
            {
                var items = await _context.DataDictionaryItems
                    .Where(i => batchDto.Ids.Contains(i.Id))
                    .ToListAsync();

                if (items.Count == 0)
                {
                    return NotFound(new ApiResponseDto<object>(404, "未找到要更新的字典项"));
                }

                foreach (var item in items)
                {
                    item.Status = batchDto.Status;
                    item.UpdateTime = DateTime.Now;
                    item.UpdateBy = 1; // TODO: 从当前用户上下文获取
                }

                await _context.SaveChangesAsync();

                var action = batchDto.Status ? "启用" : "禁用";
                return Ok(new ApiResponseDto<object>(200, $"批量{action}成功，共{action}了 {items.Count} 个字典项"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"批量更新字典项状态失败: {ex.Message}"));
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 根据字典编码获取字典项（对外接口）
        /// </summary>
        [HttpGet("by-code/{code}")]
        public async Task<ActionResult<ApiResponseDto<List<DataDictionaryItemDto>>>> GetDataDictionaryItemsByCode(string code)
        {
            try
            {
                var items = await _context.DataDictionaryItems
                    .Where(i => i.DataDictionary != null && i.DataDictionary.Code == code.ToUpper() && 
                               i.DataDictionary.Status && i.Status)
                    .OrderBy(i => i.Sort)
                    .Select(i => new DataDictionaryItemDto
                    {
                        Id = i.Id,
                        DictId = i.DictId,
                        Label = i.Label,
                        Value = i.Value,
                        Sort = i.Sort,
                        Status = i.Status,
                        Description = i.Description,
                        ExtProperty1 = i.ExtProperty1,
                        ExtProperty2 = i.ExtProperty2,
                        ExtProperty3 = i.ExtProperty3,
                        CreateTime = i.CreateTime,
                        UpdateTime = i.UpdateTime
                    })
                    .ToListAsync();

                return Ok(new ApiResponseDto<List<DataDictionaryItemDto>>(200, "获取字典项成功", items));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"根据编码获取字典项失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取数据字典类型列表
        /// </summary>
        [HttpGet("types")]
        public async Task<ActionResult<ApiResponseDto<List<string>>>> GetDataDictionaryTypes()
        {
            try
            {
                var types = await _context.DataDictionaries
                    .Where(d => d.Status)
                    .Select(d => d.Type)
                    .Distinct()
                    .ToListAsync();

                return Ok(new ApiResponseDto<List<string>>(200, "获取字典类型成功", types));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"获取字典类型失败: {ex.Message}"));
            }
        }

        #endregion
    }
} 