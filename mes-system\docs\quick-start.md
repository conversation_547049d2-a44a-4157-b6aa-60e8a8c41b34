# 快速开始

本指南将帮助您快速搭建开发环境并运行MES系统。

## 环境要求

### 前端环境
- **Node.js**: >= 16.0.0
- **包管理器**: npm >= 7.0.0 或 pnpm >= 6.0.0 (推荐)
- **浏览器**: Chrome >= 88, Firefox >= 85, Safari >= 14

### 后端环境
- **.NET**: >= 8.0
- **数据库**: SQL Server >= 2017 或 MySQL >= 8.0
- **IDE**: Visual Studio 2022 或 Visual Studio Code

## 快速安装

### 1. 克隆项目
```bash
git clone [项目地址]
cd AMPER
```

### 2. 前端环境搭建
```bash
# 进入前端目录
cd mes-system

# 安装依赖 (推荐使用pnpm)
pnpm install

# 或使用npm
npm install
```

### 3. 后端环境搭建
```bash
# 进入后端目录
cd mes-system-server

# 还原NuGet包
dotnet restore

# 数据库迁移 (首次运行)
dotnet ef database update
```

## 启动开发服务器

### 前端开发服务器
```bash
cd mes-system

# 启动开发服务器
pnpm dev

# 服务器将在 http://localhost:5173 启动
```

### 后端API服务器
```bash
cd mes-system-server

# 启动API服务器
dotnet run

# API服务器将在 http://localhost:5000 启动
# Swagger文档: http://localhost:5000/swagger
```

## 首次使用

### 1. 访问系统
打开浏览器访问: http://localhost:5173

### 2. 默认登录信息
- **用户名**: admin
- **密码**: admin123

### 3. 初始化菜单数据
登录后，前往"系统管理 -> 菜单管理"，点击"初始化菜单"按钮来创建默认菜单结构。

### 4. 体验核心功能
- **菜单管理**: `/home/<USER>/menus` - 配置系统菜单
- **用户管理**: `/home/<USER>/users` - 管理系统用户
- **角色权限**: `/home/<USER>/roles` - 设置用户角色和权限
- **PageTemplate演示**: `/home/<USER>/page-template-demo` - 查看通用页面模板效果

## 开发工作流

### 1. 创建新菜单
1. 访问菜单管理页面
2. 点击"新增菜单"
3. 填写菜单信息
4. 启用"自动生成页面文件"
5. 保存并下载生成的代码

### 2. 添加新页面
1. 将下载的Vue文件放到对应目录
2. 在router/index.js中添加路由配置
3. 刷新页面即可访问

### 3. 自定义页面
1. 使用PageTemplate组件作为基础
2. 配置搜索字段、表格列、表单字段
3. 实现业务逻辑和API调用

## 常用命令

### 前端命令
```bash
# 开发模式
pnpm dev

# 构建生产版本
pnpm build

# 预览生产版本
pnpm preview

# 代码检查
pnpm lint

# 代码格式化
pnpm format
```

### 后端命令
```bash
# 启动开发服务器
dotnet run

# 监听模式启动 (文件变化自动重启)
dotnet watch run

# 构建项目
dotnet build

# 发布项目
dotnet publish

# 数据库迁移
dotnet ef migrations add [迁移名称]
dotnet ef database update
```

## 目录结构说明

### 前端目录结构
```
mes-system/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口定义
│   ├── assets/            # 资源文件 (图片、样式等)
│   ├── components/        # 通用组件
│   │   ├── PageTemplate.vue  # 通用页面模板
│   │   └── Dashboard.vue     # 仪表板组件
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理 (Pinia)
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   │   ├── system/        # 系统管理页面
│   │   ├── engineering/   # 工程管理页面
│   │   ├── production/    # 生产管理页面
│   │   ├── quality/       # 质量管理页面
│   │   ├── warehouse/     # 仓储管理页面
│   │   ├── equipment/     # 设备管理页面
│   │   ├── hr/            # 人力资源页面
│   │   └── example/       # 示例页面
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── docs/                  # 文档目录
├── package.json           # 项目配置
└── vite.config.js         # Vite配置
```

### 后端目录结构
```
mes-system-server/
├── Controllers/           # 控制器
├── Models/               # 数据模型
├── Services/             # 业务服务层
├── Data/                 # 数据访问层
├── DTOs/                 # 数据传输对象
├── Middleware/           # 中间件
├── Extensions/           # 扩展方法
├── appsettings.json      # 配置文件
└── Program.cs            # 启动文件
```

## 下一步

现在您已经成功启动了MES系统，建议继续阅读以下文档：

- [项目概述](./project-overview.md) - 了解系统架构和技术栈
- [菜单系统使用指南](./menu-system.md) - 学习如何配置菜单
- [PageTemplate使用说明](./page-template.md) - 掌握通用页面模板
- [添加新菜单示例](./add-menu-example.md) - 通过实例学习开发流程

## 常见问题

### 前端相关
**Q: 启动时报错 "Module not found"**
A: 确保已正确安装依赖，尝试删除node_modules文件夹后重新安装

**Q: 页面样式异常**
A: 检查Element Plus是否正确引入，查看浏览器控制台是否有CSS加载错误

### 后端相关
**Q: 数据库连接失败**
A: 检查appsettings.json中的数据库连接字符串是否正确

**Q: API接口404错误**
A: 确认后端服务器已启动，检查路由配置是否正确

---

如果遇到其他问题，请查看[常见问题](./faq.md)或提交Issue。 