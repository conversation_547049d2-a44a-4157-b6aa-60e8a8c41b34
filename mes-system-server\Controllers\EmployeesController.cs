using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using mes_system_server.Data;
using mes_system_server.DTOs;
using mes_system_server.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace mes_system_server.Controllers
{
    [Route("api/employees")]
    [ApiController]
    [AllowAnonymous]
    public class EmployeesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public EmployeesController(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取员工列表
        /// </summary>
        /// <param name="page">页码，默认为1</param>
        /// <param name="pageSize">每页记录数，默认为10</param>
        /// <param name="searchType">搜索类型: employeeId, name, phone, email, employeeCardNo</param>
        /// <param name="searchQuery">搜索关键词</param>
        /// <param name="department">部门ID</param>
        /// <param name="status">状态: active, inactive</param>
        /// <returns>员工列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetEmployees(
            [FromQuery] int page = 1, 
            [FromQuery] int pageSize = 10, 
            [FromQuery] string searchType = "", 
            [FromQuery] string searchQuery = "",
            [FromQuery] int? department = null,
            [FromQuery] string status = "")
        {
            try
            {
                var query = _context.Employees
                    .Include(e => e.Department)
                    .AsQueryable();

                // 应用搜索条件
                if (!string.IsNullOrEmpty(searchQuery))
                {
                    switch (searchType)
                    {
                        case "employeeId":
                            query = query.Where(e => e.EmployeeId.Contains(searchQuery));
                            break;
                        case "name":
                            query = query.Where(e => e.Name.Contains(searchQuery));
                            break;
                        case "phone":
                            query = query.Where(e => e.Phone.Contains(searchQuery));
                            break;
                        case "email":
                            query = query.Where(e => e.Email.Contains(searchQuery));
                            break;
                        case "employeeCardNo":
                            query = query.Where(e => e.IdCard.Contains(searchQuery));
                            break;
                        default:
                            // 默认搜索姓名
                            query = query.Where(e => e.Name.Contains(searchQuery));
                            break;
                    }
                }

                // 按部门筛选
                if (department.HasValue)
                {
                    query = query.Where(e => e.DepartmentId == department.Value);
                }

                // 按状态筛选
                if (!string.IsNullOrEmpty(status))
                {
                    query = query.Where(e => e.Status == status);
                }

                // 计算总记录数
                var total = await query.CountAsync();

                // 应用分页
                var employees = await query
                    .OrderByDescending(e => e.CreateTime)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // 构建响应数据
                var employeeDtos = employees.Select(e => new EmployeeDto
                {
                    Id = e.Id,
                    EmployeeId = e.EmployeeId,
                    Name = e.Name,
                    DepartmentId = e.DepartmentId,
                    Department = e.Department.Name,
                    Position = e.Position,
                    Phone = e.Phone,
                    Email = e.Email,
                    EntryDate = e.EntryDate,
                    Status = e.Status,
                    Birthday = e.Birthday,
                    IdCard = e.IdCard,
                    Age = e.Age,
                    IdCardIssueDate = e.IdCardIssueDate,
                    Gender = e.Gender,
                    IdCardIssuePlace = e.IdCardIssuePlace,
                    Education = e.Education,
                    ContractNo = e.ContractNo,
                    Level = e.Level,
                    SkillLevel = e.SkillLevel,
                    PerformanceLevel = e.PerformanceLevel,
                    FactoryInfo = new FactoryInfoDto
                    {
                        Name = e.FactoryName,
                        Address = e.FactoryAddress,
                        Contact = e.FactoryContact,
                        Phone = e.FactoryPhone,
                        SalaryType = e.SalaryType,
                        Team = e.Team,
                        BaseSalary = e.BaseSalary,
                        WorkYears = e.WorkYears
                    },
                    CreateTime = e.CreateTime
                }).ToList();

                var response = new EmployeeResponseDto
                {
                    Total = total,
                    List = employeeDtos
                };

                return Ok(ApiResponseDto<EmployeeResponseDto>.Success(response));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取员工列表失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 根据ID获取员工详情
        /// </summary>
        /// <param name="id">员工ID</param>
        /// <returns>员工详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetEmployee(int id)
        {
            try
            {
                var employee = await _context.Employees
                    .Include(e => e.Department)
                    .FirstOrDefaultAsync(e => e.Id == id);

                if (employee == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("员工不存在", 404));
                }

                var employeeDto = new EmployeeDto
                {
                    Id = employee.Id,
                    EmployeeId = employee.EmployeeId,
                    Name = employee.Name,
                    DepartmentId = employee.DepartmentId,
                    Department = employee.Department.Name,
                    Position = employee.Position,
                    Phone = employee.Phone,
                    Email = employee.Email,
                    EntryDate = employee.EntryDate,
                    Status = employee.Status,
                    Birthday = employee.Birthday,
                    IdCard = employee.IdCard,
                    Age = employee.Age,
                    IdCardIssueDate = employee.IdCardIssueDate,
                    Gender = employee.Gender,
                    IdCardIssuePlace = employee.IdCardIssuePlace,
                    Education = employee.Education,
                    ContractNo = employee.ContractNo,
                    Level = employee.Level,
                    SkillLevel = employee.SkillLevel,
                    PerformanceLevel = employee.PerformanceLevel,
                    FactoryInfo = new FactoryInfoDto
                    {
                        Name = employee.FactoryName,
                        Address = employee.FactoryAddress,
                        Contact = employee.FactoryContact,
                        Phone = employee.FactoryPhone,
                        SalaryType = employee.SalaryType,
                        Team = employee.Team,
                        BaseSalary = employee.BaseSalary,
                        WorkYears = employee.WorkYears
                    },
                    CreateTime = employee.CreateTime
                };

                return Ok(ApiResponseDto<EmployeeDto>.Success(employeeDto));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取员工详情失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 创建员工
        /// </summary>
        /// <param name="employeeDto">员工信息</param>
        /// <returns>创建的员工</returns>
        [HttpPost]
        public async Task<IActionResult> CreateEmployee([FromBody] CreateEmployeeDto employeeDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                // 检查员工工号是否已存在
                var existingEmployee = await _context.Employees
                    .FirstOrDefaultAsync(e => e.EmployeeId == employeeDto.EmployeeId);

                if (existingEmployee != null)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("员工工号已存在"));
                }

                // 检查部门是否存在
                var department = await _context.Departments.FindAsync(employeeDto.DepartmentId);
                if (department == null)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("所选部门不存在"));
                }

                var employee = new Employee
                {
                    EmployeeId = employeeDto.EmployeeId,
                    Name = employeeDto.Name,
                    DepartmentId = employeeDto.DepartmentId,
                    Position = employeeDto.Position,
                    Phone = employeeDto.Phone,
                    Email = employeeDto.Email,
                    EntryDate = employeeDto.EntryDate,
                    Status = employeeDto.Status,
                    Birthday = employeeDto.Birthday,
                    IdCard = employeeDto.IdCard,
                    Gender = employeeDto.Gender,
                    Education = employeeDto.Education,
                    FactoryName = employeeDto.FactoryInfo?.Name ?? string.Empty,
                    FactoryAddress = employeeDto.FactoryInfo?.Address ?? string.Empty,
                    FactoryContact = employeeDto.FactoryInfo?.Contact ?? string.Empty,
                    FactoryPhone = employeeDto.FactoryInfo?.Phone ?? string.Empty,
                    SalaryType = employeeDto.FactoryInfo?.SalaryType ?? string.Empty,
                    Team = employeeDto.FactoryInfo?.Team ?? string.Empty,
                    BaseSalary = employeeDto.FactoryInfo?.BaseSalary ?? string.Empty,
                    WorkYears = employeeDto.FactoryInfo?.WorkYears,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };

                _context.Employees.Add(employee);
                await _context.SaveChangesAsync();

                // 返回新创建的员工ID
                return Ok(ApiResponseDto<object>.Success(new { employeeId = employee.EmployeeId }, "添加成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"创建员工失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 更新员工
        /// </summary>
        /// <param name="id">员工ID</param>
        /// <param name="employeeDto">员工信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateEmployee(int id, [FromBody] UpdateEmployeeDto employeeDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                var employee = await _context.Employees.FindAsync(id);
                if (employee == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("员工不存在", 404));
                }

                // 检查部门是否存在
                var department = await _context.Departments.FindAsync(employeeDto.DepartmentId);
                if (department == null)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("所选部门不存在"));
                }

                // 更新员工信息
                employee.Name = employeeDto.Name;
                employee.DepartmentId = employeeDto.DepartmentId;
                employee.Position = employeeDto.Position;
                employee.Phone = employeeDto.Phone;
                employee.Email = employeeDto.Email;
                employee.EntryDate = employeeDto.EntryDate;
                employee.Status = employeeDto.Status;
                employee.Birthday = employeeDto.Birthday;
                employee.IdCard = employeeDto.IdCard;
                employee.Gender = employeeDto.Gender;
                employee.Education = employeeDto.Education;
                
                // 更新工厂信息
                if (employeeDto.FactoryInfo != null)
                {
                    employee.FactoryName = employeeDto.FactoryInfo.Name;
                    employee.FactoryAddress = employeeDto.FactoryInfo.Address;
                    employee.FactoryContact = employeeDto.FactoryInfo.Contact;
                    employee.FactoryPhone = employeeDto.FactoryInfo.Phone;
                    employee.SalaryType = employeeDto.FactoryInfo.SalaryType;
                    employee.Team = employeeDto.FactoryInfo.Team;
                    employee.BaseSalary = employeeDto.FactoryInfo.BaseSalary;
                    employee.WorkYears = employeeDto.FactoryInfo.WorkYears;
                }
                
                employee.UpdateTime = DateTime.Now;

                _context.Entry(employee).State = EntityState.Modified;
                await _context.SaveChangesAsync();

                return Ok(ApiResponseDto<string>.Success("更新成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"更新员工失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 删除员工
        /// </summary>
        /// <param name="id">员工ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEmployee(int id)
        {
            try
            {
                var employee = await _context.Employees.FindAsync(id);
                if (employee == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("员工不存在", 404));
                }

                _context.Employees.Remove(employee);
                await _context.SaveChangesAsync();

                return Ok(ApiResponseDto<string>.Success("删除成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"删除员工失败: {ex.Message}", 500));
            }
        }
    }
} 