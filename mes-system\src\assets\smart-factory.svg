<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 - 更浅的蓝色 -->
    <linearGradient id="backgroundGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#64B5F6;stop-opacity:1" />
    </linearGradient>
    
    <!-- 机器人臂部分的渐变 - 更协调的蓝色 -->
    <linearGradient id="robotGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0D47A1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
    </linearGradient>
    
    <!-- 工厂地板渐变 - 更浅的色调 -->
    <linearGradient id="floorGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ECEFF1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#CFD8DC;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="url(#backgroundGrad)"/>
  
  <!-- 工厂地板 -->
  <path d="M0,800 L1920,900 L1920,1080 L0,1080 Z" fill="url(#floorGrad)"/>
  
  <!-- 网格线 -->
  <g stroke="#ffffff40" stroke-width="1">
    <path d="M0,820 L1920,920" />
    <path d="M0,840 L1920,940" />
    <path d="M0,860 L1920,960" />
    <path d="M0,880 L1920,980" />
    <path d="M200,800 L200,1080" />
    <path d="M400,800 L400,1080" />
    <path d="M600,800 L600,1080" />
    <path d="M800,800 L800,1080" />
    <path d="M1000,800 L1000,1080" />
    <path d="M1200,800 L1200,1080" />
    <path d="M1400,800 L1400,1080" />
    <path d="M1600,800 L1600,1080" />
    <path d="M1800,800 L1800,1080" />
  </g>
  
  <!-- 机器人手臂1 -->
  <g transform="translate(400,500)">
    <path d="M0,0 L100,-50 L200,0 L250,-100" 
          stroke="url(#robotGrad)" 
          stroke-width="20" 
          fill="none" 
          stroke-linecap="round"/>
    <circle cx="0" cy="0" r="20" fill="#1976D2"/>
    <circle cx="100" cy="-50" r="15" fill="#1976D2"/>
    <circle cx="200" cy="0" r="15" fill="#1976D2"/>
    <circle cx="250" cy="-100" r="25" fill="#1976D2"/>
  </g>
  
  <!-- 机器人手臂2 -->
  <g transform="translate(800,400)">
    <path d="M0,0 L-50,100 L50,150 L0,200" 
          stroke="url(#robotGrad)" 
          stroke-width="20" 
          fill="none" 
          stroke-linecap="round"/>
    <circle cx="0" cy="0" r="20" fill="#1976D2"/>
    <circle cx="-50" cy="100" r="15" fill="#1976D2"/>
    <circle cx="50" cy="150" r="15" fill="#1976D2"/>
    <circle cx="0" cy="200" r="25" fill="#1976D2"/>
  </g>
  
  <!-- 机器人手臂3 -->
  <g transform="translate(1200,450)">
    <path d="M0,0 L100,50 L150,-50 L250,0" 
          stroke="url(#robotGrad)" 
          stroke-width="20" 
          fill="none" 
          stroke-linecap="round"/>
    <circle cx="0" cy="0" r="20" fill="#1976D2"/>
    <circle cx="100" cy="50" r="15" fill="#1976D2"/>
    <circle cx="150" cy="-50" r="15" fill="#1976D2"/>
    <circle cx="250" cy="0" r="25" fill="#1976D2"/>
  </g>
  
  <!-- 生产线 -->
  <g transform="translate(0,700)">
    <rect x="100" y="0" width="1720" height="30" fill="#90CAF9"/>
    <g id="boxes" fill="#42A5F5">
      <rect x="200" y="-20" width="40" height="40" rx="5"/>
      <rect x="600" y="-20" width="40" height="40" rx="5"/>
      <rect x="1000" y="-20" width="40" height="40" rx="5"/>
      <rect x="1400" y="-20" width="40" height="40" rx="5"/>
    </g>
  </g>
  
  <!-- 悬浮的数据面板 -->
  <g transform="translate(300,200)">
    <rect x="0" y="0" width="200" height="120" rx="10" 
          fill="#ffffff30" stroke="#ffffff60" stroke-width="2"/>
    <line x1="20" y1="30" x2="180" y2="30" stroke="#ffffff80" stroke-width="2"/>
    <line x1="20" y1="60" x2="140" y2="60" stroke="#ffffff80" stroke-width="2"/>
    <line x1="20" y1="90" x2="160" y2="90" stroke="#ffffff80" stroke-width="2"/>
  </g>
  
  <g transform="translate(1400,300)">
    <rect x="0" y="0" width="200" height="120" rx="10" 
          fill="#ffffff30" stroke="#ffffff60" stroke-width="2"/>
    <line x1="20" y1="30" x2="180" y2="30" stroke="#ffffff80" stroke-width="2"/>
    <line x1="20" y1="60" x2="140" y2="60" stroke="#ffffff80" stroke-width="2"/>
    <line x1="20" y1="90" x2="160" y2="90" stroke="#ffffff80" stroke-width="2"/>
  </g>
  
  <!-- 粒子效果 -->
  <g fill="#ffffff60">
    <circle cx="100" cy="100" r="2"/>
    <circle cx="500" cy="200" r="2"/>
    <circle cx="900" cy="150" r="2"/>
    <circle cx="1300" cy="250" r="2"/>
    <circle cx="1700" cy="180" r="2"/>
    <circle cx="300" cy="400" r="2"/>
    <circle cx="700" cy="350" r="2"/>
    <circle cx="1100" cy="300" r="2"/>
    <circle cx="1500" cy="450" r="2"/>
  </g>
  
  <!-- 连接线 -->
  <g stroke="#ffffff40" stroke-width="1" stroke-dasharray="5,5">
    <path d="M100,100 L300,200"/>
    <path d="M500,200 L700,350"/>
    <path d="M900,150 L1100,300"/>
    <path d="M1300,250 L1500,450"/>
  </g>
</svg> 