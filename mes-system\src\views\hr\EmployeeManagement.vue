<template>
  <div class="employee-management">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="search-filters">
        <el-select v-model="searchType" placeholder="查询类型" clearable class="filter-item" style="width: 100px;">
          <el-option label="工号" value="employeeId" />
          <el-option label="姓名" value="name" />
          <el-option label="电话" value="phone" />
          <el-option label="邮箱" value="email" />
          <el-option label="员工卡号" value="employeeCardNo" />
        </el-select>
        
        <el-input
          v-model="searchQuery"
          :placeholder="getPlaceholder"
          class="search-input"
          clearable
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button :icon="Search" @click="handleSearch" style="background-color: #409EFF; border-color: #409EFF; color: #ffffff;">搜索</el-button>
          </template>
        </el-input>

        <el-select v-model="departmentFilter" clearable placeholder="部门" class="filter-item" style="width: 100px;" @change="handleSearch">
          <el-option v-for="dept in departmentOptions" :key="dept.id" :label="dept.name" :value="dept.id" />
        </el-select>

        <el-select v-model="statusFilter" clearable placeholder="在职" class="filter-item" style="width: 80px;" @change="handleSearch">
          <el-option v-for="status in statuses" :key="status.value" :label="status.label" :value="status.value" />
        </el-select>
      </div>

      <div class="operations">
        <el-button class="import-button" @click="handleImport">
          <el-icon><Upload /></el-icon>导入
        </el-button>
        <el-button class="export-button" @click="handleExport">
          <el-icon><Download /></el-icon>导出
        </el-button>
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>添加员工
        </el-button>
        <el-button type="primary" @click="handleEdit(currentEmployee)" :disabled="!currentEmployee || isEditing">
          <el-icon><Edit /></el-icon>编辑
        </el-button>
        <el-button type="success" @click="handleSave" :disabled="!isEditing">
          <el-icon><Check /></el-icon>保存
        </el-button>
        <el-button type="info" @click="handleCancelEdit" :disabled="!isEditing">
          取消
        </el-button>
        <el-button type="danger" @click="handleDelete(currentEmployee)" :disabled="!currentEmployee || isEditing">
          <el-icon><Delete /></el-icon>删除
        </el-button>
      </div>
    </div>

    <el-container class="main-container">
      <!-- 左侧员工列表 -->
      <el-aside width="300px" class="employee-list">
        <el-scrollbar>
          <div class="employee-items">
            <div
              v-for="employee in employeeList"
              :key="employee.employeeId"
              class="employee-item"
              :class="{ 'is-active': currentEmployee?.employeeId === employee.employeeId }"
              @click="handleSelectEmployee(employee)"
            >
              <div class="employee-avatar">
                <el-image
                  :src="employee.avatar || '/default-avatar.png'"
                  fit="cover"
                >
                  <template #error>
                    <div class="image-placeholder">
                      <el-icon><User /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
              <div class="employee-info">
                <div class="employee-name">{{ employee.name }}</div>
                <div class="employee-id">工号：{{ employee.employeeId }}</div>
                <div class="employee-dept">{{ employee.department }} - {{ employee.position }}</div>
              </div>
              <el-tag size="small" :type="employee.status === 'active' ? 'success' : 'danger'">
                {{ employee.status === 'active' ? '在职' : '离职' }}
              </el-tag>
            </div>
          </div>
        </el-scrollbar>

        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-aside>

      <!-- 右侧详细信息 -->
      <el-main class="employee-detail">
        <div v-if="currentEmployee" class="detail-container">
          <div class="tabs-header">
            <el-tabs v-model="activeTab" class="detail-tabs">
              <el-tab-pane label="员工基础信息" name="employee">
                <el-form :model="currentEmployee" label-width="120px">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="姓名">
                        <el-input v-model="currentEmployee.name" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="工号">
                        <el-input v-model="currentEmployee.employeeId" disabled />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="出生日期">
                        <el-date-picker
                          v-model="currentEmployee.birthday"
                          type="date"
                          placeholder="请选择出生日期"
                          style="width: 100%"
                          :disabled="!isEditing"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="身份证号">
                        <el-input v-model="currentEmployee.idCard" placeholder="请输入身份证号" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="年龄">
                        <el-input v-model="currentEmployee.age" placeholder="请输入年龄" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="身份证签发日期">
                        <el-date-picker
                          v-model="currentEmployee.idCardIssueDate"
                          type="date"
                          placeholder="请选择身份证签发日期"
                          style="width: 100%"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="性别">
                        <el-select v-model="currentEmployee.gender" placeholder="请选择性别" style="width: 100%" :disabled="!isEditing">
                          <el-option label="男" value="男" />
                          <el-option label="女" value="女" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="身份证签发地">
                        <el-input v-model="currentEmployee.idCardIssuePlace" placeholder="请输入身份证签发地" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="教育程度">
                        <el-select v-model="currentEmployee.education" placeholder="请选择教育程度" style="width: 100%" :disabled="!isEditing">
                          <el-option label="高中" value="高中" />
                          <el-option label="专科" value="专科" />
                          <el-option label="本科" value="本科" />
                          <el-option label="硕士" value="硕士" />
                          <el-option label="博士" value="博士" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="合同编号">
                        <el-input v-model="currentEmployee.contractNo" placeholder="请输入合同编号" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="婚姻状况">
                        <el-select v-model="currentEmployee.maritalStatus" placeholder="请选择婚姻状况" style="width: 100%" :disabled="!isEditing">
                          <el-option label="未婚" value="未婚" />
                          <el-option label="已婚" value="已婚" />
                          <el-option label="离异" value="离异" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="合同类型">
                        <el-select v-model="currentEmployee.contractType" placeholder="请选择合同类型" style="width: 100%">
                          <el-option label="全职" value="全职" />
                          <el-option label="兼职" value="兼职" />
                          <el-option label="实习" value="实习" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="电话">
                        <el-input v-model="currentEmployee.phone" placeholder="请输入电话" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="合同开始日期">
                        <el-date-picker
                          v-model="currentEmployee.contractStartDate"
                          type="date"
                          placeholder="请选择合同开始日期"
                          style="width: 100%"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="邮箱">
                        <el-input v-model="currentEmployee.email" placeholder="请输入邮箱" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="合同结束日期">
                        <el-date-picker
                          v-model="currentEmployee.contractEndDate"
                          type="date"
                          placeholder="请选择合同结束日期"
                          style="width: 100%"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="社保卡号">
                        <el-input v-model="currentEmployee.socialSecurityNo" placeholder="请输入社保卡号" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="员工卡号">
                        <el-input v-model="currentEmployee.employeeCardNo" placeholder="请输入员工卡号" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="银行卡号">
                        <el-input v-model="currentEmployee.bankCardNo" placeholder="请输入银行卡号" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="开户银行">
                        <el-select v-model="currentEmployee.bankName" placeholder="请选择开户银行" style="width: 100%" :disabled="!isEditing">
                          <el-option label="工商银行" value="工商银行" />
                          <el-option label="建设银行" value="建设银行" />
                          <el-option label="农业银行" value="农业银行" />
                          <el-option label="中国银行" value="中国银行" />
                          <el-option label="交通银行" value="交通银行" />
                          <el-option label="招商银行" value="招商银行" />
                          <el-option label="浦发银行" value="浦发银行" />
                          <el-option label="中信银行" value="中信银行" />
                          <el-option label="光大银行" value="光大银行" />
                          <el-option label="民生银行" value="民生银行" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="籍贯">
                        <el-cascader
                          v-model="currentEmployee.hometown"
                          :options="provinceOptions"
                          :props="{ expandTrigger: 'hover' }"
                          placeholder="请选择籍贯"
                          style="width: 100%"
                          :disabled="!isEditing"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="民族">
                        <el-select v-model="currentEmployee.ethnicity" placeholder="请选择民族" style="width: 100%" :disabled="!isEditing">
                          <el-option label="汉族" value="汉族" />
                          <el-option label="壮族" value="壮族" />
                          <el-option label="满族" value="满族" />
                          <el-option label="回族" value="回族" />
                          <el-option label="苗族" value="苗族" />
                          <el-option label="维吾尔族" value="维吾尔族" />
                          <el-option label="土家族" value="土家族" />
                          <el-option label="彝族" value="彝族" />
                          <el-option label="蒙古族" value="蒙古族" />
                          <el-option label="藏族" value="藏族" />
                          <el-option label="布依族" value="布依族" />
                          <el-option label="侗族" value="侗族" />
                          <el-option label="瑶族" value="瑶族" />
                          <el-option label="朝鲜族" value="朝鲜族" />
                          <el-option label="白族" value="白族" />
                          <el-option label="其他" value="其他" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="24">
                      <el-form-item label="现居住址">
                        <el-input
                          v-model="currentEmployee.address"
                          type="textarea"
                          :rows="2"
                          placeholder="请输入现居住址"
                          :disabled="!isEditing"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>
              <el-tab-pane label="工厂基本信息" name="factory">
                <el-form :model="currentEmployee.factoryInfo" label-width="120px">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="工厂名称">
                        <el-input v-model="currentEmployee.factoryInfo.name" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="工厂代码">
                        <el-input v-model="currentEmployee.factoryInfo.code" placeholder="请输入工厂代码" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form-item label="工厂地址">
                        <el-input v-model="currentEmployee.factoryInfo.address" placeholder="请输入工厂地址" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="部门">
                        <el-select v-model="currentEmployee.factoryInfo.departmentId" placeholder="请选择部门" style="width: 100%" :disabled="!isEditing">
                          <el-option 
                            v-for="dept in departmentOptions" 
                            :key="dept.id" 
                            :label="dept.name" 
                            :value="dept.id" 
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="班组">
                        <el-select v-model="currentEmployee.factoryInfo.team" placeholder="请选择班组" style="width: 100%" :disabled="!isEditing">
                          <el-option label="早班组" value="早班组" />
                          <el-option label="中班组" value="中班组" />
                          <el-option label="晚班组" value="晚班组" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="状态">
                        <el-select v-model="currentEmployee.factoryInfo.status" placeholder="请选择状态" style="width: 100%" :disabled="!isEditing">
                          <el-option label="在职" value="active" />
                          <el-option label="离职" value="inactive" />
                          <el-option label="试用期" value="probation" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="固定班次">
                        <el-select v-model="currentEmployee.factoryInfo.fixedShift" placeholder="请选择固定班次" style="width: 100%" :disabled="!isEditing">
                          <el-option label="早班" value="早班" />
                          <el-option label="中班" value="中班" />
                          <el-option label="晚班" value="晚班" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="申请离职日期">
                        <el-date-picker
                          v-model="currentEmployee.factoryInfo.resignationApplyDate"
                          type="date"
                          placeholder="请选择申请离职日期"
                          style="width: 100%"
                          :disabled="!isEditing"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="离职日期">
                        <el-date-picker
                          v-model="currentEmployee.factoryInfo.resignationDate"
                          type="date"
                          placeholder="请选择离职日期"
                          style="width: 100%"
                          :disabled="!isEditing"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="入职日期">
                        <el-date-picker
                          v-model="currentEmployee.factoryInfo.entryDate"
                          type="date"
                          placeholder="请选择入职日期"
                          style="width: 100%"
                          :disabled="!isEditing"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="试用开始日期">
                        <el-date-picker
                          v-model="currentEmployee.factoryInfo.probationStartDate"
                          type="date"
                          placeholder="请选择试用开始日期"
                          style="width: 100%"
                          :disabled="!isEditing"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="试用期(天)">
                        <el-input-number
                          v-model="currentEmployee.factoryInfo.probationDays"
                          :min="0"
                          :max="180"
                          style="width: 100%"
                          :disabled="!isEditing"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="工资类型">
                        <el-select v-model="currentEmployee.factoryInfo.salaryType" placeholder="请选择工资类型" style="width: 100%" :disabled="!isEditing">
                          <el-option label="计时工资" value="计时工资" />
                          <el-option label="计件工资" value="计件工资" />
                          <el-option label="固定工资" value="固定工资" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="基本薪资">
                        <el-input-number
                          v-model="currentEmployee.factoryInfo.baseSalary"
                          :min="0"
                          :precision="2"
                          :step="100"
                          style="width: 100%"
                          :disabled="!isEditing"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="岗位津贴">
                        <el-input-number v-model="currentEmployee.factoryInfo.positionAllowance" :min="0" :precision="2" :step="100" style="width: 100%" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="房屋津贴">
                        <el-input-number v-model="currentEmployee.factoryInfo.housingAllowance" :min="0" :precision="2" :step="100" style="width: 100%" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="女工津贴">
                        <el-input-number v-model="currentEmployee.factoryInfo.femaleAllowance" :min="0" :precision="2" :step="100" style="width: 100%" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="全勤补贴">
                        <el-input-number v-model="currentEmployee.factoryInfo.attendanceBonus" :min="0" :precision="2" :step="100" style="width: 100%" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="个税">
                        <el-input-number v-model="currentEmployee.factoryInfo.personalTax" :min="0" :precision="2" :step="100" style="width: 100%" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="24">
                      <el-form-item label="离职原因">
                        <el-input
                          v-model="currentEmployee.factoryInfo.leaveReason"
                          type="textarea"
                          :rows="2"
                          placeholder="请输入离职原因"
                          :disabled="!isEditing"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>
              <el-tab-pane label="员工等级" name="level">
                <el-form :model="levelInfo" label-width="100px" class="detail-form">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="员工等级">
                        <el-select v-model="levelInfo.level" style="width: 100%" :disabled="!isEditing">
                          <el-option label="初级" value="junior" />
                          <el-option label="中级" value="intermediate" />
                          <el-option label="高级" value="senior" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="职位">
                        <el-select v-model="currentEmployee.positionId" placeholder="请选择职位" style="width: 100%" :disabled="!isEditing">
                          <el-option 
                            v-for="pos in positionOptions" 
                            :key="pos.id" 
                            :label="pos.name" 
                            :value="pos.id" 
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="技能等级">
                        <el-rate v-model="levelInfo.skillLevel" :max="5" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="绩效等级">
                        <el-rate v-model="levelInfo.performanceLevel" :max="5" :disabled="!isEditing" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>
              <el-tab-pane label="附件列表" name="attachments">
                <!-- 删除这里的附件列表内容，因为在tabs-content中已经有了 -->
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="tabs-content">
            <div v-show="activeTab === 'attachments'" class="info-section">
              <div class="upload-area">
                <div class="upload-content">
                  <div class="file-list">
                    <el-upload
                      class="upload-demo"
                      action="#"
                      :auto-upload="false"
                      :on-change="handleFileChange"
                      :limit="3"
                      multiple
                      :disabled="!isEditing"
                    >
                      <template #trigger>
                        <el-button type="primary" :disabled="!isEditing" style="width: 120px; height: 32px; margin: 0;">选择文件</el-button>
                      </template>
                      <el-button 
                        class="ml-3" 
                        type="success" 
                        :disabled="!isEditing"
                        style="width: 120px; height: 32px; margin: 0;" 
                        @click="handleUpload"
                      >
                        上传到服务器
                      </el-button>
                    </el-upload>
                  </div>
                </div>
              </div>
              <el-table :data="attachmentsList" style="width: 100%; margin-top: 20px">
                <el-table-column prop="employeeId" label="工号" width="120">
                  <template #default>
                    <span>{{ currentEmployee?.employeeId }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="文件名" />
                <el-table-column prop="size" label="大小" width="120" />
                <el-table-column prop="uploadTime" label="上传时间" width="180" />
                <el-table-column label="操作" width="120">
                  <template #default="{ row }">
                    <el-button type="primary" link @click="handleDownload(row)">下载</el-button>
                    <el-button 
                      type="danger" 
                      link 
                      @click="handleRemoveFile(row)"
                      :disabled="!isEditing"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
        <div v-else class="no-selection">
          <el-empty description="请选择一个员工查看详细信息" />
        </div>
      </el-main>
    </el-container>

    <!-- 员工表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增员工' : '编辑员工'"
      width="500px"
    >
      <el-form
        ref="employeeFormRef"
        :model="employeeForm"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="工号" prop="employeeId">
          <el-input v-model="employeeForm.employeeId" :disabled="dialogType === 'edit'" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="employeeForm.name" />
        </el-form-item>
        <el-form-item label="部门" prop="departmentId">
          <el-select v-model="employeeForm.departmentId" placeholder="请选择部门">
            <el-option 
              v-for="dept in departmentOptions" 
              :key="dept.id" 
              :label="dept.name" 
              :value="dept.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="职位" prop="positionId">
          <el-select v-model="employeeForm.positionId" placeholder="请选择职位">
            <el-option 
              v-for="pos in positionOptions" 
              :key="pos.id" 
              :label="pos.name" 
              :value="pos.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="employeeForm.phone" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="employeeForm.email" />
        </el-form-item>
        <el-form-item label="入职日期" prop="entryDate">
          <el-date-picker
            v-model="employeeForm.entryDate"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Upload, Download, Edit, Check, Delete, User } from '@element-plus/icons-vue'
import { getEmployees, getEmployee, createEmployee, updateEmployee, deleteEmployee, importEmployees, exportEmployees } from '@/api/employee'
import { getDepartments } from '@/api/department'
import { getPositions } from '@/api/position'

// 搜索和筛选条件
const searchQuery = ref('')
const departmentFilter = ref(null)
const statusFilter = ref('')

// 表格数据
const loading = ref(false)
const employeeList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 当前选中的标签页
const activeTab = ref('employee')

// 当前选中的员工
const currentEmployee = ref(null)

// 使用普通对象而不是响应式对象，因为这些数据不需要响应式
const factoryInfo = {
  salaryType: 'time',
  team: 'morning',
  baseSalary: 3000,
  workYears: 0
}

const basicInfo = {
  idCard: '******************',
  birthday: '1990-01-01',
  gender: '男',
  maritalStatus: '已婚',
  education: '本科'
}

const levelInfo = {
  level: 'junior',
  skillLevel: 3,
  performanceLevel: 4
}

// 使用普通数组而不是响应式数组，因为这些数据不需要响应式
const attachmentsList = [
  {
    name: '身份证正面.jpg',
    size: '1.2MB',
    uploadTime: '2024-01-15 10:30:00'
  },
  {
    name: '学历证书.pdf',
    size: '2.5MB',
    uploadTime: '2024-01-15 10:31:00'
  }
]

// 表单对话框
const dialogVisible = ref(false)
const dialogType = ref('add')
const employeeFormRef = ref(null)
const employeeForm = reactive({
  employeeId: '',
  name: '',
  departmentId: null,
  positionId: null,
  phone: '',
  email: '',
  entryDate: ''
})

// 表单验证规则
const rules = {
  employeeId: [
    { required: true, message: '请输入工号', trigger: 'blur' },
    { min: 4, max: 20, message: '长度在 4 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  departmentId: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  positionId: [
    { required: true, message: '请选择职位', trigger: 'change' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  entryDate: [
    { required: true, message: '请选择入职日期', trigger: 'change' }
  ]
}

// 选择员工
const handleSelectEmployee = async (employee) => {
  try {
    // 如果已经有详细信息，直接使用
    if (employee.age !== undefined) {
      currentEmployee.value = { ...employee }
      activeTab.value = 'employee'
      return
    }
    
    // 否则获取详细信息
    const response = await getEmployee(employee.id)
    if (response.data.code === 200 && response.data.data) {
      currentEmployee.value = response.data.data
      activeTab.value = 'employee'
    } else {
      ElMessage.error('获取员工详情失败')
    }
  } catch (error) {
    console.error('获取员工详情失败:', error)
    ElMessage.error('获取员工详情失败，请稍后重试')
  }
}

// 文件上传相关方法
const handleFileChange = (file) => {
  console.log('选择文件:', file)
}

const handleUpload = () => {
  ElMessage.success('文件上传成功')
}

const handleDownload = (file) => {
  console.log('下载文件:', file)
}

const handleRemoveFile = (file) => {
  console.log('删除文件:', file)
}

// 添加搜索类型
const searchType = ref('name')

// 根据搜索类型获取输入框占位符
const getPlaceholder = computed(() => {
  switch (searchType.value) {
    case 'employeeId': return '请输入工号'
    case 'name': return '请输入姓名'
    case 'phone': return '请输入电话'
    case 'email': return '请输入邮箱'
    case 'employeeCardNo': return '请输入员工卡号'
    default: return '请输入关键词'
  }
})

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await getDepartments({ pageSize: 100 }) // 获取更多部门
    if (response.data.code === 200 && response.data.data) {
      // 提取部门名称列表
      departments.value = response.data.data.list.map(item => item.name)
      // 保存完整部门数据供下拉框使用
      departmentOptions.value = response.data.data.list
    }
  } catch (error) {
    console.error('获取部门列表失败:', error)
  }
}

// 获取职位列表
const fetchPositions = async () => {
  try {
    const response = await getPositions({ pageSize: 100 }) // 获取更多职位
    if (response.data.code === 200 && response.data.data) {
      // 保存完整职位数据供下拉框使用
      positionOptions.value = response.data.data.list
    }
  } catch (error) {
    console.error('获取职位列表失败:', error)
  }
}

// 搜索处理函数
const handleSearch = () => {
  currentPage.value = 1 // 重置页码
  fetchEmployeeList()
}

// 获取员工列表
const fetchEmployeeList = async () => {
  loading.value = true
  try {
    // 构建请求参数
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      searchType: searchType.value,
      searchQuery: searchQuery.value,
      status: statusFilter.value || ''
    }

    // 如果有部门筛选，直接使用部门ID
    if (departmentFilter.value) {
      params.department = departmentFilter.value
    }

    // 调用API获取员工列表
    const response = await getEmployees(params)
    
    if (response.data.code === 200 && response.data.data) {
      const { total: totalCount, list } = response.data.data
      
      // 处理返回数据
      employeeList.value = list
      total.value = totalCount
      
      // 如果当前没有选中的员工，选择第一个员工
      if (!currentEmployee.value && employeeList.value.length > 0) {
        handleSelectEmployee(employeeList.value[0])
      }
    } else {
      ElMessage.error('获取员工列表失败')
    }
    
    loading.value = false
  } catch (error) {
    console.error('获取员工列表失败:', error)
    ElMessage.error('获取员工列表失败，请稍后重试')
    loading.value = false
  }
}

// 新增员工
const handleAdd = () => {
  dialogType.value = 'add'
  Object.keys(employeeForm).forEach(key => {
    employeeForm[key] = ''
  })
  // 设置入职日期默认值为当前日期
  employeeForm.entryDate = new Date()
  // 设置状态默认值为active(在职)
  employeeForm.status = 'active'
  dialogVisible.value = true
}

// 添加编辑状态控制
const isEditing = ref(false)

// 修改编辑按钮处理函数
const handleEdit = (employee) => {
  if (!employee) return
  isEditing.value = true
  ElMessage.success('已进入编辑模式')
}

// 修改保存按钮处理函数
const handleSave = async () => {
  try {
    if (!currentEmployee.value) return
    
    const employeeData = { ...currentEmployee.value }
    
    // 获取部门名称，用于显示
    if (employeeData.factoryInfo && employeeData.factoryInfo.departmentId) {
      const selectedDept = departmentOptions.value.find(dept => dept.id === employeeData.factoryInfo.departmentId)
      employeeData.factoryInfo.department = selectedDept ? selectedDept.name : ''
    }
    
    // 获取职位名称，用于显示
    if (employeeData.positionId) {
      const selectedPos = positionOptions.value.find(pos => pos.id === employeeData.positionId)
      employeeData.position = selectedPos ? selectedPos.name : ''
    }
    
    // 确保工厂信息中baseSalary是字符串类型，workYears是数字类型
    if (employeeData.factoryInfo) {
      // 将baseSalary转换为字符串
      if (employeeData.factoryInfo.baseSalary !== undefined) {
        employeeData.factoryInfo.baseSalary = String(employeeData.factoryInfo.baseSalary)
      }
      
      // 确保workYears是数字类型
      if (employeeData.factoryInfo.workYears !== undefined && employeeData.factoryInfo.workYears !== null) {
        employeeData.factoryInfo.workYears = Number(employeeData.factoryInfo.workYears)
      }
    }
    
    // 调用API更新员工信息
    await updateEmployee(employeeData.id, employeeData)
    
    isEditing.value = false
    ElMessage.success('保存成功')
    fetchEmployeeList() // 刷新列表
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请稍后重试')
  }
}

// 添加取消编辑函数
const handleCancelEdit = () => {
  isEditing.value = false
  ElMessage.info('已取消编辑')
  // 重新获取数据以恢复原始状态
  fetchEmployeeList()
}

// 删除员工
const handleDelete = async (employee) => {
  if (!employee) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除员工 ${employee.name} 吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用API删除员工
    await deleteEmployee(employee.id)
    
    ElMessage.success('删除成功')
    if (currentEmployee.value?.id === employee.id) {
      currentEmployee.value = null
    }
    fetchEmployeeList() // 刷新列表
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除员工失败:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!employeeFormRef.value) return
  
  try {
    await employeeFormRef.value.validate()
    
    // 准备提交数据
    const submitData = { ...employeeForm }
    
    // 获取部门名称，用于显示
    if (submitData.departmentId) {
      const selectedDept = departmentOptions.value.find(dept => dept.id === submitData.departmentId)
      submitData.department = selectedDept ? selectedDept.name : ''
    }
    
    // 获取职位名称，用于显示
    if (submitData.positionId) {
      const selectedPos = positionOptions.value.find(pos => pos.id === submitData.positionId)
      submitData.position = selectedPos ? selectedPos.name : ''
    }
    
    // 确保工厂信息中baseSalary是字符串类型，workYears是数字类型
    if (submitData.factoryInfo) {
      // 将baseSalary转换为字符串
      if (submitData.factoryInfo.baseSalary !== undefined) {
        submitData.factoryInfo.baseSalary = String(submitData.factoryInfo.baseSalary)
      }
      
      // 确保workYears是数字类型
      if (submitData.factoryInfo.workYears !== undefined && submitData.factoryInfo.workYears !== null) {
        submitData.factoryInfo.workYears = Number(submitData.factoryInfo.workYears)
      }
    }
    
    if (dialogType.value === 'add') {
      // 创建新员工
      await createEmployee(submitData)
      ElMessage.success('添加成功')
    } else {
      // 更新员工
      await updateEmployee(submitData.id, submitData)
      ElMessage.success('更新成功')
    }
    
    dialogVisible.value = false
    fetchEmployeeList() // 刷新列表
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 分页处理函数
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchEmployeeList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchEmployeeList()
}

// 在 script setup 部分添加省份数据
const provinceOptions = [
  {
    value: '广东',
    label: '广东省',
    children: [
      { value: '广州', label: '广州市' },
      { value: '深圳', label: '深圳市' },
      { value: '东莞', label: '东莞市' },
      { value: '佛山', label: '佛山市' },
      { value: '中山', label: '中山市' }
    ]
  },
  {
    value: '湖南',
    label: '湖南省',
    children: [
      { value: '长沙', label: '长沙市' },
      { value: '株洲', label: '株洲市' },
      { value: '湘潭', label: '湘潭市' },
      { value: '衡阳', label: '衡阳市' }
    ]
  },
  // 可以继续添加更多省份数据
]

// 添加部门数据
const departments = ref([])
// 部门选项列表 - 用于下拉选择框
const departmentOptions = ref([])

// 添加职位数据选项列表
const positionOptions = ref([])

// 添加状态数据
const statuses = [
  { label: '在职', value: 'active' },
  { label: '离职', value: 'inactive' }
]

// 添加工号数据
const employeeIds = ref([
  'EMP001',
  'EMP002',
  'EMP003',
  'EMP004',
  'EMP005',
  'EMP006'
])

// 修改导入员工数据方法
const handleImport = () => {
  // 创建文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.xls'
  input.onchange = async (event) => {
    const file = event.target.files[0]
    if (file) {
      try {
        ElMessage.info('正在导入数据，请稍候...')
        const response = await importEmployees(file)
        if (response.data.code === 200) {
          ElMessage.success('导入成功')
          fetchEmployeeList() // 刷新列表
        } else {
          ElMessage.error(response.data.message || '导入失败')
        }
      } catch (error) {
        console.error('导入失败:', error)
        ElMessage.error('导入失败，请稍后重试')
      }
    }
  }
  input.click()
}

// 修改导出员工数据方法
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')
    
    // 构建导出参数，可以加入筛选条件
    const params = {
      searchType: searchType.value,
      searchQuery: searchQuery.value,
      department: departmentFilter.value,
      status: statusFilter.value
    }
    
    const response = await exportEmployees(params)
    
    // 创建Blob对象并下载
    const blob = new Blob([response.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `员工数据_${new Date().toISOString().split('T')[0]}.xlsx`
    link.click()
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  }
}

// 初始化
onMounted(() => {
  fetchDepartments()
  fetchPositions()
  fetchEmployeeList()
})
</script>

<style scoped>
.employee-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-filters {
  display: flex;
  gap: 10px;
  align-items: center;
}

.filter-item {
  margin-right: 10px;
  width: 100px;
}

.search-input {
  width: 240px;
  margin-right: 10px;
}

.search-button {
  display: none;
}

.operations {
  display: flex;
  gap: 10px;
}

.main-container {
  flex: 1;
  overflow: hidden;
}

.employee-list {
  border-right: 1px solid #dcdfe6;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
}

.employee-items {
  padding: 10px;
  flex: 1;
  overflow-y: auto;
}

.employee-item {
  padding: 15px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 10px;
  border: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
  gap: 15px;
}

.employee-item:hover {
  background-color: #f5f7fa;
}

.employee-item.is-active {
  background-color: #ecf5ff;
  border-color: #409eff;
}

.employee-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.employee-avatar .el-image {
  width: 100%;
  height: 100%;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  color: #909399;
}

.employee-info {
  flex: 1;
  min-width: 0;
}

.employee-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.employee-id {
  font-size: 13px;
  color: #909399;
  margin-bottom: 5px;
}

.employee-dept {
  font-size: 13px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pagination-container {
  padding: 10px;
  border-top: 1px solid #dcdfe6;
  display: flex;
  justify-content: center;
}

.employee-detail {
  padding: 20px;
  background-color: #f5f7fa;
  height: 100%;
  overflow: hidden;
}

.detail-container {
  background-color: #fff;
  border-radius: 4px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tabs-header {
  background-color: #fff;
  border-bottom: 1px solid #dcdfe6;
}

.tabs-content {
  flex: 1;
  overflow: auto;
  padding: 20px;
}

:deep(.el-tabs__header) {
  margin: 0;
  padding: 0 20px;
}

:deep(.el-tabs__nav-wrap) {
  margin-bottom: 0;
}

:deep(.el-tabs__nav) {
  border: none;
}

:deep(.el-tabs__item) {
  height: 40px;
  line-height: 40px;
  padding: 0 20px;
  font-size: 14px;
  color: #606266;
  font-weight: bold;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: bold;
}

:deep(.el-tabs__active-bar) {
  height: 3px;
  background-color: #409eff;
  border-radius: 3px;
}

.info-section {
  height: 100%;
  background-color: #fff;
}

.detail-form {
  max-width: 1000px;
  margin: 0 auto;
}

.upload-area {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.upload-content {
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 140px;
}

.file-list {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
}

.file-list :deep(.el-upload) {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-list :deep(.el-button) {
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.file-list :deep(.el-upload-list) {
  position: absolute;
  left: 0;
  top: 20px;
  margin: 0;
  padding: 0;
  width: 70%;
  display: grid;
  grid-template-columns: minmax(75px, 1fr);
  grid-template-rows: repeat(3, auto);
  gap: 12px;
  max-height: 180px;
  overflow-y: auto;
}

.file-list :deep(.el-upload-list__item) {
  margin: 0;
  padding: 8px 12px;
  transition: none;
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-width: 75px;
  width: 70%;
}

.file-list :deep(.el-upload-list__item:hover) {
  background-color: #e6e8eb;
}

.file-list :deep(.el-upload-list__item-name) {
  margin-right: 10px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-list :deep(.el-upload-list__item .el-icon) {
  height: auto;
  margin-top: 0;
  position: static;
  transform: none;
}

.file-list :deep(.el-upload-list__item-status-label) {
  display: none;
}

.no-selection {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.import-button {
  background-color: #67c23a;
  border-color: #67c23a;
  color: #ffffff;
}

.import-button:hover {
  background-color: #85ce61;
  border-color: #85ce61;
  color: #ffffff;
}

.import-button:active {
  background-color: #5daf34;
  border-color: #5daf34;
  color: #ffffff;
}

.export-button {
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: #ffffff;
}

.export-button:hover {
  background-color: #ebb563;
  border-color: #ebb563;
  color: #ffffff;
}

.export-button:active {
  background-color: #cf9236;
  border-color: #cf9236;
  color: #ffffff;
}
</style> 