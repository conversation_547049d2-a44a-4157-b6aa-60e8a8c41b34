using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using mes_system_server.Data;
using mes_system_server.DTOs;
using mes_system_server.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace mes_system_server.Controllers
{
    [Route("api/production-lines")]
    [ApiController]
    [AllowAnonymous]
    public class ProductionLinesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public ProductionLinesController(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取产线列表
        /// </summary>
        /// <param name="page">页码，默认为1</param>
        /// <param name="pageSize">每页记录数，默认为10</param>
        /// <param name="search">搜索关键词</param>
        /// <param name="includeWorkCenters">是否包含工作中心数据</param>
        /// <returns>产线列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetProductionLines(
            [FromQuery] int page = 1, 
            [FromQuery] int pageSize = 10, 
            [FromQuery] string search = "",
            [FromQuery] bool includeWorkCenters = false)
        {
            try
            {
                var query = _context.ProductionLines.Where(pl => pl.IsActive);

                // 应用搜索条件
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(pl => 
                        pl.Name.Contains(search) || 
                        pl.Code.Contains(search) ||
                        (pl.Description != null && pl.Description.Contains(search))
                    );
                }

                // 计算总记录数
                var total = await query.CountAsync();

                // 应用分页
                var productionLines = await query
                    .OrderBy(pl => pl.Id)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // 构建响应数据
                var productionLineDtos = new List<ProductionLineDto>();

                foreach (var pl in productionLines)
                {
                    var dto = new ProductionLineDto
                    {
                        Id = pl.Id,
                        Name = pl.Name,
                        Code = pl.Code,
                        Description = pl.Description,
                        IsActive = pl.IsActive,
                        CreateTime = pl.CreateTime,
                        UpdateTime = pl.UpdateTime
                    };

                    // 如果需要包含工作中心数据
                    if (includeWorkCenters)
                    {
                        var workCenters = await _context.WorkCenters
                            .Where(wc => wc.ProductionLineId == pl.Id && wc.IsActive)
                            .OrderBy(wc => wc.SortOrder)
                            .ToListAsync();

                        foreach (var wc in workCenters)
                        {
                            var equipment = await _context.Equipment
                                .Where(eq => eq.WorkCenterId == wc.Id && eq.IsActive)
                                .OrderBy(eq => eq.SortOrder)
                                .ToListAsync();

                            dto.WorkCenters.Add(new WorkCenterDto
                            {
                                Id = wc.Id,
                                Name = wc.Name,
                                Code = wc.Code,
                                Description = wc.Description,
                                ProductionLineId = wc.ProductionLineId,
                                SortOrder = wc.SortOrder,
                                IsActive = wc.IsActive,
                                CreateTime = wc.CreateTime,
                                UpdateTime = wc.UpdateTime,
                                Equipment = equipment.Select(eq => new EquipmentDto
                                {
                                    Id = eq.Id,
                                    Name = eq.Name,
                                    Code = eq.Code,
                                    Description = eq.Description,
                                    WorkCenterId = eq.WorkCenterId,
                                    SortOrder = eq.SortOrder,
                                    IsActive = eq.IsActive,
                                    CreateTime = eq.CreateTime,
                                    UpdateTime = eq.UpdateTime
                                }).ToList()
                            });
                        }
                    }

                    productionLineDtos.Add(dto);
                }

                var response = new ProductionLineResponseDto
                {
                    Total = total,
                    List = productionLineDtos
                };

                return Ok(ApiResponseDto<ProductionLineResponseDto>.Success(response));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取产线列表失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 根据ID获取产线详情
        /// </summary>
        /// <param name="id">产线ID</param>
        /// <returns>产线详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetProductionLine(int id)
        {
            try
            {
                var productionLine = await _context.ProductionLines
                    .FirstOrDefaultAsync(pl => pl.Id == id && pl.IsActive);

                if (productionLine == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("产线不存在", 404));
                }

                var productionLineDto = new ProductionLineDto
                {
                    Id = productionLine.Id,
                    Name = productionLine.Name,
                    Code = productionLine.Code,
                    Description = productionLine.Description,
                    IsActive = productionLine.IsActive,
                    CreateTime = productionLine.CreateTime,
                    UpdateTime = productionLine.UpdateTime
                };

                return Ok(ApiResponseDto<ProductionLineDto>.Success(productionLineDto));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取产线详情失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 创建产线
        /// </summary>
        /// <param name="productionLineDto">产线信息</param>
        /// <returns>创建的产线</returns>
        [HttpPost]
        public async Task<IActionResult> CreateProductionLine([FromBody] CreateProductionLineDto productionLineDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                // 检查产线编码是否已存在
                var existingProductionLine = await _context.ProductionLines
                    .FirstOrDefaultAsync(pl => pl.Code == productionLineDto.Code && pl.IsActive);

                if (existingProductionLine != null)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("产线编码已存在"));
                }

                var productionLine = new ProductionLine
                {
                    Name = productionLineDto.Name,
                    Code = productionLineDto.Code,
                    Description = productionLineDto.Description,
                    IsActive = true,
                    CreateTime = DateTime.UtcNow,
                    UpdateTime = DateTime.UtcNow
                };

                _context.ProductionLines.Add(productionLine);
                await _context.SaveChangesAsync();

                var createdProductionLineDto = new ProductionLineDto
                {
                    Id = productionLine.Id,
                    Name = productionLine.Name,
                    Code = productionLine.Code,
                    Description = productionLine.Description,
                    IsActive = productionLine.IsActive,
                    CreateTime = productionLine.CreateTime,
                    UpdateTime = productionLine.UpdateTime
                };

                return CreatedAtAction(nameof(GetProductionLine), new { id = productionLine.Id }, 
                    ApiResponseDto<ProductionLineDto>.Success(createdProductionLineDto, "添加成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"创建产线失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 更新产线
        /// </summary>
        /// <param name="id">产线ID</param>
        /// <param name="productionLineDto">产线信息</param>
        /// <returns>更新的产线</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateProductionLine(int id, [FromBody] UpdateProductionLineDto productionLineDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                var productionLine = await _context.ProductionLines
                    .FirstOrDefaultAsync(pl => pl.Id == id && pl.IsActive);

                if (productionLine == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("产线不存在", 404));
                }

                // 检查产线编码是否与其他产线冲突
                var existingProductionLine = await _context.ProductionLines
                    .FirstOrDefaultAsync(pl => pl.Code == productionLineDto.Code && pl.Id != id && pl.IsActive);

                if (existingProductionLine != null)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("产线编码已存在"));
                }

                // 更新产线信息
                productionLine.Name = productionLineDto.Name;
                productionLine.Code = productionLineDto.Code;
                productionLine.Description = productionLineDto.Description;
                productionLine.IsActive = productionLineDto.IsActive;
                productionLine.UpdateTime = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                var updatedProductionLineDto = new ProductionLineDto
                {
                    Id = productionLine.Id,
                    Name = productionLine.Name,
                    Code = productionLine.Code,
                    Description = productionLine.Description,
                    IsActive = productionLine.IsActive,
                    CreateTime = productionLine.CreateTime,
                    UpdateTime = productionLine.UpdateTime
                };

                return Ok(ApiResponseDto<ProductionLineDto>.Success(updatedProductionLineDto, "更新成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"更新产线失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 删除产线
        /// </summary>
        /// <param name="id">产线ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteProductionLine(int id)
        {
            try
            {
                var productionLine = await _context.ProductionLines
                    .FirstOrDefaultAsync(pl => pl.Id == id && pl.IsActive);

                if (productionLine == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("产线不存在", 404));
                }

                // 检查是否有关联的工作中心
                var hasWorkCenters = await _context.WorkCenters
                    .AnyAsync(wc => wc.ProductionLineId == id && wc.IsActive);

                if (hasWorkCenters)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("该产线下存在工作中心，无法删除"));
                }

                // 软删除
                productionLine.IsActive = false;
                productionLine.UpdateTime = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(ApiResponseDto<string>.Success(null, "删除成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"删除产线失败: {ex.Message}", 500));
            }
        }
    }
} 