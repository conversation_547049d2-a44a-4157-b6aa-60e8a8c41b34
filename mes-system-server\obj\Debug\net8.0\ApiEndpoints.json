[{"ContainingType": "mes_system_server.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "mes_system_server.DTOs.LoginDto", "IsRequired": true}], "ReturnTypes": [{"Type": "mes_system_server.DTOs.LoginResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "mes_system_server.Controllers.CompanyController", "Method": "GetCompanyInfo", "RelativePath": "api/company/info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "mes_system_server.Controllers.CompanyController", "Method": "UpdateCompanyInfo", "RelativePath": "api/company/info", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateDto", "Type": "mes_system_server.DTOs.UpdateCompanyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "mes_system_server.Controllers.UsersController", "Method": "GetUsers", "RelativePath": "api/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": false}, {"Name": "name", "Type": "System.String", "IsRequired": false}, {"Name": "department", "Type": "System.String", "IsRequired": false}, {"Name": "role", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[mes_system_server.DTOs.UserListDto, mes-system-server, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "mes_system_server.Controllers.UsersController", "Method": "CreateUser", "RelativePath": "api/users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createUserDto", "Type": "mes_system_server.DTOs.CreateUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "mes_system_server.Models.User", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "mes_system_server.Controllers.UsersController", "Method": "UpdateUser", "RelativePath": "api/users/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateUserDto", "Type": "mes_system_server.DTOs.UpdateUserDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "mes_system_server.Controllers.UsersController", "Method": "ResetPassword", "RelativePath": "api/users/{id}/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "mes_system_server.Controllers.UsersController", "Method": "UpdateUserStatus", "RelativePath": "api/users/{id}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "status", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Program+<>c__DisplayClass0_0", "Method": "<<Main>$>b__6", "RelativePath": "weatherforecast", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WeatherForecast[]", "MediaTypes": ["application/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]