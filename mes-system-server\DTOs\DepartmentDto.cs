using System;
using System.ComponentModel.DataAnnotations;

namespace mes_system_server.DTOs
{
    public class DepartmentDto
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "部门名称不能为空")]
        [StringLength(100, ErrorMessage = "部门名称最长为100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "部门代码不能为空")]
        [StringLength(20, ErrorMessage = "部门代码最长为20个字符")]
        public string Code { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "部门主管最长为50个字符")]
        public string Manager { get; set; } = string.Empty;

        public int? ParentId { get; set; }

        [Required(ErrorMessage = "部门层级不能为空")]
        [StringLength(1, ErrorMessage = "部门层级应为单个字符")]
        public string Level { get; set; } = "1";

        [Range(0, int.MaxValue, ErrorMessage = "员工数量必须是非负整数")]
        public int EmployeeCount { get; set; } = 0;

        [StringLength(500, ErrorMessage = "部门描述最长为500个字符")]
        public string Description { get; set; } = string.Empty;

        public DateTime CreateTime { get; set; }
    }

    public class CreateDepartmentDto
    {
        [Required(ErrorMessage = "部门名称不能为空")]
        [StringLength(100, ErrorMessage = "部门名称最长为100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "部门代码不能为空")]
        [StringLength(20, ErrorMessage = "部门代码最长为20个字符")]
        public string Code { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "部门主管最长为50个字符")]
        public string Manager { get; set; } = string.Empty;

        public int? ParentId { get; set; }

        [Required(ErrorMessage = "部门层级不能为空")]
        [StringLength(1, ErrorMessage = "部门层级应为单个字符")]
        public string Level { get; set; } = "1";

        [Range(0, int.MaxValue, ErrorMessage = "员工数量必须是非负整数")]
        public int EmployeeCount { get; set; } = 0;

        [StringLength(500, ErrorMessage = "部门描述最长为500个字符")]
        public string Description { get; set; } = string.Empty;
    }

    public class UpdateDepartmentDto
    {
        [Required(ErrorMessage = "部门名称不能为空")]
        [StringLength(100, ErrorMessage = "部门名称最长为100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "部门代码不能为空")]
        [StringLength(20, ErrorMessage = "部门代码最长为20个字符")]
        public string Code { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "部门主管最长为50个字符")]
        public string Manager { get; set; } = string.Empty;

        public int? ParentId { get; set; }

        [Required(ErrorMessage = "部门层级不能为空")]
        [StringLength(1, ErrorMessage = "部门层级应为单个字符")]
        public string Level { get; set; } = "1";

        [Range(0, int.MaxValue, ErrorMessage = "员工数量必须是非负整数")]
        public int EmployeeCount { get; set; } = 0;

        [StringLength(500, ErrorMessage = "部门描述最长为500个字符")]
        public string Description { get; set; } = string.Empty;
    }

    public class DepartmentResponseDto
    {
        public int Total { get; set; }
        public List<DepartmentDto> List { get; set; } = new List<DepartmentDto>();
    }
} 