using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using mes_system_server.Models;
using mes_system_server.Data;
using mes_system_server.DTOs;

namespace mes_system_server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class RolesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public RolesController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/roles
        [HttpGet]
        public async Task<ActionResult<ApiResponseDto<PagedRoleResultDto>>> GetRoles(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? search = null)
        {
            try
            {
                var query = _context.Roles.AsQueryable();

                // 搜索过滤
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(r => r.RoleName.Contains(search) || r.RoleCode.Contains(search));
                }

                // 获取总数
                var total = await query.CountAsync();

                // 分页查询
                var roles = await query
                    .OrderBy(r => r.Id)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(r => new RoleListDto
                    {
                        Id = r.Id,
                        RoleName = r.RoleName,
                        RoleCode = r.RoleCode,
                        Description = r.Description,
                        Status = r.Status,
                        CreateTime = r.CreateTime,
                        UpdateTime = r.UpdateTime
                    })
                    .ToListAsync();

                var result = new PagedRoleResultDto
                {
                    List = roles,
                    Total = total,
                    Page = page,
                    PageSize = pageSize
                };

                return Ok(new ApiResponseDto<PagedRoleResultDto>(200, "获取角色列表成功", result));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"获取角色列表失败: {ex.Message}"));
            }
        }

        // GET: api/roles/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponseDto<RoleDetailDto>>> GetRole(int id)
        {
            try
            {
                var role = await _context.Roles.FindAsync(id);

                if (role == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "角色不存在"));
                }

                // 获取该角色关联的用户
                var users = await _context.Users
                    .Where(u => u.RoleId == id)
                    .Select(u => new UserListDto
                    {
                        Id = u.Id,
                        Username = u.Username,
                        Name = u.Name,
                        Department = u.Department,
                        Role = u.Role,
                        Email = u.Email ?? "",
                        Phone = u.Phone ?? "",
                        Status = u.Status,
                        CreateTime = u.CreateTime
                    }).ToListAsync();

                var roleDetail = new RoleDetailDto
                {
                    Id = role.Id,
                    RoleName = role.RoleName,
                    RoleCode = role.RoleCode,
                    Description = role.Description,
                    Status = role.Status,
                    CreateTime = role.CreateTime,
                    UpdateTime = role.UpdateTime,
                    Users = users
                };

                return Ok(new ApiResponseDto<RoleDetailDto>(200, "获取角色详情成功", roleDetail));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"获取角色详情失败: {ex.Message}"));
            }
        }

        // POST: api/roles
        [HttpPost]
        public async Task<ActionResult<ApiResponseDto<RoleListDto>>> CreateRole(CreateRoleDto createRoleDto)
        {
            try
            {
                // 检查角色编码是否已存在
                var existingRole = await _context.Roles
                    .FirstOrDefaultAsync(r => r.RoleCode == createRoleDto.RoleCode);

                if (existingRole != null)
                {
                    return BadRequest(new ApiResponseDto<object>(400, "角色编码已存在"));
                }

                var role = new Role
                {
                    RoleName = createRoleDto.RoleName,
                    RoleCode = createRoleDto.RoleCode,
                    Description = createRoleDto.Description,
                    Status = createRoleDto.Status,
                    CreateTime = DateTime.Now
                };

                _context.Roles.Add(role);
                await _context.SaveChangesAsync();

                var roleDto = new RoleListDto
                {
                    Id = role.Id,
                    RoleName = role.RoleName,
                    RoleCode = role.RoleCode,
                    Description = role.Description,
                    Status = role.Status,
                    CreateTime = role.CreateTime,
                    UpdateTime = role.UpdateTime
                };

                return CreatedAtAction(nameof(GetRole), new { id = role.Id }, new ApiResponseDto<RoleListDto>(200, "创建角色成功", roleDto));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"创建角色失败: {ex.Message}"));
            }
        }

        // PUT: api/roles/{id}
        [HttpPut("{id}")]
        public async Task<ActionResult<ApiResponseDto<RoleListDto>>> UpdateRole(int id, UpdateRoleDto updateRoleDto)
        {
            try
            {
                var role = await _context.Roles.FindAsync(id);

                if (role == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "角色不存在"));
                }

                role.RoleName = updateRoleDto.RoleName;
                role.Description = updateRoleDto.Description;
                role.Status = updateRoleDto.Status;
                role.UpdateTime = DateTime.Now;

                await _context.SaveChangesAsync();

                var roleDto = new RoleListDto
                {
                    Id = role.Id,
                    RoleName = role.RoleName,
                    RoleCode = role.RoleCode,
                    Description = role.Description,
                    Status = role.Status,
                    CreateTime = role.CreateTime,
                    UpdateTime = role.UpdateTime
                };

                return Ok(new ApiResponseDto<RoleListDto>(200, "更新角色成功", roleDto));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"更新角色失败: {ex.Message}"));
            }
        }

        // PUT: api/roles/{id}/status
        [HttpPut("{id}/status")]
        public async Task<ActionResult<ApiResponseDto<object>>> UpdateRoleStatus(int id, RoleStatusDto statusDto)
        {
            try
            {
                var role = await _context.Roles.FindAsync(id);

                if (role == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "角色不存在"));
                }

                role.Status = statusDto.Status;
                role.UpdateTime = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new ApiResponseDto<object>(200, "角色状态更新成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"更新角色状态失败: {ex.Message}"));
            }
        }

        // DELETE: api/roles/{id}
        [HttpDelete("{id}")]
        public async Task<ActionResult<ApiResponseDto<object>>> DeleteRole(int id)
        {
            try
            {
                var role = await _context.Roles.FindAsync(id);

                if (role == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "角色不存在"));
                }

                // 检查是否有用户关联此角色
                var hasUsers = await _context.Users.AnyAsync(u => u.RoleId == id);
                if (hasUsers)
                {
                    return BadRequest(new ApiResponseDto<object>(400, "该角色下还有用户，无法删除"));
                }

                _context.Roles.Remove(role);
                await _context.SaveChangesAsync();

                return Ok(new ApiResponseDto<object>(200, "删除角色成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"删除角色失败: {ex.Message}"));
            }
        }

        // GET: api/roles/{id}/users
        [HttpGet("{id}/users")]
        public async Task<ActionResult<ApiResponseDto<List<UserListDto>>>> GetRoleUsers(int id)
        {
            try
            {
                var role = await _context.Roles.FindAsync(id);

                if (role == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "角色不存在"));
                }

                var users = await _context.Users
                    .Where(u => u.RoleId == id)
                    .Select(u => new UserListDto
                    {
                        Id = u.Id,
                        Username = u.Username,
                        Name = u.Name,
                        Department = u.Department,
                        Role = u.Role,
                        Email = u.Email ?? "",
                        Phone = u.Phone ?? "",
                        Status = u.Status,
                        CreateTime = u.CreateTime
                    }).ToListAsync();

                return Ok(new ApiResponseDto<List<UserListDto>>(200, "获取角色用户列表成功", users));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"获取角色用户列表失败: {ex.Message}"));
            }
        }

        // GET: api/roles/{id}/menus
        [HttpGet("{id}/menus")]
        public async Task<ActionResult<ApiResponseDto<List<string>>>> GetRoleMenus(int id)
        {
            try
            {
                var role = await _context.Roles.FindAsync(id);

                if (role == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "角色不存在"));
                }

                // 调试：先查看该角色的RoleMenus记录
                var roleMenuRecords = await _context.RoleMenus
                    .Where(rm => rm.RoleId == id)
                    .ToListAsync();

                Console.WriteLine($"角色ID {id} 的RoleMenus记录数: {roleMenuRecords.Count}");
                foreach (var record in roleMenuRecords)
                {
                    Console.WriteLine($"RoleMenu记录: RoleId={record.RoleId}, MenuId={record.MenuId}");
                }

                // 获取角色关联的菜单ID列表
                var menuIds = await _context.RoleMenus
                    .Where(rm => rm.RoleId == id)
                    .Join(_context.Menus, rm => rm.MenuId, m => m.Id, (rm, m) => m.MenuId)
                    .ToListAsync();

                Console.WriteLine($"最终返回的menuIds: {string.Join(", ", menuIds)}");

                return Ok(new ApiResponseDto<List<string>>(200, "获取角色菜单权限成功", menuIds));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取角色菜单权限异常: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                return StatusCode(500, new ApiResponseDto<object>(500, $"获取角色菜单权限失败: {ex.Message}"));
            }
        }

        // PUT: api/roles/{id}/menus
        [HttpPut("{id}/menus")]
        public async Task<ActionResult<ApiResponseDto<object>>> SaveRoleMenus(int id, SaveRoleMenusDto saveRoleMenusDto)
        {
            try
            {
                var role = await _context.Roles.FindAsync(id);

                if (role == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "角色不存在"));
                }

                // 开启事务
                using var transaction = await _context.Database.BeginTransactionAsync();

                try
                {
                    // 1. 删除该角色现有的菜单权限
                    var existingRoleMenus = await _context.RoleMenus
                        .Where(rm => rm.RoleId == id)
                        .ToListAsync();

                    if (existingRoleMenus.Any())
                    {
                        _context.RoleMenus.RemoveRange(existingRoleMenus);
                    }

                    // 2. 添加新的菜单权限
                    if (saveRoleMenusDto.MenuIds != null && saveRoleMenusDto.MenuIds.Any())
                    {
                        var menuEntities = await _context.Menus
                            .Where(m => saveRoleMenusDto.MenuIds.Contains(m.MenuId))
                            .ToListAsync();

                        var newRoleMenus = menuEntities.Select(menu => new RoleMenu
                        {
                            RoleId = id,
                            MenuId = menu.Id,
                            CreateTime = DateTime.Now
                        }).ToList();

                        await _context.RoleMenus.AddRangeAsync(newRoleMenus);
                    }

                    // 3. 保存更改
                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    return Ok(new ApiResponseDto<object>(200, "保存角色菜单权限成功"));
                }
                catch (Exception)
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"保存角色菜单权限失败: {ex.Message}"));
            }
        }

        // 临时方法：修复用户的RoleId数据
        [HttpPost("fix-user-roles")]
        public async Task<ActionResult<ApiResponseDto<object>>> FixUserRoles()
        {
            try
            {
                // 查找所有角色为"系统管理员"的用户，并设置其RoleId为1
                var adminUsers = await _context.Users
                    .Where(u => u.Role == "系统管理员" && u.RoleId == null)
                    .ToListAsync();

                foreach (var user in adminUsers)
                {
                    user.RoleId = 1; // 系统管理员角色ID
                }

                // 查找所有角色为"生产经理"的用户，并设置其RoleId为2
                var managerUsers = await _context.Users
                    .Where(u => u.Role == "生产经理" && u.RoleId == null)
                    .ToListAsync();

                foreach (var user in managerUsers)
                {
                    user.RoleId = 2; // 生产经理角色ID
                }

                // 查找所有角色为"质量检验员"的用户，并设置其RoleId为3
                var inspectorUsers = await _context.Users
                    .Where(u => u.Role == "质量检验员" && u.RoleId == null)
                    .ToListAsync();

                foreach (var user in inspectorUsers)
                {
                    user.RoleId = 3; // 质量检验员角色ID
                }

                await _context.SaveChangesAsync();

                return Ok(new ApiResponseDto<object>(200, $"成功修复 {adminUsers.Count + managerUsers.Count + inspectorUsers.Count} 个用户的角色关联"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"修复用户角色关联失败: {ex.Message}"));
            }
        }

        // POST: api/roles/initialize-permissions
        [HttpPost("initialize-permissions")]
        public async Task<ActionResult<ApiResponseDto<object>>> InitializeRoleMenuPermissions()
        {
            try
            {
                // 检查是否已有角色菜单数据
                var existingCount = await _context.RoleMenus.CountAsync();
                if (existingCount > 0)
                {
                    return BadRequest(new ApiResponseDto<object>(400, "角色菜单权限数据已存在，无需重复初始化"));
                }

                // 获取角色和菜单数据
                var roles = await _context.Roles.ToListAsync();
                var menus = await _context.Menus.ToListAsync();

                if (!roles.Any() || !menus.Any())
                {
                    return BadRequest(new ApiResponseDto<object>(400, "请先初始化角色和菜单数据"));
                }

                var roleMenusToAdd = new List<RoleMenu>();

                // 系统管理员 (RoleId = 1) - 拥有所有权限
                var adminRole = roles.FirstOrDefault(r => r.Id == 1);
                if (adminRole != null)
                {
                    foreach (var menu in menus.Where(m => m.Type == "menu-item"))
                    {
                        roleMenusToAdd.Add(new RoleMenu
                        {
                            RoleId = adminRole.Id,
                            MenuId = menu.Id,
                            CreateTime = DateTime.Now
                        });
                    }
                }

                // 生产经理 (RoleId = 2) - 生产相关权限
                var managerRole = roles.FirstOrDefault(r => r.Id == 2);
                if (managerRole != null)
                {
                    var productionMenus = menus.Where(m => 
                        m.Type == "menu-item" && 
                        (m.MenuId.StartsWith("production-") || m.MenuId.StartsWith("quality-"))
                    ).ToList();

                    foreach (var menu in productionMenus)
                    {
                        roleMenusToAdd.Add(new RoleMenu
                        {
                            RoleId = managerRole.Id,
                            MenuId = menu.Id,
                            CreateTime = DateTime.Now
                        });
                    }
                }

                // 质量检验员 (RoleId = 3) - 质量相关权限
                var inspectorRole = roles.FirstOrDefault(r => r.Id == 3);
                if (inspectorRole != null)
                {
                    var qualityMenus = menus.Where(m => 
                        m.Type == "menu-item" && 
                        m.MenuId.StartsWith("quality-")
                    ).ToList();

                    foreach (var menu in qualityMenus)
                    {
                        roleMenusToAdd.Add(new RoleMenu
                        {
                            RoleId = inspectorRole.Id,
                            MenuId = menu.Id,
                            CreateTime = DateTime.Now
                        });
                    }
                }

                // 批量插入数据
                if (roleMenusToAdd.Any())
                {
                    await _context.RoleMenus.AddRangeAsync(roleMenusToAdd);
                    await _context.SaveChangesAsync();
                }

                return Ok(new ApiResponseDto<object>(200, $"成功初始化 {roleMenusToAdd.Count} 条角色菜单权限数据"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"初始化角色菜单权限失败: {ex.Message}"));
            }
        }
    }
} 