<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    width="75%"
    :before-close="handleClose"
    @open="handleOpen"
  >
    <div class="employee-selector">
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-input
          v-model="searchQuery"
          placeholder="请输入员工姓名或工号"
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button :icon="Search" @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
        
        <el-select v-model="departmentFilter" clearable placeholder="部门" @change="handleSearch">
          <el-option
            v-for="dept in departments"
            :key="dept.id"
            :label="dept.name"
            :value="dept.id"
          />
        </el-select>
      </div>
      
      <!-- 员工列表 -->
      <el-table
        v-loading="loading"
        :data="employeeList"
        border
        style="width: 100%; margin-top: 15px;"
        height="400px"
        @row-click="handleRowClick"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ textAlign: 'center', backgroundColor: '#f5f7fa' }"
      >
        <el-table-column prop="employeeId" label="工号" min-width="90" />
        <el-table-column prop="name" label="姓名" min-width="90" />
        <el-table-column prop="department" label="部门" min-width="90" />
        <el-table-column prop="position" label="职位" min-width="110" />
        <el-table-column prop="phone" label="手机号" min-width="120" />
        <el-table-column label="操作" min-width="80">
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              @click.stop="handleSelect(row)"
            >
              选择
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { getEmployees } from '@/api/employee'
import { getDepartments } from '@/api/department'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '选择员工'
  }
})

const emit = defineEmits(['update:visible', 'select'])

// 计算属性连接visible与对话框状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => {
    emit('update:visible', val)
  }
})

// 搜索和筛选条件
const searchQuery = ref('')
const departmentFilter = ref(null)

// 部门数据
const departments = ref([])

// 表格数据
const loading = ref(false)
const employeeList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const res = await getDepartments()
    if (res.code === 200 && res.data) {
      departments.value = res.data.list || []
    }
  } catch (error) {
    console.error('获取部门列表失败:', error)
  }
}

// 获取员工列表
const fetchEmployeeList = async () => {
  console.log('开始获取员工列表')
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      searchType: 'name',
      searchQuery: searchQuery.value || undefined,
      department: departmentFilter.value || undefined
    }
    
    console.log('员工查询参数:', params)
    
    try {
      const response = await getEmployees(params)
      console.log('API返回结果:', response)
      
      // 处理后端返回的数据格式
      if (response && response.data && response.data.code === 200) {
        // 使用正确的数据结构提取员工列表
        employeeList.value = response.data.data.list || []
        total.value = response.data.data.total || 0
        
        console.log('处理后的员工列表:', employeeList.value)
        console.log('总数:', total.value)
      } else {
        console.log('API返回数据格式不正确')
        employeeList.value = []
        total.value = 0
      }
    } catch (error) {
      console.error('API调用失败:', error)
      employeeList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取员工列表失败:', error)
    ElMessage.error('获取员工列表失败，请稍后重试')
    employeeList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchEmployeeList()
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchEmployeeList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchEmployeeList()
}

// 点击行
const handleRowClick = (row) => {
  handleSelect(row)
}

// 选择员工
const handleSelect = (row) => {
  emit('select', {
    employeeId: row.employeeId,
    name: row.name
  })
  dialogVisible.value = false
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 对话框打开时的回调
const handleOpen = () => {
  console.log('对话框已打开')
  fetchDepartments()
  fetchEmployeeList()
}

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  console.log('EmployeeSelector visible changed:', newVal)
})
</script>

<style scoped>
.employee-selector {
  padding: 10px;
}

.search-area {
  display: flex;
  margin-bottom: 15px;
  gap: 10px;
}

.search-input {
  width: 350px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}

:deep(.el-table .cell) {
  padding-left: 5px;
  padding-right: 5px;
  text-align: center;
}
</style> 