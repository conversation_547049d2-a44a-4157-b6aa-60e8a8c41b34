import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const service = axios.create({
  timeout: 300000, // 请求超时时间 (5分钟，适合大文件上传)
})

// 获取完整URL（包含查询参数）
const getFullUrl = (config) => {
  const baseURL = 'http://111.230.239.197:5221'
  const path = config.url.startsWith('/') ? config.url : `/${config.url}`
  let url = `${baseURL}${path}`
  
  // 添加查询参数
  if (config.params) {
    const queryString = Object.entries(config.params)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&')
    if (queryString) {
      url += `?${queryString}`
    }
  }
  
  return url
}

// request拦截器
service.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    
    // 设置请求头
    if (config.method === 'post' || config.method === 'put') {
      config.headers['Content-Type'] = 'application/json'
    }
    
    // 为所有请求设置Accept头，确保接收JSON响应
    config.headers['Accept'] = 'application/json'
    
    // 特殊处理塑胶五金型号相关请求
    if (config.url.includes('hardware-plastic')) {
      console.group('🔍 塑胶五金型号API请求')
      console.log('请求URL:', getFullUrl(config))
      console.log('请求方法:', config.method.toUpperCase())
      console.log('请求头:', config.headers)
      if (config.data) console.log('请求数据:', JSON.stringify(config.data))
      if (config.params) console.log('请求参数:', JSON.stringify(config.params))
      console.groupEnd()
    }
    
    // 打印请求信息
    console.group('🚀 发送请求')
    console.log('完整URL:', getFullUrl(config))
    console.log('Method:', config.method)
    console.log('Headers:', config.headers)
    if (config.data) console.log('Data:', config.data)
    if (config.params) console.log('Params:', config.params)
    console.groupEnd()
    return config
  },
  error => {
    console.error('❌ 请求错误：', error)
    return Promise.reject(error)
  }
)

// response拦截器
service.interceptors.response.use(
  response => {
    // 特殊处理塑胶五金型号相关响应
    if (response.config.url.includes('hardware-plastic')) {
      console.group('🔍 塑胶五金型号API响应')
      console.log('响应URL:', getFullUrl(response.config))
      console.log('响应状态:', response.status, response.statusText)
      console.log('响应数据:', JSON.stringify(response.data))
      console.groupEnd()
    }
    
    // 打印响应信息
    console.group('✅ 收到响应')
    console.log('完整URL:', getFullUrl(response.config))
    console.log('Status:', response.status, response.statusText)
    console.log('Headers:', response.headers)
    console.log('Data:', response.data)
    console.groupEnd()
    // 如果响应状态码在 200-299 之间，认为请求成功
    if (response.status >= 200 && response.status < 300) {
      return response
    }
    return Promise.reject(response)
  },
  error => {
    // 特殊处理塑胶五金型号相关错误
    if (error.config && error.config.url.includes('hardware-plastic')) {
      console.group('❌ 塑胶五金型号API错误')
      console.log('错误URL:', getFullUrl(error.config))
      console.log('错误方法:', error.config.method)
      console.log('错误状态:', error.response?.status, error.response?.statusText)
      console.log('错误数据:', error.response?.data)
      console.log('错误信息:', error.message)
      console.groupEnd()
    }
    
    console.group('❌ 响应错误')
    if (error.config) {
      console.log('完整URL:', getFullUrl(error.config))
      console.log('Method:', error.config.method)
    }
    console.log('Status:', error.response?.status, error.response?.statusText)
    console.log('Error Data:', error.response?.data)
    console.log('Error Message:', error.message)
    console.groupEnd()
    
    if (error.code === 'ERR_NETWORK') {
      ElMessage.error('无法连接到服务器，请确保后端服务已启动')
    } else if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      ElMessage.error('请求超时，可能是因为数据量过大或网络较慢，请稍后重试')
    } else if (error.response) {
      // 只有在真正的错误状态码时才显示错误消息
      if (error.response.status >= 400) {
        const message = error.response.data?.message || error.response.data || '请求失败'
        switch (error.response.status) {
          case 400:
            ElMessage.error(message)
            break
          case 401:
            ElMessage.error('未授权，请重新登录')
            // 可以在这里处理登出逻辑
            break
          case 403:
            ElMessage.error('拒绝访问')
            break
          case 404:
            ElMessage.error('请求的资源不存在')
            break
          case 500:
            ElMessage.error(message || '服务器错误')
            break
          default:
            ElMessage.error(message)
        }
      }
    } else {
      ElMessage.error('请求失败，请检查网络连接')
    }
    return Promise.reject(error)
  }
)

export default service 