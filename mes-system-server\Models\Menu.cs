using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace mes_system_server.Models
{
    public class Menu
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string MenuId { get; set; } = string.Empty; // 菜单唯一标识，如 "system-users"

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty; // 菜单名称

        [StringLength(200)]
        public string? Path { get; set; } // 菜单路径

        [StringLength(50)]
        public string? Icon { get; set; } // 菜单图标

        [StringLength(20)]
        public string Type { get; set; } = string.Empty; // 菜单类型：menu-item, sub-menu

        public int? ParentId { get; set; } // 父菜单ID

        public int SortOrder { get; set; } = 0; // 排序序号

        public bool Status { get; set; } = true; // 菜单状态

        [StringLength(200)]
        public string? Description { get; set; } // 菜单描述

        public DateTime CreateTime { get; set; } = DateTime.Now;

        public DateTime? UpdateTime { get; set; }

        // 导航属性
        [ForeignKey("ParentId")]
        public virtual Menu? Parent { get; set; }

        public virtual ICollection<Menu> Children { get; set; } = new List<Menu>();

        public virtual ICollection<RoleMenu> RoleMenus { get; set; } = new List<RoleMenu>();
    }
} 