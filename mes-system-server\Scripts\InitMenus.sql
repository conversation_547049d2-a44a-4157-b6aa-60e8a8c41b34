-- 菜单初始化数据脚本
-- 此脚本用于将系统菜单配置插入到数据库中

-- 清理现有数据
DELETE FROM RoleMenus;
DELETE FROM Menus;

-- 重置自增ID
DBCC CHECKIDENT ('Menus', RESEED, 0);

-- 插入菜单数据
INSERT INTO Menus (MenuId, Name, Path, Icon, Type, ParentId, SortOrder, Status, Description, CreateTime) VALUES

-- 一级菜单（主菜单项）
('dashboard', '仪表盘', '/home', 'Monitor', 'menu-item', NULL, 1, 1, '系统仪表盘', GETDATE()),

-- 二级菜单（子菜单目录）
('engineering', '工程管理', NULL, 'Setting', 'sub-menu', NULL, 2, 1, '工程相关管理功能', GETDATE()),
('production', '生产管理', NULL, 'Odometer', 'sub-menu', NULL, 3, 1, '生产相关管理功能', GETDATE()),
('quality', '质量管理', NULL, 'Aim', 'sub-menu', NULL, 4, 1, '质量相关管理功能', GETDATE()),
('warehouse', '仓储管理', NULL, 'Box', 'sub-menu', NULL, 5, 1, '仓储相关管理功能', GETDATE()),
('equipment', '设备管理', NULL, 'Tools', 'sub-menu', NULL, 6, 1, '设备相关管理功能', GETDATE()),
('hr', '人事管理', NULL, 'UserFilled', 'sub-menu', NULL, 7, 1, '人事相关管理功能', GETDATE()),
('system', '系统管理', NULL, 'Setting', 'sub-menu', NULL, 8, 1, '系统相关管理功能', GETDATE());

-- 获取父菜单ID用于插入子菜单
DECLARE @engineeringId INT = (SELECT Id FROM Menus WHERE MenuId = 'engineering');
DECLARE @productionId INT = (SELECT Id FROM Menus WHERE MenuId = 'production');
DECLARE @qualityId INT = (SELECT Id FROM Menus WHERE MenuId = 'quality');
DECLARE @warehouseId INT = (SELECT Id FROM Menus WHERE MenuId = 'warehouse');
DECLARE @equipmentId INT = (SELECT Id FROM Menus WHERE MenuId = 'equipment');
DECLARE @hrId INT = (SELECT Id FROM Menus WHERE MenuId = 'hr');
DECLARE @systemId INT = (SELECT Id FROM Menus WHERE MenuId = 'system');

-- 插入子菜单项
INSERT INTO Menus (MenuId, Name, Path, Icon, Type, ParentId, SortOrder, Status, Description, CreateTime) VALUES

-- 工程管理子菜单
('engineering-model', '型号管理', '/home/<USER>/model-management', 'Files', 'menu-item', @engineeringId, 1, 1, '产品型号管理', GETDATE()),
('engineering-process', '工艺管理', '/engineering/process', 'Operation', 'menu-item', @engineeringId, 2, 1, '工艺流程管理', GETDATE()),
('engineering-bom', 'BOM管理', '/engineering/bom', 'List', 'menu-item', @engineeringId, 3, 1, '物料清单管理', GETDATE()),

-- 生产管理子菜单
('production-plan', '生产计划', '/production/plan', 'Calendar', 'menu-item', @productionId, 1, 1, '生产计划管理', GETDATE()),
('production-task', '生产任务', '/production/task', 'Document', 'menu-item', @productionId, 2, 1, '生产任务管理', GETDATE()),
('production-monitor', '生产监控', '/production/monitor', 'VideoCamera', 'menu-item', @productionId, 3, 1, '生产过程监控', GETDATE()),

-- 质量管理子菜单
('quality-inspection', '质量检验', '/quality/inspection', 'Check', 'menu-item', @qualityId, 1, 1, '质量检验管理', GETDATE()),
('quality-report', '质量报表', '/quality/report', 'DataAnalysis', 'menu-item', @qualityId, 2, 1, '质量报表统计', GETDATE()),

-- 仓储管理子菜单
('warehouse-material', '物料管理', '/warehouse/material', 'Goods', 'menu-item', @warehouseId, 1, 1, '物料信息管理', GETDATE()),
('warehouse-inventory', '库存管理', '/warehouse/inventory', 'Collection', 'menu-item', @warehouseId, 2, 1, '库存信息管理', GETDATE()),

-- 设备管理子菜单
('equipment-list', '设备台账', '/equipment/list', 'Platform', 'menu-item', @equipmentId, 1, 1, '设备台账管理', GETDATE()),
('equipment-maintenance', '设备维护', '/equipment/maintenance', 'Service', 'menu-item', @equipmentId, 2, 1, '设备维护管理', GETDATE()),

-- 人事管理子菜单
('hr-employees', '员工管理', '/home/<USER>/employees', 'User', 'menu-item', @hrId, 1, 1, '员工信息管理', GETDATE()),
('hr-attendance', '考勤管理', '/home/<USER>/attendance', 'Calendar', 'menu-item', @hrId, 2, 1, '员工考勤管理', GETDATE()),
('hr-salary', '薪资管理', '/home/<USER>/salary', 'Money', 'menu-item', @hrId, 3, 1, '员工薪资管理', GETDATE()),
('hr-company-settings', '公司设置', '/home/<USER>/company-settings', 'OfficeBuilding', 'menu-item', @hrId, 4, 1, '公司信息设置', GETDATE()),

-- 系统管理子菜单
('system-users', '用户管理', '/home/<USER>/users', 'Avatar', 'menu-item', @systemId, 1, 1, '系统用户管理', GETDATE()),
('system-roles', '角色权限', '/home/<USER>/roles', 'Lock', 'menu-item', @systemId, 2, 1, '角色权限管理', GETDATE()),
('system-menus', '菜单管理', '/home/<USER>/menus', 'List', 'menu-item', @systemId, 3, 1, '系统菜单管理', GETDATE());

-- 验证插入结果
SELECT 
    Id,
    MenuId,
    Name,
    Path,
    Type,
    ParentId,
    SortOrder,
    Status,
    CreateTime
FROM Menus 
ORDER BY ParentId ASC, SortOrder ASC;

PRINT '菜单数据初始化完成！'; 