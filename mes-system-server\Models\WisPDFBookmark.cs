using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace mes_system_server.Models
{
    /// <summary>
    /// WIS PDF书签实体类
    /// </summary>
    [Table("WisPDFBookmarks")]
    public class WisPDFBookmark
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// PDF文件名（不含扩展名）
        /// </summary>
        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 原始书签名称
        /// </summary>
        [Required]
        [StringLength(500)]
        public string OriginalBookmarkName { get; set; } = string.Empty;

        /// <summary>
        /// 过滤后的书签名称（移除空格、冒号等特殊字符）
        /// </summary>
        [Required]
        [StringLength(500)]
        public string FilteredBookmarkName { get; set; } = string.Empty;

        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        [Required]
        public int PageNumber { get; set; }

        /// <summary>
        /// 图片Base64数据（可选，可以存储在文件系统或云存储）
        /// </summary>
        [Column(TypeName = "text")]
        public string? ImageBase64 { get; set; }

        /// <summary>
        /// 图片文件路径（云存储或本地路径）
        /// </summary>
        [StringLength(1000)]
        public string? ImagePath { get; set; }

        /// <summary>
        /// 图片文件大小（字节）
        /// </summary>
        public long? ImageSize { get; set; }

        /// <summary>
        /// 图片宽度
        /// </summary>
        public int? ImageWidth { get; set; }

        /// <summary>
        /// 图片高度
        /// </summary>
        public int? ImageHeight { get; set; }

        /// <summary>
        /// 状态（1=正常，0=删除）
        /// </summary>
        [Required]
        public bool Status { get; set; } = true;

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(1000)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Required]
        public DateTime CreateTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        [Required]
        public DateTime UpdateTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者ID（可关联用户表）
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新者ID（可关联用户表）
        /// </summary>
        public int? UpdatedBy { get; set; }
    }
} 