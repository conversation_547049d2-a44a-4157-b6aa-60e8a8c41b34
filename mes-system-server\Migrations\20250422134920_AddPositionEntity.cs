﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace mes_system_server.Migrations
{
    /// <inheritdoc />
    public partial class AddPositionEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Positions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    DepartmentId = table.Column<int>(type: "int", nullable: false),
                    Level = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Positions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Positions_Departments_DepartmentId",
                        column: x => x.DepartmentId,
                        principalTable: "Departments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 4, 22, 13, 49, 20, 723, DateTimeKind.Utc).AddTicks(2976), new DateTime(2025, 4, 22, 13, 49, 20, 723, DateTimeKind.Utc).AddTicks(2977) });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 22, 13, 49, 20, 723, DateTimeKind.Utc).AddTicks(3150));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 22, 13, 49, 20, 723, DateTimeKind.Utc).AddTicks(3303));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 22, 13, 49, 20, 723, DateTimeKind.Utc).AddTicks(3312));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 22, 13, 49, 20, 723, DateTimeKind.Utc).AddTicks(3319));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 22, 13, 49, 20, 723, DateTimeKind.Utc).AddTicks(3325));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 22, 13, 49, 20, 723, DateTimeKind.Utc).AddTicks(3331));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 22, 13, 49, 20, 723, DateTimeKind.Utc).AddTicks(3337));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 22, 13, 49, 20, 723, DateTimeKind.Utc).AddTicks(3343));

            migrationBuilder.CreateIndex(
                name: "IX_Positions_DepartmentId",
                table: "Positions",
                column: "DepartmentId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Positions");

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(1953), new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(1953) });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2124));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2363));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2384));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2392));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2411));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2420));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2428));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2435));
        }
    }
}
