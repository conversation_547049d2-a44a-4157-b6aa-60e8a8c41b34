# 用户管理页面多语言功能使用指南

## 功能概述

用户管理页面现已完全支持中英文多语言切换，包括所有的界面文本、按钮标题、表格列标题、对话框内容、验证消息、提示信息等。

## 支持的多语言内容

### 1. 页面基本元素
| 功能区域 | 中文显示 | 英文显示 |
|---------|---------|---------|
| 页面标题 | 用户管理 | User Management |
| 搜索功能 | 搜索 | Search |
| 操作按钮 | 添加用户、导入、导出 | Add User, Import, Export |

### 2. 表格列标题
| 中文列名 | 英文列名 |
|---------|---------|
| 用户名 | Username |
| 姓名 | Name |
| 部门 | Department |
| 角色 | Role |
| 邮箱 | Email |
| 电话 | Phone |
| 状态 | Status |
| 创建时间 | Create Time |
| 操作 | Operation |

### 3. 状态显示
| 中文状态 | 英文状态 |
|---------|---------|
| 启用 | Active |
| 禁用 | Inactive |
| 全部 | All |

### 4. 操作按钮
| 中文按钮 | 英文按钮 |
|---------|---------|
| 编辑 | Edit |
| 重置密码 | Reset Password |
| 启用 | Enable |
| 禁用 | Disable |
| 确定 | Confirm |
| 取消 | Cancel |

### 5. 表单标签和占位符
| 中文内容 | 英文内容 |
|---------|---------|
| 请选择部门 | Please select department |
| 请选择角色 | Please select role |
| 选择员工 | Select Employee |

### 6. 对话框标题
| 中文标题 | 英文标题 |
|---------|---------|
| 新增用户 | Add User |
| 编辑用户 | Edit User |
| 选择员工 | Select Employee |

### 7. 表单验证消息
| 中文验证消息 | 英文验证消息 |
|-------------|-------------|
| 请输入用户名 | Please enter username |
| 长度在 3 到 20 个字符 | Length should be 3 to 20 characters |
| 请输入姓名 | Please enter name |
| 请选择部门 | Please select department |
| 请选择角色 | Please select role |
| 请输入正确的邮箱地址 | Please enter valid email address |
| 请输入正确的手机号码 | Please enter valid phone number |
| 请输入密码 | Please enter password |
| 长度在 6 到 20 个字符 | Length should be 6 to 20 characters |

### 8. 操作确认消息
| 操作类型 | 中文确认消息 | 英文确认消息 |
|---------|-------------|-------------|
| 重置密码 | 确定要重置用户 {用户名} 的密码吗？ | Are you sure to reset password for user {用户名}? |
| 禁用用户 | 确定要禁用用户 {用户名} 吗？ | Are you sure to disable user {用户名}? |
| 启用用户 | 确定要启用用户 {用户名} 吗？ | Are you sure to enable user {用户名}? |

### 9. 成功提示消息
| 操作结果 | 中文提示 | 英文提示 |
|---------|---------|---------|
| 添加成功 | 添加用户成功 | User added successfully |
| 更新成功 | 更新用户成功 | User updated successfully |
| 重置密码成功 | 密码重置成功 | Password reset successfully |
| 启用成功 | 启用成功 | User enabled successfully |
| 禁用成功 | 禁用成功 | User disabled successfully |

### 10. 错误提示消息
| 错误类型 | 中文提示 | 英文提示 |
|---------|---------|---------|
| 获取列表失败 | 获取用户列表失败，请稍后重试 | Failed to fetch user list, please try again later |
| 操作失败 | 操作失败，请稍后重试 | Operation failed, please try again later |
| 用户ID不存在 | 用户ID不存在 | User ID not found |
| 启用失败 | 启用用户失败 | Failed to enable user |
| 禁用失败 | 禁用用户失败 | Failed to disable user |

## 使用方法

### 1. 访问用户管理页面
1. 登录MES系统
2. 在左侧导航栏中点击"系统管理"
3. 选择"用户管理"子菜单

### 2. 切换语言
1. 点击页面右上角的语言切换按钮
2. 选择"English"切换到英文界面
3. 选择"中文"切换回中文界面

### 3. 验证多语言功能
**中文状态下的验证：**
- 确认页面显示"用户管理"标题
- 确认表格列标题显示中文
- 确认按钮显示中文文字
- 确认表单验证消息为中文

**英文状态下的验证：**
- 确认页面显示"User Management"标题
- 确认表格列标题显示英文
- 确认按钮显示英文文字
- 确认表单验证消息为英文

### 4. 测试表单验证
1. 点击"添加用户"按钮
2. 不填写任何信息直接点击"确定"
3. 观察验证错误消息的语言
4. 切换语言后重复测试

### 5. 测试操作确认
1. 选择一个用户点击"重置密码"
2. 观察确认对话框的语言
3. 切换语言后重复测试

## 技术实现

### 1. i18n配置扩展
在 `src/i18n/index.js` 中添加了完整的用户管理模块翻译：
```javascript
zh: {
  user: {
    management: '用户管理',
    addUser: '添加用户',
    // ... 更多翻译
  }
},
en: {
  user: {
    management: 'User Management',
    addUser: 'Add User',
    // ... 更多翻译
  }
}
```

### 2. 组件翻译集成
- 导入 `useI18n` 组合式函数
- 使用 `t()` 函数进行文本翻译
- 使用 `computed` 确保动态验证规则的响应性

### 3. 动态内容翻译
支持带参数的翻译消息：
```javascript
// 中文: "确定要重置用户 张三 的密码吗？"
// 英文: "Are you sure to reset password for user 张三?"
t('user.confirmResetPassword', { name: row.name })
```

## 扩展其他页面

### 复制翻译配置
如果需要为其他页面添加多语言支持，可以参考用户管理页面的实现：

1. **在i18n配置中添加翻译**：
```javascript
// 添加新模块翻译
zh: {
  newModule: {
    title: '新模块',
    // ... 更多翻译
  }
}
```

2. **在组件中使用翻译**：
```javascript
import { useI18n } from 'vue-i18n'
const { t } = useI18n()

// 在模板中使用
{{ $t('newModule.title') }}
```

3. **处理表单验证**：
```javascript
const rules = computed(() => ({
  field: [
    { required: true, message: t('newModule.fieldRequired'), trigger: 'blur' }
  ]
}))
```

## 维护和更新

### 添加新的翻译键
1. 在 `src/i18n/index.js` 中的中英文配置中同时添加新的翻译键
2. 在组件中使用新的翻译键
3. 测试语言切换功能

### 修改现有翻译
1. 直接在 `src/i18n/index.js` 文件中修改对应的翻译内容
2. 保存后翻译会立即生效
3. 无需重启开发服务器

### 翻译命名规范
- 使用描述性的键名：`userRequired` 而不是 `field1`
- 保持层级结构清晰：`user.confirmResetPassword`
- 使用统一的命名风格：驼峰命名法

## 最佳实践

1. **保持翻译同步**：每次添加中文翻译时，同时添加对应的英文翻译
2. **使用参数化翻译**：对于包含动态内容的消息，使用参数化翻译
3. **测试完整性**：确保所有界面元素都支持多语言切换
4. **保持一致性**：在整个应用中使用统一的翻译键命名规范

通过以上配置，用户管理页面现在完全支持中英文多语言切换，为用户提供了更好的国际化体验！ 