# WIS PDF转图片云服务器保存功能

## 🎯 功能概述

这个新功能允许用户选择PDF文件后，系统自动将PDF转换成图片，并以文件流的形式调用后端API，将图片保存到云服务器的指定文件夹中，并以书签名命名图片文件。

## 🚀 核心特性

### ✨ 主要功能
- **PDF自动转换**：选择PDF文件后自动转换为高质量JPEG图片
- **智能书签提取**：自动提取PDF书签，如无书签则生成页面书签
- **云服务器存储**：图片文件保存到云服务器的文件夹结构中
- **书签命名**：图片文件以过滤后的书签名命名
- **灵活存储选项**：可选择是否同时保存Base64数据到数据库

### 🔧 技术实现
- **后端**：新增 `uploadPDFImageToCloud` API接口
- **前端**：新增"保存到云服务器文件夹"按钮
- **文件系统**：自动创建文件夹结构并管理文件命名
- **并发控制**：支持批量上传，控制并发数量避免服务器压力

## 📁 文件组织结构

```
mes-system-server/wwwroot/uploads/wis-pdf-images/
├── PDF文件名1/
│   ├── 书签名1_第1页.jpg
│   ├── 书签名2_第2页.jpg
│   ├── 书签名3_第3页.jpg
│   └── ...
├── PDF文件名2/
│   ├── 第1章导读_第1页.jpg
│   ├── 第2章实施_第2页.jpg
│   └── ...
└── README.md
```

## 🎮 使用步骤

### 1. 访问WIS管理页面
- 在左侧导航菜单中点击 **工程管理** → **WIS管理**

### 2. 上传PDF文件
- 点击"选择PDF文件（可多选）"按钮
- 选择一个或多个PDF文件（支持最大50MB）
- 系统自动开始处理PDF文件

### 3. 保存到云服务器
- 处理完成后，点击 **"保存到云服务器文件夹"** 按钮（橙色按钮）
- 选择存储选项：
  - "只保存文件到服务器"：仅保存图片文件
  - "同时保存Base64到数据库"：文件+数据库双重保存
- 点击"确定保存"开始上传

### 4. 查看处理进度
- 系统显示详细的处理进度
- 实时显示当前处理的文件和页面
- 完成后显示成功/失败统计

## 🔧 API接口详情

### 后端新增接口

#### `POST /api/wis/uploadPDFImageToCloud`
**功能**：上传PDF图片到云服务器文件夹

**请求参数**：
```json
{
  "fileName": "PDF文件名",
  "bookmarkName": "书签名称", 
  "pageNumber": 1,
  "imageBase64": "base64图片数据",
  "imageWidth": 1920,
  "imageHeight": 1080,
  "remarks": "备注信息",
  "saveBase64": false
}
```

**响应结果**：
```json
{
  "code": 200,
  "message": "图片上传到云服务器成功",
  "data": {
    "imagePath": "/uploads/wis-pdf-images/PDF文件名/书签名_第1页.jpg",
    "fileName": "书签名_第1页.jpg",
    "fileSize": 123456
  }
}
```

### 前端新增方法

#### `uploadPDFImageToCloud(data)`
**功能**：单个图片上传到云服务器

#### `batchUploadPDFImagesToCloud(imageList, concurrency, onProgress, saveBase64)`
**功能**：批量图片上传到云服务器，支持并发控制

## 🎨 界面设计

### 新增按钮
- **位置**：PDF处理器页面顶部操作区域
- **样式**：橙色警告色（type="warning"）
- **图标**：文件夹添加图标（FolderAdd）
- **文字**：保存到云服务器文件夹

### 进度显示
- **详细进度条**：显示处理百分比
- **状态信息**：当前处理的文件和书签名
- **错误提示**：失败文件的详细信息

## 📊 功能对比

| 功能项 | 原有"上传图片到数据库" | 新增"保存到云服务器文件夹" |
|--------|----------------------|---------------------------|
| 存储位置 | 数据库Base64字段 | 云服务器文件系统 |
| 文件命名 | 页码命名 | 书签名命名 |
| 存储空间 | 数据库存储压力大 | 文件系统存储，数据库压力小 |
| 访问方式 | API查询获取 | 直接文件路径访问 |
| 管理方便度 | 需要API操作 | 可直接操作文件 |
| 备份方式 | 数据库备份 | 文件系统备份 |

## ⚙️ 配置说明

### 文件路径配置
- **基础路径**：`wwwroot/uploads/wis-pdf-images/`
- **文件夹结构**：按PDF文件名自动创建子文件夹
- **文件命名规则**：`[过滤后的书签名]_第[页码]页.jpg`

### 性能配置
- **并发数量**：默认2个并发上传，可调整
- **文件大小**：支持大图片文件，无特殊限制
- **超时设置**：10分钟超时，适合大文件处理

## 🛡️ 安全考虑

### 文件名过滤
- 自动过滤特殊字符，确保文件系统安全
- 移除空格、冒号等可能导致问题的字符

### 路径安全
- 使用相对路径存储，避免路径遍历攻击
- 文件夹自动创建，确保路径存在

### 权限控制
- API接口允许匿名访问（根据现有设计）
- 建议后续添加用户权限验证

## 📈 监控建议

### 存储监控
- 定期检查 `wwwroot/uploads/wis-pdf-images/` 目录大小
- 监控磁盘空间使用情况

### 性能监控
- 监控PDF处理时间
- 监控图片上传成功率

### 错误监控
- 记录处理失败的PDF文件
- 监控API调用错误率

## 🔄 后续优化建议

1. **权限管理**：添加用户权限验证
2. **文件清理**：定期清理不再使用的文件
3. **CDN集成**：考虑集成CDN加速图片访问
4. **缩略图生成**：自动生成缩略图提升加载速度
5. **文件压缩**：添加图片压缩功能减少存储空间

## 📞 技术支持

如有问题，请查看：
- 浏览器控制台错误信息
- 服务器日志文件
- API响应状态码和错误信息 