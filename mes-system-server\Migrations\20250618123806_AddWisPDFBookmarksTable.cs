﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace mes_system_server.Migrations
{
    /// <inheritdoc />
    public partial class AddWisPDFBookmarksTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "WisPDFBookmarks",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FileName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    OriginalBookmarkName = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    FilteredBookmarkName = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    PageNumber = table.Column<int>(type: "int", nullable: false),
                    ImageBase64 = table.Column<string>(type: "text", nullable: true),
                    ImagePath = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    ImageSize = table.Column<long>(type: "bigint", nullable: true),
                    ImageWidth = table.Column<int>(type: "int", nullable: true),
                    ImageHeight = table.Column<int>(type: "int", nullable: true),
                    Status = table.Column<bool>(type: "bit", nullable: false),
                    Remarks = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<int>(type: "int", nullable: true),
                    UpdatedBy = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WisPDFBookmarks", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3573), new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3573) });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3604));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3616));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3621));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3625));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3630));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3634));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3639));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 6, 18, 12, 38, 6, 407, DateTimeKind.Utc).AddTicks(3643));

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3698), new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3698) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3708), new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3709) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3717), new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3717) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3726), new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3726) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3734), new DateTime(2025, 6, 18, 20, 38, 6, 407, DateTimeKind.Local).AddTicks(3735) });

            migrationBuilder.CreateIndex(
                name: "IX_WisPDFBookmarks_FileName",
                table: "WisPDFBookmarks",
                column: "FileName");

            migrationBuilder.CreateIndex(
                name: "IX_WisPDFBookmarks_FileName_PageNumber_Status",
                table: "WisPDFBookmarks",
                columns: new[] { "FileName", "PageNumber", "Status" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "WisPDFBookmarks");

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8270), new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8270) });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8286));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8295));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8301));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8305));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8310));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8314));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8319));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 5, 30, 13, 14, 24, 745, DateTimeKind.Utc).AddTicks(8323));

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8362), new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8362) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8373), new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8373) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8382), new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8382) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8390), new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8391) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8400), new DateTime(2025, 5, 30, 21, 14, 24, 745, DateTimeKind.Local).AddTicks(8400) });
        }
    }
}
