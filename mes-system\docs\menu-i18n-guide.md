# 菜单多语言功能使用指南

## 功能概述

本指南介绍了如何让左侧导航栏的菜单名称支持多语言切换。当用户在顶部切换语言时，菜单名称也会相应地从中文切换到英文。

## 实现原理

### 1. 翻译映射系统
系统通过 `menuTranslations.js` 工具将后端返回的菜单名称映射到i18n翻译键：

```javascript
// 中文菜单名称 → 翻译键
'系统管理' → 'menu.system'
'用户管理' → 'menu.userManagement'

// 翻译键 → 目标语言
'menu.system' → 'System' (英文)
'menu.userManagement' → 'User Management' (英文)
```

### 2. 动态翻译显示
菜单显示时使用 `getMenuDisplayName()` 函数：
- 获取后端菜单名称
- 查找对应的翻译键
- 根据当前语言返回翻译结果

### 3. 实时更新机制
监听语言切换事件，自动更新菜单显示：
- 监听 `languageChanged` 事件
- 强制重新渲染菜单组件
- 菜单名称使用新语言显示

## 支持的菜单翻译

### 主要菜单模块
| 中文名称 | 英文名称 | 翻译键 |
|---------|---------|--------|
| 仪表盘 | Dashboard | menu.dashboard |
| 工程管理 | Engineering | menu.engineering |
| 生产管理 | Production | menu.production |
| 人事管理 | Human Resources | menu.hr |
| 系统管理 | System | menu.system |

### 系统管理子菜单
| 中文名称 | 英文名称 | 翻译键 |
|---------|---------|--------|
| 用户管理 | User Management | menu.userManagement |
| 角色管理 | Role Management | menu.roleManagement |
| 菜单管理 | Menu Management | menu.menuManagement |
| 多语言管理 | I18n Management | menu.i18nManagement |
| 数据字典 | Data Dictionary | menu.dataDictionary |

### 人事管理子菜单
| 中文名称 | 英文名称 | 翻译键 |
|---------|---------|--------|
| 员工管理 | Employee Management | menu.employeeManagement |
| 公司设置 | Company Settings | menu.companySettings |
| 考勤管理 | Attendance Management | menu.attendanceManagement |

## 使用方法

### 验证多语言菜单功能
1. **访问主页**：登录后进入系统主页
2. **查看中文菜单**：确认左侧菜单显示中文名称
3. **切换语言**：点击顶部的语言切换按钮，选择"English"
4. **验证英文菜单**：确认菜单名称已切换为英文
5. **切换回中文**：再次点击语言切换按钮，选择"中文"
6. **验证中文菜单**：确认菜单名称恢复为中文

### 测试步骤
```
初始状态：左侧菜单显示 "系统管理"
↓
点击语言切换按钮 → 选择 "English"
↓
验证：左侧菜单显示 "System"
↓
点击语言切换按钮 → 选择 "中文"
↓
验证：左侧菜单显示 "系统管理"
```

## 扩展新菜单

### 添加新的菜单翻译
如果后端添加了新的菜单项，需要在以下位置添加翻译：

#### 1. i18n配置文件 (`src/i18n/index.js`)
```javascript
// 中文翻译
zh: {
  menu: {
    // 现有翻译...
    newMenu: '新菜单名称'
  }
}

// 英文翻译
en: {
  menu: {
    // 现有翻译...
    newMenu: 'New Menu Name'
  }
}
```

#### 2. 菜单翻译映射 (`src/utils/menuTranslations.js`)
```javascript
const menuNameMap = {
  // 现有映射...
  '新菜单名称': 'menu.newMenu',
  'New Menu Name': 'menu.newMenu'
}
```

### 批量添加菜单翻译
```javascript
import { addMenuTranslations } from '@/utils/menuTranslations'

// 批量添加新的菜单翻译映射
addMenuTranslations({
  '新菜单1': 'menu.newMenu1',
  '新菜单2': 'menu.newMenu2',
  'New Menu 1': 'menu.newMenu1',
  'New Menu 2': 'menu.newMenu2'
})
```

## 故障排除

### 常见问题

#### Q: 某个菜单项没有翻译，仍显示原始名称？
**A**: 检查以下步骤：
1. 确认 `i18n/index.js` 中是否添加了对应的翻译键
2. 确认 `menuTranslations.js` 中是否添加了名称映射
3. 检查菜单名称是否与映射表中的名称完全匹配

#### Q: 语言切换后菜单没有立即更新？
**A**: 确认：
1. `Home.vue` 是否正确监听了 `languageChanged` 事件
2. 浏览器控制台是否有错误信息
3. 尝试刷新页面查看是否正常

#### Q: 如何调试菜单翻译？
**A**: 在浏览器控制台中执行：
```javascript
// 查看当前菜单数据
console.log('Navigation Menus:', navigationMenus.value)

// 查看翻译函数
console.log('Translation test:', t('menu.system'))

// 查看菜单翻译映射
import { getMenuTranslationKey } from '@/utils/menuTranslations'
console.log('Translation key:', getMenuTranslationKey('系统管理'))
```

## 技术架构

### 文件结构
```
src/
├── i18n/
│   └── index.js                      # 翻译配置文件
├── utils/
│   └── menuTranslations.js           # 菜单翻译映射工具
├── views/
│   └── Home.vue                      # 主页组件（菜单显示）
└── components/
    └── LanguageSwitcher.vue          # 语言切换组件
```

### 核心函数
- `getMenuTranslationKey()`: 获取菜单翻译键
- `getTranslatedMenuName()`: 获取翻译后的菜单名称
- `getMenuDisplayName()`: 在组件中使用的显示名称获取函数

## 性能优化

### 1. 翻译缓存
翻译结果由Vue i18n自动缓存，避免重复计算。

### 2. 事件监听优化
语言切换事件使用防抖处理，避免频繁更新。

### 3. 组件更新策略
使用数组引用更新触发重新渲染，避免深度比较。

通过以上配置，您的MES系统菜单现在完全支持多语言切换，提供了更好的国际化用户体验！ 