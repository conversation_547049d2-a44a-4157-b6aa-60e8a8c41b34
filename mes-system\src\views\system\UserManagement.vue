<template>
  <div class="user-management">
    <!-- 搜索和操作区域 -->
    <div class="operation-bar">
      <div class="search-area">
        <el-input
          v-model="searchQuery"
          :placeholder="t('user.searchPlaceholder') + ' ' + searchTypes.find(t => t.value === searchType).label"
          class="search-input"
          clearable
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        >
          <template #prepend>
            <el-select v-model="searchType" style="width: 100px">
              <el-option
                v-for="type in searchTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </template>
          <template #append>
            <el-button type="primary" :icon="Search" @click="handleSearch">{{ $t('user.search') }}</el-button>
          </template>
        </el-input>

        <el-select v-model="roleFilter" clearable :placeholder="$t('user.role')" class="filter-select" @change="handleSearch">
          <el-option 
            v-for="role in roleOptions" 
            :key="role.id" 
            :label="role.roleName" 
            :value="role.roleName" 
          />
        </el-select>

        <el-select v-model="statusFilter" clearable :placeholder="$t('user.status')" class="filter-select" @change="handleSearch">
          <el-option :label="$t('user.all')" value="all" />
          <el-option :label="$t('user.active')" value="active" />
          <el-option :label="$t('user.inactive')" value="inactive" />
        </el-select>

        <el-button type="primary" :icon="Plus" @click="handleAdd">{{ $t('user.addUser') }}</el-button>
        <el-button type="success" :icon="Upload">{{ $t('user.import') }}</el-button>
        <el-button type="warning" :icon="Download">{{ $t('user.export') }}</el-button>
      </div>
    </div>

    <!-- 用户列表 -->
    <el-table
      v-loading="loading"
      :data="userList"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="username" :label="$t('user.username')" min-width="120" align="center" />
      <el-table-column prop="name" :label="$t('user.name')" min-width="100" align="center" />
      <el-table-column prop="department" :label="$t('user.department')" min-width="120" align="center" />
      <el-table-column prop="role" :label="$t('user.role')" min-width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getRoleType(row.role)">{{ getRoleLabel(row.role) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="email" :label="$t('user.email')" min-width="180" align="center" />
      <el-table-column prop="phone" :label="$t('user.phone')" min-width="120" align="center" />
      <el-table-column prop="status" :label="$t('user.status')" min-width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status ? 'success' : 'danger'">
            {{ row.status ? $t('user.active') : $t('user.inactive') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" :label="$t('user.createTime')" min-width="180" align="center" />
      <el-table-column :label="$t('user.operation')" width="280" align="center">
        <template #default="{ row }">
          <el-button-group class="operation-buttons">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              {{ $t('user.edit') }}
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleResetPwd(row)"
            >
              {{ $t('user.resetPassword') }}
            </el-button>
            <el-button
              :type="row.status ? 'danger' : 'success'"
              size="small"
              @click="handleToggleStatus(row)"
            >
              {{ row.status ? $t('user.disable') : $t('user.enable') }}
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 用户表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? $t('user.addUserDialog') : $t('user.editUserDialog')"
      width="500px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item :label="$t('user.username')" prop="username">
          <el-input v-model="userForm.username" :disabled="dialogType === 'edit'" />
        </el-form-item>
        <el-form-item :label="$t('user.name')" prop="name">
          <div class="input-with-button">
            <el-input v-model="userForm.name" />
            <el-button type="primary" @click.stop="openEmployeeSelector">{{ $t('user.selectEmployee') }}</el-button>
          </div>
        </el-form-item>
        <el-form-item :label="$t('user.department')" prop="department">
          <el-select v-model="userForm.department" :placeholder="$t('user.selectDepartment')">
            <el-option 
              v-for="dept in departmentOptions" 
              :key="dept.id" 
              :label="dept.name" 
              :value="dept.name" 
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('user.role')" prop="role">
          <el-select v-model="userForm.role" :placeholder="$t('user.selectRole')">
            <el-option 
              v-for="role in roleOptions" 
              :key="role.id" 
              :label="role.roleName" 
              :value="role.roleName" 
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('user.email')" prop="email">
          <el-input v-model="userForm.email" />
        </el-form-item>
        <el-form-item :label="$t('user.phone')" prop="phone">
          <el-input v-model="userForm.phone" />
        </el-form-item>
        <el-form-item :label="$t('user.password')" prop="password" v-if="dialogType === 'add'">
          <el-input v-model="userForm.password" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">{{ $t('user.cancel') }}</el-button>
          <el-button type="primary" @click="handleSubmit">{{ $t('user.confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 员工选择器弹窗 -->
    <EmployeeSelector 
      v-model:visible="employeeSelectorVisible"
      :title="$t('user.selectEmployeeDialog')"
      @select="handleEmployeeSelect"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  Search,
  Plus,
  Upload,
  Download,
  Edit,
  Key,
  Switch
} from '@element-plus/icons-vue'
import { addUser, updateUser, deleteUser, getUserList, resetUserPassword, toggleUserStatus } from '@/api/user'
import { getDepartments } from '@/api/department'
import { getRoles } from '@/api/role'
import EmployeeSelector from '@/components/EmployeeSelector.vue'

const { t } = useI18n()

// 搜索和筛选条件
const searchQuery = ref('')
const roleFilter = ref('')
const statusFilter = ref('')

// 表格数据
const loading = ref(false)
const userList = ref([
  {
    id: 1,
    username: 'admin',
    name: '管理员',
    department: '研发部',
    role: 'admin',
    email: '<EMAIL>',
    phone: '13800138000',
    status: 'active',
    createTime: '2024-03-14 10:00:00'
  },
  {
    id: 2,
    username: 'engineer1',
    name: '张工',
    department: '生产部',
    role: 'engineer',
    email: '<EMAIL>',
    phone: '13800138001',
    status: 'active',
    createTime: '2024-03-14 10:30:00'
  },
  {
    id: 3,
    username: 'operator1',
    name: '李工',
    department: '质检部',
    role: 'operator',
    email: '<EMAIL>',
    phone: '13800138002',
    status: 'inactive',
    createTime: '2024-03-14 11:00:00'
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 表单对话框
const dialogVisible = ref(false)
const dialogType = ref('add')
const userFormRef = ref(null)
const userForm = reactive({
  username: '',
  name: '',
  department: '',
  role: '',
  email: '',
  phone: '',
  password: ''
})

// 表单验证规则
const rules = computed(() => ({
  username: [
    { required: true, message: t('user.usernameRequired'), trigger: 'blur' },
    { min: 3, max: 20, message: t('user.usernameLength'), trigger: 'blur' }
  ],
  name: [
    { required: true, message: t('user.nameRequired'), trigger: 'blur' }
  ],
  department: [
    { required: true, message: t('user.departmentRequired'), trigger: 'change' }
  ],
  role: [
    { required: true, message: t('user.roleRequired'), trigger: 'change' }
  ],
  email: [
    { type: 'email', message: t('user.emailFormat'), trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: t('user.phoneFormat'), trigger: 'blur' }
  ],
  password: [
    { required: true, message: t('user.passwordRequired'), trigger: 'blur' },
    { min: 6, max: 20, message: t('user.passwordLength'), trigger: 'blur' }
  ]
}))

// 角色映射计算属性
const roleMap = computed(() => {
  const map = {}
  roleOptions.value.forEach(role => {
    // 使用角色名称作为key，而不是角色编码
    map[role.roleName] = {
      name: role.roleName,
      type: role.roleCode === 'admin' ? 'danger' : role.roleCode === 'production_manager' ? 'warning' : 'info'
    }
  })
  return map
})

// 获取角色标签类型
const getRoleType = (role) => {
  return roleMap.value[role]?.type || 'info'
}

// 获取角色标签文本
const getRoleLabel = (role) => {
  return roleMap.value[role]?.name || role
}

// 搜索类型选项
const searchTypes = computed(() => [
  { label: t('user.username'), value: 'username' },
  { label: t('user.name'), value: 'name' },
  { label: t('user.department'), value: 'department' }
])

// 当前选择的搜索类型
const searchType = ref('username')

// 员工选择器
const employeeSelectorVisible = ref(false)

// 部门数据
const departmentOptions = ref([])

// 角色数据
const roleOptions = ref([])

// 获取一级部门数据
const fetchDepartments = async () => {
  try {
    const response = await getDepartments({
      page: 1,
      pageSize: 100,
      search: ''
    })
    
    if (response.data && response.data.code === 200) {
      // 筛选出一级部门（parentId为null的部门）
      const departments = response.data.data.list || []
      departmentOptions.value = departments.filter(dept => dept.parentId === null || dept.level === '1')
    } else {
      console.error('获取部门数据失败:', response)
    }
  } catch (error) {
    console.error('获取部门数据异常:', error)
  }
}

// 获取角色数据
const fetchRoles = async () => {
  try {
    const response = await getRoles({
      page: 1,
      pageSize: 100,
      search: ''
    })
    
    // 兼容不同的响应格式
    const responseData = response.data || response
    
    if (responseData.code === 200) {
      // 只获取启用状态的角色
      const roles = responseData.data.list || []
      roleOptions.value = roles.filter(role => role.status === true)
    } else {
      console.error('获取角色数据失败:', responseData.message)
    }
  } catch (error) {
    console.error('获取角色数据异常:', error)
  }
}

// 打开员工选择器
const openEmployeeSelector = () => {
  console.log('打开员工选择器，当前状态:', employeeSelectorVisible.value)
  employeeSelectorVisible.value = true
  console.log('员工选择器设置后状态:', employeeSelectorVisible.value)
}

// 处理员工选择
const handleEmployeeSelect = (employee) => {
  userForm.name = employee.name
  // 不再自动填充用户名，只填充姓名
  // if (dialogType.value === 'add' && !userForm.username) {
  //   userForm.username = employee.employeeId
  // }
}

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      status: statusFilter.value === 'inactive' ? false : statusFilter.value === 'active' ? true : undefined
    }
    
    // 根据搜索条件构建不同的查询参数
    if (searchQuery.value) {
      switch (searchType.value) {
        case 'username':
          params.username = searchQuery.value
          break
        case 'name':
          params.name = searchQuery.value
          break
        case 'department':
          params.department = searchQuery.value
          break
      }
    }
    
    if (roleFilter.value) {
      params.role = roleFilter.value
    }
    
    console.log('Search params:', params)
    const response = await getUserList(params)
    console.log('Search response:', response)
    
    if (response?.data) {
      userList.value = response.data.map(user => ({
        ...user,
        status: Boolean(user.status) // 确保 status 是布尔值
      }))
      const totalCount = response.headers['x-total-count']
      total.value = totalCount ? parseInt(totalCount) : userList.value.length
    } else {
      userList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error(t('user.fetchListError'))
    userList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  console.log('Searching with type:', searchType.value, 'query:', searchQuery.value)
  currentPage.value = 1
  fetchUserList()
}

// 新增用户
const handleAdd = async () => {
  dialogType.value = 'add'
  Object.keys(userForm).forEach(key => {
    userForm[key] = ''
  })
  // 确保在显示对话框前已获取最新的部门数据
  await fetchDepartments()
  dialogVisible.value = true
}

// 编辑用户
const handleEdit = async (row) => {
  dialogType.value = 'edit'
  Object.keys(userForm).forEach(key => {
    if (key !== 'password') {
      userForm[key] = row[key]
    }
  })
  // 保存用户ID
  userForm.id = row.id
  // 确保在显示对话框前已获取最新的部门数据
  await fetchDepartments()
  dialogVisible.value = true
}

// 重置密码
const handleResetPwd = async (row) => {
  try {
    await ElMessageBox.confirm(
      t('user.confirmResetPassword', { name: row.name }),
      t('user.warning'),
      {
        confirmButtonText: t('user.confirm'),
        cancelButtonText: t('user.cancel'),
        type: 'warning'
      }
    )
    await resetUserPassword(row.id)
    ElMessage.success(t('user.resetPasswordSuccess'))
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置密码失败:', error)
    }
  }
}

// 切换用户状态
const handleToggleStatus = async (row) => {
  // 将字符串状态转换为布尔值
  const currentStatus = row.status === true || row.status === 'active'
  const newStatus = !currentStatus
  const action = currentStatus ? t('user.disable') : t('user.enable')
  
  // 输出详细信息
  console.group('切换用户状态')
  console.log('用户ID:', row.id)
  console.log('当前状态:', row.status)
  console.log('转换后的当前状态:', currentStatus)
  console.log('将要切换到的状态:', newStatus)
  console.groupEnd()

  try {
    const confirmMessage = currentStatus 
      ? t('user.confirmDisable', { name: row.name })
      : t('user.confirmEnable', { name: row.name })
    
    await ElMessageBox.confirm(
      confirmMessage,
      t('user.warning'),
      {
        confirmButtonText: t('user.confirm'),
        cancelButtonText: t('user.cancel'),
        type: 'warning'
      }
    )
    const response = await toggleUserStatus(row.id, newStatus)
    if (response.status >= 200 && response.status < 300) {
      row.status = newStatus
      const successMessage = currentStatus ? t('user.disableSuccess') : t('user.enableSuccess')
      ElMessage.success(successMessage)
    }
  } catch (error) {
    if (error === 'cancel') return
    
    console.group('切换用户状态失败')
    console.error('错误详情:', error)
    console.error('请求数据:', { id: row.id, status: newStatus })
    if (error.response) {
      console.error('响应状态:', error.response.status)
      console.error('响应数据:', error.response.data)
    }
    console.groupEnd()

    if (error.response?.data) {
      ElMessage.error(typeof error.response.data === 'string' ? error.response.data : (currentStatus ? t('user.disableError') : t('user.enableError')))
    } else if (error.message) {
      ElMessage.error(error.message)
    } else {
      const errorMessage = currentStatus ? t('user.disableError') : t('user.enableError')
      ElMessage.error(`${errorMessage}，${t('user.retryLater')}`)
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!userFormRef.value) return
  
  try {
    await userFormRef.value.validate()
    
    if (dialogType.value === 'add') {
      await addUser(userForm)
      ElMessage.success(t('user.addSuccess'))
    } else {
      // 确保在编辑时传递用户ID
      const userId = userForm.id
      if (!userId) {
        throw new Error(t('user.userIdNotFound'))
      }
      await updateUser(userId, {
        name: userForm.name,
        department: userForm.department,
        role: userForm.role,
        email: userForm.email,
        phone: userForm.phone
      })
      ElMessage.success(t('user.updateSuccess'))
    }
    
    dialogVisible.value = false
    fetchUserList()
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error(error.message || t('user.operationError'))
  }
}

// 导入导出
const handleImport = () => {
  ElMessage.info(t('user.importInDevelopment'))
}

const handleExport = () => {
  ElMessage.info(t('user.exportInDevelopment'))
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchUserList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchUserList()
}

// 表格选择
const handleSelectionChange = (selection) => {
  console.log('选中的行：', selection)
}

// 初始化
onMounted(() => {
  console.log('UserManagement组件已挂载')
  console.log('员工选择器初始状态:', employeeSelectorVisible.value)
  fetchUserList()
  fetchDepartments() // 加载部门数据
  fetchRoles() // 加载角色数据
})
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.operation-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.search-area {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;
}

.search-input {
  width: 360px;
}

.search-input :deep(.el-input-group__append) {
  padding: 0;
  background-color: var(--el-color-primary);
}

.search-input :deep(.el-input-group__append .el-button) {
  margin: 0;
  border: none;
  height: 32px;
  padding: 8px 16px;
  color: white;
  background-color: transparent;
}

.search-input :deep(.el-input-group__append .el-button:hover) {
  background-color: var(--el-color-primary-light-3);
  border-color: transparent;
}

.filter-select {
  width: 120px;
}

.action-area {
  display: flex;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.operation-buttons {
  display: inline-flex;
  justify-content: center;
  width: 100%;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .operation-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-area {
    flex-direction: column;
  }

  .search-input {
    width: 100%;
  }

  .action-area {
    justify-content: space-between;
  }
}

.input-with-button {
  display: flex;
  gap: 10px;
}
</style> 