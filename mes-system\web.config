﻿<?xml version="1.0" encoding="UTF-8"?>
   <configuration>
     <system.webServer>
       <rewrite>
         <rules>
           <!-- 处理API请求代理（根据你的实际接口路径配置） -->
           <rule name="api" stopProcessing="true">
             <match url="^api/(.*)$" />
             <action type="Rewrite" url="http://111.230.239.197:5221/api/{R:1}" />
           </rule>
           <!-- 处理前端路由 -->
           <rule name="Handle History Mode and custom 404/500" stopProcessing="true">
             <match url="^((?!(api)).)*$" />
             <conditions logicalGrouping="MatchAll" trackAllCaptures="false">
               <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
               <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
             </conditions>
             <action type="Rewrite" url="/" />
           </rule>
         </rules>
       </rewrite>
       <modules runAllManagedModulesForAllRequests="true">
         <remove name="WebDAVModule" />
       </modules>
       <handlers>
         <remove name="WebDAV" />
       </handlers>
       <security>
         <requestFiltering>
           <verbs allowUnlisted="false">
             <add verb="GET" allowed="true" />
             <add verb="POST" allowed="true" />
             <add verb="PUT" allowed="true" />
             <add verb="DELETE" allowed="true" />
             <add verb="OPTIONS" allowed="true" />
           </verbs>
         </requestFiltering>
       </security>
     </system.webServer>
   </configuration>