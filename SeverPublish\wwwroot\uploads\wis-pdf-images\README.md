# WIS PDF图片存储文件夹

这个文件夹用于存储从PDF文件转换而来的图片文件。

## 文件夹结构

```
wis-pdf-images/
├── [PDF文件名1]/
│   ├── [书签名1]_第1页.jpg
│   ├── [书签名2]_第2页.jpg
│   └── ...
├── [PDF文件名2]/
│   ├── [书签名1]_第1页.jpg
│   └── ...
└── README.md
```

## 功能说明

- **自动创建文件夹**：系统会根据PDF文件名自动创建子文件夹
- **书签命名**：图片文件以过滤后的书签名命名，格式为`[书签名]_第[页码]页.jpg`
- **重复处理**：如果相同的PDF页面重新上传，会覆盖原有文件
- **数据库记录**：每个图片文件的路径会保存到数据库中，便于查询和管理

## API接口

- **上传接口**：`POST /api/wis/uploadPDFImageToCloud`
- **支持选项**：可选择是否同时保存Base64数据到数据库

## 注意事项

- 文件名会自动过滤特殊字符，确保文件系统兼容性
- 建议定期清理不再使用的PDF图片文件
- 确保服务器有足够的存储空间 