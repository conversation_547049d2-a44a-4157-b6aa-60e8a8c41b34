using System;
using System.ComponentModel.DataAnnotations;

namespace mes_system_server.DTOs
{
    /// <summary>
    /// WIS PDF书签数据传输对象
    /// </summary>
    public class WisPDFBookmarkDto
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "PDF文件名不能为空")]
        [StringLength(255, ErrorMessage = "PDF文件名最长为255个字符")]
        public string FileName { get; set; } = string.Empty;

        [Required(ErrorMessage = "书签名称不能为空")]
        [StringLength(500, ErrorMessage = "书签名称最长为500个字符")]
        public string OriginalBookmarkName { get; set; } = string.Empty;

        [Required(ErrorMessage = "过滤后书签名称不能为空")]
        [StringLength(500, ErrorMessage = "过滤后书签名称最长为500个字符")]
        public string FilteredBookmarkName { get; set; } = string.Empty;

        [Required(ErrorMessage = "页码不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "页码必须大于0")]
        public int PageNumber { get; set; }

        public string? ImageBase64 { get; set; }

        public string? ImagePath { get; set; }

        public long? ImageSize { get; set; }

        public int? ImageWidth { get; set; }

        public int? ImageHeight { get; set; }

        public bool Status { get; set; } = true;

        public string? Remarks { get; set; }

        public DateTime CreateTime { get; set; }

        public DateTime UpdateTime { get; set; }

        public int? CreatedBy { get; set; }

        public int? UpdatedBy { get; set; }
    }

    /// <summary>
    /// 上传PDF图片请求DTO
    /// </summary>
    public class UploadPDFImageDto
    {
        [Required(ErrorMessage = "PDF文件名不能为空")]
        [StringLength(255, ErrorMessage = "PDF文件名最长为255个字符")]
        public string FileName { get; set; } = string.Empty;

        [Required(ErrorMessage = "书签名称不能为空")]
        [StringLength(500, ErrorMessage = "书签名称最长为500个字符")]
        public string BookmarkName { get; set; } = string.Empty;

        [Required(ErrorMessage = "页码不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "页码必须大于0")]
        public int PageNumber { get; set; }

        [Required(ErrorMessage = "图片数据不能为空")]
        public string ImageBase64 { get; set; } = string.Empty;

        public int? ImageWidth { get; set; }

        public int? ImageHeight { get; set; }

        public string? Remarks { get; set; }
    }

    /// <summary>
    /// 上传PDF图片到云服务器请求DTO
    /// </summary>
    public class UploadPDFImageToCloudDto
    {
        [Required(ErrorMessage = "PDF文件名不能为空")]
        [StringLength(255, ErrorMessage = "PDF文件名最长为255个字符")]
        public string FileName { get; set; } = string.Empty;

        [Required(ErrorMessage = "书签名称不能为空")]
        [StringLength(500, ErrorMessage = "书签名称最长为500个字符")]
        public string BookmarkName { get; set; } = string.Empty;

        [Required(ErrorMessage = "页码不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "页码必须大于0")]
        public int PageNumber { get; set; }

        [Required(ErrorMessage = "图片数据不能为空")]
        public string ImageBase64 { get; set; } = string.Empty;

        public int? ImageWidth { get; set; }

        public int? ImageHeight { get; set; }

        public string? Remarks { get; set; }
        
        /// <summary>
        /// 是否同时保存Base64数据到数据库
        /// </summary>
        public bool SaveBase64 { get; set; } = false;
    }

    /// <summary>
    /// 批量上传PDF图片请求DTO
    /// </summary>
    public class BatchUploadPDFImagesDto
    {
        [Required(ErrorMessage = "PDF文件名不能为空")]
        [StringLength(255, ErrorMessage = "PDF文件名最长为255个字符")]
        public string FileName { get; set; } = string.Empty;

        [Required(ErrorMessage = "图片数据不能为空")]
        [MinLength(1, ErrorMessage = "至少要上传一张图片")]
        public List<PDFImageItem> Images { get; set; } = new List<PDFImageItem>();
    }

    /// <summary>
    /// PDF图片项目
    /// </summary>
    public class PDFImageItem
    {
        [Required(ErrorMessage = "书签名称不能为空")]
        [StringLength(500, ErrorMessage = "书签名称最长为500个字符")]
        public string BookmarkName { get; set; } = string.Empty;

        [Required(ErrorMessage = "页码不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "页码必须大于0")]
        public int PageNumber { get; set; }

        [Required(ErrorMessage = "图片数据不能为空")]
        public string ImageBase64 { get; set; } = string.Empty;

        public int? ImageWidth { get; set; }

        public int? ImageHeight { get; set; }

        public string? Remarks { get; set; }
    }

    /// <summary>
    /// 保存PDF书签请求DTO
    /// </summary>
    public class SavePDFBookmarksDto
    {
        [Required(ErrorMessage = "PDF文件名不能为空")]
        [StringLength(255, ErrorMessage = "PDF文件名最长为255个字符")]
        public string FileName { get; set; } = string.Empty;

        [Required(ErrorMessage = "书签数据不能为空")]
        [MinLength(1, ErrorMessage = "至少要保存一个书签")]
        public List<PDFBookmarkItem> Bookmarks { get; set; } = new List<PDFBookmarkItem>();
    }

    /// <summary>
    /// PDF书签项目
    /// </summary>
    public class PDFBookmarkItem
    {
        [Required(ErrorMessage = "原始书签名称不能为空")]
        [StringLength(500, ErrorMessage = "原始书签名称最长为500个字符")]
        public string OriginalBookmarkName { get; set; } = string.Empty;

        [Required(ErrorMessage = "过滤后书签名称不能为空")]
        [StringLength(500, ErrorMessage = "过滤后书签名称最长为500个字符")]
        public string FilteredBookmarkName { get; set; } = string.Empty;

        [Required(ErrorMessage = "页码不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "页码必须大于0")]
        public int PageNumber { get; set; }

        public string? Remarks { get; set; }
    }

    /// <summary>
    /// 查询PDF书签请求DTO
    /// </summary>
    public class QueryPDFBookmarksDto
    {
        /// <summary>
        /// PDF文件名（支持模糊查询）
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// 书签名称（支持模糊查询）
        /// </summary>
        public string? BookmarkName { get; set; }

        /// <summary>
        /// 状态筛选
        /// </summary>
        public bool? Status { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "页码必须大于0")]
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        [Range(1, 100, ErrorMessage = "每页大小必须在1-100之间")]
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 排序字段
        /// </summary>
        public string? SortField { get; set; }

        /// <summary>
        /// 排序方向（asc/desc）
        /// </summary>
        public string? SortOrder { get; set; } = "desc";
    }

    /// <summary>
    /// 分页查询结果DTO
    /// </summary>
    public class PagedResultDto<T>
    {
        /// <summary>
        /// 数据列表
        /// </summary>
        public List<T> Items { get; set; } = new List<T>();

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

        /// <summary>
        /// 是否有上一页
        /// </summary>
        public bool HasPreviousPage => PageIndex > 1;

        /// <summary>
        /// 是否有下一页
        /// </summary>
        public bool HasNextPage => PageIndex < TotalPages;
    }

    /// <summary>
    /// 删除PDF数据请求DTO
    /// </summary>
    public class DeletePDFDataDto
    {
        [Required(ErrorMessage = "PDF文件名不能为空")]
        [StringLength(255, ErrorMessage = "PDF文件名最长为255个字符")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 是否同时删除图片文件
        /// </summary>
        public bool DeleteImageFiles { get; set; } = true;
    }

    /// <summary>
    /// 获取PDF图片响应DTO
    /// </summary>
    public class GetPDFImageResponseDto
    {
        public int Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string BookmarkName { get; set; } = string.Empty;
        public int PageNumber { get; set; }
        public string ImageBase64 { get; set; } = string.Empty;
        public string? ImagePath { get; set; }
        public long? ImageSize { get; set; }
        public int? ImageWidth { get; set; }
        public int? ImageHeight { get; set; }
        public DateTime CreateTime { get; set; }
    }
} 