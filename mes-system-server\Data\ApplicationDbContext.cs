using Microsoft.EntityFrameworkCore;
using mes_system_server.Models;

namespace mes_system_server.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<Menu> Menus { get; set; }
        public DbSet<RoleMenu> RoleMenus { get; set; }
        public DbSet<Company> Companies { get; set; }
        public DbSet<Department> Departments { get; set; }
        public DbSet<Position> Positions { get; set; }
        public DbSet<Employee> Employees { get; set; }
        public DbSet<WisPDFBookmark> WisPDFBookmarks { get; set; }
        
        // 数据字典相关表
        public DbSet<DataDictionary> DataDictionaries { get; set; }
        public DbSet<DataDictionaryItem> DataDictionaryItems { get; set; }
        
        // 工作中心设置相关表
        public DbSet<ProductionLine> ProductionLines { get; set; }
        public DbSet<WorkCenter> WorkCenters { get; set; }
        public DbSet<Equipment> Equipment { get; set; }

        // 物料型号管理相关表
        public DbSet<MaterialModel> MaterialModels { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 添加用户名唯一索引
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            // 添加邮箱唯一索引
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Email)
                .IsUnique();
                
            // 添加员工工号唯一索引
            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.EmployeeId)
                .IsUnique();

            // 添加角色编码唯一索引
            modelBuilder.Entity<Role>()
                .HasIndex(r => r.RoleCode)
                .IsUnique();

            // WIS PDF书签配置
            modelBuilder.Entity<WisPDFBookmark>()
                .HasIndex(b => new { b.FileName, b.PageNumber, b.Status })
                .HasDatabaseName("IX_WisPDFBookmarks_FileName_PageNumber_Status");

            // WIS PDF书签文件名索引
            modelBuilder.Entity<WisPDFBookmark>()
                .HasIndex(b => b.FileName)
                .HasDatabaseName("IX_WisPDFBookmarks_FileName");

            // 添加新的优化索引
            // 状态索引 - 用于快速过滤有效记录
            modelBuilder.Entity<WisPDFBookmark>()
                .HasIndex(b => b.Status)
                .HasDatabaseName("IX_WisPDFBookmarks_Status");

            // 复合索引 - 支持按文件名和状态查询，按页码排序
            modelBuilder.Entity<WisPDFBookmark>()
                .HasIndex(b => new { b.FileName, b.Status, b.PageNumber })
                .HasDatabaseName("IX_WisPDFBookmarks_FileName_Status_PageNumber");

            // 书签名称搜索索引
            modelBuilder.Entity<WisPDFBookmark>()
                .HasIndex(b => new { b.OriginalBookmarkName, b.Status })
                .HasDatabaseName("IX_WisPDFBookmarks_OriginalBookmarkName_Status");

            // 时间范围查询索引
            modelBuilder.Entity<WisPDFBookmark>()
                .HasIndex(b => new { b.CreateTime, b.Status })
                .HasDatabaseName("IX_WisPDFBookmarks_CreateTime_Status");

            // 添加菜单ID唯一索引
            modelBuilder.Entity<Menu>()
                .HasIndex(m => m.MenuId)
                .IsUnique();

            // 配置用户和角色的关系
            modelBuilder.Entity<User>()
                .HasOne(u => u.UserRole)
                .WithMany(r => r.Users)
                .HasForeignKey(u => u.RoleId)
                .OnDelete(DeleteBehavior.SetNull);

            // 配置角色菜单关联关系
            modelBuilder.Entity<RoleMenu>()
                .HasOne(rm => rm.Role)
                .WithMany(r => r.RoleMenus)
                .HasForeignKey(rm => rm.RoleId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<RoleMenu>()
                .HasOne(rm => rm.Menu)
                .WithMany(m => m.RoleMenus)
                .HasForeignKey(rm => rm.MenuId)
                .OnDelete(DeleteBehavior.Cascade);

            // 配置角色菜单复合唯一索引
            modelBuilder.Entity<RoleMenu>()
                .HasIndex(rm => new { rm.RoleId, rm.MenuId })
                .IsUnique();

            // 配置菜单父子关系
            modelBuilder.Entity<Menu>()
                .HasOne(m => m.Parent)
                .WithMany(m => m.Children)
                .HasForeignKey(m => m.ParentId)
                .OnDelete(DeleteBehavior.Restrict);

            // 工作中心设置相关实体配置
            // 产线编码唯一索引
            modelBuilder.Entity<ProductionLine>()
                .HasIndex(pl => pl.Code)
                .IsUnique();

            // 工作中心编码在同一产线内唯一
            modelBuilder.Entity<WorkCenter>()
                .HasIndex(wc => new { wc.ProductionLineId, wc.Code })
                .IsUnique();

            // 设备编码在同一工作中心内唯一
            modelBuilder.Entity<Equipment>()
                .HasIndex(eq => new { eq.WorkCenterId, eq.Code })
                .IsUnique();

            // 配置产线和工作中心的关系
            modelBuilder.Entity<WorkCenter>()
                .HasOne(wc => wc.ProductionLine)
                .WithMany(pl => pl.WorkCenters)
                .HasForeignKey(wc => wc.ProductionLineId)
                .OnDelete(DeleteBehavior.Cascade);

            // 配置工作中心和设备的关系
            modelBuilder.Entity<Equipment>()
                .HasOne(eq => eq.WorkCenter)
                .WithMany(wc => wc.Equipment)
                .HasForeignKey(eq => eq.WorkCenterId)
                .OnDelete(DeleteBehavior.Cascade);

            // 数据字典相关配置
            // 数据字典编码唯一索引
            modelBuilder.Entity<DataDictionary>()
                .HasIndex(dd => dd.Code)
                .IsUnique();

            // 数据字典排序索引
            modelBuilder.Entity<DataDictionary>()
                .HasIndex(dd => new { dd.Type, dd.Sort })
                .HasDatabaseName("IX_DataDictionaries_Type_Sort");

            // 数据字典状态索引
            modelBuilder.Entity<DataDictionary>()
                .HasIndex(dd => dd.Status)
                .HasDatabaseName("IX_DataDictionaries_Status");

            // 数据字典项唯一约束（同一字典下值唯一）
            modelBuilder.Entity<DataDictionaryItem>()
                .HasIndex(ddi => new { ddi.DictId, ddi.Value })
                .IsUnique();

            // 数据字典项排序索引
            modelBuilder.Entity<DataDictionaryItem>()
                .HasIndex(ddi => new { ddi.DictId, ddi.Sort })
                .HasDatabaseName("IX_DataDictionaryItems_DictId_Sort");

            // 数据字典项状态索引
            modelBuilder.Entity<DataDictionaryItem>()
                .HasIndex(ddi => new { ddi.DictId, ddi.Status })
                .HasDatabaseName("IX_DataDictionaryItems_DictId_Status");

            // 配置数据字典和字典项的关系
            modelBuilder.Entity<DataDictionaryItem>()
                .HasOne(ddi => ddi.DataDictionary)
                .WithMany(dd => dd.Items)
                .HasForeignKey(ddi => ddi.DictId)
                .OnDelete(DeleteBehavior.Cascade);

            // 物料型号管理相关配置
            // 物料型号编码在同一类型内唯一
            modelBuilder.Entity<MaterialModel>()
                .HasIndex(mm => new { mm.Type, mm.Code })
                .IsUnique();

            // 物料型号名称索引
            modelBuilder.Entity<MaterialModel>()
                .HasIndex(mm => mm.Name)
                .HasDatabaseName("IX_MaterialModels_Name");

            // 物料型号状态索引
            modelBuilder.Entity<MaterialModel>()
                .HasIndex(mm => new { mm.Type, mm.Status, mm.IsActive })
                .HasDatabaseName("IX_MaterialModels_Type_Status_IsActive");

            // 物料型号分类索引
            modelBuilder.Entity<MaterialModel>()
                .HasIndex(mm => new { mm.Type, mm.Category, mm.IsActive })
                .HasDatabaseName("IX_MaterialModels_Type_Category_IsActive");

            // 物料型号搜索索引
            modelBuilder.Entity<MaterialModel>()
                .HasIndex(mm => new { mm.Type, mm.Name, mm.Code, mm.IsActive })
                .HasDatabaseName("IX_MaterialModels_Type_Name_Code_IsActive");

            // 物料型号创建时间索引
            modelBuilder.Entity<MaterialModel>()
                .HasIndex(mm => new { mm.Type, mm.CreateTime, mm.IsActive })
                .HasDatabaseName("IX_MaterialModels_Type_CreateTime_IsActive");

            // 角色默认数据
            modelBuilder.Entity<Role>().HasData(
                new Role
                {
                    Id = 1,
                    RoleName = "系统管理员",
                    RoleCode = "admin",
                    Description = "系统管理员，拥有所有权限",
                    Status = true,
                    CreateTime = DateTime.Parse("2024-01-01 00:00:00")
                },
                new Role
                {
                    Id = 2,
                    RoleName = "生产经理",
                    RoleCode = "production_manager",
                    Description = "负责生产管理相关工作",
                    Status = true,
                    CreateTime = DateTime.Parse("2024-01-02 00:00:00")
                },
                new Role
                {
                    Id = 3,
                    RoleName = "质量检验员",
                    RoleCode = "quality_inspector",
                    Description = "负责产品质量检验工作",
                    Status = true,
                    CreateTime = DateTime.Parse("2024-01-03 00:00:00")
                }
            );

            // 菜单默认数据
            modelBuilder.Entity<Menu>().HasData(
                // 一级菜单
                new Menu { Id = 1, MenuId = "warehouse", Name = "仓储管理", Type = "sub-menu", SortOrder = 1, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00"), Description = "仓储管理模块" },
                new Menu { Id = 2, MenuId = "production", Name = "生产管理", Type = "sub-menu", SortOrder = 2, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00"), Description = "生产管理模块" },
                new Menu { Id = 3, MenuId = "engineering", Name = "工程管理", Type = "sub-menu", SortOrder = 3, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00"), Description = "工程管理模块" },
                new Menu { Id = 4, MenuId = "production-line", Name = "生产管理", Type = "sub-menu", SortOrder = 4, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00"), Description = "生产线管理" },
                new Menu { Id = 5, MenuId = "quality", Name = "质量管理", Type = "sub-menu", SortOrder = 5, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00"), Description = "质量管理模块" },
                new Menu { Id = 6, MenuId = "equipment", Name = "设备管理", Type = "sub-menu", SortOrder = 6, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00"), Description = "设备管理模块" },
                new Menu { Id = 7, MenuId = "system", Name = "系统管理", Type = "sub-menu", SortOrder = 7, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00"), Description = "系统管理模块" },

                // 仓储管理子菜单
                new Menu { Id = 8, MenuId = "warehouse-in", Name = "入库管理", Path = "/home/<USER>/in", Type = "menu-item", ParentId = 1, SortOrder = 1, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },
                new Menu { Id = 9, MenuId = "warehouse-out", Name = "出库管理", Path = "/home/<USER>/out", Type = "menu-item", ParentId = 1, SortOrder = 2, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },
                new Menu { Id = 10, MenuId = "warehouse-transfer", Name = "库存调拨", Path = "/home/<USER>/transfer", Type = "menu-item", ParentId = 1, SortOrder = 3, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },
                new Menu { Id = 11, MenuId = "warehouse-check", Name = "库存盘点", Path = "/home/<USER>/check", Type = "menu-item", ParentId = 1, SortOrder = 4, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },

                // 生产管理子菜单
                new Menu { Id = 12, MenuId = "production-plan", Name = "生产计划", Path = "/home/<USER>/plan", Type = "menu-item", ParentId = 2, SortOrder = 1, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },
                new Menu { Id = 13, MenuId = "production-order", Name = "生产订单", Path = "/home/<USER>/order", Type = "menu-item", ParentId = 2, SortOrder = 2, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },
                new Menu { Id = 14, MenuId = "production-schedule", Name = "排产管理", Path = "/home/<USER>/schedule", Type = "menu-item", ParentId = 2, SortOrder = 3, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },

                // 工程管理子菜单
                new Menu { Id = 15, MenuId = "engineering-process", Name = "工艺管理", Path = "/home/<USER>/process", Type = "menu-item", ParentId = 3, SortOrder = 1, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },
                new Menu { Id = 16, MenuId = "engineering-bom", Name = "BOM管理", Path = "/home/<USER>/bom", Type = "menu-item", ParentId = 3, SortOrder = 2, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },
                new Menu { Id = 17, MenuId = "engineering-model", Name = "型号管理", Path = "/home/<USER>/models", Type = "menu-item", ParentId = 3, SortOrder = 3, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },

                // 生产线管理子菜单
                new Menu { Id = 18, MenuId = "production-monitor", Name = "生产监控", Path = "/home/<USER>/monitor", Type = "menu-item", ParentId = 4, SortOrder = 1, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },
                new Menu { Id = 19, MenuId = "production-task", Name = "生产任务", Path = "/home/<USER>/task", Type = "menu-item", ParentId = 4, SortOrder = 2, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },

                // 质量管理子菜单
                new Menu { Id = 20, MenuId = "quality-inspection", Name = "质量检验", Path = "/home/<USER>/inspection", Type = "menu-item", ParentId = 5, SortOrder = 1, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },
                new Menu { Id = 21, MenuId = "quality-report", Name = "质量报告", Path = "/home/<USER>/report", Type = "menu-item", ParentId = 5, SortOrder = 2, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },

                // 设备管理子菜单
                new Menu { Id = 22, MenuId = "equipment-list", Name = "设备台账", Path = "/home/<USER>/list", Type = "menu-item", ParentId = 6, SortOrder = 1, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },

                // 系统管理子菜单
                new Menu { Id = 23, MenuId = "system-users", Name = "用户管理", Path = "/home/<USER>/users", Type = "menu-item", ParentId = 7, SortOrder = 1, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },
                new Menu { Id = 24, MenuId = "system-roles", Name = "角色权限", Path = "/home/<USER>/roles", Type = "menu-item", ParentId = 7, SortOrder = 2, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },
                new Menu { Id = 25, MenuId = "system-menus", Name = "菜单管理", Path = "/home/<USER>/menus", Type = "menu-item", ParentId = 7, SortOrder = 3, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }
                // 数据字典菜单 - 暂时注释，手动添加
                // new Menu { Id = 27, MenuId = "system-dictionary", Name = "数据字典", Path = "/home/<USER>/data-dictionary", Type = "menu-item", ParentId = 7, SortOrder = 4, Status = true, CreateTime = DateTime.Parse("2024-01-01 00:00:00"), Description = "数据字典管理" }
            );

            // 数据字典默认数据
            modelBuilder.Entity<DataDictionary>().HasData(
                new DataDictionary
                {
                    Id = 1,
                    Name = "用户状态",
                    Code = "USER_STATUS",
                    Type = "system",
                    Description = "用户账号状态配置",
                    Sort = 1,
                    Status = true,
                    Remark = "系统核心配置",
                    CreateTime = DateTime.Parse("2024-01-01 08:00:00")
                },
                new DataDictionary
                {
                    Id = 2,
                    Name = "部门类型",
                    Code = "DEPT_TYPE",
                    Type = "business",
                    Description = "部门分类配置",
                    Sort = 2,
                    Status = true,
                    Remark = "业务配置",
                    CreateTime = DateTime.Parse("2024-01-01 09:00:00")
                },
                new DataDictionary
                {
                    Id = 3,
                    Name = "性别",
                    Code = "GENDER",
                    Type = "system",
                    Description = "性别分类",
                    Sort = 3,
                    Status = true,
                    Remark = "基础数据",
                    CreateTime = DateTime.Parse("2024-01-01 10:00:00")
                },
                new DataDictionary
                {
                    Id = 4,
                    Name = "学历",
                    Code = "EDUCATION",
                    Type = "system",
                    Description = "学历分类",
                    Sort = 4,
                    Status = true,
                    Remark = "基础数据",
                    CreateTime = DateTime.Parse("2024-01-01 11:00:00")
                }
            );

            // 数据字典项默认数据
            modelBuilder.Entity<DataDictionaryItem>().HasData(
                // 用户状态字典项
                new DataDictionaryItem { Id = 1, DictId = 1, Label = "正常", Value = "1", Sort = 1, Status = true, Description = "用户正常状态", CreateTime = DateTime.Parse("2024-01-01 08:00:00") },
                new DataDictionaryItem { Id = 2, DictId = 1, Label = "禁用", Value = "0", Sort = 2, Status = true, Description = "用户被禁用", CreateTime = DateTime.Parse("2024-01-01 08:00:00") },
                new DataDictionaryItem { Id = 3, DictId = 1, Label = "锁定", Value = "2", Sort = 3, Status = true, Description = "用户被锁定", CreateTime = DateTime.Parse("2024-01-01 08:00:00") },

                // 部门类型字典项
                new DataDictionaryItem { Id = 4, DictId = 2, Label = "技术部门", Value = "tech", Sort = 1, Status = true, Description = "技术研发部门", CreateTime = DateTime.Parse("2024-01-01 09:00:00") },
                new DataDictionaryItem { Id = 5, DictId = 2, Label = "销售部门", Value = "sales", Sort = 2, Status = true, Description = "销售业务部门", CreateTime = DateTime.Parse("2024-01-01 09:00:00") },
                new DataDictionaryItem { Id = 6, DictId = 2, Label = "人事部门", Value = "hr", Sort = 3, Status = true, Description = "人力资源部门", CreateTime = DateTime.Parse("2024-01-01 09:00:00") },
                new DataDictionaryItem { Id = 7, DictId = 2, Label = "财务部门", Value = "finance", Sort = 4, Status = true, Description = "财务管理部门", CreateTime = DateTime.Parse("2024-01-01 09:00:00") },
                new DataDictionaryItem { Id = 8, DictId = 2, Label = "行政部门", Value = "admin", Sort = 5, Status = true, Description = "行政管理部门", CreateTime = DateTime.Parse("2024-01-01 09:00:00") },

                // 性别字典项
                new DataDictionaryItem { Id = 9, DictId = 3, Label = "男", Value = "male", Sort = 1, Status = true, Description = "男性", CreateTime = DateTime.Parse("2024-01-01 10:00:00") },
                new DataDictionaryItem { Id = 10, DictId = 3, Label = "女", Value = "female", Sort = 2, Status = true, Description = "女性", CreateTime = DateTime.Parse("2024-01-01 10:00:00") },

                // 学历字典项
                new DataDictionaryItem { Id = 11, DictId = 4, Label = "小学", Value = "primary", Sort = 1, Status = true, Description = "小学学历", CreateTime = DateTime.Parse("2024-01-01 11:00:00") },
                new DataDictionaryItem { Id = 12, DictId = 4, Label = "初中", Value = "junior", Sort = 2, Status = true, Description = "初中学历", CreateTime = DateTime.Parse("2024-01-01 11:00:00") },
                new DataDictionaryItem { Id = 13, DictId = 4, Label = "高中", Value = "senior", Sort = 3, Status = true, Description = "高中学历", CreateTime = DateTime.Parse("2024-01-01 11:00:00") },
                new DataDictionaryItem { Id = 14, DictId = 4, Label = "大专", Value = "college", Sort = 4, Status = true, Description = "大专学历", CreateTime = DateTime.Parse("2024-01-01 11:00:00") },
                new DataDictionaryItem { Id = 15, DictId = 4, Label = "本科", Value = "bachelor", Sort = 5, Status = true, Description = "本科学历", CreateTime = DateTime.Parse("2024-01-01 11:00:00") },
                new DataDictionaryItem { Id = 16, DictId = 4, Label = "硕士", Value = "master", Sort = 6, Status = true, Description = "硕士学历", CreateTime = DateTime.Parse("2024-01-01 11:00:00") },
                new DataDictionaryItem { Id = 17, DictId = 4, Label = "博士", Value = "doctor", Sort = 7, Status = true, Description = "博士学历", CreateTime = DateTime.Parse("2024-01-01 11:00:00") }
            );

            // 角色菜单权限默认数据 - 暂时注释，稍后手动添加
            /*
            modelBuilder.Entity<RoleMenu>().HasData(
                // 系统管理员 - 拥有所有权限
                new RoleMenu { Id = 1, RoleId = 1, MenuId = 8, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },   // 入库管理
                new RoleMenu { Id = 2, RoleId = 1, MenuId = 9, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },   // 出库管理
                new RoleMenu { Id = 3, RoleId = 1, MenuId = 10, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },  // 库存调拨
                new RoleMenu { Id = 4, RoleId = 1, MenuId = 11, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },  // 库存盘点
                new RoleMenu { Id = 5, RoleId = 1, MenuId = 12, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },  // 生产计划
                new RoleMenu { Id = 6, RoleId = 1, MenuId = 13, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },  // 生产订单
                new RoleMenu { Id = 7, RoleId = 1, MenuId = 14, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },  // 排产管理
                new RoleMenu { Id = 8, RoleId = 1, MenuId = 15, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },  // 工艺管理
                new RoleMenu { Id = 9, RoleId = 1, MenuId = 16, CreateTime = DateTime.Parse("2024-01-01 00:00:00") },  // BOM管理
                new RoleMenu { Id = 10, RoleId = 1, MenuId = 17, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 型号管理
                new RoleMenu { Id = 11, RoleId = 1, MenuId = 18, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 生产监控
                new RoleMenu { Id = 12, RoleId = 1, MenuId = 19, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 生产任务
                new RoleMenu { Id = 13, RoleId = 1, MenuId = 20, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 质量检验
                new RoleMenu { Id = 14, RoleId = 1, MenuId = 21, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 质量报告
                new RoleMenu { Id = 15, RoleId = 1, MenuId = 22, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 设备台账
                new RoleMenu { Id = 16, RoleId = 1, MenuId = 23, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 用户管理
                new RoleMenu { Id = 17, RoleId = 1, MenuId = 24, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 角色权限
                new RoleMenu { Id = 18, RoleId = 1, MenuId = 25, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 菜单管理

                // 生产经理 - 生产相关权限
                new RoleMenu { Id = 19, RoleId = 2, MenuId = 12, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 生产计划
                new RoleMenu { Id = 20, RoleId = 2, MenuId = 13, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 生产订单
                new RoleMenu { Id = 21, RoleId = 2, MenuId = 14, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 排产管理
                new RoleMenu { Id = 22, RoleId = 2, MenuId = 18, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 生产监控
                new RoleMenu { Id = 23, RoleId = 2, MenuId = 19, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 生产任务
                new RoleMenu { Id = 24, RoleId = 2, MenuId = 20, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 质量检验

                // 质量检验员 - 质量相关权限
                new RoleMenu { Id = 25, RoleId = 3, MenuId = 20, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }, // 质量检验
                new RoleMenu { Id = 26, RoleId = 3, MenuId = 21, CreateTime = DateTime.Parse("2024-01-01 00:00:00") }  // 质量报告
            );
            */
                
            // 公司信息默认数据
            modelBuilder.Entity<Company>().HasData(
                new Company
                {
                    Id = 1,
                    Name = "丰信科技有限公司",
                    CreditCode = "91310000XXXXXXXXXX",
                    LegalRepresentative = "张三",
                    RegisteredCapital = "1000万元",
                    Address = "上海市浦东新区张江高科技园区",
                    Phone = "021-12345678",
                    Email = "<EMAIL>",
                    CompanyType = "LLC",
                    RegistrationDate = new DateTime(2020, 1, 1),
                    Description = "丰信科技是一家专注于智能制造领域的高新技术企业，致力于为工业企业提供先进的智能制造解决方案和服务。",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            );

            // 部门默认数据
            modelBuilder.Entity<Department>().HasData(
                new Department 
                { 
                    Id = 1, 
                    Name = "研发部", 
                    Code = "RD", 
                    Manager = "张三", 
                    ParentId = null, 
                    Level = "1", 
                    EmployeeCount = 20, 
                    CreateTime = DateTime.Parse("2023-01-01 08:00:00"),
                    Description = "负责产品研发"
                },
                new Department 
                { 
                    Id = 2, 
                    Name = "生产部", 
                    Code = "PROD", 
                    Manager = "李四", 
                    ParentId = null, 
                    Level = "1", 
                    EmployeeCount = 50, 
                    CreateTime = DateTime.Parse("2023-01-02 09:00:00"),
                    Description = "负责产品生产"
                },
                new Department 
                { 
                    Id = 3, 
                    Name = "质检部", 
                    Code = "QA", 
                    Manager = "王五", 
                    ParentId = 2, 
                    Level = "2", 
                    EmployeeCount = 15, 
                    CreateTime = DateTime.Parse("2023-01-03 10:00:00"),
                    Description = "负责质量检测"
                },
                new Department 
                { 
                    Id = 4, 
                    Name = "仓储部", 
                    Code = "WH", 
                    Manager = "赵六", 
                    ParentId = null, 
                    Level = "1", 
                    EmployeeCount = 10, 
                    CreateTime = DateTime.Parse("2023-01-04 11:00:00"),
                    Description = "负责仓库管理"
                },
                new Department 
                { 
                    Id = 5, 
                    Name = "人力资源部", 
                    Code = "HR", 
                    Manager = "钱七", 
                    ParentId = null, 
                    Level = "1", 
                    EmployeeCount = 8, 
                    CreateTime = DateTime.Parse("2023-01-05 12:00:00"),
                    Description = "负责人力资源管理"
                },
                new Department 
                { 
                    Id = 6, 
                    Name = "前端开发组", 
                    Code = "FE", 
                    Manager = "孙八", 
                    ParentId = 1, 
                    Level = "2", 
                    EmployeeCount = 12, 
                    CreateTime = DateTime.Parse("2023-01-06 13:00:00"),
                    Description = "负责前端开发"
                },
                new Department 
                { 
                    Id = 7, 
                    Name = "后端开发组", 
                    Code = "BE", 
                    Manager = "周九", 
                    ParentId = 1, 
                    Level = "2", 
                    EmployeeCount = 8, 
                    CreateTime = DateTime.Parse("2023-01-07 14:00:00"),
                    Description = "负责后端开发"
                },
                new Department 
                { 
                    Id = 8, 
                    Name = "UI设计团队", 
                    Code = "UI", 
                    Manager = "吴十", 
                    ParentId = 6, 
                    Level = "3", 
                    EmployeeCount = 5, 
                    CreateTime = DateTime.Parse("2023-01-08 15:00:00"),
                    Description = "负责UI设计"
                }
            );

            // 员工默认数据
            modelBuilder.Entity<Employee>().HasData(
                new Employee
                {
                    Id = 1,
                    EmployeeId = "EMP001",
                    Name = "张大明",
                    DepartmentId = 1, // 研发部
                    Position = "软件工程师",
                    Phone = "13800138001",
                    Email = "<EMAIL>",
                    EntryDate = DateTime.Parse("2022-01-15"),
                    Status = "active",
                    Birthday = DateTime.Parse("1990-05-10"),
                    IdCard = "310101199005103215",
                    Age = 33,
                    Gender = "男",
                    Education = "本科",
                    ContractNo = "HT2022001",
                    Level = "3",
                    SkillLevel = "高级",
                    PerformanceLevel = "A",
                    FactoryName = "总部研发中心",
                    FactoryAddress = "上海市浦东新区张江高科技园区",
                    FactoryContact = "王经理",
                    FactoryPhone = "021-12345678",
                    SalaryType = "月薪",
                    Team = "后端开发组",
                    BaseSalary = "15000",
                    WorkYears = 8,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                },
                new Employee
                {
                    Id = 2,
                    EmployeeId = "EMP002",
                    Name = "李小红",
                    DepartmentId = 6, // 前端开发组
                    Position = "前端开发工程师",
                    Phone = "13900139002",
                    Email = "<EMAIL>",
                    EntryDate = DateTime.Parse("2022-03-20"),
                    Status = "active",
                    Birthday = DateTime.Parse("1992-08-15"),
                    IdCard = "310101199208153624",
                    Age = 31,
                    Gender = "女",
                    Education = "本科",
                    ContractNo = "HT2022015",
                    Level = "2",
                    SkillLevel = "中级",
                    PerformanceLevel = "B+",
                    FactoryName = "总部研发中心",
                    FactoryAddress = "上海市浦东新区张江高科技园区",
                    FactoryContact = "王经理",
                    FactoryPhone = "021-12345678",
                    SalaryType = "月薪",
                    Team = "前端开发组",
                    BaseSalary = "12000",
                    WorkYears = 5,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                },
                new Employee
                {
                    Id = 3,
                    EmployeeId = "EMP003",
                    Name = "王强",
                    DepartmentId = 2, // 生产部
                    Position = "生产主管",
                    Phone = "13700137003",
                    Email = "<EMAIL>",
                    EntryDate = DateTime.Parse("2021-05-10"),
                    Status = "active",
                    Birthday = DateTime.Parse("1985-12-20"),
                    IdCard = "310101198512204871",
                    Age = 38,
                    Gender = "男",
                    Education = "大专",
                    ContractNo = "HT2021052",
                    Level = "2",
                    SkillLevel = "中级",
                    PerformanceLevel = "A",
                    FactoryName = "松江生产基地",
                    FactoryAddress = "上海市松江区新桥镇新茸路888号",
                    FactoryContact = "赵经理",
                    FactoryPhone = "021-87654321",
                    SalaryType = "月薪",
                    Team = "生产一组",
                    BaseSalary = "10000",
                    WorkYears = 10,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                },
                new Employee
                {
                    Id = 4,
                    EmployeeId = "EMP004",
                    Name = "赵静",
                    DepartmentId = 5, // 人力资源部
                    Position = "人力资源专员",
                    Phone = "13600136004",
                    Email = "<EMAIL>",
                    EntryDate = DateTime.Parse("2022-08-01"),
                    Status = "active",
                    Birthday = DateTime.Parse("1993-03-25"),
                    IdCard = "310101199303251234",
                    Age = 30,
                    Gender = "女",
                    Education = "硕士",
                    ContractNo = "HT2022089",
                    Level = "2",
                    SkillLevel = "中级",
                    PerformanceLevel = "A-",
                    FactoryName = "总部",
                    FactoryAddress = "上海市浦东新区张江高科技园区",
                    FactoryContact = "钱经理",
                    FactoryPhone = "021-12345678",
                    SalaryType = "月薪",
                    Team = "招聘组",
                    BaseSalary = "11000",
                    WorkYears = 3,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                },
                new Employee
                {
                    Id = 5,
                    EmployeeId = "EMP005",
                    Name = "陈明",
                    DepartmentId = 3, // 质检部
                    Position = "质检专员",
                    Phone = "13500135005",
                    Email = "<EMAIL>",
                    EntryDate = DateTime.Parse("2022-04-15"),
                    Status = "active",
                    Birthday = DateTime.Parse("1988-07-08"),
                    IdCard = "310101198807085678",
                    Age = 35,
                    Gender = "男",
                    Education = "大专",
                    ContractNo = "HT2022035",
                    Level = "1",
                    SkillLevel = "初级",
                    PerformanceLevel = "B",
                    FactoryName = "松江生产基地",
                    FactoryAddress = "上海市松江区新桥镇新茸路888号",
                    FactoryContact = "赵经理",
                    FactoryPhone = "021-87654321",
                    SalaryType = "月薪",
                    Team = "质检一组",
                    BaseSalary = "8000",
                    WorkYears = 5,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                }
            );

            // 注释掉职位默认数据，稍后手动添加
            /*
            modelBuilder.Entity<Position>().HasData(
                new Position 
                { 
                    Id = 1, 
                    Name = "软件工程师", 
                    DepartmentId = 1, // 研发部
                    Level = "3", 
                    Description = "负责软件开发",
                    CreateTime = DateTime.Parse("2023-01-01 08:00:00")
                },
                new Position 
                { 
                    Id = 2, 
                    Name = "产品经理", 
                    DepartmentId = 1, // 研发部 
                    Level = "3", 
                    Description = "负责产品规划与设计",
                    CreateTime = DateTime.Parse("2023-01-02 09:00:00")
                },
                new Position 
                { 
                    Id = 3, 
                    Name = "生产线长", 
                    DepartmentId = 2, // 生产部
                    Level = "2", 
                    Description = "负责生产线管理",
                    CreateTime = DateTime.Parse("2023-01-03 10:00:00")
                },
                new Position 
                { 
                    Id = 4, 
                    Name = "质检员", 
                    DepartmentId = 3, // 质检部
                    Level = "1", 
                    Description = "负责产品质量检测",
                    CreateTime = DateTime.Parse("2023-01-04 11:00:00")
                },
                new Position 
                { 
                    Id = 5, 
                    Name = "仓库管理员", 
                    DepartmentId = 4, // 仓储部
                    Level = "1", 
                    Description = "负责仓库管理",
                    CreateTime = DateTime.Parse("2023-01-05 12:00:00")
                },
                new Position 
                { 
                    Id = 6, 
                    Name = "前端开发工程师", 
                    DepartmentId = 6, // 前端开发组
                    Level = "2", 
                    Description = "负责前端开发",
                    CreateTime = DateTime.Parse("2023-01-06 13:00:00")
                },
                new Position 
                { 
                    Id = 7, 
                    Name = "后端开发工程师", 
                    DepartmentId = 7, // 后端开发组
                    Level = "2", 
                    Description = "负责后端开发",
                    CreateTime = DateTime.Parse("2023-01-07 14:00:00")
                },
                new Position 
                { 
                    Id = 8, 
                    Name = "UI设计师", 
                    DepartmentId = 6, // 前端开发组
                    Level = "2", 
                    Description = "负责UI设计",
                    CreateTime = DateTime.Parse("2023-01-08 15:00:00")
                }
            );
            */
        }
    }
} 