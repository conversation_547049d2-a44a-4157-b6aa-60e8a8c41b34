# 示例：添加新菜单项 - 库存监控

本示例展示如何在系统中添加一个新的"库存监控"菜单项，演示整个动态菜单系统的工作流程。

## 步骤 1：修改菜单配置

编辑 `src/utils/menuConfig.js`，在仓储管理子菜单中添加新的菜单项：

```javascript
{
  id: 'warehouse',
  name: '仓储管理',
  icon: Box,
  index: '4',
  type: 'sub-menu',
  children: [
    {
      id: 'warehouse-material',
      name: '物料管理',
      path: '/warehouse/material',
      icon: Goods,
      index: '/warehouse/material',
      type: 'menu-item'
    },
    {
      id: 'warehouse-inventory',
      name: '库存管理',
      path: '/warehouse/inventory',
      icon: Collection,
      index: '/warehouse/inventory',
      type: 'menu-item'
    },
    // 新增的菜单项
    {
      id: 'warehouse-monitor',
      name: '库存监控',
      path: '/warehouse/monitor',
      icon: Monitor,
      index: '/warehouse/monitor',
      type: 'menu-item'
    }
  ]
}
```

## 步骤 2：创建 Vue 组件

创建 `src/views/warehouse/InventoryMonitor.vue`：

```vue
<template>
  <div class="inventory-monitor">
    <div class="page-header">
      <h2 class="page-title">库存监控</h2>
      <div class="page-actions">
        <el-button type="primary" :icon="RefreshRight" @click="refreshData">
          刷新数据
        </el-button>
        <el-button type="success" :icon="Download" @click="exportData">
          导出报表
        </el-button>
      </div>
    </div>

    <!-- 监控仪表板 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon color="#409EFF"><Box /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">1,234</div>
              <div class="stats-label">总库存量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon color="#67C23A"><TrendCharts /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">89%</div>
              <div class="stats-label">库存周转率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon color="#E6A23C"><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">23</div>
              <div class="stats-label">库存预警</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon color="#F56C6C"><CircleClose /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">5</div>
              <div class="stats-label">缺货商品</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 库存监控表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>实时库存监控</span>
          <el-button text type="primary" @click="handleSettings">
            设置监控规则
          </el-button>
        </div>
      </template>
      
      <el-table :data="inventoryData" border style="width: 100%">
        <el-table-column prop="itemCode" label="物料编码" width="120" />
        <el-table-column prop="itemName" label="物料名称" min-width="150" />
        <el-table-column prop="category" label="分类" width="100" />
        <el-table-column prop="currentStock" label="当前库存" width="100" align="right" />
        <el-table-column prop="safetyStock" label="安全库存" width="100" align="right" />
        <el-table-column prop="maxStock" label="最大库存" width="100" align="right" />
        <el-table-column label="库存状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStockStatusType(row)">
              {{ getStockStatusText(row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastUpdate" label="最后更新" width="150" />
        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetails(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  RefreshRight,
  Download,
  Box,
  TrendCharts,
  Warning,
  CircleClose
} from '@element-plus/icons-vue'

// 库存数据
const inventoryData = ref([
  {
    itemCode: 'MAT001',
    itemName: '钢板A型',
    category: '原材料',
    currentStock: 150,
    safetyStock: 100,
    maxStock: 500,
    lastUpdate: '2024-01-10 14:30'
  },
  {
    itemCode: 'MAT002',
    itemName: '螺栓M8',
    category: '标准件',
    currentStock: 50,
    safetyStock: 100,
    maxStock: 1000,
    lastUpdate: '2024-01-10 14:25'
  },
  {
    itemCode: 'MAT003',
    itemName: '电机组件',
    category: '零部件',
    currentStock: 0,
    safetyStock: 10,
    maxStock: 50,
    lastUpdate: '2024-01-10 14:20'
  }
])

// 获取库存状态类型
const getStockStatusType = (row) => {
  if (row.currentStock === 0) return 'danger'
  if (row.currentStock < row.safetyStock) return 'warning'
  if (row.currentStock > row.maxStock) return 'info'
  return 'success'
}

// 获取库存状态文本
const getStockStatusText = (row) => {
  if (row.currentStock === 0) return '缺货'
  if (row.currentStock < row.safetyStock) return '预警'
  if (row.currentStock > row.maxStock) return '超储'
  return '正常'
}

// 刷新数据
const refreshData = () => {
  ElMessage.success('数据刷新成功')
}

// 导出数据
const exportData = () => {
  ElMessage.success('报表导出成功')
}

// 查看详情
const viewDetails = (row) => {
  ElMessage.info(`查看 ${row.itemName} 的详细信息`)
}

// 设置监控规则
const handleSettings = () => {
  ElMessage.info('打开监控规则设置')
}

// 组件挂载后加载数据
onMounted(() => {
  // 模拟数据加载
  console.log('库存监控页面已加载')
})
</script>

<style scoped>
.inventory-monitor {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  color: #303133;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 100px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  font-size: 40px;
  margin-right: 15px;
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.table-card {
  margin-top: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
```

## 步骤 3：添加路由配置

编辑 `src/router/index.js`，导入新组件并添加路由：

```javascript
import InventoryMonitor from '../views/warehouse/InventoryMonitor.vue'

// 在 children 数组中添加新路由
{
  path: 'warehouse/monitor',
  name: 'InventoryMonitor',
  component: InventoryMonitor,
  meta: { requiresAuth: true, title: '库存监控' }
}
```

## 步骤 4：测试功能

1. **启动开发服务器**：
   ```bash
   cd mes-system && npm run dev
   ```

2. **登录系统并验证**：
   - 左侧导航菜单中的"仓储管理"下应该出现"库存监控"选项
   - 点击菜单项应该能正常跳转到新页面

3. **检查角色权限设置**：
   - 访问"系统管理 -> 角色权限"
   - 切换到"角色菜单设置"标签页
   - 菜单树中应该自动包含新的"库存监控"菜单项
   - 可以为不同角色设置是否可访问该菜单

## 步骤 5：验证动态同步

1. **修改菜单配置**：
   - 在 `menuConfig.js` 中修改菜单名称或添加新菜单
   - 刷新页面

2. **检查同步效果**：
   - 左侧导航菜单应立即反映更改
   - 角色权限设置中的菜单树也应同步更新

## 预期结果

完成以上步骤后，您应该能看到：

1. **左侧导航菜单**：
   ```
   仓储管理
   ├── 物料管理
   ├── 库存管理
   └── 库存监控  ← 新增的菜单项
   ```

2. **角色权限设置菜单树**：
   - 自动包含"库存监控"选项
   - 可以为不同角色单独设置权限

3. **页面功能**：
   - 库存监控页面正常显示
   - 包含统计卡片和数据表格
   - 支持基本的交互操作

## 高级用法

### 添加菜单元数据
```javascript
{
  id: 'warehouse-monitor',
  name: '库存监控',
  path: '/warehouse/monitor',
  icon: Monitor,
  index: '/warehouse/monitor',
  type: 'menu-item',
  // 添加元数据
  meta: {
    description: '实时监控库存状态',
    requiresPermission: ['inventory:read'],
    order: 3,
    badge: 'new'
  }
}
```

### 条件显示菜单
```javascript
// 在 getFilteredMenuByPermissions 中添加条件逻辑
const showMenu = (menuItem, userPermissions) => {
  // 根据用户权限、角色等条件决定是否显示菜单
  return userPermissions.includes(menuItem.meta?.requiresPermission)
}
```

这个示例展示了完整的菜单添加流程，包括配置、组件、路由和权限设置。新增的菜单会自动同步到角色权限设置中，实现了真正的动态菜单管理。 