using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using mes_system_server.Data;
using mes_system_server.DTOs;
using Microsoft.AspNetCore.Authorization;

namespace mes_system_server.Controllers
{
    [ApiController]
    [Route("api/categories")]
    [AllowAnonymous]
    public class CategoriesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<CategoriesController> _logger;

        public CategoriesController(ApplicationDbContext context, ILogger<CategoriesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取产品分类
        /// </summary>
        /// <returns>产品分类列表</returns>
        [HttpGet("products")]
        public async Task<ActionResult<ApiResponseDto<List<MaterialCategoryDto>>>> GetProductCategories()
        {
            try
            {
                var categories = await GetCategoriesByType("REF_FG");
                return Ok(ApiResponseDto<List<MaterialCategoryDto>>.Success(categories));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取产品分类失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取产品分类失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 获取组件分类
        /// </summary>
        /// <returns>组件分类列表</returns>
        [HttpGet("components")]
        public async Task<ActionResult<ApiResponseDto<List<MaterialCategoryDto>>>> GetComponentCategories()
        {
            try
            {
                var categories = await GetCategoriesByType("REF_SUB");
                return Ok(ApiResponseDto<List<MaterialCategoryDto>>.Success(categories));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取组件分类失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取组件分类失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 获取零件分类
        /// </summary>
        /// <returns>零件分类列表</returns>
        [HttpGet("parts")]
        public async Task<ActionResult<ApiResponseDto<List<MaterialCategoryDto>>>> GetPartCategories()
        {
            try
            {
                var categories = await GetCategoriesByType("REF_COMP");
                return Ok(ApiResponseDto<List<MaterialCategoryDto>>.Success(categories));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取零件分类失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取零件分类失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 获取辅料包材分类
        /// </summary>
        /// <returns>辅料包材分类列表</returns>
        [HttpGet("auxiliary")]
        public async Task<ActionResult<ApiResponseDto<List<MaterialCategoryDto>>>> GetAuxiliaryCategories()
        {
            try
            {
                var categories = await GetCategoriesByType("REF_ACC");
                return Ok(ApiResponseDto<List<MaterialCategoryDto>>.Success(categories));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取辅料包材分类失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取辅料包材分类失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 获取五金塑胶分类
        /// </summary>
        /// <returns>五金塑胶分类列表</returns>
        [HttpGet("hardware-plastic")]
        public async Task<ActionResult<ApiResponseDto<List<MaterialCategoryDto>>>> GetHardwarePlasticCategories()
        {
            try
            {
                var categories = await GetCategoriesByType("REF_MET");
                return Ok(ApiResponseDto<List<MaterialCategoryDto>>.Success(categories));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取五金塑胶分类失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取五金塑胶分类失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 获取所有物料分类
        /// </summary>
        /// <returns>所有物料分类列表</returns>
        [HttpGet("all")]
        public async Task<ActionResult<ApiResponseDto<Dictionary<string, List<MaterialCategoryDto>>>>> GetAllCategories()
        {
            try
            {
                var result = new Dictionary<string, List<MaterialCategoryDto>>();
                
                result["products"] = await GetCategoriesByType("REF_FG");
                result["components"] = await GetCategoriesByType("REF_SUB");
                result["parts"] = await GetCategoriesByType("REF_COMP");
                result["auxiliary"] = await GetCategoriesByType("REF_ACC");
                result["hardware-plastic"] = await GetCategoriesByType("REF_MET");

                return Ok(ApiResponseDto<Dictionary<string, List<MaterialCategoryDto>>>.Success(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有物料分类失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取所有物料分类失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 根据类型获取分类
        /// </summary>
        /// <param name="categoryType">分类类型</param>
        /// <returns>分类列表</returns>
        private async Task<List<MaterialCategoryDto>> GetCategoriesByType(string categoryType)
        {
            // 首先尝试从数据字典中获取
            var dictItems = await _context.DataDictionaryItems
                .Include(ddi => ddi.DataDictionary)
                .Where(ddi => ddi.DataDictionary.Code == "MATERIAL_CATEGORY" && 
                             ddi.Value == categoryType && 
                             ddi.Status == true)
                .OrderBy(ddi => ddi.Sort)
                .Select(ddi => new MaterialCategoryDto
                {
                    Code = ddi.Value,
                    Name = ddi.Label,
                    Description = ddi.Description ?? string.Empty,
                    Sort = ddi.Sort
                })
                .ToListAsync();

            // 如果数据字典中没有数据，返回默认分类
            if (dictItems.Any())
            {
                return dictItems;
            }

            // 返回默认分类
            return GetDefaultCategories(categoryType);
        }

        /// <summary>
        /// 获取默认分类
        /// </summary>
        /// <param name="categoryType">分类类型</param>
        /// <returns>默认分类列表</returns>
        private List<MaterialCategoryDto> GetDefaultCategories(string categoryType)
        {
            return categoryType switch
            {
                "REF_FG" => new List<MaterialCategoryDto>
                {
                    new MaterialCategoryDto { Code = "REF_FG", Name = "成品型号", Description = "最终制成品", Sort = 1 }
                },
                "REF_SUB" => new List<MaterialCategoryDto>
                {
                    new MaterialCategoryDto { Code = "REF_SUB", Name = "组件型号", Description = "产品组成部件", Sort = 2 }
                },
                "REF_COMP" => new List<MaterialCategoryDto>
                {
                    new MaterialCategoryDto { Code = "REF_COMP", Name = "零件型号", Description = "最小粒度零件", Sort = 3 }
                },
                "REF_ACC" => new List<MaterialCategoryDto>
                {
                    new MaterialCategoryDto { Code = "REF_ACC", Name = "辅料包材型号", Description = "辅助和包装材料", Sort = 4 }
                },
                "REF_MET" => new List<MaterialCategoryDto>
                {
                    new MaterialCategoryDto { Code = "REF_MET", Name = "塑胶五金型号", Description = "塑胶五金制品", Sort = 5 }
                },
                _ => new List<MaterialCategoryDto>()
            };
        }

        /// <summary>
        /// 获取物料分类统计信息
        /// </summary>
        /// <returns>分类统计信息</returns>
        [HttpGet("statistics")]
        public async Task<ActionResult<ApiResponseDto<Dictionary<string, object>>>> GetCategoryStatistics()
        {
            try
            {
                var statistics = new Dictionary<string, object>();

                // 统计每种类型的物料数量
                var materialCounts = await _context.MaterialModels
                    .Where(mm => mm.IsActive)
                    .GroupBy(mm => mm.Type)
                    .Select(g => new { Type = g.Key, Count = g.Count() })
                    .ToListAsync();

                foreach (var item in materialCounts)
                {
                    var typeName = item.Type switch
                    {
                        "products" => "成品型号",
                        "components" => "组件型号",
                        "parts" => "零件型号",
                        "auxiliary" => "辅料包材型号",
                        "hardware-plastic" => "塑胶五金型号",
                        _ => item.Type
                    };
                    
                    statistics[typeName] = item.Count;
                }

                return Ok(ApiResponseDto<Dictionary<string, object>>.Success(statistics));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分类统计信息失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取分类统计信息失败: {ex.Message}", 500));
            }
        }
    }
} 