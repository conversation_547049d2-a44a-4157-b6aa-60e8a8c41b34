<template>
  <div class="attendance-management">
    <div class="operation-bar">
      <div class="search-area">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :shortcuts="dateShortcuts"
          @change="handleDateChange"
        />

        <el-input
          v-model="searchQuery"
          placeholder="请输入员工姓名/工号"
          class="search-input"
          clearable
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
          </template>
        </el-input>

        <el-select v-model="departmentFilter" clearable placeholder="部门" class="filter-select" @change="handleSearch">
          <el-option label="研发部" value="研发部" />
          <el-option label="生产部" value="生产部" />
          <el-option label="质检部" value="质检部" />
          <el-option label="仓储部" value="仓储部" />
        </el-select>

        <el-button type="primary" :icon="Plus" @click="handleAdd">登记考勤</el-button>
        <el-button type="success" :icon="Upload">导入</el-button>
        <el-button type="warning" :icon="Download">导出</el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="attendanceList"
      border
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="date" label="日期" width="120" align="center" />
      <el-table-column prop="employeeId" label="工号" width="100" align="center" />
      <el-table-column prop="name" label="姓名" width="120" align="center" />
      <el-table-column prop="department" label="部门" width="120" align="center" />
      <el-table-column prop="checkIn" label="上班时间" width="120" align="center" />
      <el-table-column prop="checkOut" label="下班时间" width="120" align="center" />
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusLabel(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" min-width="180" align="center" />
      <el-table-column label="操作" width="150" align="center">
        <template #default="{ row }">
          <el-button-group>
            <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 考勤登记对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '登记考勤' : '编辑考勤'"
      width="500px"
    >
      <el-form
        ref="attendanceFormRef"
        :model="attendanceForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="日期" prop="date">
          <el-date-picker
            v-model="attendanceForm.date"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="员工" prop="employeeId">
          <el-select v-model="attendanceForm.employeeId" placeholder="请选择员工" style="width: 100%">
            <el-option
              v-for="employee in employeeOptions"
              :key="employee.id"
              :label="employee.name"
              :value="employee.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="上班时间" prop="checkIn">
          <el-time-picker
            v-model="attendanceForm.checkIn"
            format="HH:mm"
            placeholder="选择时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="下班时间" prop="checkOut">
          <el-time-picker
            v-model="attendanceForm.checkOut"
            format="HH:mm"
            placeholder="选择时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="attendanceForm.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="正常" value="normal" />
            <el-option label="迟到" value="late" />
            <el-option label="早退" value="early" />
            <el-option label="缺勤" value="absent" />
            <el-option label="请假" value="leave" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="attendanceForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Upload, Download } from '@element-plus/icons-vue'

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

// 搜索和筛选条件
const dateRange = ref([])
const searchQuery = ref('')
const departmentFilter = ref('')

// 表格数据
const loading = ref(false)
const attendanceList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 员工选项
const employeeOptions = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' }
])

// 表单对话框
const dialogVisible = ref(false)
const dialogType = ref('add')
const attendanceFormRef = ref(null)
const attendanceForm = reactive({
  date: '',
  employeeId: '',
  checkIn: '',
  checkOut: '',
  status: '',
  remark: ''
})

// 表单验证规则
const rules = {
  date: [
    { required: true, message: '请选择日期', trigger: 'change' }
  ],
  employeeId: [
    { required: true, message: '请选择员工', trigger: 'change' }
  ],
  checkIn: [
    { required: true, message: '请选择上班时间', trigger: 'change' }
  ],
  checkOut: [
    { required: true, message: '请选择下班时间', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 获取状态标签类型
const getStatusType = (status) => {
  const types = {
    normal: 'success',
    late: 'warning',
    early: 'warning',
    absent: 'danger',
    leave: 'info'
  }
  return types[status] || 'info'
}

// 获取状态标签文本
const getStatusLabel = (status) => {
  const labels = {
    normal: '正常',
    late: '迟到',
    early: '早退',
    absent: '缺勤',
    leave: '请假'
  }
  return labels[status] || status
}

// 日期变更
const handleDateChange = () => {
  handleSearch()
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchAttendanceList()
}

// 获取考勤列表
const fetchAttendanceList = async () => {
  loading.value = true
  try {
    // TODO: 调用后端API获取考勤列表
    loading.value = false
  } catch (error) {
    console.error('获取考勤列表失败:', error)
    ElMessage.error('获取考勤列表失败，请稍后重试')
    loading.value = false
  }
}

// 新增考勤
const handleAdd = () => {
  dialogType.value = 'add'
  Object.keys(attendanceForm).forEach(key => {
    attendanceForm[key] = ''
  })
  dialogVisible.value = true
}

// 编辑考勤
const handleEdit = (row) => {
  dialogType.value = 'edit'
  Object.keys(attendanceForm).forEach(key => {
    attendanceForm[key] = row[key]
  })
  dialogVisible.value = true
}

// 删除考勤
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除该考勤记录吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    // TODO: 调用后端API删除考勤记录
    ElMessage.success('删除成功')
    fetchAttendanceList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除考勤记录失败:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!attendanceFormRef.value) return
  
  try {
    await attendanceFormRef.value.validate()
    // TODO: 调用后端API保存考勤信息
    ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
    dialogVisible.value = false
    fetchAttendanceList()
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchAttendanceList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchAttendanceList()
}

// 初始化
onMounted(() => {
  fetchAttendanceList()
})
</script>

<style scoped>
.attendance-management {
  padding: 20px;
}

.operation-bar {
  margin-bottom: 20px;
}

.search-area {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input {
  width: 200px;
}

.filter-select {
  width: 120px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 