using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using mes_system_server.Data;
using mes_system_server.DTOs;
using mes_system_server.Models;
using System.Text.RegularExpressions;

namespace mes_system_server.Controllers
{
    /// <summary>
    /// WIS管理控制器
    /// </summary>
    [Route("api/wis")]
    [ApiController]
    public class WisController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<WisController> _logger;

        public WisController(ApplicationDbContext context, ILogger<WisController> logger)
        {
            _context = context;
            _logger = logger;
        }

        #region PDF图片管理

        /// <summary>
        /// 上传PDF图片
        /// </summary>
        /// <param name="request">上传请求</param>
        /// <returns>上传结果</returns>
        [HttpPost("uploadPDFImage")]
        [AllowAnonymous]
        public async Task<IActionResult> UploadPDFImage([FromBody] UploadPDFImageDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                // 过滤书签名称
                var filteredBookmarkName = FilterBookmarkName(request.BookmarkName);

                // 检查是否已存在相同的记录
                var existingBookmark = await _context.WisPDFBookmarks
                    .FirstOrDefaultAsync(b => b.FileName == request.FileName 
                                            && b.PageNumber == request.PageNumber 
                                            && b.Status);

                if (existingBookmark != null)
                {
                    // 更新现有记录
                    existingBookmark.OriginalBookmarkName = request.BookmarkName;
                    existingBookmark.FilteredBookmarkName = filteredBookmarkName;
                    existingBookmark.ImageBase64 = request.ImageBase64;
                    existingBookmark.ImageWidth = request.ImageWidth;
                    existingBookmark.ImageHeight = request.ImageHeight;
                    existingBookmark.ImageSize = GetBase64Size(request.ImageBase64);
                    existingBookmark.Remarks = request.Remarks;
                    existingBookmark.UpdateTime = DateTime.UtcNow;

                    _context.WisPDFBookmarks.Update(existingBookmark);
                }
                else
                {
                    // 创建新记录
                    var bookmark = new WisPDFBookmark
                    {
                        FileName = request.FileName,
                        OriginalBookmarkName = request.BookmarkName,
                        FilteredBookmarkName = filteredBookmarkName,
                        PageNumber = request.PageNumber,
                        ImageBase64 = request.ImageBase64,
                        ImageWidth = request.ImageWidth,
                        ImageHeight = request.ImageHeight,
                        ImageSize = GetBase64Size(request.ImageBase64),
                        Status = true,
                        Remarks = request.Remarks,
                        CreateTime = DateTime.UtcNow,
                        UpdateTime = DateTime.UtcNow
                    };

                    _context.WisPDFBookmarks.Add(bookmark);
                }

                await _context.SaveChangesAsync();
                return Ok(ApiResponseDto<object>.Success(null, "上传成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "上传PDF图片失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"上传失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 上传PDF图片到云服务器文件夹
        /// </summary>
        /// <param name="request">上传请求</param>
        /// <returns>上传结果</returns>
        [HttpPost("uploadPDFImageToCloud")]
        [AllowAnonymous]
        public async Task<IActionResult> UploadPDFImageToCloud([FromBody] UploadPDFImageToCloudDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                // 过滤书签名称，用作文件名
                var filteredBookmarkName = FilterBookmarkName(request.BookmarkName);
                
                // 创建云服务器文件夹路径
                var cloudFolderPath = Path.Combine("uploads", "wis-pdf-images", request.FileName);
                var fullCloudPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "wwwroot", cloudFolderPath);
                
                // 确保文件夹存在
                Directory.CreateDirectory(fullCloudPath);
                
                // 生成文件名：书签名_页码.jpg
                var fileName = $"{filteredBookmarkName}_第{request.PageNumber}页.jpg";
                var filePath = Path.Combine(fullCloudPath, fileName);
                var relativeImagePath = $"/{cloudFolderPath.Replace("\\", "/")}/{fileName}";
                
                // 将Base64转换为图片文件并保存
                var base64Data = request.ImageBase64;
                if (base64Data.Contains(","))
                {
                    base64Data = base64Data.Split(',')[1]; // 移除data:image/jpeg;base64,前缀
                }
                
                var imageBytes = Convert.FromBase64String(base64Data);
                await System.IO.File.WriteAllBytesAsync(filePath, imageBytes);
                
                // 检查是否已存在相同的记录
                var existingBookmark = await _context.WisPDFBookmarks
                    .FirstOrDefaultAsync(b => b.FileName == request.FileName 
                                            && b.PageNumber == request.PageNumber 
                                            && b.Status);

                if (existingBookmark != null)
                {
                    // 更新现有记录
                    existingBookmark.OriginalBookmarkName = request.BookmarkName;
                    existingBookmark.FilteredBookmarkName = filteredBookmarkName;
                    existingBookmark.ImagePath = relativeImagePath; // 保存相对路径
                    existingBookmark.ImageSize = imageBytes.Length;
                    existingBookmark.ImageWidth = request.ImageWidth;
                    existingBookmark.ImageHeight = request.ImageHeight;
                    existingBookmark.Remarks = request.Remarks;
                    existingBookmark.UpdateTime = DateTime.UtcNow;
                    
                    // 如果需要同时保存Base64，可以保留
                    if (request.SaveBase64)
                    {
                        existingBookmark.ImageBase64 = request.ImageBase64;
                    }

                    _context.WisPDFBookmarks.Update(existingBookmark);
                }
                else
                {
                    // 创建新记录
                    var bookmark = new WisPDFBookmark
                    {
                        FileName = request.FileName,
                        OriginalBookmarkName = request.BookmarkName,
                        FilteredBookmarkName = filteredBookmarkName,
                        PageNumber = request.PageNumber,
                        ImagePath = relativeImagePath, // 保存相对路径
                        ImageSize = imageBytes.Length,
                        ImageWidth = request.ImageWidth,
                        ImageHeight = request.ImageHeight,
                        Status = true,
                        Remarks = request.Remarks,
                        CreateTime = DateTime.UtcNow,
                        UpdateTime = DateTime.UtcNow
                    };
                    
                    // 如果需要同时保存Base64，可以保留
                    if (request.SaveBase64)
                    {
                        bookmark.ImageBase64 = request.ImageBase64;
                    }

                    _context.WisPDFBookmarks.Add(bookmark);
                }

                await _context.SaveChangesAsync();
                
                return Ok(ApiResponseDto<object>.Success(new 
                { 
                    imagePath = relativeImagePath,
                    fileName = fileName,
                    fileSize = imageBytes.Length
                }, "图片上传到云服务器成功"));
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "上传PDF图片到云服务器失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"上传失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 获取PDF图片
        /// </summary>
        /// <param name="fileName">PDF文件名</param>
        /// <param name="pageNumber">页码</param>
        /// <returns>图片数据</returns>
        [HttpGet("getPDFImage")]
        [AllowAnonymous]
        public async Task<IActionResult> GetPDFImage([FromQuery] string fileName, [FromQuery] int pageNumber)
        {
            if (string.IsNullOrWhiteSpace(fileName) || pageNumber <= 0)
            {
                return BadRequest(ApiResponseDto<string>.Fail("文件名和页码不能为空"));
            }

            try
            {
                var bookmark = await _context.WisPDFBookmarks
                    .FirstOrDefaultAsync(b => b.FileName == fileName 
                                            && b.PageNumber == pageNumber 
                                            && b.Status);

                if (bookmark == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("未找到对应的PDF图片", 404));
                }

                var result = new GetPDFImageResponseDto
                {
                    Id = bookmark.Id,
                    FileName = bookmark.FileName,
                    BookmarkName = bookmark.FilteredBookmarkName,
                    PageNumber = bookmark.PageNumber,
                    ImageBase64 = bookmark.ImageBase64 ?? string.Empty,
                    ImagePath = bookmark.ImagePath,
                    ImageSize = bookmark.ImageSize,
                    ImageWidth = bookmark.ImageWidth,
                    ImageHeight = bookmark.ImageHeight,
                    CreateTime = bookmark.CreateTime
                };

                return Ok(ApiResponseDto<GetPDFImageResponseDto>.Success(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取PDF图片失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 删除PDF图片数据
        /// </summary>
        /// <param name="request">删除请求</param>
        /// <returns>删除结果</returns>
        [HttpDelete("deletePDFImages")]
        [AllowAnonymous]
        public async Task<IActionResult> DeletePDFImages([FromBody] DeletePDFDataDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                var bookmarks = await _context.WisPDFBookmarks
                    .Where(b => b.FileName == request.FileName && b.Status)
                    .ToListAsync();

                if (!bookmarks.Any())
                {
                    return NotFound(ApiResponseDto<string>.Fail("未找到要删除的数据", 404));
                }

                // 标记为删除状态
                foreach (var bookmark in bookmarks)
                {
                    bookmark.Status = false;
                    bookmark.UpdateTime = DateTime.UtcNow;
                }

                _context.WisPDFBookmarks.UpdateRange(bookmarks);
                await _context.SaveChangesAsync();

                return Ok(ApiResponseDto<object>.Success(null, $"删除成功，共删除 {bookmarks.Count} 条记录"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除PDF图片失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"删除失败: {ex.Message}", 500));
            }
        }

        #endregion

        #region PDF书签管理

        /// <summary>
        /// 保存PDF书签数据
        /// </summary>
        /// <param name="request">保存请求</param>
        /// <returns>保存结果</returns>
        [HttpPost("savePDFBookmarks")]
        [AllowAnonymous]
        public async Task<IActionResult> SavePDFBookmarks([FromBody] SavePDFBookmarksDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                var bookmarksToAdd = new List<WisPDFBookmark>();
                var bookmarksToUpdate = new List<WisPDFBookmark>();

                foreach (var bookmarkItem in request.Bookmarks)
                {
                    // 检查是否已存在相同的记录
                    var existingBookmark = await _context.WisPDFBookmarks
                        .FirstOrDefaultAsync(b => b.FileName == request.FileName 
                                                && b.PageNumber == bookmarkItem.PageNumber 
                                                && b.Status);

                    if (existingBookmark != null)
                    {
                        // 更新现有记录
                        existingBookmark.OriginalBookmarkName = bookmarkItem.OriginalBookmarkName;
                        existingBookmark.FilteredBookmarkName = bookmarkItem.FilteredBookmarkName;
                        existingBookmark.Remarks = bookmarkItem.Remarks;
                        existingBookmark.UpdateTime = DateTime.UtcNow;

                        bookmarksToUpdate.Add(existingBookmark);
                    }
                    else
                    {
                        // 创建新记录
                        var bookmark = new WisPDFBookmark
                        {
                            FileName = request.FileName,
                            OriginalBookmarkName = bookmarkItem.OriginalBookmarkName,
                            FilteredBookmarkName = bookmarkItem.FilteredBookmarkName,
                            PageNumber = bookmarkItem.PageNumber,
                            Status = true,
                            Remarks = bookmarkItem.Remarks,
                            CreateTime = DateTime.UtcNow,
                            UpdateTime = DateTime.UtcNow
                        };

                        bookmarksToAdd.Add(bookmark);
                    }
                }

                if (bookmarksToAdd.Any())
                {
                    _context.WisPDFBookmarks.AddRange(bookmarksToAdd);
                }

                if (bookmarksToUpdate.Any())
                {
                    _context.WisPDFBookmarks.UpdateRange(bookmarksToUpdate);
                }

                await _context.SaveChangesAsync();
                return Ok(ApiResponseDto<object>.Success(null, $"保存成功，新增 {bookmarksToAdd.Count} 条，更新 {bookmarksToUpdate.Count} 条"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存PDF书签失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"保存失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 获取PDF书签数据
        /// </summary>
        /// <param name="fileName">PDF文件名</param>
        /// <returns>书签列表</returns>
        [HttpGet("getPDFBookmarks")]
        [AllowAnonymous]
        public async Task<IActionResult> GetPDFBookmarks([FromQuery] string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
            {
                return BadRequest(ApiResponseDto<string>.Fail("文件名不能为空"));
            }

            try
            {
                var bookmarks = await _context.WisPDFBookmarks
                    .Where(b => b.FileName == fileName && b.Status)
                    .OrderBy(b => b.PageNumber)
                    .Select(b => new WisPDFBookmarkDto
                    {
                        Id = b.Id,
                        FileName = b.FileName,
                        OriginalBookmarkName = b.OriginalBookmarkName,
                        FilteredBookmarkName = b.FilteredBookmarkName,
                        PageNumber = b.PageNumber,
                        ImageBase64 = b.ImageBase64,
                        ImagePath = b.ImagePath,
                        ImageSize = b.ImageSize,
                        ImageWidth = b.ImageWidth,
                        ImageHeight = b.ImageHeight,
                        Status = b.Status,
                        Remarks = b.Remarks,
                        CreateTime = b.CreateTime,
                        UpdateTime = b.UpdateTime,
                        CreatedBy = b.CreatedBy,
                        UpdatedBy = b.UpdatedBy
                    })
                    .ToListAsync();

                return Ok(ApiResponseDto<List<WisPDFBookmarkDto>>.Success(bookmarks));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取PDF书签失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 删除PDF书签数据
        /// </summary>
        /// <param name="request">删除请求</param>
        /// <returns>删除结果</returns>
        [HttpDelete("deletePDFBookmarks")]
        [AllowAnonymous]
        public async Task<IActionResult> DeletePDFBookmarks([FromBody] DeletePDFDataDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                var bookmarks = await _context.WisPDFBookmarks
                    .Where(b => b.FileName == request.FileName && b.Status)
                    .ToListAsync();

                if (!bookmarks.Any())
                {
                    return NotFound(ApiResponseDto<string>.Fail("未找到要删除的数据", 404));
                }

                // 标记为删除状态
                foreach (var bookmark in bookmarks)
                {
                    bookmark.Status = false;
                    bookmark.UpdateTime = DateTime.UtcNow;
                }

                _context.WisPDFBookmarks.UpdateRange(bookmarks);
                await _context.SaveChangesAsync();

                return Ok(ApiResponseDto<object>.Success(null, $"删除成功，共删除 {bookmarks.Count} 条记录"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除PDF书签失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"删除失败: {ex.Message}", 500));
            }
        }

        #endregion

        #region 查询接口优化

        /// <summary>
        /// 查询PDF书签 - 支持分页和搜索
        /// </summary>
        /// <param name="keyword">搜索关键词</param>
        /// <param name="pageSize">每页数量</param>
        /// <param name="pageNumber">页码</param>
        /// <param name="loadImages">是否加载图片数据</param>
        /// <returns>分页查询结果</returns>
        [HttpGet("bookmarks/search")]
        [AllowAnonymous]
        public async Task<IActionResult> QueryPDFBookmarks(
            [FromQuery] string? keyword = null,
            [FromQuery] int pageSize = 50,
            [FromQuery] int pageNumber = 1,
            [FromQuery] bool loadImages = false)
        {
            try
            {
                var query = _context.WisPDFBookmarks
                    .Where(b => b.Status);

                // 应用搜索条件
                if (!string.IsNullOrWhiteSpace(keyword))
                {
                    query = query.Where(b => 
                        b.FileName.Contains(keyword) || 
                        b.OriginalBookmarkName.Contains(keyword) ||
                        b.FilteredBookmarkName.Contains(keyword));
                }

                // 获取总数
                var totalCount = await query.CountAsync();
                
                // 分页查询
                var bookmarks = await query
                    .OrderBy(b => b.FileName)
                    .ThenBy(b => b.PageNumber)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .Select(b => new WisPDFBookmarkDto
                    {
                        Id = b.Id,
                        FileName = b.FileName,
                        OriginalBookmarkName = b.OriginalBookmarkName,
                        FilteredBookmarkName = b.FilteredBookmarkName,
                        PageNumber = b.PageNumber,
                        // 根据参数决定是否加载图片数据
                        ImageBase64 = loadImages ? b.ImageBase64 : null,
                        ImagePath = b.ImagePath,
                        ImageSize = b.ImageSize,
                        ImageWidth = b.ImageWidth,
                        ImageHeight = b.ImageHeight,
                        Status = b.Status,
                        Remarks = b.Remarks,
                        CreateTime = b.CreateTime,
                        UpdateTime = b.UpdateTime,
                        CreatedBy = b.CreatedBy,
                        UpdatedBy = b.UpdatedBy
                    })
                    .ToListAsync();

                var result = new
                {
                    Data = bookmarks,
                    TotalCount = totalCount,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
                };

                return Ok(ApiResponseDto<object>.Success(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询PDF书签失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"查询失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 根据文件名获取PDF书签列表 - 优化版本
        /// </summary>
        /// <param name="fileName">PDF文件名</param>
        /// <param name="loadImages">是否加载图片数据</param>
        /// <param name="pageSize">每页数量，默认100</param>
        /// <param name="pageNumber">页码，默认1</param>
        /// <returns>书签列表</returns>
        [HttpGet("bookmarks/by-filename")]
        [AllowAnonymous]
        public async Task<IActionResult> GetPDFBookmarksByFileName(
            [FromQuery] string fileName,
            [FromQuery] bool loadImages = false,
            [FromQuery] int pageSize = 100,
            [FromQuery] int pageNumber = 1)
        {
            if (string.IsNullOrWhiteSpace(fileName))
            {
                return BadRequest(ApiResponseDto<string>.Fail("文件名不能为空"));
            }

            try
            {
                var query = _context.WisPDFBookmarks
                    .Where(b => b.FileName == fileName && b.Status);

                // 获取总数
                var totalCount = await query.CountAsync();

                if (totalCount == 0)
                {
                    return Ok(ApiResponseDto<List<WisPDFBookmarkDto>>.Success(new List<WisPDFBookmarkDto>()));
                }

                // 分页查询
                var bookmarks = await query
                    .OrderBy(b => b.PageNumber)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .Select(b => new WisPDFBookmarkDto
                    {
                        Id = b.Id,
                        FileName = b.FileName,
                        OriginalBookmarkName = b.OriginalBookmarkName,
                        FilteredBookmarkName = b.FilteredBookmarkName,
                        PageNumber = b.PageNumber,
                        // 根据参数决定是否加载图片数据
                        ImageBase64 = loadImages ? b.ImageBase64 : null,
                        ImagePath = b.ImagePath,
                        ImageSize = b.ImageSize,
                        ImageWidth = b.ImageWidth,
                        ImageHeight = b.ImageHeight,
                        Status = b.Status,
                        Remarks = b.Remarks,
                        CreateTime = b.CreateTime,
                        UpdateTime = b.UpdateTime,
                        CreatedBy = b.CreatedBy,
                        UpdatedBy = b.UpdatedBy
                    })
                    .ToListAsync();

                return Ok(ApiResponseDto<List<WisPDFBookmarkDto>>.Success(bookmarks));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取PDF书签失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 获取PDF文件的基本信息（不含图片数据）
        /// </summary>
        /// <param name="fileName">PDF文件名</param>
        /// <returns>PDF基本信息</returns>
        [HttpGet("pdf/info")]
        [AllowAnonymous]
        public async Task<IActionResult> GetPDFInfo([FromQuery] string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
            {
                return BadRequest(ApiResponseDto<string>.Fail("文件名不能为空"));
            }

            try
            {
                var info = await _context.WisPDFBookmarks
                    .Where(b => b.FileName == fileName && b.Status)
                    .GroupBy(b => b.FileName)
                    .Select(g => new
                    {
                        FileName = g.Key,
                        TotalPages = g.Count(),
                        TotalSize = g.Sum(b => b.ImageSize ?? 0),
                        CreateTime = g.Min(b => b.CreateTime),
                        UpdateTime = g.Max(b => b.UpdateTime)
                    })
                    .FirstOrDefaultAsync();

                if (info == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("未找到PDF文件信息", 404));
                }

                return Ok(ApiResponseDto<object>.Success(info));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取PDF信息失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 获取所有PDF文件名列表
        /// </summary>
        /// <returns>PDF文件名列表</returns>
        [HttpGet("pdf-list")]
        [AllowAnonymous]
        public async Task<IActionResult> GetPDFList()
        {
            try
            {
                var pdfList = await _context.WisPDFBookmarks
                    .Where(b => b.Status)
                    .GroupBy(b => b.FileName)
                    .Select(g => new
                    {
                        FileName = g.Key,
                        BookmarkCount = g.Count(),
                        TotalSize = g.Sum(b => b.ImageSize ?? 0),
                        CreateTime = g.Min(b => b.CreateTime),
                        UpdateTime = g.Max(b => b.UpdateTime)
                    })
                    .OrderByDescending(p => p.UpdateTime)
                    .ToListAsync();

                return Ok(ApiResponseDto<object>.Success(pdfList));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取PDF文件列表失败");
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取失败: {ex.Message}", 500));
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 过滤书签名称，移除空格和冒号
        /// </summary>
        /// <param name="bookmarkName">原始书签名称</param>
        /// <returns>过滤后的书签名称</returns>
        private static string FilterBookmarkName(string bookmarkName)
        {
            if (string.IsNullOrWhiteSpace(bookmarkName))
                return string.Empty;

            // 使用正则表达式移除空格、冒号等特殊字符
            return Regex.Replace(bookmarkName, @"[\s:：]", "");
        }

        /// <summary>
        /// 计算Base64字符串的大小
        /// </summary>
        /// <param name="base64String">Base64字符串</param>
        /// <returns>字节大小</returns>
        private static long GetBase64Size(string base64String)
        {
            if (string.IsNullOrWhiteSpace(base64String))
                return 0;

            // 移除data:image前缀
            var base64Data = base64String;
            if (base64String.StartsWith("data:"))
            {
                var commaIndex = base64String.IndexOf(',');
                if (commaIndex > 0)
                {
                    base64Data = base64String.Substring(commaIndex + 1);
                }
            }

            // 计算实际字节大小
            var padding = base64Data.EndsWith("==") ? 2 : base64Data.EndsWith("=") ? 1 : 0;
            return (base64Data.Length * 3) / 4 - padding;
        }

        #endregion
    }
} 