<template>
  <div class="wis-pdf-viewer">
    <div class="page-header">
      <h2 class="page-title">WIS PDF处理器</h2>
      <div class="page-actions">
        <el-upload
          :key="uploadKey"
          class="pdf-upload"
          action="#"
          :auto-upload="false"
          :show-file-list="false"
          accept=".pdf"
          multiple
          :on-change="handlePDFChange"
          :disabled="uploading"
        >
          <el-button type="primary" :loading="uploading" :icon="Upload" size="large">
            {{ uploading ? '处理中...' : '选择PDF文件（可多选）' }}
          </el-button>
        </el-upload>
        <el-button 
          v-if="processedPDFs.length > 0" 
          type="success" 
          @click="uploadImagesToServer" 
          :icon="Upload" 
          size="large"
          :loading="uploadingImages"
        >
          {{ uploadingImages ? '上传中...' : '上传图片到数据库' }}
        </el-button>
        <!-- 新增：上传到云服务器文件夹 -->
        <el-button 
          v-if="processedPDFs.length > 0" 
          type="warning" 
          @click="uploadImagesToCloudFolder" 
          :icon="FolderAdd" 
          size="large"
          :loading="uploadingToCloud"
        >
          {{ uploadingToCloud ? '保存中...' : '保存到云服务器文件夹' }}
        </el-button>
        <el-button v-if="processedPDFs.length > 0" type="danger" @click="clearPDF" :icon="Delete" size="large">
          清除PDF
        </el-button>
      </div>
    </div>

    <div class="pdf-content">
      <div class="bookmark-panel">
        <div class="panel-header">
          <div class="panel-title-section">
            <h3>PDF书签</h3>
            <div class="pdf-info" v-if="processedPDFs.length > 0">
              <el-select 
                v-model="currentPDFIndex" 
                @change="switchToPDF"
                size="small"
                style="width: 200px; margin-bottom: 4px;"
                placeholder="选择PDF文件"
              >
                <el-option
                  v-for="(pdf, index) in processedPDFs"
                  :key="index"
                  :label="pdf.originalName"
                  :value="index"
                />
              </el-select>
              <el-text type="info" size="small" v-if="bookmarks.length">
                共 {{ bookmarks.length }} 页 | 已处理 {{ processedPDFs.length }} 个PDF
              </el-text>
              <el-text type="info" size="small" v-else>
                已处理 {{ processedPDFs.length }} 个PDF
              </el-text>
            </div>
            <el-text type="info" size="small" v-else>暂无PDF文件</el-text>
          </div>
          <el-button 
            v-if="processedPDFs.length > 0" 
            type="primary" 
            size="small" 
            @click="saveBookmarksToDatabase"
            :loading="savingBookmarks"
            :icon="Upload"
          >
            {{ savingBookmarks ? '保存中...' : '保存' }}
          </el-button>
        </div>
        <el-scrollbar class="bookmark-list">
          <div v-if="bookmarks.length">
            <div
              v-for="bookmark in bookmarks"
              :key="bookmark.id"
              :class="['bookmark-item', { active: activeBookmark === bookmark.id }]"
              @click="handleBookmarkSelect(bookmark.id)"
            >
              <el-icon><Document /></el-icon>
              <span class="bookmark-title">{{ bookmark.title }}</span>
              <el-tag size="small" type="info">第{{ bookmark.pageNumber }}页</el-tag>
            </div>
          </div>
          <div v-else class="empty-bookmarks">
            <el-empty description="请先上传PDF文件" :image-size="80" />
          </div>
        </el-scrollbar>
      </div>

      <div class="image-panel">
        <div class="panel-header">
          <h3>页面预览</h3>
          <div class="image-actions" v-if="currentImage">
            <el-button-group>
              <el-button size="small" @click="zoomOut" :disabled="zoomLevel <= 0.5">
                <el-icon><ZoomOut /></el-icon>
              </el-button>
              <el-button size="small" @click="resetZoom">
                {{ Math.round(zoomLevel * 100) }}%
              </el-button>
              <el-button size="small" @click="zoomIn" :disabled="zoomLevel >= 3">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
            </el-button-group>
            <el-button size="small" @click="downloadCurrentImage" :icon="Download">
              下载图片
            </el-button>
          </div>
        </div>
        <el-scrollbar class="image-display">
          <div v-if="imageLoading" class="loading-container">
            <el-loading-spinner />
            <p>加载图片中...</p>
          </div>
          <el-image
            v-else-if="currentImage"
            :src="currentImage"
            fit="contain"
            :style="{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }"
            @load="handleImageLoad"
            @error="handleImageError"
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
                <p>图片加载失败</p>
              </div>
            </template>
          </el-image>
          <div v-else class="no-image">
            <el-icon><Picture /></el-icon>
            <p v-if="bookmarks.length">请选择左侧书签查看对应页面</p>
            <p v-else>请先上传PDF文件查看内容</p>
          </div>
        </el-scrollbar>
      </div>
    </div>

    <!-- 处理进度对话框 -->
    <el-dialog
      v-model="showProgress"
      title="PDF处理进度"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="progress-content">
        <el-progress
          :percentage="progressPercentage"
          :status="progressStatus"
          :stroke-width="8"
        />
        <p class="progress-text">{{ progressText }}</p>
        <div class="progress-details">
          <el-text size="small" type="info">
            {{ progressDetails }}
          </el-text>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Upload,
  Delete,
  Document,
  ZoomOut,
  ZoomIn,
  Download,
  Picture,
  FolderAdd
} from '@element-plus/icons-vue'
import { 
  uploadPDFImage, 
  savePDFBookmarks, 
  batchUploadPDFImagesWithConcurrency,
  batchUploadPDFImagesToCloud
} from '@/api/wis'

// 响应式数据
const uploading = ref(false)
const uploadingImages = ref(false)
const uploadingToCloud = ref(false)
const savingBookmarks = ref(false)
const bookmarks = ref([])
const activeBookmark = ref('')
const currentImage = ref('')
const imageLoading = ref(false)
const zoomLevel = ref(1)
const showProgress = ref(false)
const progressPercentage = ref(0)
const progressStatus = ref('')
const progressText = ref('')
const progressDetails = ref('')

// 多PDF文件管理相关变量
const processedPDFs = ref([])  // 存储所有已处理的PDF信息
const currentPDFIndex = ref(-1)  // 当前显示的PDF索引
const uploadKey = ref(0)  // 用于重置上传组件

// PDF.js相关变量
let pdfDoc = null
let currentPDFName = ''

// 动态导入PDF.js
const loadPDFJS = async () => {
  try {
    // 使用CDN方式加载PDF.js
    if (!window.pdfjsLib) {
      const script = document.createElement('script')
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js'
      document.head.appendChild(script)
      
      await new Promise((resolve, reject) => {
        script.onload = resolve
        script.onerror = reject
      })
      
      // 设置worker
      window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
    }
    return window.pdfjsLib
  } catch (error) {
    console.error('加载PDF.js失败:', error)
    throw new Error('PDF.js加载失败，请检查网络连接')
  }
}

// 辅助函数：构建outline到页面的映射关系
const buildOutlinePageMapping = async (pdfDocument, outlineItems) => {
  const mapping = new Map() // pageNumber -> outlineItem
  
  const processOutlineItems = async (items) => {
    for (const item of items) {
      try {
        if (item.dest) {
          let pageNumber = null
          
          // 处理不同类型的destination
          if (typeof item.dest === 'string') {
            // 如果dest是字符串，需要通过getDestination解析
            const dest = await pdfDocument.getDestination(item.dest)
            if (dest && dest[0]) {
              const pageRef = dest[0]
              pageNumber = await pdfDocument.getPageIndex(pageRef) + 1
            }
          } else if (Array.isArray(item.dest) && item.dest[0]) {
            // 如果dest已经是数组格式
            const pageRef = item.dest[0]
            pageNumber = await pdfDocument.getPageIndex(pageRef) + 1
          }
          
                     if (pageNumber && pageNumber > 0) {
             // 如果该页面还没有映射，或者当前项的标题更具体，则更新映射
             if (!mapping.has(pageNumber) || item.title.length > mapping.get(pageNumber).title.length) {
               mapping.set(pageNumber, item)
               console.log(`    映射: 第${pageNumber}页 -> "${item.title}"`)
             }
           } else {
             console.log(`    无法解析页面号: dest=${JSON.stringify(item.dest)}, title="${item.title}"`)
           }
        }
        
        // 递归处理子项
        if (item.items && item.items.length > 0) {
          await processOutlineItems(item.items)
        }
      } catch (error) {
        console.warn(`处理outline项失败: "${item.title}"`, error)
      }
    }
  }
  
  await processOutlineItems(outlineItems)
  return mapping
}

// 辅助函数：查找指定页面对应的outline项
const findOutlineItemForPage = async (pdfDocument, outlineItems, targetPageNumber, pageMapping) => {
  // 如果有预构建的映射，直接使用
  if (pageMapping && pageMapping.has(targetPageNumber)) {
    return pageMapping.get(targetPageNumber)
  }
  
  // 备用方案：遍历查找
  for (const item of outlineItems) {
    try {
      if (item.dest) {
        let pageNumber = null
        
        if (typeof item.dest === 'string') {
          const dest = await pdfDocument.getDestination(item.dest)
          if (dest && dest[0]) {
            const pageRef = dest[0]
            pageNumber = await pdfDocument.getPageIndex(pageRef) + 1
          }
        } else if (Array.isArray(item.dest) && item.dest[0]) {
          const pageRef = item.dest[0]
          pageNumber = await pdfDocument.getPageIndex(pageRef) + 1
        }
        
        if (pageNumber === targetPageNumber) {
          return item
        }
      }
      
      // 递归查找子项
      if (item.items && item.items.length > 0) {
        const found = await findOutlineItemForPage(pdfDocument, item.items, targetPageNumber, pageMapping)
        if (found) return found
      }
    } catch (error) {
      // 忽略单个项的错误，继续处理其他项
    }
  }
  return null
}

// 用于防止重复处理的变量
let processingTimeout = null

// 处理文件选择事件
const handlePDFChange = (file, fileList) => {
  // 清除之前的超时
  if (processingTimeout) {
    clearTimeout(processingTimeout)
  }
  
  console.log('文件选择事件:', {
    currentFile: file.name,
    totalFiles: fileList.length,
    fileList: fileList.map(f => f.name)
  })
  
  // 延迟处理，避免多次快速选择文件时重复处理
  processingTimeout = setTimeout(() => {
    handlePDFUpload(fileList)
  }, 300) // 300ms 防抖
}

// 处理PDF上传（支持多文件）
const handlePDFUpload = async (fileList) => {
  // 防止重复处理
  if (uploading.value) {
    console.log('正在处理中，跳过重复请求')
    return
  }
  
  if (!fileList || fileList.length === 0) {
    ElMessage.error('请选择有效的PDF文件')
    return
  }
  
  console.log('开始处理PDF文件:', fileList.map(f => f.name))
  
  // 验证文件
  const validFiles = []
  for (const fileItem of fileList) {
    const targetFile = fileItem.raw || fileItem
    
    if (!targetFile) {
      continue
    }

    // 检查文件类型
    if (targetFile.type !== 'application/pdf') {
      ElMessage.warning(`文件 ${targetFile.name} 不是PDF格式，已跳过`)
      continue
    }

    // 检查文件大小（限制50MB）
    const maxSize = 50 * 1024 * 1024
    if (targetFile.size > maxSize) {
      ElMessage.warning(`文件 ${targetFile.name} 大小超过50MB，已跳过`)
      continue
    }

    // 检查是否已经处理过
    const fileName = targetFile.name.replace('.pdf', '')
    const existingPDF = processedPDFs.value.find(pdf => pdf.name === fileName)
    if (existingPDF) {
      console.log(`文件 ${targetFile.name} 已经处理过，跳过处理`)
      ElMessage.warning(`文件 ${targetFile.name} 已经处理过，已跳过`)
      continue
    }

    // 检查当前批次中是否有重复文件
    const duplicateInBatch = validFiles.find(f => {
      const existingFileName = f.name.replace('.pdf', '')
      return existingFileName === fileName
    })
    
    if (duplicateInBatch) {
      console.log(`文件 ${targetFile.name} 在当前批次中重复，跳过处理`)
      ElMessage.warning(`文件 ${targetFile.name} 重复选择，已跳过`)
      continue
    }

    console.log(`添加文件到处理队列: ${targetFile.name}`)
    validFiles.push(targetFile)
  }

  console.log(`验证后的有效文件数量: ${validFiles.length}`)
  console.log('有效文件列表:', validFiles.map(f => f.name))
  
  if (validFiles.length === 0) {
    ElMessage.error('没有有效的PDF文件可以处理')
    return
  }

  try {
    uploading.value = true
    showProgress.value = true
    progressPercentage.value = 0
    progressStatus.value = ''
    progressText.value = '正在加载PDF.js...'
    progressDetails.value = '初始化PDF处理环境'

    // 加载PDF.js
    const pdfjsLib = await loadPDFJS()
    
    let totalProgress = 0
    const progressPerFile = 100 / validFiles.length

    // 处理每个PDF文件
    for (let i = 0; i < validFiles.length; i++) {
      const targetFile = validFiles[i]
      const fileName = targetFile.name.replace('.pdf', '')
      
      progressText.value = `正在处理文件 ${i + 1}/${validFiles.length}: ${targetFile.name}`
      progressDetails.value = `文件大小: ${(targetFile.size / 1024 / 1024).toFixed(2)}MB`
      
      try {
        // 读取文件
        const arrayBuffer = await targetFile.arrayBuffer()
        
        // 解析PDF
        const pdfDocument = await pdfjsLib.getDocument(arrayBuffer).promise
        
        progressDetails.value = `PDF共有 ${pdfDocument.numPages} 页`

        // 生成书签
        const outline = await pdfDocument.getOutline()
        const generatedBookmarks = []
        
        console.log(`${fileName} - PDF Outline信息:`, outline ? `共${outline.length}个outline项` : '无outline')
        
        // 过滤书签名中的空格和冒号
        const filterBookmarkTitle = (title) => {
          return title.replace(/[\s:：]/g, '')
        }
        
        let pageMapping = null
        
        // 如果有outline，构建页面映射关系
        if (outline && outline.length > 0) {
          console.log(`  开始构建outline映射关系...`)
          outline.forEach((item, index) => {
            console.log(`  Outline ${index + 1}: "${item.title}"`)
          })
          
          try {
            pageMapping = await buildOutlinePageMapping(pdfDocument, outline)
            console.log(`  成功构建映射关系，共${pageMapping.size}个页面有对应的书签`)
          } catch (error) {
            console.warn(`  构建映射关系失败:`, error)
          }
        }
        
        // 修复：始终按照PDF实际页数生成书签，确保1:1对应
        for (let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++) {
          let originalBookmarkTitle = `第${pageNum}页` // 默认标题（原始）
          
          // 如果有outline，尝试找到对应页面的书签标题
          if (outline && outline.length > 0) {
            try {
              const matchingOutlineItem = await findOutlineItemForPage(pdfDocument, outline, pageNum, pageMapping)
              if (matchingOutlineItem) {
                originalBookmarkTitle = matchingOutlineItem.title || originalBookmarkTitle
                console.log(`  第${pageNum}页找到匹配的outline: "${matchingOutlineItem.title}"`)
              } else {
                console.log(`  第${pageNum}页未找到匹配的outline，使用默认标题`)
              }
            } catch (error) {
              console.warn(`查找第${pageNum}页书签失败:`, error)
            }
          }
          
          // 过滤书签名称（移除空格和冒号）
          const filteredBookmarkTitle = filterBookmarkTitle(originalBookmarkTitle)
          
          console.log(`  生成书签 - 第${pageNum}页: 原始="${originalBookmarkTitle}" -> 过滤后="${filteredBookmarkTitle}" -> 显示="${fileName}_${filteredBookmarkTitle}"`)
          
          generatedBookmarks.push({
            id: `${fileName}_${pageNum}`,
            title: `${fileName}_${filteredBookmarkTitle}`, // 显示格式：PDF文件名_过滤后的书签名
            originalBookmarkTitle: originalBookmarkTitle,    // 保存原始书签名
            filteredBookmarkTitle: filteredBookmarkTitle,    // 保存过滤后的书签名
            pageNumber: pageNum,
            pdfName: fileName
          })
        }
        
        console.log(`${fileName}: PDF页数=${pdfDocument.numPages}, 生成书签数=${generatedBookmarks.length}`)
        
        // 转换PDF页面为图片
        progressDetails.value = `正在转换 ${fileName} 的页面为图片...`
        const images = await convertSinglePDFToImages(pdfDocument, fileName)
        
        // 保存PDF信息
        const pdfInfo = {
          name: fileName,
          originalName: targetFile.name,
          numPages: pdfDocument.numPages,
          bookmarks: generatedBookmarks,
          images: images,
          fileSize: targetFile.size
        }
        
        processedPDFs.value.push(pdfInfo)
        console.log(`成功处理文件: ${fileName}, 当前已处理文件总数: ${processedPDFs.value.length}`)
        
        // 清理当前PDF文档
        pdfDocument.destroy()
        
        totalProgress += progressPerFile
        progressPercentage.value = Math.round(totalProgress)
        
      } catch (error) {
        console.error(`处理文件 ${targetFile.name} 失败:`, error)
        ElMessage.error(`处理文件 ${targetFile.name} 失败: ${error.message}`)
        totalProgress += progressPerFile
        progressPercentage.value = Math.round(totalProgress)
      }
    }
    
    progressPercentage.value = 100
    progressStatus.value = 'success'
    progressText.value = 'PDF文件处理完成！'
    progressDetails.value = `成功处理 ${processedPDFs.value.length} 个PDF文件`
    
    const totalPages = processedPDFs.value.reduce((sum, pdf) => sum + pdf.numPages, 0)
    const totalBookmarks = processedPDFs.value.reduce((sum, pdf) => sum + pdf.bookmarks.length, 0)
    
    console.log('=== PDF处理完成总结 ===')
    console.log(`输入文件数: ${fileList.length}`)
    console.log(`有效文件数: ${validFiles.length}`)
    console.log(`成功处理数: ${processedPDFs.value.length}`)
    console.log(`总页数: ${totalPages}`)
    console.log(`总书签数: ${totalBookmarks}`)
    console.log('已处理的PDF文件:', processedPDFs.value.map(pdf => `${pdf.originalName}(${pdf.numPages}页/${pdf.bookmarks.length}书签)`))
    console.log('========================')
    
    // 更新当前显示的内容
    if (processedPDFs.value.length > 0) {
      // 显示最新处理的PDF
      const latestPDFIndex = processedPDFs.value.length - 1
      switchToPDF(latestPDFIndex)
    }
    
    // 延迟关闭进度对话框
    setTimeout(() => {
      showProgress.value = false
      const successCount = processedPDFs.value.length
      ElMessage.success(`成功处理 ${successCount} 个PDF文件`)
    }, 1500)

  } catch (error) {
    console.error('PDF批量处理失败:', error)
    progressStatus.value = 'exception'
    progressText.value = 'PDF处理失败'
    progressDetails.value = error.message || '未知错误'
    
    setTimeout(() => {
      showProgress.value = false
      ElMessage.error('PDF处理失败: ' + (error.message || '未知错误'))
    }, 2000)
  } finally {
    uploading.value = false
  }
}

// 转换单个PDF页面为图片
const convertSinglePDFToImages = async (pdfDocument, fileName) => {
  const totalPages = pdfDocument.numPages
  const images = {}
  
  for (let i = 1; i <= totalPages; i++) {
    try {
      const page = await pdfDocument.getPage(i)
      const viewport = page.getViewport({ scale: 2.0 }) // 提高分辨率
      
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      canvas.height = viewport.height
      canvas.width = viewport.width
      
      await page.render({
        canvasContext: context,
        viewport: viewport
      }).promise
      
      // 转换为base64图片
      const imageDataUrl = canvas.toDataURL('image/jpeg', 0.9)
      images[i] = imageDataUrl
      
    } catch (error) {
      console.error(`处理第${i}页失败:`, error)
      throw new Error(`处理第${i}页失败: ${error.message}`)
    }
  }
  
  return images
}

// 切换到指定的PDF
const switchToPDF = (pdfIndex) => {
  if (pdfIndex < 0 || pdfIndex >= processedPDFs.value.length) {
    return
  }
  
  currentPDFIndex.value = pdfIndex
  const pdfInfo = processedPDFs.value[pdfIndex]
  
  // 更新当前PDF相关变量
  currentPDFName = pdfInfo.name
  bookmarks.value = pdfInfo.bookmarks
  
  console.log(`切换到PDF: ${pdfInfo.name}`)
  console.log('当前书签列表:')
  pdfInfo.bookmarks.forEach((bookmark, index) => {
    console.log(`  ${index + 1}. 显示标题: "${bookmark.title}"`)
    console.log(`      原始书签: "${bookmark.originalBookmarkTitle}"`)
    console.log(`      过滤书签: "${bookmark.filteredBookmarkTitle}"`)
    console.log(`      页码: ${bookmark.pageNumber}`)
  })
  
  // 清除当前图片显示
  currentImage.value = ''
  activeBookmark.value = ''
  
  // 自动选择第一页
  if (bookmarks.value.length > 0) {
    handleBookmarkSelect(bookmarks.value[0].id)
  }
}

// 处理书签选择
const handleBookmarkSelect = async (bookmarkId) => {
  activeBookmark.value = bookmarkId
  const bookmark = bookmarks.value.find(b => b.id === bookmarkId)
  
  if (bookmark) {
    try {
      imageLoading.value = true
      
      // 从当前PDF信息中获取图片
      if (currentPDFIndex.value >= 0 && currentPDFIndex.value < processedPDFs.value.length) {
        const pdfInfo = processedPDFs.value[currentPDFIndex.value]
        if (pdfInfo.images && pdfInfo.images[bookmark.pageNumber]) {
          currentImage.value = pdfInfo.images[bookmark.pageNumber]
        } else {
          throw new Error('图片数据不存在')
        }
      } else {
        throw new Error('PDF信息不存在')
      }
      
    } catch (error) {
      console.error('获取图片失败:', error)
      ElMessage.error('获取图片失败')
      currentImage.value = ''
    } finally {
      imageLoading.value = false
    }
  }
}

// 清除PDF
const clearPDF = async () => {
  if (processedPDFs.value.length === 0) {
    ElMessage.warning('没有PDF文件需要清除')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要清除所有PDF文件吗？这将删除${processedPDFs.value.length}个已处理的PDF文件数据。`,
      '确认清除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // 清除超时
    if (processingTimeout) {
      clearTimeout(processingTimeout)
      processingTimeout = null
    }
    
    // 清除所有数据
    processedPDFs.value = []
    currentPDFIndex.value = -1
    bookmarks.value = []
    activeBookmark.value = ''
    currentImage.value = ''
    pdfDoc = null
    currentPDFName = ''
    uploadKey.value++ // 重置上传组件
    
    ElMessage.success('所有PDF文件已清除')
    
  } catch (error) {
    // 用户取消操作
  }
}

// 缩放控制
const zoomIn = () => {
  if (zoomLevel.value < 3) {
    zoomLevel.value = Math.min(3, zoomLevel.value + 0.25)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.25)
  }
}

const resetZoom = () => {
  zoomLevel.value = 1
}

// 下载当前图片
const downloadCurrentImage = () => {
  if (!currentImage.value) {
    ElMessage.warning('没有可下载的图片')
    return
  }
  
  const bookmark = bookmarks.value.find(b => b.id === activeBookmark.value)
  if (bookmark) {
    const link = document.createElement('a')
    link.href = currentImage.value
    link.download = `${currentPDFName}_第${bookmark.pageNumber}页.jpg`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('图片下载成功')
  }
}

// 上传图片到云服务器
const uploadImagesToServer = async () => {
  if (processedPDFs.value.length === 0) {
    ElMessage.warning('没有可上传的图片')
    return
  }

  // 计算总图片数量（使用实际的书签数量，确保准确性）
  const totalImages = processedPDFs.value.reduce((sum, pdf) => sum + pdf.bookmarks.length, 0)

  try {
    await ElMessageBox.confirm(
      `确定要将所有PDF文件的${totalImages}张图片上传到云服务器吗？`,
      '确认上传',
      {
        confirmButtonText: '确定上传',
        cancelButtonText: '取消',
        type: 'info',
      }
    )

    uploadingImages.value = true
    showProgress.value = true
    progressPercentage.value = 0
    progressStatus.value = ''
    progressText.value = '正在准备上传图片到云服务器...'
    progressDetails.value = '正在构建上传队列'

    // 构建上传队列
    const uploadQueue = []
    for (const pdfInfo of processedPDFs.value) {
      console.log(`构建上传队列 - ${pdfInfo.name}，页数: ${pdfInfo.numPages}，书签数: ${pdfInfo.bookmarks.length}，图片数: ${Object.keys(pdfInfo.images).length}`)
      
      // 遍历该PDF的所有页面
      for (const [pageNumber, imageDataUrl] of Object.entries(pdfInfo.images)) {
        // 获取对应的书签信息
        const bookmark = pdfInfo.bookmarks.find(b => b.pageNumber === parseInt(pageNumber))
        let bookmarkName = ''
        
        if (bookmark) {
          // 直接使用过滤后的书签名称
          bookmarkName = bookmark.filteredBookmarkTitle || `第${pageNumber}页`
        } else {
          // 如果没有找到书签，使用默认名称
          bookmarkName = `第${pageNumber}页`
        }
        
        // 获取图片尺寸
        const img = new Image()
        img.src = imageDataUrl
        await new Promise((resolve) => {
          img.onload = resolve
        })
        
        // 准备API请求数据，按照后端期望的格式
        const uploadData = {
          fileName: pdfInfo.name,                    // PDF文件名（不含扩展名）
          bookmarkName: bookmarkName,                // 过滤后的书签名称
          pageNumber: parseInt(pageNumber),          // 页码
          imageBase64: imageDataUrl,                 // 图片Base64数据（包含data:image前缀）
          imageWidth: img.width,                     // 图片宽度
          imageHeight: img.height,                   // 图片高度
          remarks: `PDF页面${pageNumber}图片上传`      // 备注
        }
        
        uploadQueue.push(uploadData)
      }
    }

    console.log(`上传队列构建完成，共${uploadQueue.length}个文件`)
    progressDetails.value = `上传队列构建完成，共${uploadQueue.length}个文件`

    // 使用批量上传（控制并发数为2，避免服务器压力过大）
    const uploadResult = await batchUploadPDFImagesWithConcurrency(
      uploadQueue, 
      2, // 并发数量设为2
      (progress) => {
        // 更新进度显示
        progressPercentage.value = progress.percentage
        progressText.value = `正在上传图片 (${progress.completed + progress.failed}/${progress.total})`
        
        if (progress.current) {
          progressDetails.value = `当前: ${progress.current.fileName} 第${progress.current.pageNumber}页`
          
          if (progress.error) {
            console.warn(`上传失败: ${progress.current.fileName} 第${progress.current.pageNumber}页 - ${progress.error}`)
          } else if (progress.completed > 0) {
            console.log(`上传成功: ${progress.current.fileName} 第${progress.current.pageNumber}页`)
          }
        }
        
        // 如果有失败的，显示警告信息
        if (progress.failed > 0) {
          progressDetails.value += ` (成功:${progress.completed}, 失败:${progress.failed})`
        }
      }
    )

    // 处理上传结果
    const { total, completed, failed } = uploadResult
    
    if (failed === 0) {
      progressStatus.value = 'success'
      progressText.value = '图片上传完成！'
      progressDetails.value = `成功上传 ${completed} 张图片到云服务器`

      setTimeout(() => {
        showProgress.value = false
        ElMessage.success(`成功上传${completed}张图片到云服务器`)
      }, 1500)
    } else {
      progressStatus.value = 'warning'
      progressText.value = '图片上传完成（部分失败）'
      progressDetails.value = `成功上传 ${completed} 张，失败 ${failed} 张`

      setTimeout(() => {
        showProgress.value = false
        ElMessage.warning(`上传完成：成功${completed}张，失败${failed}张。请查看控制台了解失败详情。`)
      }, 2000)
    }

    console.log('=== 上传完成总结 ===')
    console.log(`总数: ${total}`)
    console.log(`成功: ${completed}`)
    console.log(`失败: ${failed}`)
    console.log('失败的文件:', uploadResult.results.filter(r => !r.success).map(r => `${r.imageData.fileName}_第${r.imageData.pageNumber}页`))
    console.log('========================')

  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作
      return
    }
    
    console.error('上传图片失败:', error)
    progressStatus.value = 'exception'
    progressText.value = '图片上传失败'
    progressDetails.value = error.message || '未知错误'
    
    setTimeout(() => {
      showProgress.value = false
      ElMessage.error('图片上传失败: ' + (error.message || '未知错误'))
    }, 2000)
  } finally {
    uploadingImages.value = false
  }
}

// 图片加载事件
const handleImageLoad = () => {
  imageLoading.value = false
}

const handleImageError = () => {
  imageLoading.value = false
  ElMessage.error('图片加载失败')
}

// 上传图片到云服务器文件夹
const uploadImagesToCloudFolder = async () => {
  if (processedPDFs.value.length === 0) {
    ElMessage.warning('没有可上传的图片')
    return
  }

  // 计算总图片数量
  const totalImages = processedPDFs.value.reduce((sum, pdf) => sum + pdf.bookmarks.length, 0)

  try {
    const { action, value } = await ElMessageBox.prompt(
      `确定要将所有PDF文件的${totalImages}张图片保存到云服务器文件夹吗？
      
图片将以书签名命名并保存到对应PDF的文件夹中。
      
是否同时保存Base64数据到数据库？`,
      '确认保存到云服务器',
      {
        confirmButtonText: '确定保存',
        cancelButtonText: '取消',
        inputType: 'select',
        inputValue: 'false',
        inputOptions: [
          { value: 'false', label: '只保存文件到服务器' },
          { value: 'true', label: '同时保存Base64到数据库' }
        ],
        inputPlaceholder: '选择保存选项'
      }
    )

    const saveBase64 = value === 'true'

    uploadingToCloud.value = true
    showProgress.value = true
    progressPercentage.value = 0
    progressStatus.value = ''
    progressText.value = '正在准备保存图片到云服务器文件夹...'
    progressDetails.value = '正在构建上传队列'

    // 构建上传队列
    const uploadQueue = []
    for (const pdfInfo of processedPDFs.value) {
      console.log(`构建云服务器上传队列 - ${pdfInfo.name}，页数: ${pdfInfo.numPages}，书签数: ${pdfInfo.bookmarks.length}`)
      
      // 遍历该PDF的所有页面
      for (const [pageNumber, imageDataUrl] of Object.entries(pdfInfo.images)) {
        // 获取对应的书签信息
        const bookmark = pdfInfo.bookmarks.find(b => b.pageNumber === parseInt(pageNumber))
        let bookmarkName = ''
        
        if (bookmark) {
          // 直接使用过滤后的书签名称
          bookmarkName = bookmark.filteredBookmarkTitle || `第${pageNumber}页`
        } else {
          // 如果没有找到书签，使用默认名称
          bookmarkName = `第${pageNumber}页`
        }
        
        // 获取图片尺寸
        const img = new Image()
        img.src = imageDataUrl
        await new Promise((resolve) => {
          img.onload = resolve
        })
        
        // 准备API请求数据
        const uploadData = {
          fileName: pdfInfo.name,                    // PDF文件名（不含扩展名）
          bookmarkName: bookmarkName,                // 过滤后的书签名称
          pageNumber: parseInt(pageNumber),          // 页码
          imageBase64: imageDataUrl,                 // 图片Base64数据
          imageWidth: img.width,                     // 图片宽度
          imageHeight: img.height,                   // 图片高度
          remarks: `PDF页面${pageNumber}图片保存到云服务器文件夹`,
          saveBase64: saveBase64                     // 是否同时保存Base64
        }
        
        uploadQueue.push(uploadData)
      }
    }

    console.log(`云服务器上传队列构建完成，共${uploadQueue.length}个文件`)
    progressDetails.value = `上传队列构建完成，共${uploadQueue.length}个文件`

    // 使用批量上传到云服务器（并发数设为2）
    const uploadResult = await batchUploadPDFImagesToCloud(
      uploadQueue, 
      2, // 并发数量
      (progress) => {
        // 更新进度显示
        progressPercentage.value = progress.percentage
        progressText.value = `正在保存图片到云服务器 (${progress.completed + progress.failed}/${progress.total})`
        
        if (progress.current) {
          progressDetails.value = `当前: ${progress.current.fileName} - ${progress.current.bookmarkName}`
          
          if (progress.error) {
            console.warn(`保存失败: ${progress.current.fileName} 第${progress.current.pageNumber}页 - ${progress.error}`)
          } else if (progress.completed > 0) {
            console.log(`保存成功: ${progress.current.fileName} 第${progress.current.pageNumber}页`)
          }
        }
        
        // 如果有失败的，显示警告信息
        if (progress.failed > 0) {
          progressDetails.value += ` (成功:${progress.completed}, 失败:${progress.failed})`
        }
      },
      saveBase64 // 传递是否保存Base64的选项
    )

    // 处理上传结果
    const { total, completed, failed } = uploadResult
    
    if (failed === 0) {
      progressStatus.value = 'success'
      progressText.value = '图片保存到云服务器完成！'
      progressDetails.value = `成功保存 ${completed} 张图片到云服务器文件夹`

      setTimeout(() => {
        showProgress.value = false
        ElMessage.success(`成功保存${completed}张图片到云服务器文件夹，文件已按书签名命名`)
      }, 1500)
    } else {
      progressStatus.value = 'warning'
      progressText.value = '图片保存完成（部分失败）'
      progressDetails.value = `成功保存 ${completed} 张，失败 ${failed} 张`

      setTimeout(() => {
        showProgress.value = false
        ElMessage.warning(`保存完成：成功${completed}张，失败${failed}张。请查看控制台了解失败详情。`)
      }, 2000)
    }

    console.log('=== 云服务器保存完成总结 ===')
    console.log(`总数: ${total}`)
    console.log(`成功: ${completed}`)
    console.log(`失败: ${failed}`)
    console.log('保存位置: uploads/wis-pdf-images/[PDF文件名]/[书签名]_第[页码]页.jpg')
    console.log('========================')

  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作
      return
    }
    
    console.error('保存图片到云服务器失败:', error)
    progressStatus.value = 'exception'
    progressText.value = '图片保存失败'
    progressDetails.value = error.message || '未知错误'
    
    setTimeout(() => {
      showProgress.value = false
      ElMessage.error('图片保存失败: ' + (error.message || '未知错误'))
    }, 2000)
  } finally {
    uploadingToCloud.value = false
  }
}

// 保存书签到数据库
const saveBookmarksToDatabase = async () => {
  if (processedPDFs.value.length === 0) {
    ElMessage.warning('没有可保存的书签数据')
    return
  }

  try {
    savingBookmarks.value = true
    
    // 过滤书签名称，移除空格和冒号
    const filterBookmarkName = (bookmarkName) => {
      if (!bookmarkName) return ''
      return bookmarkName.replace(/[\s:：]/g, '')
    }
    
    let successCount = 0
    let totalBookmarks = 0
    
    // 为每个PDF文件单独调用后端API
    for (const pdfInfo of processedPDFs.value) {
      try {
        // 准备当前PDF的书签数据，按照后端期望的格式
        const bookmarkData = {
          fileName: pdfInfo.name, // PDF文件名（不含扩展名）
          bookmarks: pdfInfo.bookmarks.map(bookmark => {
            return {
              originalBookmarkName: bookmark.originalBookmarkTitle || `第${bookmark.pageNumber}页`,
              filteredBookmarkName: bookmark.filteredBookmarkTitle || filterBookmarkName(bookmark.originalBookmarkTitle || `第${bookmark.pageNumber}页`),
              pageNumber: bookmark.pageNumber,
              remarks: `页面 ${bookmark.pageNumber}` // 添加简单的备注
            }
          })
        }
        
        console.log('准备保存PDF书签:', pdfInfo.name, bookmarkData)
        
        // 调用后端API保存书签
        const result = await savePDFBookmarks(bookmarkData)
        
        if (result && result.data && result.data.code === 200) {
          successCount++
          totalBookmarks += bookmarkData.bookmarks.length
          console.log(`PDF ${pdfInfo.name} 书签保存成功:`, result.data.message)
        } else {
          console.error(`PDF ${pdfInfo.name} 书签保存失败:`, result)
          ElMessage.warning(`PDF ${pdfInfo.name} 书签保存失败: ${result?.data?.message || '未知错误'}`)
        }
        
      } catch (error) {
        console.error(`保存PDF ${pdfInfo.name} 书签时出错:`, error)
        ElMessage.warning(`保存PDF ${pdfInfo.name} 书签失败: ${error.message || '网络错误'}`)
      }
    }
    
    // 显示最终结果
    if (successCount > 0) {
      ElMessage.success(`书签保存完成！成功保存 ${successCount} 个PDF文件的 ${totalBookmarks} 个书签`)
    } else {
      ElMessage.error('所有PDF书签保存失败，请检查网络连接和服务器状态')
    }
    
  } catch (error) {
    console.error('保存书签过程出错:', error)
    ElMessage.error('保存书签失败: ' + (error.message || '未知错误'))
  } finally {
    savingBookmarks.value = false
  }
}

// 组件卸载时清理
onUnmounted(() => {
  // 清理超时
  if (processingTimeout) {
    clearTimeout(processingTimeout)
  }
  
  // 清理可能的内存占用
  if (pdfDoc) {
    pdfDoc.destroy()
  }
  
  // 清理所有处理过的PDF数据
  processedPDFs.value = []
  currentPDFIndex.value = -1
  bookmarks.value = []
  activeBookmark.value = ''
  currentImage.value = ''
})
</script>

<style scoped>
.wis-pdf-viewer {
  height: calc(100vh - 120px);
  min-height: 600px;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  height: 80px;
  min-height: 80px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.page-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.page-actions .el-button {
  font-size: 16px;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
}

.pdf-content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  min-height: 0;
  height: calc(100% - 80px);
}

.bookmark-panel {
  width: 300px;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.image-panel {
  flex: 1;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.pdf-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.image-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.bookmark-list {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
  max-height: calc(100% - 60px);
}

.bookmark-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  margin-bottom: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.bookmark-item:hover {
  background-color: #f0f9ff;
  border-color: #409eff;
}

.bookmark-item.active {
  background-color: #409eff;
  color: #fff;
}

.bookmark-item.active .el-tag {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border-color: rgba(255, 255, 255, 0.3);
}

.bookmark-title {
  flex: 1;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-display {
  flex: 1;
  padding: 16px;
  overflow: auto;
  max-height: calc(100% - 60px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.loading-container p {
  margin-top: 16px;
  font-size: 14px;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;
}

.no-image .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.no-image p {
  font-size: 16px;
  margin: 0;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #f56c6c;
}

.image-error .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-bookmarks {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-state {
  flex: 1;
  height: calc(100% - 80px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
}

.empty-state .el-empty {
  padding: 40px;
}

.empty-state .el-empty__description {
  font-size: 18px;
  color: #909399;
  margin-top: 20px;
}

.progress-content {
  text-align: center;
}

.progress-text {
  margin: 16px 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.progress-details {
  margin-top: 8px;
  min-height: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pdf-content {
    flex-direction: column;
    gap: 12px;
  }
  
  .bookmark-panel {
    width: 100%;
    max-height: 200px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .page-actions {
    justify-content: center;
  }
}
</style>