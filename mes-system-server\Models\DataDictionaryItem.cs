using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace mes_system_server.Models
{
    /// <summary>
    /// 数据字典项表
    /// </summary>
    public class DataDictionaryItem
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 字典ID
        /// </summary>
        [Required]
        public int DictId { get; set; }

        /// <summary>
        /// 字典标签（显示文本）
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// 字典值（实际值）
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 排序序号
        /// </summary>
        public int Sort { get; set; } = 0;

        /// <summary>
        /// 状态（true:启用, false:禁用）
        /// </summary>
        public bool Status { get; set; } = true;

        /// <summary>
        /// 描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 扩展属性1
        /// </summary>
        [StringLength(200)]
        public string? ExtProperty1 { get; set; }

        /// <summary>
        /// 扩展属性2
        /// </summary>
        [StringLength(200)]
        public string? ExtProperty2 { get; set; }

        /// <summary>
        /// 扩展属性3
        /// </summary>
        [StringLength(200)]
        public string? ExtProperty3 { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public int? CreateBy { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public int? UpdateBy { get; set; }

        // 导航属性
        /// <summary>
        /// 所属数据字典
        /// </summary>
        [ForeignKey("DictId")]
        public virtual DataDictionary? DataDictionary { get; set; }
    }
} 