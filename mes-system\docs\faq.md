# 常见问题解答 (FAQ)

这里收集了用户在使用MES系统过程中遇到的常见问题和解决方案。

## 🚀 环境和安装

### Q1: Node.js版本要求是什么？
**A:** 建议使用Node.js 16.0.0或更高版本。可以通过以下命令检查版本：
```bash
node --version
npm --version
```

### Q2: 推荐使用哪个包管理器？
**A:** 推荐使用pnpm，它比npm更快且节省磁盘空间：
```bash
# 安装pnpm
npm install -g pnpm

# 使用pnpm安装依赖
pnpm install
```

### Q3: 启动时报错 "Error: Cannot find module"
**A:** 解决方案：
1. 删除node_modules文件夹和package-lock.json
2. 清除npm缓存：`npm cache clean --force`
3. 重新安装依赖：`pnpm install`

## 🎯 PageTemplate相关

### Q4: 为什么我看到的页面是仪表板而不是PageTemplate页面？
**A:** 这是常见的误解。请注意：
- `/home` 路径显示的是仪表板页面（Dashboard）
- PageTemplate用于生成**管理页面**，不是仪表板页面
- 要查看PageTemplate效果，请访问：`/home/<USER>/page-template-demo`

### Q5: 如何使用PageTemplate生成新页面？
**A:** 完整流程：
1. 前往"菜单管理" (`/home/<USER>/menus`)
2. 点击"新增菜单"
3. 填写菜单信息
4. 启用"自动生成页面文件"开关
5. 保存后下载生成的文件
6. 手动添加路由配置

### Q6: 生成的页面无法访问怎么办？
**A:** 检查以下几点：
1. Vue文件是否放在正确的目录
2. 路由配置是否正确添加
3. 组件导入路径是否正确
4. 浏览器是否已刷新

## 🎛️ 菜单系统

### Q7: 如何添加新的菜单项？
**A:** 步骤：
1. 访问菜单管理页面
2. 点击"新增菜单"
3. 选择菜单类型（菜单项/子菜单）
4. 填写必要信息：名称、ID、路径、图标
5. 设置排序和状态
6. 保存菜单

### Q8: 菜单不显示怎么办？
**A:** 检查：
1. 菜单状态是否为"启用"
2. 用户是否有对应菜单权限
3. 菜单路径配置是否正确
4. 前端路由是否已配置

## 👥 角色权限

### Q9: 如何为用户分配角色？
**A:** 在用户管理页面：
1. 编辑用户信息
2. 在角色字段中选择对应角色
3. 保存更改

### Q10: 如何设置角色菜单权限？
**A:** 在角色权限页面：
1. 切换到"角色菜单设置"标签
2. 选择要设置的角色
3. 在菜单树中勾选允许访问的菜单
4. 点击"保存菜单权限"

## 🔧 开发问题

### Q11: 如何调试前端代码？
**A:** 使用Vue DevTools：
1. 安装Vue DevTools浏览器扩展
2. 在开发模式下打开F12
3. 找到Vue标签页进行调试

### Q12: API接口调用失败怎么办？
**A:** 检查：
1. 后端服务器是否正常启动
2. API接口地址是否正确
3. 网络请求是否有跨域问题
4. 查看浏览器Network标签的错误信息

## 📞 获取帮助

如果以上解答没有解决您的问题，您可以：

1. **查看详细文档**：[项目概述](./project-overview.md)
2. **提交Issue**：在项目仓库中详细描述问题
3. **联系技术支持**：<EMAIL>

---

*本FAQ持续更新中，最后更新：2024-01-15* 