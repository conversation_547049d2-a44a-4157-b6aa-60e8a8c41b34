using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace mes_system_server.Models
{
    public class Employee
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string EmployeeId { get; set; } = string.Empty; // 工号

        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty; // 姓名

        [Required]
        public int DepartmentId { get; set; } // 部门ID

        [ForeignKey("DepartmentId")]
        public Department Department { get; set; } // 部门对象

        [Required]
        [StringLength(50)]
        public string Position { get; set; } = string.Empty; // 职位

        [StringLength(20)]
        public string Phone { get; set; } = string.Empty; // 电话

        [StringLength(100)]
        [EmailAddress]
        public string Email { get; set; } = string.Empty; // 邮箱

        public DateTime EntryDate { get; set; } = DateTime.Now; // 入职日期

        public string Status { get; set; } = "active"; // 状态：active=在职, inactive=离职

        [StringLength(20)]
        public DateTime? Birthday { get; set; } // 出生日期

        [StringLength(18)]
        public string IdCard { get; set; } = string.Empty; // 身份证号

        public int? Age { get; set; } // 年龄

        public DateTime? IdCardIssueDate { get; set; } // 身份证签发日期

        [StringLength(10)]
        public string Gender { get; set; } = string.Empty; // 性别

        [StringLength(100)]
        public string IdCardIssuePlace { get; set; } = string.Empty; // 身份证签发地

        [StringLength(50)]
        public string Education { get; set; } = string.Empty; // 教育程度

        [StringLength(50)]
        public string ContractNo { get; set; } = string.Empty; // 合同编号

        [StringLength(20)]
        public string Level { get; set; } = string.Empty; // 员工等级

        [StringLength(20)]
        public string SkillLevel { get; set; } = string.Empty; // 技能等级

        [StringLength(20)]
        public string PerformanceLevel { get; set; } = string.Empty; // 绩效等级

        // 工厂信息
        [StringLength(100)]
        public string FactoryName { get; set; } = string.Empty; // 工厂名称

        [StringLength(200)]
        public string FactoryAddress { get; set; } = string.Empty; // 工厂地址

        [StringLength(50)]
        public string FactoryContact { get; set; } = string.Empty; // 联系人

        [StringLength(20)]
        public string FactoryPhone { get; set; } = string.Empty; // 联系电话

        [StringLength(20)]
        public string SalaryType { get; set; } = string.Empty; // 薪资类型

        [StringLength(50)]
        public string Team { get; set; } = string.Empty; // 班组

        [StringLength(20)]
        public string BaseSalary { get; set; } = string.Empty; // 基本薪资

        public int? WorkYears { get; set; } // 工作年限

        public DateTime CreateTime { get; set; } = DateTime.Now; // 创建时间
        
        public DateTime? UpdateTime { get; set; } // 更新时间
    }
} 