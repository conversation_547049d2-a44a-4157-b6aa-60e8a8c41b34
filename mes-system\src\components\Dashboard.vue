<template>
  <div class="dashboard-container">
    <!-- 数据卡片区域 -->
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="data-card bg-blue">
          <div class="card-header">
            <span>今日产量</span>
            <el-icon><DataLine /></el-icon>
          </div>
          <div class="card-content">
            <div class="main-num">1,286</div>
            <div class="sub-info">
              <span>台</span>
              <div class="status up">
                <el-icon><ArrowUp /></el-icon>
                <span>5.8%</span>
              </div>
            </div>
          </div>
          <el-progress :percentage="85.7" color="#409EFF" />
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="data-card bg-green">
          <div class="card-header">
            <span>设备运行</span>
            <el-icon><Monitor /></el-icon>
          </div>
          <div class="card-content">
            <div class="main-num">46</div>
            <div class="sub-info">
              <span>台</span>
              <div class="status up">
                <el-icon><ArrowUp /></el-icon>
                <span>2.3%</span>
              </div>
            </div>
          </div>
          <el-progress :percentage="92.5" color="#67C23A" />
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="data-card bg-orange">
          <div class="card-header">
            <span>质量监控</span>
            <el-icon><Warning /></el-icon>
          </div>
          <div class="card-content">
            <div class="main-num">98.5%</div>
            <div class="sub-info">
              <span>合格率</span>
              <div class="status down">
                <el-icon><ArrowDown /></el-icon>
                <span>0.3%</span>
              </div>
            </div>
          </div>
          <el-progress :percentage="98.5" color="#E6A23C" />
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="data-card bg-red">
          <div class="card-header">
            <span>能耗监控</span>
            <el-icon><Lightning /></el-icon>
          </div>
          <div class="card-content">
            <div class="main-num">435</div>
            <div class="sub-info">
              <span>kW·h</span>
              <div class="status down">
                <el-icon><ArrowDown /></el-icon>
                <span>1.7%</span>
              </div>
            </div>
          </div>
          <el-progress :percentage="75" color="#F56C6C" />
        </div>
      </el-col>
    </el-row>

    <!-- 图表卡片区域 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <div class="chart-card">
          <div class="chart-header">
            <h3>生产趋势分析</h3>
            <div class="chart-actions">
              <el-radio-group v-model="timeRange" size="small">
                <el-radio-button label="daily">日</el-radio-button>
                <el-radio-button label="weekly">周</el-radio-button>
                <el-radio-button label="monthly">月</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div class="chart-content" ref="trendChartRef"></div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>产品类型分布</h3>
          </div>
          <div class="chart-content" ref="distributionChartRef"></div>
        </div>
      </el-col>
    </el-row>

    <!-- 告警信息区域 -->
    <el-row :gutter="20" class="alert-row">
      <el-col :span="24">
        <div class="alert-card">
          <div class="alert-header">
            <h3>
              <el-icon><Warning /></el-icon>
              实时告警信息
            </h3>
            <el-tag type="danger" round>{{ alertCount }}条未处理</el-tag>
          </div>
          <div class="alert-list">
            <el-scrollbar height="150px">
              <div v-for="(alert, index) in alertList" :key="index" class="alert-item">
                <el-tag :type="alert.level" size="small" class="alert-level">{{ alert.levelText }}</el-tag>
                <span class="alert-time">{{ alert.time }}</span>
                <span class="alert-content">{{ alert.content }}</span>
                <el-button type="primary" link size="small">处理</el-button>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { 
  DataLine, ArrowUp, ArrowDown, Monitor, Warning, Lightning,
  Connection, Timer, Box
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 时间范围选择
const timeRange = ref('daily')

// 图表引用
const trendChartRef = ref(null)
const distributionChartRef = ref(null)

// 图表实例
let trendChart = null
let distributionChart = null

// 告警信息数据
const alertCount = ref(5)
const alertList = ref([
  {
    level: 'danger',
    levelText: '严重',
    time: '2024-03-14 10:23:45',
    content: '1号生产线断路器温升超过阈值，请立即检查'
  },
  {
    level: 'warning',
    levelText: '警告',
    time: '2024-03-14 10:15:32',
    content: '2号测试台绝缘电阻测试未达标，需要重新校准'
  },
  {
    level: 'warning',
    levelText: '警告',
    time: '2024-03-14 09:58:21',
    content: '3号生产线能耗异常，建议检查设备运行状态'
  },
  {
    level: 'info',
    levelText: '提示',
    time: '2024-03-14 09:45:16',
    content: '日常设备维护提醒：请对4号生产线进行例行检查'
  },
  {
    level: 'danger',
    levelText: '严重',
    time: '2024-03-14 09:30:08',
    content: '5号测试工位机械寿命测试异常，需要立即处理'
  }
])

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value) return
  
  trendChart = echarts.init(trendChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['绝缘性能', '温升指标', '机械寿命', '一次合格率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1日', '2日', '3日', '4日', '5日', '6日', '7日']
    },
    yAxis: {
      type: 'value',
      name: '合格率',
      min: 90,
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '绝缘性能',
        type: 'line',
        data: [99.8, 99.9, 99.7, 99.8, 99.9, 99.8, 99.9],
        itemStyle: {
          color: '#909399'
        }
      },
      {
        name: '温升指标',
        type: 'line',
        data: [97.5, 97.8, 97.2, 97.6, 97.9, 97.7, 97.8],
        itemStyle: {
          color: '#00CED1'
        }
      },
      {
        name: '机械寿命',
        type: 'line',
        data: [96.2, 96.5, 96.1, 96.4, 96.7, 96.3, 96.5],
        itemStyle: {
          color: '#A0522D'
        }
      },
      {
        name: '一次合格率',
        type: 'line',
        data: [98.2, 98.5, 98.3, 98.4, 98.6, 98.4, 98.5],
        itemStyle: {
          color: '#E6A23C'
        }
      }
    ]
  }
  
  trendChart.setOption(option)
}

// 初始化分布图表
const initDistributionChart = () => {
  if (!distributionChartRef.value) return
  
  distributionChart = echarts.init(distributionChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}台 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 'right'
    },
    series: [
      {
        name: '产品类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 1048, name: '断路器' },
          { value: 735, name: '继电器' },
          { value: 580, name: '接触器' },
          { value: 484, name: '漏电保护器' },
          { value: 300, name: '其他' }
        ]
      }
    ]
  }
  
  distributionChart.setOption(option)
}

// 窗口大小变化时重置图表大小
const handleResize = () => {
  trendChart?.resize()
  distributionChart?.resize()
}

// 监听时间范围变化
const watchTimeRange = () => {
  console.log('时间范围变更为:', timeRange.value)
  // 这里可以添加根据时间范围获取新数据的逻辑
}

onMounted(() => {
  initTrendChart()
  initDistributionChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  trendChart?.dispose()
  distributionChart?.dispose()
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.dashboard-header {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.dashboard-header .title {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.dashboard-header .welcome {
  font-size: 16px;
  color: #909399;
}

.data-card {
  height: 200px;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
  position: relative;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.card-content {
  padding: 10px 0 15px;
}

.main-num {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 10px;
}

.sub-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-size: 14px;
}

.status {
  display: flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.2;
}

.status.up {
  color: #67C23A;
  background-color: rgba(103, 194, 58, 0.1);
}

.status.down {
  color: #F56C6C;
  background-color: rgba(245, 108, 108, 0.1);
}

.bg-blue .card-header {
  color: #409EFF;
}

.bg-green .card-header {
  color: #67C23A;
}

.bg-orange .card-header {
  color: #E6A23C;
}

.bg-red .card-header {
  color: #F56C6C;
}

.bg-purple .card-header {
  color: #909399;
}

.bg-cyan .card-header {
  color: #00CED1;
}

.bg-brown .card-header {
  color: #A0522D;
}

.bg-teal .card-header {
  color: #008080;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-card {
  min-height: 320px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.chart-content {
  height: 280px;
}

.alert-row {
  margin-bottom: 20px;
}

.alert-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.alert-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.alert-list {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}

.alert-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #EBEEF5;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-level {
  margin-right: 12px;
  width: 48px;
  text-align: center;
}

.alert-time {
  color: #909399;
  font-size: 13px;
  width: 160px;
}

.alert-content {
  flex: 1;
  margin: 0 12px;
  color: #303133;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }

  .data-card {
    height: auto;
    min-height: 180px;
  }

  .chart-card {
    min-height: 280px;
  }

  .chart-content {
    height: 240px;
  }

  .alert-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .alert-time {
    width: auto;
  }
  
  .alert-content {
    margin: 8px 0;
  }
}
</style> 