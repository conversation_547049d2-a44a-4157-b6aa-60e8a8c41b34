﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace mes_system_server.Migrations
{
    /// <inheritdoc />
    public partial class AddMaterialModels : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MaterialModels",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Type = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Category = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Specification = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Status = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Image = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Unit = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Material = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Size = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    SalesPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    SalesUnit = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    MinOrderQty = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    MaxOrderQty = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    LeadTime = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Warranty = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    StorageLocation = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    SafetyStock = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    MaxStock = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    ReorderPoint = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    StorageCondition = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    ShelfLife = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    StandardCost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    AverageCost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    ValuationMethod = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    TaxRate = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    AccountSubject = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    CostCenter = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ProductionType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ProductionLeadTime = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    SetupTime = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CycleTime = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    BatchSize = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    WorkCenter = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    QualityStandard = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Supplier = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    PurchasePrice = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PurchaseUnit = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    MinPurchaseQty = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PurchaseLeadTime = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    QualityLevel = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PurchaseNote = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    HsCode = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    OriginCountry = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ImportTaxRate = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    ExportTaxRefund = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    IsDangerous = table.Column<bool>(type: "bit", nullable: false),
                    TransportMode = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PackingRequirement = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    InspectionRequired = table.Column<bool>(type: "bit", nullable: false),
                    LicenseRequirement = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    PlanningStrategy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PlanningCycle = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ForecastMethod = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    AbcCategory = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    DemandSource = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Planner = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    SafetyStockDays = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    LotSizeRule = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    DemandTimeFence = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    SupplyTimeFence = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PlanningNote = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreateBy = table.Column<int>(type: "int", nullable: false),
                    UpdateBy = table.Column<int>(type: "int", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MaterialModels", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2245), new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2245) });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2263));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2274));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2279));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2285));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2290));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2295));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2300));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2305));

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2355), new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2355) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2368), new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2369) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2379), new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2379) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2389), new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2389) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2400), new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2400) });

            migrationBuilder.CreateIndex(
                name: "IX_MaterialModels_Name",
                table: "MaterialModels",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_MaterialModels_Type_Category_IsActive",
                table: "MaterialModels",
                columns: new[] { "Type", "Category", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_MaterialModels_Type_Code",
                table: "MaterialModels",
                columns: new[] { "Type", "Code" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MaterialModels_Type_CreateTime_IsActive",
                table: "MaterialModels",
                columns: new[] { "Type", "CreateTime", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_MaterialModels_Type_Name_Code_IsActive",
                table: "MaterialModels",
                columns: new[] { "Type", "Name", "Code", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_MaterialModels_Type_Status_IsActive",
                table: "MaterialModels",
                columns: new[] { "Type", "Status", "IsActive" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MaterialModels");

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2561), new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2562) });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2576));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2585));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2591));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2595));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2599));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2604));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2608));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 7, 10, 13, 6, 4, 150, DateTimeKind.Utc).AddTicks(2612));

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2656), new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2657) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2667), new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2667) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2676), new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2676) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2684), new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2684) });

            migrationBuilder.UpdateData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "CreateTime", "UpdateTime" },
                values: new object[] { new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2693), new DateTime(2025, 7, 10, 21, 6, 4, 150, DateTimeKind.Local).AddTicks(2694) });
        }
    }
}
