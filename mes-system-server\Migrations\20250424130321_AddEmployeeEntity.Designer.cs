﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using mes_system_server.Data;

#nullable disable

namespace mes_system_server.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250424130321_AddEmployeeEntity")]
    partial class AddEmployeeEntity
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.15")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("mes_system_server.Models.Company", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CompanyType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreditCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LegalRepresentative")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("RegisteredCapital")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("RegistrationDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Companies");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Address = "上海市浦东新区张江高科技园区",
                            CompanyType = "LLC",
                            CreatedAt = new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4500),
                            CreditCode = "91310000XXXXXXXXXX",
                            Description = "丰信科技是一家专注于智能制造领域的高新技术企业，致力于为工业企业提供先进的智能制造解决方案和服务。",
                            Email = "<EMAIL>",
                            LegalRepresentative = "张三",
                            Name = "丰信科技有限公司",
                            Phone = "021-12345678",
                            RegisteredCapital = "1000万元",
                            RegistrationDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            UpdatedAt = new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4500)
                        });
                });

            modelBuilder.Entity("mes_system_server.Models.Department", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("EmployeeCount")
                        .HasColumnType("int");

                    b.Property<string>("Level")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("nvarchar(1)");

                    b.Property<string>("Manager")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.ToTable("Departments");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "RD",
                            CreateTime = new DateTime(2023, 1, 1, 8, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责产品研发",
                            EmployeeCount = 20,
                            Level = "1",
                            Manager = "张三",
                            Name = "研发部",
                            UpdateTime = new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4611)
                        },
                        new
                        {
                            Id = 2,
                            Code = "PROD",
                            CreateTime = new DateTime(2023, 1, 2, 9, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责产品生产",
                            EmployeeCount = 50,
                            Level = "1",
                            Manager = "李四",
                            Name = "生产部",
                            UpdateTime = new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4741)
                        },
                        new
                        {
                            Id = 3,
                            Code = "QA",
                            CreateTime = new DateTime(2023, 1, 3, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责质量检测",
                            EmployeeCount = 15,
                            Level = "2",
                            Manager = "王五",
                            Name = "质检部",
                            ParentId = 2,
                            UpdateTime = new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4748)
                        },
                        new
                        {
                            Id = 4,
                            Code = "WH",
                            CreateTime = new DateTime(2023, 1, 4, 11, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责仓库管理",
                            EmployeeCount = 10,
                            Level = "1",
                            Manager = "赵六",
                            Name = "仓储部",
                            UpdateTime = new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4753)
                        },
                        new
                        {
                            Id = 5,
                            Code = "HR",
                            CreateTime = new DateTime(2023, 1, 5, 12, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责人力资源管理",
                            EmployeeCount = 8,
                            Level = "1",
                            Manager = "钱七",
                            Name = "人力资源部",
                            UpdateTime = new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4762)
                        },
                        new
                        {
                            Id = 6,
                            Code = "FE",
                            CreateTime = new DateTime(2023, 1, 6, 13, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责前端开发",
                            EmployeeCount = 12,
                            Level = "2",
                            Manager = "孙八",
                            Name = "前端开发组",
                            ParentId = 1,
                            UpdateTime = new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4766)
                        },
                        new
                        {
                            Id = 7,
                            Code = "BE",
                            CreateTime = new DateTime(2023, 1, 7, 14, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责后端开发",
                            EmployeeCount = 8,
                            Level = "2",
                            Manager = "周九",
                            Name = "后端开发组",
                            ParentId = 1,
                            UpdateTime = new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4771)
                        },
                        new
                        {
                            Id = 8,
                            Code = "UI",
                            CreateTime = new DateTime(2023, 1, 8, 15, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责UI设计",
                            EmployeeCount = 5,
                            Level = "3",
                            Manager = "吴十",
                            Name = "UI设计团队",
                            ParentId = 6,
                            UpdateTime = new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4775)
                        });
                });

            modelBuilder.Entity("mes_system_server.Models.Employee", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("Age")
                        .HasColumnType("int");

                    b.Property<string>("BaseSalary")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime?>("Birthday")
                        .HasMaxLength(20)
                        .HasColumnType("datetime2");

                    b.Property<string>("ContractNo")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("int");

                    b.Property<string>("Education")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("EntryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FactoryAddress")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FactoryContact")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FactoryName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FactoryPhone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Gender")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("IdCard")
                        .IsRequired()
                        .HasMaxLength(18)
                        .HasColumnType("nvarchar(18)");

                    b.Property<DateTime?>("IdCardIssueDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IdCardIssuePlace")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Level")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PerformanceLevel")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Position")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("SalaryType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("SkillLevel")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Team")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("WorkYears")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("EmployeeId")
                        .IsUnique();

                    b.ToTable("Employees");
                });

            modelBuilder.Entity("mes_system_server.Models.Position", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Level")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.ToTable("Positions");
                });

            modelBuilder.Entity("mes_system_server.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasFilter("[Email] IS NOT NULL");

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("mes_system_server.Models.Department", b =>
                {
                    b.HasOne("mes_system_server.Models.Department", "ParentDepartment")
                        .WithMany()
                        .HasForeignKey("ParentId");

                    b.Navigation("ParentDepartment");
                });

            modelBuilder.Entity("mes_system_server.Models.Employee", b =>
                {
                    b.HasOne("mes_system_server.Models.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("mes_system_server.Models.Position", b =>
                {
                    b.HasOne("mes_system_server.Models.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");
                });
#pragma warning restore 612, 618
        }
    }
}
