<template>
  <div class="permission-test">
    <el-card header="权限控制测试页面">
      <div class="test-section">
        <h3>当前用户信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户名">{{ userInfo?.username || '未登录' }}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ userInfo?.name || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="角色">{{ userInfo?.role || '未分配' }}</el-descriptions-item>
          <el-descriptions-item label="角色ID">{{ userInfo?.roleId || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="部门">{{ userInfo?.department || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ userInfo?.email || '未设置' }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="test-section">
        <h3>用户菜单权限</h3>
        <el-button @click="loadUserPermissions" type="primary" :loading="loading">
          {{ loading ? '加载中...' : '刷新权限信息' }}
        </el-button>
        <div v-if="userMenuPermissions.length > 0" style="margin-top: 10px;">
          <el-tag 
            v-for="menuId in userMenuPermissions" 
            :key="menuId" 
            style="margin: 2px;"
            type="success"
          >
            {{ menuId }}
          </el-tag>
        </div>
        <el-empty v-else description="暂无菜单权限" />
      </div>

      <div class="test-section">
        <h3>权限测试</h3>
        <el-form :model="testForm" label-width="120px">
          <el-form-item label="测试菜单ID">
            <el-input v-model="testForm.menuId" placeholder="请输入菜单ID，如：system-users" />
          </el-form-item>
          <el-form-item>
            <el-button @click="testPermission" type="primary">检查权限</el-button>
          </el-form-item>
        </el-form>
        <div v-if="testResult !== null" style="margin-top: 10px;">
          <el-alert 
            :title="testResult ? '有权限访问' : '无权限访问'" 
            :type="testResult ? 'success' : 'error'" 
            show-icon 
          />
        </div>
      </div>

      <div class="test-section">
        <h3>菜单过滤测试</h3>
        <el-button @click="loadFilteredMenus" type="primary" :loading="menuLoading">
          {{ menuLoading ? '加载中...' : '加载过滤后的菜单' }}
        </el-button>
        <div v-if="filteredMenus.length > 0" style="margin-top: 10px;">
          <el-tree
            :data="filteredMenus"
            :props="{
              label: 'name',
              children: 'children'
            }"
            default-expand-all
          />
        </div>
        <el-empty v-else description="暂无可访问的菜单" />
      </div>

      <div class="test-section">
        <h3>操作按钮</h3>
        <el-space>
          <el-button @click="refreshUserInfo" type="info">刷新用户信息</el-button>
          <el-button @click="clearTestData" type="warning">清除测试数据</el-button>
          <el-button @click="goToLogin" type="danger">退出登录</el-button>
        </el-space>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  getCurrentUser, 
  getCurrentUserMenuPermissions, 
  hasMenuPermission, 
  filterMenusByPermissions,
  logout 
} from '@/utils/permission'
import { getMenuTree } from '@/api/menu'

const router = useRouter()

// 响应式数据
const userInfo = ref(null)
const userMenuPermissions = ref([])
const loading = ref(false)
const menuLoading = ref(false)
const filteredMenus = ref([])
const testResult = ref(null)

const testForm = reactive({
  menuId: ''
})

// 加载用户信息
const loadUserInfo = () => {
  userInfo.value = getCurrentUser()
  console.log('当前用户信息:', userInfo.value)
}

// 加载用户权限
const loadUserPermissions = async () => {
  loading.value = true
  try {
    const permissions = await getCurrentUserMenuPermissions()
    userMenuPermissions.value = permissions
    console.log('用户菜单权限:', permissions)
    ElMessage.success(`加载成功，共 ${permissions.length} 个菜单权限`)
  } catch (error) {
    console.error('加载用户权限失败:', error)
    ElMessage.error('加载用户权限失败')
    userMenuPermissions.value = []
  } finally {
    loading.value = false
  }
}

// 测试权限
const testPermission = () => {
  if (!testForm.menuId.trim()) {
    ElMessage.warning('请输入要测试的菜单ID')
    return
  }

  const hasPermission = hasMenuPermission(testForm.menuId, userMenuPermissions.value)
  testResult.value = hasPermission
  
  ElMessage({
    message: `菜单"${testForm.menuId}"权限检查结果: ${hasPermission ? '有权限' : '无权限'}`,
    type: hasPermission ? 'success' : 'error'
  })
}

// 加载过滤后的菜单
const loadFilteredMenus = async () => {
  menuLoading.value = true
  try {
    // 获取所有菜单
    const menuResponse = await getMenuTree()
    if (menuResponse.data.code !== 200) {
      throw new Error(menuResponse.data.message)
    }

    const allMenus = menuResponse.data.data || []
    
    // 转换菜单格式
    const convertMenuData = (menus) => {
      return menus.map(menu => ({
        id: menu.menuId,
        name: menu.name,
        path: menu.path,
        type: menu.type,
        children: menu.children && menu.children.length > 0 ? convertMenuData(menu.children) : undefined
      }))
    }

    const convertedMenus = convertMenuData(allMenus.filter(menu => menu.status))
    
    // 根据用户权限过滤菜单
    const filtered = filterMenusByPermissions(convertedMenus, userMenuPermissions.value)
    filteredMenus.value = filtered
    
    console.log('过滤后的菜单:', filtered)
    ElMessage.success(`菜单过滤完成，共 ${filtered.length} 个可访问菜单`)
  } catch (error) {
    console.error('加载菜单失败:', error)
    ElMessage.error('加载菜单失败')
    filteredMenus.value = []
  } finally {
    menuLoading.value = false
  }
}

// 刷新用户信息
const refreshUserInfo = () => {
  loadUserInfo()
  loadUserPermissions()
  ElMessage.success('用户信息已刷新')
}

// 清除测试数据
const clearTestData = () => {
  userMenuPermissions.value = []
  filteredMenus.value = []
  testResult.value = null
  testForm.menuId = ''
  ElMessage.info('测试数据已清除')
}

// 退出登录
const goToLogin = () => {
  logout()
  router.push('/login')
  ElMessage.success('已退出登录')
}

// 组件挂载时加载数据
onMounted(() => {
  loadUserInfo()
  loadUserPermissions()
})
</script>

<style scoped>
.permission-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  background-color: #fafafa;
}

.test-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #409eff;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}
</style> 