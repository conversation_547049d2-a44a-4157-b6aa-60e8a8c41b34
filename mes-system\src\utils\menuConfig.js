/**
 * 系统菜单配置
 * 用于统一管理左侧导航菜单和角色权限菜单
 */

// 导入图标
import {
  Monitor,
  Setting,
  Files,
  Operation,
  List,
  Odometer,
  Calendar,
  Document,
  VideoCamera,
  Aim,
  Check,
  DataAnalysis,
  Box,
  Goods,
  Collection,
  Tools,
  Platform,
  Service,
  UserFilled,
  User,
  Money,
  Avatar,
  Lock,
  OfficeBuilding,
  Notebook
} from '@element-plus/icons-vue'

// 系统菜单配置
export const menuConfig = [
  {
    id: 'dashboard',
    name: '仪表盘',
    path: '/home',
    icon: Monitor,
    index: '/home',
    type: 'menu-item'
  },
  {
    id: 'engineering',
    name: '工程管理',
    icon: Setting,
    index: '1',
    type: 'sub-menu',
    children: [
      {
        id: 'engineering-model',
        name: '物料主数据',
        path: '/home/<USER>/model-management',
        icon: Files,
        index: '/home/<USER>/model-management',
        type: 'menu-item'
      },
      {
        id: 'engineering-process',
        name: '工艺管理',
        path: '/engineering/process',
        icon: Operation,
        index: '/engineering/process',
        type: 'menu-item'
      },
      {
        id: 'engineering-bom',
        name: 'BOM管理',
        path: '/engineering/bom',
        icon: List,
        index: '/engineering/bom',
        type: 'menu-item'
      },
      {
        id: 'engineering-wis',
        name: 'WIS管理',
        path: '/home/<USER>/wis-management',
        icon: Notebook,
        index: '/home/<USER>/wis-management',
        type: 'menu-item'
      }
    ]
  },
  {
    id: 'production',
    name: '生产管理',
    icon: Odometer,
    index: '2',
    type: 'sub-menu',
    children: [
      {
        id: 'production-plan',
        name: '生产计划',
        path: '/production/plan',
        icon: Calendar,
        index: '/production/plan',
        type: 'menu-item'
      },
      {
        id: 'production-task',
        name: '生产任务',
        path: '/production/task',
        icon: Document,
        index: '/production/task',
        type: 'menu-item'
      },
      {
        id: 'production-monitor',
        name: '生产监控',
        path: '/production/monitor',
        icon: VideoCamera,
        index: '/production/monitor',
        type: 'menu-item'
      }
    ]
  },
  {
    id: 'quality',
    name: '质量管理',
    icon: Aim,
    index: '3',
    type: 'sub-menu',
    children: [
      {
        id: 'quality-inspection',
        name: '质量检验',
        path: '/quality/inspection',
        icon: Check,
        index: '/quality/inspection',
        type: 'menu-item'
      },
      {
        id: 'quality-report',
        name: '质量报表',
        path: '/quality/report',
        icon: DataAnalysis,
        index: '/quality/report',
        type: 'menu-item'
      }
    ]
  },
  {
    id: 'warehouse',
    name: '仓储管理',
    icon: Box,
    index: '4',
    type: 'sub-menu',
    children: [
      {
        id: 'warehouse-material',
        name: '物料管理',
        path: '/warehouse/material',
        icon: Goods,
        index: '/warehouse/material',
        type: 'menu-item'
      },
      {
        id: 'warehouse-inventory',
        name: '库存管理',
        path: '/warehouse/inventory',
        icon: Collection,
        index: '/warehouse/inventory',
        type: 'menu-item'
      }
    ]
  },
  {
    id: 'equipment',
    name: '设备管理',
    icon: Tools,
    index: '5',
    type: 'sub-menu',
    children: [
      {
        id: 'equipment-list',
        name: '设备台账',
        path: '/equipment/list',
        icon: Platform,
        index: '/equipment/list',
        type: 'menu-item'
      },
      {
        id: 'equipment-maintenance',
        name: '设备维护',
        path: '/equipment/maintenance',
        icon: Service,
        index: '/equipment/maintenance',
        type: 'menu-item'
      }
    ]
  },
  {
    id: 'hr',
    name: '人事管理',
    icon: UserFilled,
    index: '6',
    type: 'sub-menu',
    children: [
      {
        id: 'hr-employees',
        name: '员工管理',
        path: '/home/<USER>/employees',
        icon: User,
        index: '/home/<USER>/employees',
        type: 'menu-item'
      },
      {
        id: 'hr-attendance',
        name: '考勤管理',
        path: '/home/<USER>/attendance',
        icon: Calendar,
        index: '/home/<USER>/attendance',
        type: 'menu-item'
      },
      {
        id: 'hr-salary',
        name: '薪资管理',
        path: '/home/<USER>/salary',
        icon: Money,
        index: '/home/<USER>/salary',
        type: 'menu-item'
      },
      {
        id: 'hr-company-settings',
        name: '公司设置',
        path: '/home/<USER>/company-settings',
        icon: OfficeBuilding,
        index: '/home/<USER>/company-settings',
        type: 'menu-item'
      }
    ]
  },
  {
    id: 'system',
    name: '系统管理',
    icon: Setting,
    index: '7',
    type: 'sub-menu',
    children: [
      {
        id: 'system-users',
        name: '用户管理',
        path: '/home/<USER>/users',
        icon: Avatar,
        index: '/home/<USER>/users',
        type: 'menu-item'
      },
      {
        id: 'system-roles',
        name: '角色权限',
        path: '/home/<USER>/roles',
        icon: Lock,
        index: '/home/<USER>/roles',
        type: 'menu-item'
      },
      {
        id: 'system-menus',
        name: '菜单管理',
        path: '/home/<USER>/menus',
        icon: List,
        index: '/home/<USER>/menus',
        type: 'menu-item'
      },
      {
        id: 'system-language',
        name: '多语言管理',
        path: '/home/<USER>/language',
        icon: Document,
        index: '/home/<USER>/language',
        type: 'menu-item'
      }
    ]
  }
]

/**
 * 转换菜单配置为树形结构（用于角色权限设置）
 * @returns {Array} 树形菜单结构
 */
export function getMenuTreeForRole() {
  const convertToTree = (menuItems) => {
    return menuItems.map(item => {
      const treeNode = {
        id: item.id,
        name: item.name,
        menuId: item.id, // 保留原始菜单ID用于权限控制
        path: item.path,
        type: item.type
      }
      
      if (item.children && item.children.length > 0) {
        treeNode.children = convertToTree(item.children)
      }
      
      return treeNode
    })
  }
  
  return convertToTree(menuConfig)
}

/**
 * 获取左侧导航菜单配置
 * @returns {Array} 导航菜单配置
 */
export function getNavigationMenu() {
  return menuConfig
}

/**
 * 根据菜单ID获取菜单项
 * @param {string} menuId 菜单ID
 * @returns {Object|null} 菜单项
 */
export function getMenuById(menuId) {
  const findMenu = (items, id) => {
    for (const item of items) {
      if (item.id === id) {
        return item
      }
      if (item.children) {
        const found = findMenu(item.children, id)
        if (found) return found
      }
    }
    return null
  }
  
  return findMenu(menuConfig, menuId)
}

/**
 * 获取所有菜单ID列表（扁平化）
 * @returns {Array} 菜单ID数组
 */
export function getAllMenuIds() {
  const collectIds = (items) => {
    const ids = []
    items.forEach(item => {
      ids.push(item.id)
      if (item.children) {
        ids.push(...collectIds(item.children))
      }
    })
    return ids
  }
  
  return collectIds(menuConfig)
}

/**
 * 根据角色权限过滤菜单
 * @param {Array} userMenuIds 用户拥有的菜单ID列表
 * @returns {Array} 过滤后的菜单配置
 */
export function getFilteredMenuByPermissions(userMenuIds) {
  if (!userMenuIds || userMenuIds.length === 0) {
    return []
  }
  
  const filterMenu = (items) => {
    return items.filter(item => {
      if (!userMenuIds.includes(item.id)) {
        return false
      }
      
      if (item.children) {
        const filteredChildren = filterMenu(item.children)
        if (filteredChildren.length > 0) {
          return {
            ...item,
            children: filteredChildren
          }
        } else {
          // 如果子菜单全部被过滤掉，但父菜单有权限，保留父菜单
          return item.type === 'menu-item'
        }
      }
      
      return true
    }).map(item => {
      if (item.children) {
        return {
          ...item,
          children: filterMenu(item.children)
        }
      }
      return item
    })
  }
  
  return filterMenu(menuConfig)
}

export default {
  menuConfig,
  getMenuTreeForRole,
  getNavigationMenu,
  getMenuById,
  getAllMenuIds,
  getFilteredMenuByPermissions
} 