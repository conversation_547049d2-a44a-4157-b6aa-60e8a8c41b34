﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using mes_system_server.Data;

#nullable disable

namespace mes_system_server.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250627131105_AddWorkCenterTables")]
    partial class AddWorkCenterTables
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.15")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("mes_system_server.Models.Company", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CompanyType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreditCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LegalRepresentative")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("RegisteredCapital")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("RegistrationDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Companies");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Address = "上海市浦东新区张江高科技园区",
                            CompanyType = "LLC",
                            CreatedAt = new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8027),
                            CreditCode = "91310000XXXXXXXXXX",
                            Description = "丰信科技是一家专注于智能制造领域的高新技术企业，致力于为工业企业提供先进的智能制造解决方案和服务。",
                            Email = "<EMAIL>",
                            LegalRepresentative = "张三",
                            Name = "丰信科技有限公司",
                            Phone = "021-12345678",
                            RegisteredCapital = "1000万元",
                            RegistrationDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            UpdatedAt = new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8028)
                        });
                });

            modelBuilder.Entity("mes_system_server.Models.Department", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("EmployeeCount")
                        .HasColumnType("int");

                    b.Property<string>("Level")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("nvarchar(1)");

                    b.Property<string>("Manager")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.ToTable("Departments");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "RD",
                            CreateTime = new DateTime(2023, 1, 1, 8, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责产品研发",
                            EmployeeCount = 20,
                            Level = "1",
                            Manager = "张三",
                            Name = "研发部",
                            UpdateTime = new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8052)
                        },
                        new
                        {
                            Id = 2,
                            Code = "PROD",
                            CreateTime = new DateTime(2023, 1, 2, 9, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责产品生产",
                            EmployeeCount = 50,
                            Level = "1",
                            Manager = "李四",
                            Name = "生产部",
                            UpdateTime = new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8067)
                        },
                        new
                        {
                            Id = 3,
                            Code = "QA",
                            CreateTime = new DateTime(2023, 1, 3, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责质量检测",
                            EmployeeCount = 15,
                            Level = "2",
                            Manager = "王五",
                            Name = "质检部",
                            ParentId = 2,
                            UpdateTime = new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8075)
                        },
                        new
                        {
                            Id = 4,
                            Code = "WH",
                            CreateTime = new DateTime(2023, 1, 4, 11, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责仓库管理",
                            EmployeeCount = 10,
                            Level = "1",
                            Manager = "赵六",
                            Name = "仓储部",
                            UpdateTime = new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8082)
                        },
                        new
                        {
                            Id = 5,
                            Code = "HR",
                            CreateTime = new DateTime(2023, 1, 5, 12, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责人力资源管理",
                            EmployeeCount = 8,
                            Level = "1",
                            Manager = "钱七",
                            Name = "人力资源部",
                            UpdateTime = new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8089)
                        },
                        new
                        {
                            Id = 6,
                            Code = "FE",
                            CreateTime = new DateTime(2023, 1, 6, 13, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责前端开发",
                            EmployeeCount = 12,
                            Level = "2",
                            Manager = "孙八",
                            Name = "前端开发组",
                            ParentId = 1,
                            UpdateTime = new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8096)
                        },
                        new
                        {
                            Id = 7,
                            Code = "BE",
                            CreateTime = new DateTime(2023, 1, 7, 14, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责后端开发",
                            EmployeeCount = 8,
                            Level = "2",
                            Manager = "周九",
                            Name = "后端开发组",
                            ParentId = 1,
                            UpdateTime = new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8103)
                        },
                        new
                        {
                            Id = 8,
                            Code = "UI",
                            CreateTime = new DateTime(2023, 1, 8, 15, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责UI设计",
                            EmployeeCount = 5,
                            Level = "3",
                            Manager = "吴十",
                            Name = "UI设计团队",
                            ParentId = 6,
                            UpdateTime = new DateTime(2025, 6, 27, 13, 11, 4, 825, DateTimeKind.Utc).AddTicks(8110)
                        });
                });

            modelBuilder.Entity("mes_system_server.Models.Employee", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("Age")
                        .HasColumnType("int");

                    b.Property<string>("BaseSalary")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime?>("Birthday")
                        .HasMaxLength(20)
                        .HasColumnType("datetime2");

                    b.Property<string>("ContractNo")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("int");

                    b.Property<string>("Education")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("EntryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FactoryAddress")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FactoryContact")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FactoryName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FactoryPhone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Gender")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("IdCard")
                        .IsRequired()
                        .HasMaxLength(18)
                        .HasColumnType("nvarchar(18)");

                    b.Property<DateTime?>("IdCardIssueDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IdCardIssuePlace")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Level")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PerformanceLevel")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Position")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("SalaryType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("SkillLevel")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Team")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("WorkYears")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("EmployeeId")
                        .IsUnique();

                    b.ToTable("Employees");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Age = 33,
                            BaseSalary = "15000",
                            Birthday = new DateTime(1990, 5, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ContractNo = "HT2022001",
                            CreateTime = new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8175),
                            DepartmentId = 1,
                            Education = "本科",
                            Email = "<EMAIL>",
                            EmployeeId = "EMP001",
                            EntryDate = new DateTime(2022, 1, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            FactoryAddress = "上海市浦东新区张江高科技园区",
                            FactoryContact = "王经理",
                            FactoryName = "总部研发中心",
                            FactoryPhone = "021-12345678",
                            Gender = "男",
                            IdCard = "310101199005103215",
                            IdCardIssuePlace = "",
                            Level = "3",
                            Name = "张大明",
                            PerformanceLevel = "A",
                            Phone = "13800138001",
                            Position = "软件工程师",
                            SalaryType = "月薪",
                            SkillLevel = "高级",
                            Status = "active",
                            Team = "后端开发组",
                            UpdateTime = new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8176),
                            WorkYears = 8
                        },
                        new
                        {
                            Id = 2,
                            Age = 31,
                            BaseSalary = "12000",
                            Birthday = new DateTime(1992, 8, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ContractNo = "HT2022015",
                            CreateTime = new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8193),
                            DepartmentId = 6,
                            Education = "本科",
                            Email = "<EMAIL>",
                            EmployeeId = "EMP002",
                            EntryDate = new DateTime(2022, 3, 20, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            FactoryAddress = "上海市浦东新区张江高科技园区",
                            FactoryContact = "王经理",
                            FactoryName = "总部研发中心",
                            FactoryPhone = "021-12345678",
                            Gender = "女",
                            IdCard = "310101199208153624",
                            IdCardIssuePlace = "",
                            Level = "2",
                            Name = "李小红",
                            PerformanceLevel = "B+",
                            Phone = "13900139002",
                            Position = "前端开发工程师",
                            SalaryType = "月薪",
                            SkillLevel = "中级",
                            Status = "active",
                            Team = "前端开发组",
                            UpdateTime = new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8195),
                            WorkYears = 5
                        },
                        new
                        {
                            Id = 3,
                            Age = 38,
                            BaseSalary = "10000",
                            Birthday = new DateTime(1985, 12, 20, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ContractNo = "HT2021052",
                            CreateTime = new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8209),
                            DepartmentId = 2,
                            Education = "大专",
                            Email = "<EMAIL>",
                            EmployeeId = "EMP003",
                            EntryDate = new DateTime(2021, 5, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            FactoryAddress = "上海市松江区新桥镇新茸路888号",
                            FactoryContact = "赵经理",
                            FactoryName = "松江生产基地",
                            FactoryPhone = "021-87654321",
                            Gender = "男",
                            IdCard = "310101198512204871",
                            IdCardIssuePlace = "",
                            Level = "2",
                            Name = "王强",
                            PerformanceLevel = "A",
                            Phone = "13700137003",
                            Position = "生产主管",
                            SalaryType = "月薪",
                            SkillLevel = "中级",
                            Status = "active",
                            Team = "生产一组",
                            UpdateTime = new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8210),
                            WorkYears = 10
                        },
                        new
                        {
                            Id = 4,
                            Age = 30,
                            BaseSalary = "11000",
                            Birthday = new DateTime(1993, 3, 25, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ContractNo = "HT2022089",
                            CreateTime = new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8224),
                            DepartmentId = 5,
                            Education = "硕士",
                            Email = "<EMAIL>",
                            EmployeeId = "EMP004",
                            EntryDate = new DateTime(2022, 8, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            FactoryAddress = "上海市浦东新区张江高科技园区",
                            FactoryContact = "钱经理",
                            FactoryName = "总部",
                            FactoryPhone = "021-12345678",
                            Gender = "女",
                            IdCard = "310101199303251234",
                            IdCardIssuePlace = "",
                            Level = "2",
                            Name = "赵静",
                            PerformanceLevel = "A-",
                            Phone = "13600136004",
                            Position = "人力资源专员",
                            SalaryType = "月薪",
                            SkillLevel = "中级",
                            Status = "active",
                            Team = "招聘组",
                            UpdateTime = new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8225),
                            WorkYears = 3
                        },
                        new
                        {
                            Id = 5,
                            Age = 35,
                            BaseSalary = "8000",
                            Birthday = new DateTime(1988, 7, 8, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ContractNo = "HT2022035",
                            CreateTime = new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8239),
                            DepartmentId = 3,
                            Education = "大专",
                            Email = "<EMAIL>",
                            EmployeeId = "EMP005",
                            EntryDate = new DateTime(2022, 4, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            FactoryAddress = "上海市松江区新桥镇新茸路888号",
                            FactoryContact = "赵经理",
                            FactoryName = "松江生产基地",
                            FactoryPhone = "021-87654321",
                            Gender = "男",
                            IdCard = "310101198807085678",
                            IdCardIssuePlace = "",
                            Level = "1",
                            Name = "陈明",
                            PerformanceLevel = "B",
                            Phone = "13500135005",
                            Position = "质检专员",
                            SalaryType = "月薪",
                            SkillLevel = "初级",
                            Status = "active",
                            Team = "质检一组",
                            UpdateTime = new DateTime(2025, 6, 27, 21, 11, 4, 825, DateTimeKind.Local).AddTicks(8241),
                            WorkYears = 5
                        });
                });

            modelBuilder.Entity("mes_system_server.Models.Equipment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("WorkCenterId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("WorkCenterId", "Code")
                        .IsUnique();

                    b.ToTable("Equipment");
                });

            modelBuilder.Entity("mes_system_server.Models.Menu", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Icon")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("MenuId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<string>("Path")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("MenuId")
                        .IsUnique();

                    b.HasIndex("ParentId");

                    b.ToTable("Menus");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "仓储管理模块",
                            MenuId = "warehouse",
                            Name = "仓储管理",
                            SortOrder = 1,
                            Status = true,
                            Type = "sub-menu"
                        },
                        new
                        {
                            Id = 2,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "生产管理模块",
                            MenuId = "production",
                            Name = "生产管理",
                            SortOrder = 2,
                            Status = true,
                            Type = "sub-menu"
                        },
                        new
                        {
                            Id = 3,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "工程管理模块",
                            MenuId = "engineering",
                            Name = "工程管理",
                            SortOrder = 3,
                            Status = true,
                            Type = "sub-menu"
                        },
                        new
                        {
                            Id = 4,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "生产线管理",
                            MenuId = "production-line",
                            Name = "生产管理",
                            SortOrder = 4,
                            Status = true,
                            Type = "sub-menu"
                        },
                        new
                        {
                            Id = 5,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "质量管理模块",
                            MenuId = "quality",
                            Name = "质量管理",
                            SortOrder = 5,
                            Status = true,
                            Type = "sub-menu"
                        },
                        new
                        {
                            Id = 6,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "设备管理模块",
                            MenuId = "equipment",
                            Name = "设备管理",
                            SortOrder = 6,
                            Status = true,
                            Type = "sub-menu"
                        },
                        new
                        {
                            Id = 7,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "系统管理模块",
                            MenuId = "system",
                            Name = "系统管理",
                            SortOrder = 7,
                            Status = true,
                            Type = "sub-menu"
                        },
                        new
                        {
                            Id = 8,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "warehouse-in",
                            Name = "入库管理",
                            ParentId = 1,
                            Path = "/home/<USER>/in",
                            SortOrder = 1,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 9,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "warehouse-out",
                            Name = "出库管理",
                            ParentId = 1,
                            Path = "/home/<USER>/out",
                            SortOrder = 2,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 10,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "warehouse-transfer",
                            Name = "库存调拨",
                            ParentId = 1,
                            Path = "/home/<USER>/transfer",
                            SortOrder = 3,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 11,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "warehouse-check",
                            Name = "库存盘点",
                            ParentId = 1,
                            Path = "/home/<USER>/check",
                            SortOrder = 4,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 12,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "production-plan",
                            Name = "生产计划",
                            ParentId = 2,
                            Path = "/home/<USER>/plan",
                            SortOrder = 1,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 13,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "production-order",
                            Name = "生产订单",
                            ParentId = 2,
                            Path = "/home/<USER>/order",
                            SortOrder = 2,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 14,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "production-schedule",
                            Name = "排产管理",
                            ParentId = 2,
                            Path = "/home/<USER>/schedule",
                            SortOrder = 3,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 15,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "engineering-process",
                            Name = "工艺管理",
                            ParentId = 3,
                            Path = "/home/<USER>/process",
                            SortOrder = 1,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 16,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "engineering-bom",
                            Name = "BOM管理",
                            ParentId = 3,
                            Path = "/home/<USER>/bom",
                            SortOrder = 2,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 17,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "engineering-model",
                            Name = "型号管理",
                            ParentId = 3,
                            Path = "/home/<USER>/models",
                            SortOrder = 3,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 18,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "production-monitor",
                            Name = "生产监控",
                            ParentId = 4,
                            Path = "/home/<USER>/monitor",
                            SortOrder = 1,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 19,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "production-task",
                            Name = "生产任务",
                            ParentId = 4,
                            Path = "/home/<USER>/task",
                            SortOrder = 2,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 20,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "quality-inspection",
                            Name = "质量检验",
                            ParentId = 5,
                            Path = "/home/<USER>/inspection",
                            SortOrder = 1,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 21,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "quality-report",
                            Name = "质量报告",
                            ParentId = 5,
                            Path = "/home/<USER>/report",
                            SortOrder = 2,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 22,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "equipment-list",
                            Name = "设备台账",
                            ParentId = 6,
                            Path = "/home/<USER>/list",
                            SortOrder = 1,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 23,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "system-users",
                            Name = "用户管理",
                            ParentId = 7,
                            Path = "/home/<USER>/users",
                            SortOrder = 1,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 24,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "system-roles",
                            Name = "角色权限",
                            ParentId = 7,
                            Path = "/home/<USER>/roles",
                            SortOrder = 2,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 25,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "system-menus",
                            Name = "菜单管理",
                            ParentId = 7,
                            Path = "/home/<USER>/menus",
                            SortOrder = 3,
                            Status = true,
                            Type = "menu-item"
                        });
                });

            modelBuilder.Entity("mes_system_server.Models.Position", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Level")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.ToTable("Positions");
                });

            modelBuilder.Entity("mes_system_server.Models.ProductionLine", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("ProductionLines");
                });

            modelBuilder.Entity("mes_system_server.Models.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("RoleCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("RoleCode")
                        .IsUnique();

                    b.ToTable("Roles");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "系统管理员，拥有所有权限",
                            RoleCode = "admin",
                            RoleName = "系统管理员",
                            Status = true
                        },
                        new
                        {
                            Id = 2,
                            CreateTime = new DateTime(2024, 1, 2, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责生产管理相关工作",
                            RoleCode = "production_manager",
                            RoleName = "生产经理",
                            Status = true
                        },
                        new
                        {
                            Id = 3,
                            CreateTime = new DateTime(2024, 1, 3, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责产品质量检验工作",
                            RoleCode = "quality_inspector",
                            RoleName = "质量检验员",
                            Status = true
                        });
                });

            modelBuilder.Entity("mes_system_server.Models.RoleMenu", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("MenuId")
                        .HasColumnType("int");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MenuId");

                    b.HasIndex("RoleId", "MenuId")
                        .IsUnique();

                    b.ToTable("RoleMenus");
                });

            modelBuilder.Entity("mes_system_server.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("RoleId")
                        .HasColumnType("int");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasFilter("[Email] IS NOT NULL");

                    b.HasIndex("RoleId");

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("mes_system_server.Models.WisPDFBookmark", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FilteredBookmarkName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ImageBase64")
                        .HasColumnType("text");

                    b.Property<int?>("ImageHeight")
                        .HasColumnType("int");

                    b.Property<string>("ImagePath")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<long?>("ImageSize")
                        .HasColumnType("bigint");

                    b.Property<int?>("ImageWidth")
                        .HasColumnType("int");

                    b.Property<string>("OriginalBookmarkName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("PageNumber")
                        .HasColumnType("int");

                    b.Property<string>("Remarks")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("FileName")
                        .HasDatabaseName("IX_WisPDFBookmarks_FileName");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_WisPDFBookmarks_Status");

                    b.HasIndex("CreateTime", "Status")
                        .HasDatabaseName("IX_WisPDFBookmarks_CreateTime_Status");

                    b.HasIndex("OriginalBookmarkName", "Status")
                        .HasDatabaseName("IX_WisPDFBookmarks_OriginalBookmarkName_Status");

                    b.HasIndex("FileName", "PageNumber", "Status")
                        .HasDatabaseName("IX_WisPDFBookmarks_FileName_PageNumber_Status");

                    b.HasIndex("FileName", "Status", "PageNumber")
                        .HasDatabaseName("IX_WisPDFBookmarks_FileName_Status_PageNumber");

                    b.ToTable("WisPDFBookmarks");
                });

            modelBuilder.Entity("mes_system_server.Models.WorkCenter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("ProductionLineId")
                        .HasColumnType("int");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ProductionLineId", "Code")
                        .IsUnique();

                    b.ToTable("WorkCenters");
                });

            modelBuilder.Entity("mes_system_server.Models.Department", b =>
                {
                    b.HasOne("mes_system_server.Models.Department", "ParentDepartment")
                        .WithMany()
                        .HasForeignKey("ParentId");

                    b.Navigation("ParentDepartment");
                });

            modelBuilder.Entity("mes_system_server.Models.Employee", b =>
                {
                    b.HasOne("mes_system_server.Models.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("mes_system_server.Models.Equipment", b =>
                {
                    b.HasOne("mes_system_server.Models.WorkCenter", "WorkCenter")
                        .WithMany("Equipment")
                        .HasForeignKey("WorkCenterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkCenter");
                });

            modelBuilder.Entity("mes_system_server.Models.Menu", b =>
                {
                    b.HasOne("mes_system_server.Models.Menu", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("mes_system_server.Models.Position", b =>
                {
                    b.HasOne("mes_system_server.Models.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("mes_system_server.Models.RoleMenu", b =>
                {
                    b.HasOne("mes_system_server.Models.Menu", "Menu")
                        .WithMany("RoleMenus")
                        .HasForeignKey("MenuId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("mes_system_server.Models.Role", "Role")
                        .WithMany("RoleMenus")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Menu");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("mes_system_server.Models.User", b =>
                {
                    b.HasOne("mes_system_server.Models.Role", "UserRole")
                        .WithMany("Users")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("UserRole");
                });

            modelBuilder.Entity("mes_system_server.Models.WorkCenter", b =>
                {
                    b.HasOne("mes_system_server.Models.ProductionLine", "ProductionLine")
                        .WithMany("WorkCenters")
                        .HasForeignKey("ProductionLineId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProductionLine");
                });

            modelBuilder.Entity("mes_system_server.Models.Menu", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("RoleMenus");
                });

            modelBuilder.Entity("mes_system_server.Models.ProductionLine", b =>
                {
                    b.Navigation("WorkCenters");
                });

            modelBuilder.Entity("mes_system_server.Models.Role", b =>
                {
                    b.Navigation("RoleMenus");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("mes_system_server.Models.WorkCenter", b =>
                {
                    b.Navigation("Equipment");
                });
#pragma warning restore 612, 618
        }
    }
}
