import request from '@/utils/request'

/**
 * 数据字典相关API
 */

// API 请求函数
const api = {
  // 获取字典列表
  getDictionaries: (params) => {
    const requestParams = {}
    if (params.name) requestParams.Name = params.name
    if (params.code) requestParams.Code = params.code
    if (params.type) requestParams.Type = params.type
    if (params.status !== null && params.status !== undefined && params.status !== '') {
      requestParams.Status = params.status
    }
    requestParams.Page = params.page || 1
    requestParams.PageSize = params.pageSize || 100
    
    return request({
      url: '/api/datadictionary',
      method: 'get',
      params: requestParams
    })
  },

  // 获取字典详情
  getDictionary: (id) => {
    return request({
      url: `/api/datadictionary/${id}`,
      method: 'get'
    })
  },

  // 根据字典编码获取字典
  getDictionaryByCode: (code) => {
    return request({
      url: '/api/datadictionary',
      method: 'get',
      params: {
        Code: code,
        Page: 1,
        PageSize: 1
      }
    })
  },

  // 获取字典项列表
  getDictionaryItems: (dictId, params = {}) => {
    return request({
      url: `/api/datadictionary/${dictId}/items`,
      method: 'get',
      params: {
        Page: params.page || 1,
        PageSize: params.pageSize || 100
      }
    })
  },

  // 根据字典编码获取字典项
  getDictionaryItemsByCode: (dictCode, params = {}) => {
    return new Promise(async (resolve, reject) => {
      try {
        // 首先根据编码获取字典
        const dictResponse = await api.getDictionaryByCode(dictCode)
        
        if (dictResponse.data.code === 200 && dictResponse.data.data.items && dictResponse.data.data.items.length > 0) {
          const dictionary = dictResponse.data.data.items[0]
          
          // 然后获取字典项
          const itemsResponse = await api.getDictionaryItems(dictionary.id, params)
          resolve(itemsResponse)
        } else {
          reject(new Error(`未找到编码为 ${dictCode} 的字典`))
        }
      } catch (error) {
        reject(error)
      }
    })
  }
}

/**
 * 获取物料分类字典项
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回物料分类字典项列表
 */
export const getMaterialCategories = async (params = {}) => {
  try {
    // 直接调用指定的API端点获取物料分类
    const response = await request({
      url: '/api/datadictionary/5/items',
      method: 'get',
      params: {
        DictId: 1,
        Status: true,
        Page: 1,
        PageSize: 10
      }
    })
    
    if (response && response.data && response.data.code === 200) {
      const items = response.data.data?.items || []
      if (items.length > 0) {
        // 转换为前端需要的格式
        const categories = items.filter(item => item.status).map(item => ({
          value: item.value,
          label: item.label,
          code: item.value,
          name: item.label,
          description: item.description,
          sort: item.sort || 0,
          extProperty1: item.extProperty1,
          extProperty2: item.extProperty2,
          extProperty3: item.extProperty3
        })).sort((a, b) => a.sort - b.sort)
        
        console.info(`成功从后端加载 ${categories.length} 个物料分类:`, categories.map(c => c.label).join(', '))
        return categories
      }
    }
    
    // 如果没有找到数据，返回默认分类
    console.warn('后端API返回空数据，使用默认分类')
    return getDefaultMaterialCategories()
  } catch (error) {
    // 如果API调用失败，使用默认分类
    console.warn('获取物料分类字典项失败，使用默认分类:', error.message)
    return getDefaultMaterialCategories()
  }
}

/**
 * 获取默认的物料分类（作为后备方案）
 * @returns {Array} - 默认物料分类列表
 */
export const getDefaultMaterialCategories = () => {
  return [
    {
      value: 'REF_FG',
      label: '成品型号',
      code: 'REF_FG',
      name: '成品型号',
      description: '最终制成品',
      sort: 1
    },
    {
      value: 'REF_SUB',
      label: '组件型号',
      code: 'REF_SUB', 
      name: '组件型号',
      description: '产品组成部件',
      sort: 2
    },
    {
      value: 'REF_COMP',
      label: '零件型号',
      code: 'REF_COMP',
      name: '零件型号', 
      description: '最小粒度零件',
      sort: 3
    },
    {
      value: 'REF_ACC',
      label: '辅料包材型号',
      code: 'REF_ACC',
      name: '辅料包材型号',
      description: '辅助和包装材料',
      sort: 4
    },
    {
      value: 'REF_MET',
      label: '塑胶五金型号',
      code: 'REF_MET',
      name: '塑胶五金型号',
      description: '塑胶五金制品',
      sort: 5
    },
    {
      value: 'REF_ADMIN',
      label: '行政物料',
      code: 'REF_ADMIN',
      name: '行政物料',
      description: '行政办公物料',
      sort: 6
    }
  ]
}

/**
 * 获取特定分类的API配置
 * @param {string} categoryCode - 分类编码
 * @returns {Object} - API配置对象
 */
export const getMaterialCategoryApi = (categoryCode) => {
  // 如果是成品型号，使用真实的API
  if (categoryCode === 'REF_FG' || categoryCode === 'products') {
    return {
      getList: (params) => {
        return request({
          url: '/api/models/products',
          method: 'get',
          params: {
            page: params.page || 1,
            pageSize: params.pageSize || 12,
            search: params.search || ''
          }
        })
      },
      create: (data) => {
        return request({
          url: '/api/models/products',
          method: 'post',
          data
        })
      },
      update: (id, data) => {
        return request({
          url: `/api/models/products/${id}`,
          method: 'put',
          data
        })
      },
      delete: (id) => {
        return request({
          url: `/api/models/products/${id}`,
          method: 'delete'
        })
      },
      getDetail: (id) => {
        return request({
          url: `/api/models/products/${id}`,
          method: 'get'
        })
      }
    }
  }
  
  // 其他类型使用模拟API
  const baseApi = {
    getList: (params) => {
      // 返回模拟数据的Promise
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            data: {
              list: [],
              total: 0
            }
          })
        }, 100) // 模拟网络延迟
      })
    },
    create: (data) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ data: { code: 200, message: '创建成功' } })
        }, 100)
      })
    },
    update: (id, data) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ data: { code: 200, message: '更新成功' } })
        }, 100)
      })
    },
    delete: (id) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ data: { code: 200, message: '删除成功' } })
        }, 100)
      })
    },
    getDetail: (id) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ data: { code: 200, data: {} } })
        }, 100)
      })
    }
  }
  
  return baseApi
}

// 导出完整的API对象
export default api 