using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace mes_system_server.DTOs
{
    public class WorkCenterDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int ProductionLineId { get; set; }
        public int SortOrder { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime UpdateTime { get; set; }
        public List<EquipmentDto> Equipment { get; set; } = new();
    }

    public class CreateWorkCenterDto
    {
        [Required(ErrorMessage = "工作中心名称不能为空")]
        [StringLength(100, ErrorMessage = "工作中心名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "工作中心编码不能为空")]
        [StringLength(50, ErrorMessage = "工作中心编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }

        public int SortOrder { get; set; } = 0;
    }

    public class UpdateWorkCenterDto
    {
        [Required(ErrorMessage = "工作中心名称不能为空")]
        [StringLength(100, ErrorMessage = "工作中心名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "工作中心编码不能为空")]
        [StringLength(50, ErrorMessage = "工作中心编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }

        public int SortOrder { get; set; } = 0;

        public bool IsActive { get; set; } = true;
    }

    public class WorkCenterResponseDto
    {
        public int Total { get; set; }
        public List<WorkCenterDto> List { get; set; } = new();
    }

    public class BatchSaveWorkCenterDto
    {
        public List<BatchWorkCenterItemDto> WorkCenters { get; set; } = new();
    }

    public class BatchWorkCenterItemDto
    {
        public int? Id { get; set; } // null表示新增
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int SortOrder { get; set; }
        public List<BatchEquipmentItemDto> Equipment { get; set; } = new();
    }
} 