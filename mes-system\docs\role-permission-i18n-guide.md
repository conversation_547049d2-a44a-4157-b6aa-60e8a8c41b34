# 角色权限管理页面多语言功能使用指南

## 功能概述

角色权限管理页面现已完全支持中英文多语言切换，包括三个主要标签页的所有界面文本、按钮标题、表格列标题、对话框内容、验证消息、提示信息等。

## 支持的多语言内容

### 1. 页面标签页
| 功能区域 | 中文显示 | 英文显示 |
|---------|---------|---------|
| 角色定义 | 角色定义 | Role Definition |
| 角色用户设置 | 角色用户设置 | Role Users |
| 角色菜单设置 | 角色菜单设置 | Role Menus |

### 2. 角色定义页面

#### 搜索和操作
| 中文内容 | 英文内容 |
|---------|---------|
| 请输入角色名称 | Please enter role name |
| 搜索 | Search |
| 添加角色 | Add Role |

#### 表格列标题
| 中文列名 | 英文列名 |
|---------|---------|
| 角色名称 | Role Name |
| 角色编码 | Role Code |
| 角色描述 | Role Description |
| 状态 | Status |
| 创建时间 | Create Time |
| 操作 | Operation |

#### 状态显示
| 中文状态 | 英文状态 |
|---------|---------|
| 启用 | Active |
| 禁用 | Inactive |

#### 操作按钮
| 中文按钮 | 英文按钮 |
|---------|---------|
| 编辑 | Edit |
| 启用 | Enable |
| 禁用 | Disable |
| 删除 | Delete |

#### 表单对话框
| 中文内容 | 英文内容 |
|---------|---------|
| 新增角色 | Add Role |
| 编辑角色 | Edit Role |
| 角色名称 | Role Name |
| 角色编码 | Role Code |
| 角色描述 | Role Description |
| 状态 | Status |
| 启用 | Active |
| 禁用 | Inactive |
| 确定 | Confirm |
| 取消 | Cancel |

### 3. 角色用户设置页面

#### 权限列表
| 中文权限 | 英文权限 |
|---------|---------|
| 新增 | Add |
| 编辑 | Edit |
| 删除 | Delete |
| 审核 | Verify |
| 导入 | Import |
| 导出 | Export |
| 查询 | Query |
| 报表预览 | Report Preview |

#### 用户信息表格
| 中文列名 | 英文列名 |
|---------|---------|
| 角色关联的用户信息表 | Role Associated Users |
| 用户名 | Username |
| 姓名 | Name |
| 部门 | Department |
| 职称 | Position |
| 邮箱 | Email |
| 电话 | Phone |

### 4. 成功提示消息
| 操作结果 | 中文提示 | 英文提示 |
|---------|---------|---------|
| 添加角色成功 | 添加角色成功 | Role added successfully |
| 更新角色成功 | 更新角色成功 | Role updated successfully |
| 删除角色成功 | 删除角色成功 | Role deleted successfully |
| 启用角色成功 | 启用角色成功 | Role enabled successfully |
| 禁用角色成功 | 禁用角色成功 | Role disabled successfully |

## 使用方法

### 1. 访问角色权限管理页面
1. 登录MES系统
2. 在左侧导航栏中点击"系统管理"
3. 选择"角色权限"子菜单

### 2. 切换语言
1. 点击页面右上角的语言切换按钮
2. 选择"English"切换到英文界面
3. 选择"中文"切换回中文界面

### 3. 验证多语言功能
**中文状态验证：**
- 确认页面显示"角色权限"标题
- 确认表格列标题显示中文
- 确认按钮显示中文文字
- 确认表单验证消息为中文

**英文状态验证：**
- 确认页面显示"Role Management"标题
- 确认表格列标题显示英文
- 确认按钮显示英文文字
- 确认表单验证消息为英文

## 技术实现

### 1. i18n配置扩展
在 `src/i18n/index.js` 中添加了完整的角色权限管理模块翻译：
```javascript
zh: {
  role: {
    management: '角色权限',
    roleDefinition: '角色定义',
    // ... 更多翻译
  }
},
en: {
  role: {
    management: 'Role Management',
    roleDefinition: 'Role Definition',
    // ... 更多翻译
  }
}
```

### 2. 组件翻译集成
- 导入 `useI18n` 组合式函数
- 使用 `t()` 函数进行文本翻译
- 使用 `computed` 确保动态验证规则的响应性

通过以上配置，角色权限管理页面现在完全支持中英文多语言切换，为用户提供了完整的国际化体验！ 