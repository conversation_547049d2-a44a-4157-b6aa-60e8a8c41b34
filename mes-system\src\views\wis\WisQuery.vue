<template>
  <div class="wis-pdf-viewer">
    <div class="page-header">
      <h2 class="page-title">WIS查询</h2>
      <div class="page-actions">
        <!-- 移除了选择PDF文件按钮 -->
      </div>
    </div>

    <div class="pdf-content">
      <div class="bookmark-panel">
        <div class="panel-header">
          <div class="panel-title-section">
            <h3>PDF书签查询</h3>
            <!-- 查询区域 -->
            <div class="search-section">
              <!-- 新增：PDF文件选择下拉框 -->
              <el-select
                v-model="selectedPdfFile"
                placeholder="请选择PDF文件"
                size="small"
                clearable
                filterable
                @change="handlePdfFileChange"
                style="width: 100%; margin-bottom: 8px;"
                :loading="loadingPdfList"
              >
                <el-option
                  v-for="pdf in pdfFileList"
                  :key="pdf.fileName"
                  :label="pdf.fileName"
                  :value="pdf.fileName"
                >
                  <div class="pdf-option">
                    <span class="pdf-name">{{ pdf.fileName }}</span>
                    <span class="pdf-info">
                      {{ pdf.bookmarkCount }}个书签 | {{ formatFileSize(pdf.totalSize) }}
                    </span>
                  </div>
                </el-option>
              </el-select>
              
              <!-- 保留原有的手动输入框（可选） -->
              <div class="manual-input-toggle" v-if="showManualInput">
                <el-input
                  v-model="searchKeyword"
                  placeholder="或手动输入PDF文件名"
                  size="small"
                  clearable
                  @keyup.enter="handleSearch"
                  style="margin-bottom: 8px;"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
              
              <div class="search-buttons">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="handleSearch"
                  :loading="searching"
                  :icon="Search"
                  :disabled="!selectedPdfFile && !searchKeyword"
                >
                  {{ searching ? '查询中...' : '查询' }}
                </el-button>
                <el-button 
                  size="small" 
                  @click="handleReset"
                  :icon="RefreshLeft"
                >
                  重置
                </el-button>
                <el-button 
                  size="small" 
                  @click="toggleManualInput"
                  :icon="Edit"
                  type="info"
                  plain
                >
                  {{ showManualInput ? '隐藏' : '手动输入' }}
                </el-button>
              </div>
            </div>
            <!-- 查询结果信息 -->
            <div class="query-info" v-if="bookmarks.length > 0">
              <el-text type="info" size="small">
                找到 {{ filteredBookmarks.length }} 条结果 / 共 {{ bookmarks.length }} 条记录
              </el-text>
              <div v-if="hasMore" class="load-more-section">
                <el-button 
                  size="small" 
                  @click="loadMoreData"
                  :loading="loadingMore"
                  type="primary"
                  plain
                >
                  {{ loadingMore ? '加载中...' : '加载更多' }}
                </el-button>
              </div>
            </div>
            <el-text type="info" size="small" v-else>请选择或输入PDF文件名查询书签</el-text>
          </div>
        </div>
        <el-scrollbar class="bookmark-list">
          <div v-if="filteredBookmarks.length">
            <div
              v-for="bookmark in filteredBookmarks"
              :key="bookmark.id"
              :class="['bookmark-item', { active: activeBookmark === bookmark.id }]"
              @click="handleBookmarkSelect(bookmark.id)"
            >
              <el-icon><Document /></el-icon>
              <span class="bookmark-title">{{ bookmark.title }}</span>
              <el-tag size="small" type="info">第{{ bookmark.pageNumber }}页</el-tag>
            </div>
          </div>
          <div v-else-if="bookmarks.length > 0" class="empty-bookmarks">
            <el-empty description="未找到匹配的书签" :image-size="80" />
          </div>
          <div v-else class="empty-bookmarks">
            <el-empty description="请输入PDF文件名查询书签" :image-size="80" />
          </div>
        </el-scrollbar>
      </div>

      <div class="image-panel">
        <div class="panel-header">
          <div class="image-header-section">
            <h3>页面预览</h3>
            <!-- 新增：图片加载方式选择 -->
            <div class="image-source-section">
              <el-text size="small" type="info">图片来源：</el-text>
              <el-radio-group v-model="imageLoadMode" size="small" @change="handleImageModeChange">
                <el-radio value="api">数据库API</el-radio>
                <el-radio value="cloud">云服务器文件</el-radio>
              </el-radio-group>
            </div>
          </div>
          <div class="image-actions" v-if="currentImage">
            <el-button-group>
              <el-button size="small" @click="zoomOut" :disabled="zoomLevel <= 0.5">
                <el-icon><ZoomOut /></el-icon>
              </el-button>
              <el-button size="small" @click="resetZoom">
                {{ Math.round(zoomLevel * 100) }}%
              </el-button>
              <el-button size="small" @click="zoomIn" :disabled="zoomLevel >= 3">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
            </el-button-group>
            <el-button size="small" @click="downloadCurrentImage" :icon="Download">
              下载图片
            </el-button>
          </div>
        </div>
        <el-scrollbar class="image-display">
          <div v-if="imageLoading" class="loading-container">
            <el-loading-spinner />
            <p>{{ imageLoadMode === 'cloud' ? '从云服务器加载图片中...' : '加载图片中...' }}</p>
          </div>
          <el-image
            v-else-if="currentImage"
            :src="currentImage"
            fit="contain"
            :style="{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }"
            @load="handleImageLoad"
            @error="handleImageError"
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
                <p>图片加载失败</p>
                <p v-if="imageLoadMode === 'cloud'" class="error-tip">
                  云服务器文件可能不存在，尝试切换到数据库API方式
                </p>
              </div>
            </template>
          </el-image>
          <div v-else class="no-image">
            <el-icon><Picture /></el-icon>
            <p v-if="bookmarks.length">请选择左侧书签查看对应页面</p>
            <p v-else>请先选择或输入PDF文件名查询书签</p>
          </div>
        </el-scrollbar>
      </div>
    </div>

    <!-- 处理进度对话框 -->
    <el-dialog
      v-model="showProgress"
      title="PDF处理进度"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="progress-content">
        <el-progress
          :percentage="progressPercentage"
          :status="progressStatus"
          :stroke-width="8"
        />
        <p class="progress-text">{{ progressText }}</p>
        <div class="progress-details">
          <el-text size="small" type="info">
            {{ progressDetails }}
          </el-text>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Upload,
  Delete,
  Document,
  ZoomOut,
  ZoomIn,
  Download,
  Picture,
  Search,
  RefreshLeft,
  Edit
} from '@element-plus/icons-vue'
import { uploadPDFImage, savePDFBookmarks, batchUploadPDFImagesWithConcurrency, batchUploadPDFImagesToCloud, queryPDFBookmarks, getPDFImage, getPDFBookmarksByFileName, getPDFImageOnDemand, getPDFInfo, getPDFList } from '@/api/wis'

// 响应式数据
const uploading = ref(false)
const uploadingImages = ref(false)
const savingBookmarks = ref(false)
const bookmarks = ref([])
const filteredBookmarks = ref([])
const activeBookmark = ref('')
const currentImage = ref('')
const imageLoading = ref(false)
const zoomLevel = ref(1)
const showProgress = ref(false)
const progressPercentage = ref(0)
const progressStatus = ref('')
const progressText = ref('')
const progressDetails = ref('')

// 查询相关数据
const searchKeyword = ref('')
const searching = ref(false)
const currentPage = ref(1)
const pageSize = ref(50)
const totalPages = ref(0)
const totalCount = ref(0)
const loadingMore = ref(false)
const hasMore = ref(false)

// 新增：PDF文件选择相关
const pdfFileList = ref([])  // PDF文件列表
const selectedPdfFile = ref('')  // 选中的PDF文件
const loadingPdfList = ref(false)  // 加载PDF列表状态
const showManualInput = ref(false)  // 是否显示手动输入框

// 查询模式不需要这些变量，保留作为兼容性
const processedPDFs = ref([])  
const currentPDFIndex = ref(-1)  
const uploadKey = ref(0)  
let pdfDoc = null
let currentPDFName = ''

// 图片缓存
const imageCache = ref(new Map())

// 图片加载方式
const imageLoadMode = ref('api')

// 新增：过滤书签名称，移除特殊字符，用于构建文件路径
const filterBookmarkNameForPath = (bookmarkName) => {
  if (!bookmarkName) return ''
  // 移除空格、冒号、斜杠等可能导致文件系统问题的字符
  return bookmarkName.replace(/[\s:：\/\\<>"|*?]/g, '')
}

// 新增：构建云服务器图片路径
const buildCloudImagePath = (bookmark) => {
  if (!bookmark || !bookmark.fileName || !bookmark.pageNumber) {
    return null
  }
  
  // 获取过滤后的书签名称
  const filteredBookmarkName = filterBookmarkNameForPath(bookmark.originalBookmarkTitle || bookmark.title || `第${bookmark.pageNumber}页`)
  
  // 构建图片文件路径
  // 格式: /uploads/wis-pdf-images/[PDF文件名]/[过滤后书签名]_第[页码]页.jpg
  const imagePath = `/uploads/wis-pdf-images/${bookmark.fileName}/${filteredBookmarkName}_第${bookmark.pageNumber}页.jpg`
  
  // 获取服务器基础URL
  const baseURL = 'http://111.230.239.197:5221' // 从request.js中获取的基础URL
  
  return `${baseURL}${imagePath}`
}

// 新增：从云服务器加载图片
const loadImageFromCloud = async (bookmark) => {
  const cloudImageUrl = buildCloudImagePath(bookmark)
  
  if (!cloudImageUrl) {
    throw new Error('无法构建云服务器图片路径')
  }
  
  console.log(`尝试从云服务器加载图片: ${cloudImageUrl}`)
  
  return new Promise((resolve, reject) => {
    const img = new Image()
    
    img.onload = () => {
      console.log(`云服务器图片加载成功: ${cloudImageUrl}`)
      resolve(cloudImageUrl)
    }
    
    img.onerror = () => {
      console.warn(`云服务器图片加载失败: ${cloudImageUrl}`)
      reject(new Error(`云服务器图片文件不存在: ${cloudImageUrl}`))
    }
    
    // 设置图片源，触发加载
    img.src = cloudImageUrl
  })
}

// 动态导入PDF.js
const loadPDFJS = async () => {
  try {
    // 使用CDN方式加载PDF.js
    if (!window.pdfjsLib) {
      const script = document.createElement('script')
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js'
      document.head.appendChild(script)
      
      await new Promise((resolve, reject) => {
        script.onload = resolve
        script.onerror = reject
      })
      
      // 设置worker
      window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
    }
    return window.pdfjsLib
  } catch (error) {
    console.error('加载PDF.js失败:', error)
    throw new Error('PDF.js加载失败，请检查网络连接')
  }
}

// 辅助函数：构建outline到页面的映射关系
const buildOutlinePageMapping = async (pdfDocument, outlineItems) => {
  const mapping = new Map() // pageNumber -> outlineItem
  
  const processOutlineItems = async (items) => {
    for (const item of items) {
      try {
        if (item.dest) {
          let pageNumber = null
          
          // 处理不同类型的destination
          if (typeof item.dest === 'string') {
            // 如果dest是字符串，需要通过getDestination解析
            const dest = await pdfDocument.getDestination(item.dest)
            if (dest && dest[0]) {
              const pageRef = dest[0]
              pageNumber = await pdfDocument.getPageIndex(pageRef) + 1
            }
          } else if (Array.isArray(item.dest) && item.dest[0]) {
            // 如果dest已经是数组格式
            const pageRef = item.dest[0]
            pageNumber = await pdfDocument.getPageIndex(pageRef) + 1
          }
          
                     if (pageNumber && pageNumber > 0) {
             // 如果该页面还没有映射，或者当前项的标题更具体，则更新映射
             if (!mapping.has(pageNumber) || item.title.length > mapping.get(pageNumber).title.length) {
               mapping.set(pageNumber, item)
               console.log(`    映射: 第${pageNumber}页 -> "${item.title}"`)
             }
           } else {
             console.log(`    无法解析页面号: dest=${JSON.stringify(item.dest)}, title="${item.title}"`)
           }
        }
        
        // 递归处理子项
        if (item.items && item.items.length > 0) {
          await processOutlineItems(item.items)
        }
      } catch (error) {
        console.warn(`处理outline项失败: "${item.title}"`, error)
      }
    }
  }
  
  await processOutlineItems(outlineItems)
  return mapping
}

// 辅助函数：查找指定页面对应的outline项
const findOutlineItemForPage = async (pdfDocument, outlineItems, targetPageNumber, pageMapping) => {
  // 如果有预构建的映射，直接使用
  if (pageMapping && pageMapping.has(targetPageNumber)) {
    return pageMapping.get(targetPageNumber)
  }
  
  // 备用方案：遍历查找
  for (const item of outlineItems) {
    try {
      if (item.dest) {
        let pageNumber = null
        
        if (typeof item.dest === 'string') {
          const dest = await pdfDocument.getDestination(item.dest)
          if (dest && dest[0]) {
            const pageRef = dest[0]
            pageNumber = await pdfDocument.getPageIndex(pageRef) + 1
          }
        } else if (Array.isArray(item.dest) && item.dest[0]) {
          const pageRef = item.dest[0]
          pageNumber = await pdfDocument.getPageIndex(pageRef) + 1
        }
        
        if (pageNumber === targetPageNumber) {
          return item
        }
      }
      
      // 递归查找子项
      if (item.items && item.items.length > 0) {
        const found = await findOutlineItemForPage(pdfDocument, item.items, targetPageNumber, pageMapping)
        if (found) return found
      }
    } catch (error) {
      // 忽略单个项的错误，继续处理其他项
    }
  }
  return null
}

// 用于防止重复处理的变量
let processingTimeout = null

// 处理文件选择事件
const handlePDFChange = (file, fileList) => {
  // 清除之前的超时
  if (processingTimeout) {
    clearTimeout(processingTimeout)
  }
  
  console.log('文件选择事件:', {
    currentFile: file.name,
    totalFiles: fileList.length,
    fileList: fileList.map(f => f.name)
  })
  
  // 延迟处理，避免多次快速选择文件时重复处理
  processingTimeout = setTimeout(() => {
    handlePDFUpload(fileList)
  }, 300) // 300ms 防抖
}

// 处理PDF上传（支持多文件）
const handlePDFUpload = async (fileList) => {
  // 防止重复处理
  if (uploading.value) {
    console.log('正在处理中，跳过重复请求')
    return
  }
  
  if (!fileList || fileList.length === 0) {
    ElMessage.error('请选择有效的PDF文件')
    return
  }
  
  console.log('开始处理PDF文件:', fileList.map(f => f.name))
  
  // 验证文件
  const validFiles = []
  for (const fileItem of fileList) {
    const targetFile = fileItem.raw || fileItem
    
    if (!targetFile) {
      continue
    }

    // 检查文件类型
    if (targetFile.type !== 'application/pdf') {
      ElMessage.warning(`文件 ${targetFile.name} 不是PDF格式，已跳过`)
      continue
    }

    // 检查文件大小（限制50MB）
    const maxSize = 50 * 1024 * 1024
    if (targetFile.size > maxSize) {
      ElMessage.warning(`文件 ${targetFile.name} 大小超过50MB，已跳过`)
      continue
    }

    // 检查是否已经处理过
    const fileName = targetFile.name.replace('.pdf', '')
    const existingPDF = processedPDFs.value.find(pdf => pdf.name === fileName)
    if (existingPDF) {
      console.log(`文件 ${targetFile.name} 已经处理过，跳过处理`)
      ElMessage.warning(`文件 ${targetFile.name} 已经处理过，已跳过`)
      continue
    }

    // 检查当前批次中是否有重复文件
    const duplicateInBatch = validFiles.find(f => {
      const existingFileName = f.name.replace('.pdf', '')
      return existingFileName === fileName
    })
    
    if (duplicateInBatch) {
      console.log(`文件 ${targetFile.name} 在当前批次中重复，跳过处理`)
      ElMessage.warning(`文件 ${targetFile.name} 重复选择，已跳过`)
      continue
    }

    console.log(`添加文件到处理队列: ${targetFile.name}`)
    validFiles.push(targetFile)
  }

  console.log(`验证后的有效文件数量: ${validFiles.length}`)
  console.log('有效文件列表:', validFiles.map(f => f.name))
  
  if (validFiles.length === 0) {
    ElMessage.error('没有有效的PDF文件可以处理')
    return
  }

  try {
    uploading.value = true
    showProgress.value = true
    progressPercentage.value = 0
    progressStatus.value = ''
    progressText.value = '正在加载PDF.js...'
    progressDetails.value = '初始化PDF处理环境'

    // 加载PDF.js
    const pdfjsLib = await loadPDFJS()
    
    let totalProgress = 0
    const progressPerFile = 100 / validFiles.length

    // 处理每个PDF文件
    for (let i = 0; i < validFiles.length; i++) {
      const targetFile = validFiles[i]
      const fileName = targetFile.name.replace('.pdf', '')
      
      progressText.value = `正在处理文件 ${i + 1}/${validFiles.length}: ${targetFile.name}`
      progressDetails.value = `文件大小: ${(targetFile.size / 1024 / 1024).toFixed(2)}MB`
      
      try {
        // 读取文件
        const arrayBuffer = await targetFile.arrayBuffer()
        
        // 解析PDF
        const pdfDocument = await pdfjsLib.getDocument(arrayBuffer).promise
        
        progressDetails.value = `PDF共有 ${pdfDocument.numPages} 页`

        // 生成书签
        const outline = await pdfDocument.getOutline()
        const generatedBookmarks = []
        
        console.log(`${fileName} - PDF Outline信息:`, outline ? `共${outline.length}个outline项` : '无outline')
        
        // 过滤书签名中的空格和冒号
        const filterBookmarkTitle = (title) => {
          return title.replace(/[\s:：]/g, '')
        }
        
        let pageMapping = null
        
        // 如果有outline，构建页面映射关系
        if (outline && outline.length > 0) {
          console.log(`  开始构建outline映射关系...`)
          outline.forEach((item, index) => {
            console.log(`  Outline ${index + 1}: "${item.title}"`)
          })
          
          try {
            pageMapping = await buildOutlinePageMapping(pdfDocument, outline)
            console.log(`  成功构建映射关系，共${pageMapping.size}个页面有对应的书签`)
          } catch (error) {
            console.warn(`  构建映射关系失败:`, error)
          }
        }
        
        // 修复：始终按照PDF实际页数生成书签，确保1:1对应
        for (let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++) {
          let originalBookmarkTitle = `第${pageNum}页` // 默认标题（原始）
          
          // 如果有outline，尝试找到对应页面的书签标题
          if (outline && outline.length > 0) {
            try {
              const matchingOutlineItem = await findOutlineItemForPage(pdfDocument, outline, pageNum, pageMapping)
              if (matchingOutlineItem) {
                originalBookmarkTitle = matchingOutlineItem.title || originalBookmarkTitle
                console.log(`  第${pageNum}页找到匹配的outline: "${matchingOutlineItem.title}"`)
              } else {
                console.log(`  第${pageNum}页未找到匹配的outline，使用默认标题`)
              }
            } catch (error) {
              console.warn(`查找第${pageNum}页书签失败:`, error)
            }
          }
          
          // 过滤书签名称（移除空格和冒号）
          const filteredBookmarkTitle = filterBookmarkTitle(originalBookmarkTitle)
          
          console.log(`  生成书签 - 第${pageNum}页: 原始="${originalBookmarkTitle}" -> 过滤后="${filteredBookmarkTitle}" -> 显示="${fileName}_${filteredBookmarkTitle}"`)
          
          generatedBookmarks.push({
            id: `${fileName}_${pageNum}`,
            title: `${fileName}_${filteredBookmarkTitle}`, // 显示格式：PDF文件名_过滤后的书签名
            originalBookmarkTitle: originalBookmarkTitle,    // 保存原始书签名
            filteredBookmarkTitle: filteredBookmarkTitle,    // 保存过滤后的书签名
            pageNumber: pageNum,
            pdfName: fileName
          })
        }
        
        console.log(`${fileName}: PDF页数=${pdfDocument.numPages}, 生成书签数=${generatedBookmarks.length}`)
        
        // 转换PDF页面为图片
        progressDetails.value = `正在转换 ${fileName} 的页面为图片...`
        const images = await convertSinglePDFToImages(pdfDocument, fileName)
        
        // 保存PDF信息
        const pdfInfo = {
          name: fileName,
          originalName: targetFile.name,
          numPages: pdfDocument.numPages,
          bookmarks: generatedBookmarks,
          images: images,
          fileSize: targetFile.size
        }
        
        processedPDFs.value.push(pdfInfo)
        console.log(`成功处理文件: ${fileName}, 当前已处理文件总数: ${processedPDFs.value.length}`)
        
        // 清理当前PDF文档
        pdfDocument.destroy()
        
        totalProgress += progressPerFile
        progressPercentage.value = Math.round(totalProgress)
        
      } catch (error) {
        console.error(`处理文件 ${targetFile.name} 失败:`, error)
        ElMessage.error(`处理文件 ${targetFile.name} 失败: ${error.message}`)
        totalProgress += progressPerFile
        progressPercentage.value = Math.round(totalProgress)
      }
    }
    
    progressPercentage.value = 100
    progressStatus.value = 'success'
    progressText.value = 'PDF文件处理完成！'
    progressDetails.value = `成功处理 ${processedPDFs.value.length} 个PDF文件`
    
    const totalPages = processedPDFs.value.reduce((sum, pdf) => sum + pdf.numPages, 0)
    const totalBookmarks = processedPDFs.value.reduce((sum, pdf) => sum + pdf.bookmarks.length, 0)
    
    console.log('=== PDF处理完成总结 ===')
    console.log(`输入文件数: ${fileList.length}`)
    console.log(`有效文件数: ${validFiles.length}`)
    console.log(`成功处理数: ${processedPDFs.value.length}`)
    console.log(`总页数: ${totalPages}`)
    console.log(`总书签数: ${totalBookmarks}`)
    console.log('已处理的PDF文件:', processedPDFs.value.map(pdf => `${pdf.originalName}(${pdf.numPages}页/${pdf.bookmarks.length}书签)`))
    console.log('========================')
    
    // 更新当前显示的内容
    if (processedPDFs.value.length > 0) {
      // 显示最新处理的PDF
      const latestPDFIndex = processedPDFs.value.length - 1
      switchToPDF(latestPDFIndex)
    }
    
    // 延迟关闭进度对话框
    setTimeout(() => {
      showProgress.value = false
      const successCount = processedPDFs.value.length
      ElMessage.success(`成功处理 ${successCount} 个PDF文件`)
    }, 1500)

  } catch (error) {
    console.error('PDF批量处理失败:', error)
    progressStatus.value = 'exception'
    progressText.value = 'PDF处理失败'
    progressDetails.value = error.message || '未知错误'
    
    setTimeout(() => {
      showProgress.value = false
      ElMessage.error('PDF处理失败: ' + (error.message || '未知错误'))
    }, 2000)
  } finally {
    uploading.value = false
  }
}

// 转换单个PDF页面为图片
const convertSinglePDFToImages = async (pdfDocument, fileName) => {
  const totalPages = pdfDocument.numPages
  const images = {}
  
  for (let i = 1; i <= totalPages; i++) {
    try {
      const page = await pdfDocument.getPage(i)
      const viewport = page.getViewport({ scale: 2.0 }) // 提高分辨率
      
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      canvas.height = viewport.height
      canvas.width = viewport.width
      
      await page.render({
        canvasContext: context,
        viewport: viewport
      }).promise
      
      // 转换为base64图片
      const imageDataUrl = canvas.toDataURL('image/jpeg', 0.9)
      images[i] = imageDataUrl
      
    } catch (error) {
      console.error(`处理第${i}页失败:`, error)
      throw new Error(`处理第${i}页失败: ${error.message}`)
    }
  }
  
  return images
}

// 切换到指定的PDF
const switchToPDF = (pdfIndex) => {
  if (pdfIndex < 0 || pdfIndex >= processedPDFs.value.length) {
    return
  }
  
  currentPDFIndex.value = pdfIndex
  const pdfInfo = processedPDFs.value[pdfIndex]
  
  // 更新当前PDF相关变量
  currentPDFName = pdfInfo.name
  bookmarks.value = pdfInfo.bookmarks
  
  console.log(`切换到PDF: ${pdfInfo.name}`)
  console.log('当前书签列表:')
  pdfInfo.bookmarks.forEach((bookmark, index) => {
    console.log(`  ${index + 1}. 显示标题: "${bookmark.title}"`)
    console.log(`      原始书签: "${bookmark.originalBookmarkTitle}"`)
    console.log(`      过滤书签: "${bookmark.filteredBookmarkTitle}"`)
    console.log(`      页码: ${bookmark.pageNumber}`)
  })
  
  // 清除当前图片显示
  currentImage.value = ''
  activeBookmark.value = ''
  
  // 自动选择第一页
  if (bookmarks.value.length > 0) {
    handleBookmarkSelect(bookmarks.value[0].id)
  }
}

// 修改：处理书签选择 - 支持多种图片加载方式
const handleBookmarkSelect = async (bookmarkId) => {
  activeBookmark.value = bookmarkId
  const bookmark = bookmarks.value.find(b => b.id === bookmarkId)
  
  if (bookmark) {
    try {
      imageLoading.value = true
      
      // 根据选择的模式加载图片
      const imageData = await loadImageData(bookmark)
      
      if (imageData) {
        currentImage.value = imageData
        console.log(`显示书签图片: ${bookmark.title} (模式: ${imageLoadMode.value})`)
        
        // 如果是云服务器模式，显示额外信息
        if (imageLoadMode.value === 'cloud') {
          console.log(`云服务器图片路径: ${buildCloudImagePath(bookmark)}`)
        }
      } else {
        throw new Error('无法加载图片数据')
      }
      
    } catch (error) {
      console.error('显示图片失败:', error)
      ElMessage.error(`显示图片失败: ${error.message}`)
      currentImage.value = ''
    } finally {
      imageLoading.value = false
    }
  }
}

// 清除PDF
const clearPDF = async () => {
  if (processedPDFs.value.length === 0) {
    ElMessage.warning('没有PDF文件需要清除')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要清除所有PDF文件吗？这将删除${processedPDFs.value.length}个已处理的PDF文件数据。`,
      '确认清除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // 清除超时
    if (processingTimeout) {
      clearTimeout(processingTimeout)
      processingTimeout = null
    }
    
    // 清除所有数据
    processedPDFs.value = []
    currentPDFIndex.value = -1
    bookmarks.value = []
    activeBookmark.value = ''
    currentImage.value = ''
    pdfDoc = null
    currentPDFName = ''
    uploadKey.value++ // 重置上传组件
    
    ElMessage.success('所有PDF文件已清除')
    
  } catch (error) {
    // 用户取消操作
  }
}

// 缩放控制
const zoomIn = () => {
  if (zoomLevel.value < 3) {
    zoomLevel.value = Math.min(3, zoomLevel.value + 0.25)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.25)
  }
}

const resetZoom = () => {
  zoomLevel.value = 1
}

// 下载当前图片
const downloadCurrentImage = () => {
  if (!currentImage.value) {
    ElMessage.warning('没有可下载的图片')
    return
  }
  
  const bookmark = bookmarks.value.find(b => b.id === activeBookmark.value)
  if (bookmark) {
    const link = document.createElement('a')
    link.href = currentImage.value
    link.download = `${bookmark.fileName}_第${bookmark.pageNumber}页.jpg`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('图片下载成功')
  }
}

// 上传图片到云服务器
const uploadImagesToServer = async () => {
  if (processedPDFs.value.length === 0) {
    ElMessage.warning('没有可上传的图片')
    return
  }

  // 计算总图片数量（使用实际的书签数量，确保准确性）
  const totalImages = processedPDFs.value.reduce((sum, pdf) => sum + pdf.bookmarks.length, 0)

  try {
    await ElMessageBox.confirm(
      `确定要将所有PDF文件的${totalImages}张图片上传到云服务器吗？`,
      '确认上传',
      {
        confirmButtonText: '确定上传',
        cancelButtonText: '取消',
        type: 'info',
      }
    )

    uploadingImages.value = true
    showProgress.value = true
    progressPercentage.value = 0
    progressStatus.value = ''
    progressText.value = '正在准备上传图片到云服务器...'
    progressDetails.value = '正在构建上传队列'

    // 构建上传队列
    const uploadQueue = []
    for (const pdfInfo of processedPDFs.value) {
      console.log(`构建上传队列 - ${pdfInfo.name}，页数: ${pdfInfo.numPages}，书签数: ${pdfInfo.bookmarks.length}，图片数: ${Object.keys(pdfInfo.images).length}`)
      
      // 遍历该PDF的所有页面
      for (const [pageNumber, imageDataUrl] of Object.entries(pdfInfo.images)) {
        // 获取对应的书签信息
        const bookmark = pdfInfo.bookmarks.find(b => b.pageNumber === parseInt(pageNumber))
        let bookmarkName = ''
        
        if (bookmark) {
          // 直接使用过滤后的书签名称
          bookmarkName = bookmark.filteredBookmarkTitle || `第${pageNumber}页`
        } else {
          // 如果没有找到书签，使用默认名称
          bookmarkName = `第${pageNumber}页`
        }
        
        // 获取图片尺寸
        const img = new Image()
        img.src = imageDataUrl
        await new Promise((resolve) => {
          img.onload = resolve
        })
        
        // 准备API请求数据，按照后端期望的格式
        const uploadData = {
          fileName: pdfInfo.name,                    // PDF文件名（不含扩展名）
          bookmarkName: bookmarkName,                // 过滤后的书签名称
          pageNumber: parseInt(pageNumber),          // 页码
          imageBase64: imageDataUrl,                 // 图片Base64数据（包含data:image前缀）
          imageWidth: img.width,                     // 图片宽度
          imageHeight: img.height,                   // 图片高度
          remarks: `PDF页面${pageNumber}图片上传`      // 备注
        }
        
        uploadQueue.push(uploadData)
      }
    }

    console.log(`上传队列构建完成，共${uploadQueue.length}个文件`)
    progressDetails.value = `上传队列构建完成，共${uploadQueue.length}个文件`

    // 使用批量上传（控制并发数为2，避免服务器压力过大）
    const uploadResult = await batchUploadPDFImagesWithConcurrency(
      uploadQueue, 
      2, // 并发数量设为2
      (progress) => {
        // 更新进度显示
        progressPercentage.value = progress.percentage
        progressText.value = `正在上传图片 (${progress.completed + progress.failed}/${progress.total})`
        
        if (progress.current) {
          progressDetails.value = `当前: ${progress.current.fileName} 第${progress.current.pageNumber}页`
          
          if (progress.error) {
            console.warn(`上传失败: ${progress.current.fileName} 第${progress.current.pageNumber}页 - ${progress.error}`)
          } else if (progress.completed > 0) {
            console.log(`上传成功: ${progress.current.fileName} 第${progress.current.pageNumber}页`)
          }
        }
        
        // 如果有失败的，显示警告信息
        if (progress.failed > 0) {
          progressDetails.value += ` (成功:${progress.completed}, 失败:${progress.failed})`
        }
      }
    )

    // 处理上传结果
    const { total, completed, failed } = uploadResult
    
    if (failed === 0) {
      progressStatus.value = 'success'
      progressText.value = '图片上传完成！'
      progressDetails.value = `成功上传 ${completed} 张图片到云服务器`

      setTimeout(() => {
        showProgress.value = false
        ElMessage.success(`成功上传${completed}张图片到云服务器`)
      }, 1500)
    } else {
      progressStatus.value = 'warning'
      progressText.value = '图片上传完成（部分失败）'
      progressDetails.value = `成功上传 ${completed} 张，失败 ${failed} 张`

      setTimeout(() => {
        showProgress.value = false
        ElMessage.warning(`上传完成：成功${completed}张，失败${failed}张。请查看控制台了解失败详情。`)
      }, 2000)
    }

    console.log('=== 上传完成总结 ===')
    console.log(`总数: ${total}`)
    console.log(`成功: ${completed}`)
    console.log(`失败: ${failed}`)
    console.log('失败的文件:', uploadResult.results.filter(r => !r.success).map(r => `${r.imageData.fileName}_第${r.imageData.pageNumber}页`))
    console.log('========================')

  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作
      return
    }
    
    console.error('上传图片失败:', error)
    progressStatus.value = 'exception'
    progressText.value = '图片上传失败'
    progressDetails.value = error.message || '未知错误'
    
    setTimeout(() => {
      showProgress.value = false
      ElMessage.error('图片上传失败: ' + (error.message || '未知错误'))
    }, 2000)
  } finally {
    uploadingImages.value = false
  }
}

// 图片加载事件
const handleImageLoad = () => {
  imageLoading.value = false
}

const handleImageError = () => {
  imageLoading.value = false
  ElMessage.error('图片加载失败')
}

// 保存书签到数据库
const saveBookmarksToDatabase = async () => {
  if (processedPDFs.value.length === 0) {
    ElMessage.warning('没有可保存的书签数据')
    return
  }

  try {
    savingBookmarks.value = true
    
    // 过滤书签名称，移除空格和冒号
    const filterBookmarkName = (bookmarkName) => {
      if (!bookmarkName) return ''
      return bookmarkName.replace(/[\s:：]/g, '')
    }
    
    let successCount = 0
    let totalBookmarks = 0
    
    // 为每个PDF文件单独调用后端API
    for (const pdfInfo of processedPDFs.value) {
      try {
        // 准备当前PDF的书签数据，按照后端期望的格式
        const bookmarkData = {
          fileName: pdfInfo.name, // PDF文件名（不含扩展名）
          bookmarks: pdfInfo.bookmarks.map(bookmark => {
            return {
              originalBookmarkName: bookmark.originalBookmarkTitle || `第${bookmark.pageNumber}页`,
              filteredBookmarkName: bookmark.filteredBookmarkTitle || filterBookmarkName(bookmark.originalBookmarkTitle || `第${bookmark.pageNumber}页`),
              pageNumber: bookmark.pageNumber,
              remarks: `页面 ${bookmark.pageNumber}` // 添加简单的备注
            }
          })
        }
        
        console.log('准备保存PDF书签:', pdfInfo.name, bookmarkData)
        
        // 调用后端API保存书签
        const result = await savePDFBookmarks(bookmarkData)
        
        if (result && result.data && result.data.code === 200) {
          successCount++
          totalBookmarks += bookmarkData.bookmarks.length
          console.log(`PDF ${pdfInfo.name} 书签保存成功:`, result.data.message)
        } else {
          console.error(`PDF ${pdfInfo.name} 书签保存失败:`, result)
          ElMessage.warning(`PDF ${pdfInfo.name} 书签保存失败: ${result?.data?.message || '未知错误'}`)
        }
        
      } catch (error) {
        console.error(`保存PDF ${pdfInfo.name} 书签时出错:`, error)
        ElMessage.warning(`保存PDF ${pdfInfo.name} 书签失败: ${error.message || '网络错误'}`)
      }
    }
    
    // 显示最终结果
    if (successCount > 0) {
      ElMessage.success(`书签保存完成！成功保存 ${successCount} 个PDF文件的 ${totalBookmarks} 个书签`)
    } else {
      ElMessage.error('所有PDF书签保存失败，请检查网络连接和服务器状态')
    }
    
  } catch (error) {
    console.error('保存书签过程出错:', error)
    ElMessage.error('保存书签失败: ' + (error.message || '未知错误'))
  } finally {
    savingBookmarks.value = false
  }
}

// 查询书签 - 优化版本
const handleSearch = async (loadMore = false) => {
  // 修改：支持下拉框选择或手动输入
  const fileName = selectedPdfFile.value || searchKeyword.value.trim()
  if (!loadMore && !fileName) {
    ElMessage.warning('请选择或输入PDF文件名')
    return
  }
  
  // 同步选择状态
  if (selectedPdfFile.value && !searchKeyword.value) {
    searchKeyword.value = selectedPdfFile.value
  }

  try {
    if (!loadMore) {
      searching.value = true
      currentPage.value = 1
      bookmarks.value = []
      filteredBookmarks.value = []
    } else {
      loadingMore.value = true
    }
    
    console.log(`查询PDF书签: ${fileName}, 页码: ${currentPage.value}`)
    
    // 首次查询只获取基本信息，不加载图片数据
    const response = await getPDFBookmarksByFileName(fileName, {
      loadImages: false, // 首次查询不加载图片
      pageSize: pageSize.value,
      pageNumber: currentPage.value
    })
    
    console.log('API响应:', response)
    
    if (response && response.code === 200 && response.data) {
      let dataArray = []
      
      // 处理返回的数据
      if (Array.isArray(response.data)) {
        dataArray = response.data
      } else if (response.data && typeof response.data === 'object') {
        if (response.data.length !== undefined && response.data.length > 0) {
          dataArray = Array.from(response.data)
        } else {
          const keys = Object.keys(response.data)
          if (keys.length > 0 && keys.every(key => !isNaN(key))) {
            const sortedKeys = keys.sort((a, b) => parseInt(a) - parseInt(b))
            dataArray = sortedKeys.map(key => response.data[key])
          } else {
            dataArray = Object.values(response.data)
          }
        }
      }
      
      console.log(`处理后的数据数组长度: ${dataArray.length}`)
      
      if (dataArray && dataArray.length > 0) {
        const newBookmarks = dataArray.map(item => ({
          id: `${item.fileName}_${item.pageNumber}`,
          title: `${item.fileName}_${item.originalBookmarkName}`,
          originalBookmarkTitle: item.originalBookmarkName,
          filteredBookmarkTitle: item.filteredBookmarkName,
          pageNumber: item.pageNumber,
          pdfName: item.fileName,
          fileName: item.fileName,
          originalFileName: item.fileName,
          imageBase64: null, // 初始不加载图片数据
          imageSize: item.imageSize,
          imageWidth: item.imageWidth,
          imageHeight: item.imageHeight,
          loaded: false // 标记图片是否已加载
        }))
        
        if (loadMore) {
          // 追加数据
          bookmarks.value.push(...newBookmarks)
          filteredBookmarks.value.push(...newBookmarks)
        } else {
          // 重新设置数据
          bookmarks.value = newBookmarks
          filteredBookmarks.value = [...newBookmarks]
          
          // 估算总数（如果返回数据等于页面大小，可能还有更多）
          if (dataArray.length === pageSize.value) {
            hasMore.value = true
            totalCount.value = Math.max(bookmarks.value.length + 1, totalCount.value)
          } else {
            hasMore.value = false
            totalCount.value = bookmarks.value.length
          }
        }
        
        console.log(`查询到 ${newBookmarks.length} 条新记录，总计 ${bookmarks.value.length} 条`)
        
        // 如果是首次查询，默认选择第一个书签并加载其图片
        if (!loadMore && bookmarks.value.length > 0) {
          handleBookmarkSelect(bookmarks.value[0].id)
        }
        
        const message = loadMore 
          ? `加载了 ${newBookmarks.length} 条记录` 
          : `找到文件"${fileName}"的 ${bookmarks.value.length} 条书签记录`;
        
        ElMessage.success(message)
      } else {
        console.log('数据数组为空')
        if (!loadMore) {
          ElMessage.info(`未找到文件"${fileName}"的书签记录`)
          bookmarks.value = []
          filteredBookmarks.value = []
        }
        hasMore.value = false
      }
    } else {
      const errorMessage = response?.message || '查询失败'
      console.log('查询结果为空或出错:', errorMessage)
      if (!loadMore) {
        ElMessage.info(`未找到文件"${fileName}"的书签记录`)
        bookmarks.value = []
        filteredBookmarks.value = []
      }
      hasMore.value = false
    }
    
  } catch (error) {
    console.error('查询书签失败:', error)
    ElMessage.error('查询失败: ' + (error.message || '网络错误'))
    if (!loadMore) {
      bookmarks.value = []
      filteredBookmarks.value = []
    }
    hasMore.value = false
  } finally {
    searching.value = false
    loadingMore.value = false
  }
}

// 加载更多数据
const loadMoreData = async () => {
  if (loadingMore.value || !hasMore.value) return
  
  currentPage.value++
  await handleSearch(true)
}

// 修改：增强的按需加载图片数据函数
const loadImageData = async (bookmark) => {
  if (bookmark.loaded && bookmark.imageBase64 && imageLoadMode.value === 'api') {
    return bookmark.imageBase64
  }
  
  const cacheKey = `${bookmark.fileName}_${bookmark.pageNumber}_${imageLoadMode.value}`
  
  // 检查缓存
  if (imageCache.value.has(cacheKey)) {
    const cachedImage = imageCache.value.get(cacheKey)
    if (imageLoadMode.value === 'api') {
      bookmark.imageBase64 = cachedImage
      bookmark.loaded = true
    }
    return cachedImage
  }
  
  try {
    let imageData = null
    
    if (imageLoadMode.value === 'cloud') {
      // 从云服务器加载图片
      console.log(`从云服务器加载图片: ${bookmark.fileName} 第${bookmark.pageNumber}页`)
      imageData = await loadImageFromCloud(bookmark)
    } else {
      // 从API加载图片（原有逻辑）
      console.log(`从API加载图片: ${bookmark.fileName} 第${bookmark.pageNumber}页`)
      
      const response = await getPDFImageOnDemand(bookmark.fileName, bookmark.pageNumber)
      
      if (response && response.data && response.data.code === 200 && response.data.data) {
        const base64Data = response.data.data.imageBase64
        
        if (base64Data) {
          imageData = base64Data.startsWith('data:') 
            ? base64Data 
            : `data:image/jpeg;base64,${base64Data}`
          
          // 更新书签数据
          bookmark.imageBase64 = imageData
          bookmark.loaded = true
        }
      }
      
      if (!imageData) {
        throw new Error('API返回的图片数据无效')
      }
    }
    
    if (imageData) {
      // 添加到缓存
      imageCache.value.set(cacheKey, imageData)
      
      console.log(`图片加载成功: ${bookmark.fileName} 第${bookmark.pageNumber}页 (模式: ${imageLoadMode.value})`)
      return imageData
    }
    
    throw new Error('无法获取图片数据')
    
  } catch (error) {
    console.error(`加载图片失败: ${bookmark.fileName} 第${bookmark.pageNumber}页 (模式: ${imageLoadMode.value})`, error)
    
    // 如果是云服务器模式失败，可以尝试自动切换到API模式
    if (imageLoadMode.value === 'cloud') {
      console.log('云服务器加载失败，尝试自动切换到API模式')
      ElMessage.warning(`云服务器图片不存在，建议切换到"数据库API"模式`)
    }
    
    bookmark.loaded = true // 标记为已尝试加载，避免重复请求
    throw error
  }
}

// 重置查询
const handleReset = () => {
  searchKeyword.value = ''
  selectedPdfFile.value = ''  // 新增：重置选中的PDF文件
  bookmarks.value = []
  filteredBookmarks.value = []
  activeBookmark.value = ''
  currentImage.value = ''
  currentPage.value = 1
  totalCount.value = 0
  hasMore.value = false
  imageCache.value.clear() // 清理图片缓存
  ElMessage.success('已重置查询条件')
}

/**
 * 加载PDF文件列表
 */
const loadPdfFileList = async () => {
  try {
    loadingPdfList.value = true
    const response = await getPDFList()
    
    console.log('PDF列表API响应:', response)
    
    // 现在getPDFList已经直接返回后端数据格式：{code: 200, message: "success", data: [...]}
    if (response && (response.code === 200 || response.success)) {
      pdfFileList.value = response.data || []
      console.log('PDF文件列表加载成功:', pdfFileList.value.length, '个文件')
      
      // 如果有数据，额外打印每个PDF的信息
      if (pdfFileList.value.length > 0) {
        console.log('PDF列表详情:', pdfFileList.value.map(pdf => ({
          文件名: pdf.fileName,
          书签数: pdf.bookmarkCount,
          大小: formatFileSize(pdf.totalSize)
        })))
        
        ElMessage.success(`成功加载${pdfFileList.value.length}个PDF文件`)
      } else {
        console.log('PDF列表为空')
        ElMessage.info('暂无PDF文件')
      }
    } else {
      const errorMsg = response?.message || '获取PDF文件列表失败'
      console.error('PDF列表获取失败:', errorMsg, response)
      ElMessage.error(errorMsg)
    }
  } catch (error) {
    console.error('加载PDF文件列表失败:', error)
    ElMessage.error(`加载PDF文件列表失败: ${error.message || error}`)
  } finally {
    loadingPdfList.value = false
  }
}

/**
 * 处理PDF文件选择变化
 */
const handlePdfFileChange = (fileName) => {
  console.log('选择PDF文件:', fileName)
  if (fileName) {
    searchKeyword.value = fileName
    handleSearch()
  } else {
    handleReset()
  }
}

/**
 * 切换手动输入模式
 */
const toggleManualInput = () => {
  showManualInput.value = !showManualInput.value
  console.log('手动输入模式:', showManualInput.value ? '显示' : '隐藏')
}

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 过滤书签（本地过滤）
const filterBookmarks = () => {
  if (!searchKeyword.value.trim()) {
    filteredBookmarks.value = [...bookmarks.value]
  } else {
    const keyword = searchKeyword.value.toLowerCase()
    filteredBookmarks.value = bookmarks.value.filter(bookmark => 
      bookmark.title.toLowerCase().includes(keyword) ||
      bookmark.originalBookmarkTitle.toLowerCase().includes(keyword) ||
      bookmark.pdfName.toLowerCase().includes(keyword) ||
      bookmark.originalFileName.toLowerCase().includes(keyword)
    )
  }
}

// 组件挂载时的初始化
onMounted(() => {
  console.log('WIS增强查询组件已挂载')
  // 初始化空的过滤结果
  filteredBookmarks.value = []
  
  // 从localStorage恢复图片加载模式偏好
  const savedMode = localStorage.getItem('wis-image-load-mode')
  if (savedMode && ['api', 'cloud'].includes(savedMode)) {
    imageLoadMode.value = savedMode
    console.log(`恢复图片加载模式: ${savedMode}`)
  }
  
  // 新增：加载PDF文件列表
  loadPdfFileList()
})

// 组件卸载时清理
onUnmounted(() => {
  // 保存图片加载模式偏好
  localStorage.setItem('wis-image-load-mode', imageLoadMode.value)
  
  // 清理超时
  if (processingTimeout) {
    clearTimeout(processingTimeout)
  }
  
  // 清理可能的内存占用
  if (pdfDoc) {
    pdfDoc.destroy()
  }
  
  // 清理所有处理过的PDF数据
  processedPDFs.value = []
  currentPDFIndex.value = -1
  bookmarks.value = []
  activeBookmark.value = ''
  currentImage.value = ''
  imageCache.value.clear()
})

// 新增：处理图片加载模式变更
const handleImageModeChange = async () => {
  console.log(`图片加载模式切换为: ${imageLoadMode.value}`)
  
  // 如果当前有选中的书签，重新加载图片
  if (activeBookmark.value) {
    const bookmark = bookmarks.value.find(b => b.id === activeBookmark.value)
    if (bookmark) {
      try {
        imageLoading.value = true
        currentImage.value = ''
        
        const imageData = await loadImageData(bookmark)
        if (imageData) {
          currentImage.value = imageData
          ElMessage.success(`已切换到${imageLoadMode.value === 'cloud' ? '云服务器文件' : '数据库API'}模式`)
        }
      } catch (error) {
        console.error('切换图片加载模式后重新加载失败:', error)
        ElMessage.error(`切换模式后重新加载失败: ${error.message}`)
      } finally {
        imageLoading.value = false
      }
    }
  }
}
</script>

<style scoped>
.wis-pdf-viewer {
  height: calc(100vh - 120px);
  min-height: 600px;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  height: 80px;
  min-height: 80px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.page-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.page-actions .el-button {
  font-size: 16px;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
}

.pdf-content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  min-height: 0;
  height: calc(100% - 80px);
}

.bookmark-panel {
  width: 300px;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.image-panel {
  flex: 1;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-title-section {
  flex: 1;
}

.search-section {
  margin-top: 12px;
}

.search-buttons {
  display: flex;
  gap: 8px;
}

.query-info {
  margin-top: 8px;
}

.load-more-section {
  margin-top: 8px;
  text-align: center;
}

.pdf-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.image-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.bookmark-list {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
  max-height: calc(100% - 60px);
}

.bookmark-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  margin-bottom: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.bookmark-item:hover {
  background-color: #f0f9ff;
  border-color: #409eff;
}

.bookmark-item.active {
  background-color: #409eff;
  color: #fff;
}

.bookmark-item.active .el-tag {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border-color: rgba(255, 255, 255, 0.3);
}

.bookmark-title {
  flex: 1;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-display {
  flex: 1;
  padding: 16px;
  overflow: auto;
  max-height: calc(100% - 60px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.loading-container p {
  margin-top: 16px;
  font-size: 14px;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.no-image .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.no-image p {
  margin: 0;
  font-size: 14px;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #f56c6c;
}

.image-error .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.image-error p {
  margin: 4px 0;
  font-size: 14px;
}

.image-error .error-tip {
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.empty-bookmarks {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.progress-content {
  text-align: center;
}

.progress-text {
  margin: 16px 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.progress-details {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  text-align: left;
  word-break: break-all;
}

/* 新增：图片头部区域样式 */
.image-header-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.image-source-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.image-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.bookmark-list {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
  max-height: calc(100% - 60px);
}

.bookmark-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  margin-bottom: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.bookmark-item:hover {
  background-color: #f0f9ff;
  border-color: #409eff;
}

.bookmark-item.active {
  background-color: #409eff;
  color: #fff;
}

.bookmark-item.active .el-tag {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border-color: rgba(255, 255, 255, 0.3);
}

.bookmark-title {
  flex: 1;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-display {
  flex: 1;
  padding: 16px;
  overflow: auto;
  max-height: calc(100% - 60px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.loading-container p {
  margin-top: 16px;
  font-size: 14px;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.no-image .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.no-image p {
  margin: 0;
  font-size: 14px;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #f56c6c;
}

.image-error .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.image-error p {
  margin: 4px 0;
  font-size: 14px;
}

.image-error .error-tip {
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.empty-bookmarks {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.progress-content {
  text-align: center;
}

.progress-text {
  margin: 16px 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.progress-details {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  text-align: left;
  word-break: break-all;
}

/* 新增：PDF选项样式 */
.pdf-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.pdf-name {
  font-weight: 500;
  color: #303133;
}

.pdf-info {
  font-size: 12px;
  color: #909399;
}

.manual-input-toggle {
  margin-top: 4px;
}

.search-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pdf-content {
    flex-direction: column;
    gap: 12px;
  }
  
  .bookmark-panel {
    width: 100%;
    max-height: 200px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .page-actions {
    justify-content: center;
  }
}
</style>