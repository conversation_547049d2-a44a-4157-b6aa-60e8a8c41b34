using System;
using System.ComponentModel.DataAnnotations;

namespace mes_system_server.Models
{
    /// <summary>
    /// 物料型号实体类
    /// </summary>
    public class MaterialModel
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 型号名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 型号编码
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 物料类型 (products, components, parts, auxiliary, hardware-plastic)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 分类
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 规格参数
        /// </summary>
        [Required]
        [StringLength(500)]
        public string Specification { get; set; } = string.Empty;

        /// <summary>
        /// 状态
        /// </summary>
        [StringLength(20)]
        public string Status { get; set; } = "启用";

        /// <summary>
        /// 图片URL
        /// </summary>
        [StringLength(500)]
        public string Image { get; set; } = string.Empty;

        /// <summary>
        /// 单位
        /// </summary>
        [StringLength(20)]
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 材质
        /// </summary>
        [StringLength(100)]
        public string Material { get; set; } = string.Empty;

        /// <summary>
        /// 尺寸
        /// </summary>
        [StringLength(100)]
        public string Size { get; set; } = string.Empty;

        // 销售信息
        /// <summary>
        /// 销售价格
        /// </summary>
        public decimal? SalesPrice { get; set; }

        /// <summary>
        /// 销售单位
        /// </summary>
        [StringLength(20)]
        public string SalesUnit { get; set; } = string.Empty;

        /// <summary>
        /// 最小订单量
        /// </summary>
        public decimal? MinOrderQty { get; set; }

        /// <summary>
        /// 最大订单量
        /// </summary>
        public decimal? MaxOrderQty { get; set; }

        /// <summary>
        /// 交货期
        /// </summary>
        [StringLength(50)]
        public string LeadTime { get; set; } = string.Empty;

        /// <summary>
        /// 保修期
        /// </summary>
        [StringLength(50)]
        public string Warranty { get; set; } = string.Empty;

        // 仓库信息
        /// <summary>
        /// 存储位置
        /// </summary>
        [StringLength(100)]
        public string StorageLocation { get; set; } = string.Empty;

        /// <summary>
        /// 安全库存
        /// </summary>
        public decimal? SafetyStock { get; set; }

        /// <summary>
        /// 最大库存
        /// </summary>
        public decimal? MaxStock { get; set; }

        /// <summary>
        /// 再订货点
        /// </summary>
        public decimal? ReorderPoint { get; set; }

        /// <summary>
        /// 存储条件
        /// </summary>
        [StringLength(200)]
        public string StorageCondition { get; set; } = string.Empty;

        /// <summary>
        /// 保质期
        /// </summary>
        [StringLength(50)]
        public string ShelfLife { get; set; } = string.Empty;

        // 财务信息
        /// <summary>
        /// 标准成本
        /// </summary>
        public decimal? StandardCost { get; set; }

        /// <summary>
        /// 平均成本
        /// </summary>
        public decimal? AverageCost { get; set; }

        /// <summary>
        /// 估价方法
        /// </summary>
        [StringLength(50)]
        public string ValuationMethod { get; set; } = string.Empty;

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 会计科目
        /// </summary>
        [StringLength(100)]
        public string AccountSubject { get; set; } = string.Empty;

        /// <summary>
        /// 成本中心
        /// </summary>
        [StringLength(100)]
        public string CostCenter { get; set; } = string.Empty;

        // 生产信息
        /// <summary>
        /// 生产类型
        /// </summary>
        [StringLength(50)]
        public string ProductionType { get; set; } = string.Empty;

        /// <summary>
        /// 生产周期
        /// </summary>
        [StringLength(50)]
        public string ProductionLeadTime { get; set; } = string.Empty;

        /// <summary>
        /// 设置时间
        /// </summary>
        [StringLength(50)]
        public string SetupTime { get; set; } = string.Empty;

        /// <summary>
        /// 循环时间
        /// </summary>
        [StringLength(50)]
        public string CycleTime { get; set; } = string.Empty;

        /// <summary>
        /// 批次大小
        /// </summary>
        [StringLength(50)]
        public string BatchSize { get; set; } = string.Empty;

        /// <summary>
        /// 工作中心
        /// </summary>
        [StringLength(100)]
        public string WorkCenter { get; set; } = string.Empty;

        /// <summary>
        /// 质量标准
        /// </summary>
        [StringLength(200)]
        public string QualityStandard { get; set; } = string.Empty;

        // 采购信息
        /// <summary>
        /// 供应商
        /// </summary>
        [StringLength(200)]
        public string Supplier { get; set; } = string.Empty;

        /// <summary>
        /// 采购价格
        /// </summary>
        public decimal? PurchasePrice { get; set; }

        /// <summary>
        /// 采购单位
        /// </summary>
        [StringLength(20)]
        public string PurchaseUnit { get; set; } = string.Empty;

        /// <summary>
        /// 最小采购量
        /// </summary>
        public decimal? MinPurchaseQty { get; set; }

        /// <summary>
        /// 采购周期
        /// </summary>
        [StringLength(50)]
        public string PurchaseLeadTime { get; set; } = string.Empty;

        /// <summary>
        /// 质量等级
        /// </summary>
        [StringLength(50)]
        public string QualityLevel { get; set; } = string.Empty;

        /// <summary>
        /// 采购备注
        /// </summary>
        [StringLength(500)]
        public string PurchaseNote { get; set; } = string.Empty;

        // 进出口信息
        /// <summary>
        /// HS编码
        /// </summary>
        [StringLength(50)]
        public string HsCode { get; set; } = string.Empty;

        /// <summary>
        /// 原产国
        /// </summary>
        [StringLength(50)]
        public string OriginCountry { get; set; } = string.Empty;

        /// <summary>
        /// 进口税率
        /// </summary>
        public decimal? ImportTaxRate { get; set; }

        /// <summary>
        /// 出口退税
        /// </summary>
        public decimal? ExportTaxRefund { get; set; }

        /// <summary>
        /// 是否危险品
        /// </summary>
        public bool IsDangerous { get; set; } = false;

        /// <summary>
        /// 运输方式
        /// </summary>
        [StringLength(50)]
        public string TransportMode { get; set; } = string.Empty;

        /// <summary>
        /// 包装要求
        /// </summary>
        [StringLength(200)]
        public string PackingRequirement { get; set; } = string.Empty;

        /// <summary>
        /// 是否需要检验
        /// </summary>
        public bool InspectionRequired { get; set; } = false;

        /// <summary>
        /// 许可证要求
        /// </summary>
        [StringLength(200)]
        public string LicenseRequirement { get; set; } = string.Empty;

        // MRP计划信息
        /// <summary>
        /// 计划策略
        /// </summary>
        [StringLength(50)]
        public string PlanningStrategy { get; set; } = string.Empty;

        /// <summary>
        /// 计划周期
        /// </summary>
        [StringLength(50)]
        public string PlanningCycle { get; set; } = string.Empty;

        /// <summary>
        /// 预测方法
        /// </summary>
        [StringLength(50)]
        public string ForecastMethod { get; set; } = string.Empty;

        /// <summary>
        /// ABC分类
        /// </summary>
        [StringLength(10)]
        public string AbcCategory { get; set; } = string.Empty;

        /// <summary>
        /// 需求来源
        /// </summary>
        [StringLength(50)]
        public string DemandSource { get; set; } = string.Empty;

        /// <summary>
        /// 计划员
        /// </summary>
        [StringLength(50)]
        public string Planner { get; set; } = string.Empty;

        /// <summary>
        /// 安全库存天数
        /// </summary>
        [StringLength(20)]
        public string SafetyStockDays { get; set; } = string.Empty;

        /// <summary>
        /// 批量规则
        /// </summary>
        [StringLength(50)]
        public string LotSizeRule { get; set; } = string.Empty;

        /// <summary>
        /// 需求时间栅栏
        /// </summary>
        [StringLength(50)]
        public string DemandTimeFence { get; set; } = string.Empty;

        /// <summary>
        /// 供应时间栅栏
        /// </summary>
        [StringLength(50)]
        public string SupplyTimeFence { get; set; } = string.Empty;

        /// <summary>
        /// 计划备注
        /// </summary>
        [StringLength(500)]
        public string PlanningNote { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建人
        /// </summary>
        public int CreateBy { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public int? UpdateBy { get; set; }

        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsActive { get; set; } = true;
    }
} 