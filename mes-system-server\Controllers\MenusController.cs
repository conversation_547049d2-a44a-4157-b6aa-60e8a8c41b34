using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using mes_system_server.Models;
using mes_system_server.Data;
using mes_system_server.DTOs;

namespace mes_system_server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class MenusController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public MenusController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/menus
        [HttpGet]
        public async Task<ActionResult<ApiResponseDto<List<MenuListDto>>>> GetMenus()
        {
            try
            {
                var menus = await _context.Menus
                    .OrderBy(m => m.SortOrder)
                    .Select(m => new MenuListDto
                    {
                        Id = m.Id,
                        MenuId = m.MenuId,
                        Name = m.Name,
                        Path = m.Path,
                        Icon = m.Icon,
                        Type = m.Type,
                        ParentId = m.ParentId,
                        SortOrder = m.SortOrder,
                        Status = m.Status,
                        Description = m.Description,
                        CreateTime = m.CreateTime,
                        UpdateTime = m.UpdateTime
                    })
                    .ToListAsync();

                return Ok(new ApiResponseDto<List<MenuListDto>>(200, "获取菜单列表成功", menus));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"获取菜单列表失败: {ex.Message}"));
            }
        }

        // GET: api/menus/tree
        [HttpGet("tree")]
        public async Task<ActionResult<ApiResponseDto<List<MenuListDto>>>> GetMenuTree()
        {
            try
            {
                var allMenus = await _context.Menus
                    .OrderBy(m => m.SortOrder)
                    .Select(m => new MenuListDto
                    {
                        Id = m.Id,
                        MenuId = m.MenuId,
                        Name = m.Name,
                        Path = m.Path,
                        Icon = m.Icon,
                        Type = m.Type,
                        ParentId = m.ParentId,
                        SortOrder = m.SortOrder,
                        Status = m.Status,
                        Description = m.Description,
                        CreateTime = m.CreateTime,
                        UpdateTime = m.UpdateTime
                    })
                    .ToListAsync();

                // 构建树形结构
                var rootMenus = allMenus.Where(m => m.ParentId == null).ToList();
                foreach (var rootMenu in rootMenus)
                {
                    BuildMenuTree(rootMenu, allMenus);
                }

                return Ok(new ApiResponseDto<List<MenuListDto>>(200, "获取菜单树成功", rootMenus));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"获取菜单树失败: {ex.Message}"));
            }
        }

        // GET: api/menus/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponseDto<MenuListDto>>> GetMenu(int id)
        {
            try
            {
                var menu = await _context.Menus.FindAsync(id);

                if (menu == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "菜单不存在"));
                }

                var menuDto = new MenuListDto
                {
                    Id = menu.Id,
                    MenuId = menu.MenuId,
                    Name = menu.Name,
                    Path = menu.Path,
                    Icon = menu.Icon,
                    Type = menu.Type,
                    ParentId = menu.ParentId,
                    SortOrder = menu.SortOrder,
                    Status = menu.Status,
                    Description = menu.Description,
                    CreateTime = menu.CreateTime,
                    UpdateTime = menu.UpdateTime
                };

                return Ok(new ApiResponseDto<MenuListDto>(200, "获取菜单详情成功", menuDto));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"获取菜单详情失败: {ex.Message}"));
            }
        }

        // POST: api/menus
        [HttpPost]
        public async Task<ActionResult<ApiResponseDto<MenuListDto>>> CreateMenu(CreateMenuDto createMenuDto)
        {
            try
            {
                // 检查菜单ID是否已存在
                var existingMenu = await _context.Menus
                    .FirstOrDefaultAsync(m => m.MenuId == createMenuDto.MenuId);

                if (existingMenu != null)
                {
                    return BadRequest(new ApiResponseDto<object>(400, "菜单ID已存在"));
                }

                var menu = new Menu
                {
                    MenuId = createMenuDto.MenuId,
                    Name = createMenuDto.Name,
                    Path = createMenuDto.Path,
                    Icon = createMenuDto.Icon,
                    Type = createMenuDto.Type,
                    ParentId = createMenuDto.ParentId,
                    SortOrder = createMenuDto.SortOrder,
                    Status = createMenuDto.Status,
                    Description = createMenuDto.Description,
                    CreateTime = DateTime.Now
                };

                _context.Menus.Add(menu);
                await _context.SaveChangesAsync();

                var menuDto = new MenuListDto
                {
                    Id = menu.Id,
                    MenuId = menu.MenuId,
                    Name = menu.Name,
                    Path = menu.Path,
                    Icon = menu.Icon,
                    Type = menu.Type,
                    ParentId = menu.ParentId,
                    SortOrder = menu.SortOrder,
                    Status = menu.Status,
                    Description = menu.Description,
                    CreateTime = menu.CreateTime,
                    UpdateTime = menu.UpdateTime
                };

                return CreatedAtAction(nameof(GetMenu), new { id = menu.Id }, new ApiResponseDto<MenuListDto>(200, "创建菜单成功", menuDto));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"创建菜单失败: {ex.Message}"));
            }
        }

        // PUT: api/menus/{id}
        [HttpPut("{id}")]
        public async Task<ActionResult<ApiResponseDto<MenuListDto>>> UpdateMenu(int id, UpdateMenuDto updateMenuDto)
        {
            try
            {
                var menu = await _context.Menus.FindAsync(id);

                if (menu == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "菜单不存在"));
                }

                menu.Name = updateMenuDto.Name;
                menu.Path = updateMenuDto.Path;
                menu.Icon = updateMenuDto.Icon;
                menu.Type = updateMenuDto.Type;
                menu.ParentId = updateMenuDto.ParentId;
                menu.SortOrder = updateMenuDto.SortOrder;
                menu.Status = updateMenuDto.Status;
                menu.Description = updateMenuDto.Description;
                menu.UpdateTime = DateTime.Now;

                await _context.SaveChangesAsync();

                var menuDto = new MenuListDto
                {
                    Id = menu.Id,
                    MenuId = menu.MenuId,
                    Name = menu.Name,
                    Path = menu.Path,
                    Icon = menu.Icon,
                    Type = menu.Type,
                    ParentId = menu.ParentId,
                    SortOrder = menu.SortOrder,
                    Status = menu.Status,
                    Description = menu.Description,
                    CreateTime = menu.CreateTime,
                    UpdateTime = menu.UpdateTime
                };

                return Ok(new ApiResponseDto<MenuListDto>(200, "更新菜单成功", menuDto));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"更新菜单失败: {ex.Message}"));
            }
        }

        // DELETE: api/menus/{id}
        [HttpDelete("{id}")]
        public async Task<ActionResult<ApiResponseDto<object>>> DeleteMenu(int id)
        {
            try
            {
                var menu = await _context.Menus.FindAsync(id);

                if (menu == null)
                {
                    return NotFound(new ApiResponseDto<object>(404, "菜单不存在"));
                }

                // 检查是否有子菜单
                var hasChildren = await _context.Menus.AnyAsync(m => m.ParentId == id);
                if (hasChildren)
                {
                    return BadRequest(new ApiResponseDto<object>(400, "该菜单下还有子菜单，无法删除"));
                }

                // 检查是否有角色关联此菜单
                var hasRoleMenus = await _context.RoleMenus.AnyAsync(rm => rm.MenuId == id);
                if (hasRoleMenus)
                {
                    return BadRequest(new ApiResponseDto<object>(400, "该菜单已被角色使用，无法删除"));
                }

                _context.Menus.Remove(menu);
                await _context.SaveChangesAsync();

                return Ok(new ApiResponseDto<object>(200, "删除菜单成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"删除菜单失败: {ex.Message}"));
            }
        }

        // 构建菜单树的递归方法
        private void BuildMenuTree(MenuListDto parentMenu, List<MenuListDto> allMenus)
        {
            var children = allMenus.Where(m => m.ParentId == parentMenu.Id).ToList();
            parentMenu.Children = children;

            foreach (var child in children)
            {
                BuildMenuTree(child, allMenus);
            }
        }

        // POST: api/menus/initialize
        [HttpPost("initialize")]
        public async Task<ActionResult<ApiResponseDto<object>>> InitializeMenus()
        {
            try
            {
                // 检查是否已有菜单数据
                var existingMenusCount = await _context.Menus.CountAsync();
                if (existingMenusCount > 0)
                {
                    return BadRequest(new ApiResponseDto<object>(400, "菜单数据已存在，无需重复初始化"));
                }

                // 定义初始菜单数据
                var initMenus = new List<Menu>
                {
                    // 一级菜单
                    new Menu { MenuId = "dashboard", Name = "仪表盘", Path = "/home", Icon = "Monitor", Type = "menu-item", ParentId = null, SortOrder = 1, Status = true, Description = "系统仪表盘", CreateTime = DateTime.Now },
                    new Menu { MenuId = "engineering", Name = "工程管理", Path = null, Icon = "Setting", Type = "sub-menu", ParentId = null, SortOrder = 2, Status = true, Description = "工程相关管理功能", CreateTime = DateTime.Now },
                    new Menu { MenuId = "production", Name = "生产管理", Path = null, Icon = "Odometer", Type = "sub-menu", ParentId = null, SortOrder = 3, Status = true, Description = "生产相关管理功能", CreateTime = DateTime.Now },
                    new Menu { MenuId = "quality", Name = "质量管理", Path = null, Icon = "Aim", Type = "sub-menu", ParentId = null, SortOrder = 4, Status = true, Description = "质量相关管理功能", CreateTime = DateTime.Now },
                    new Menu { MenuId = "warehouse", Name = "仓储管理", Path = null, Icon = "Box", Type = "sub-menu", ParentId = null, SortOrder = 5, Status = true, Description = "仓储相关管理功能", CreateTime = DateTime.Now },
                    new Menu { MenuId = "equipment", Name = "设备管理", Path = null, Icon = "Tools", Type = "sub-menu", ParentId = null, SortOrder = 6, Status = true, Description = "设备相关管理功能", CreateTime = DateTime.Now },
                    new Menu { MenuId = "hr", Name = "人事管理", Path = null, Icon = "UserFilled", Type = "sub-menu", ParentId = null, SortOrder = 7, Status = true, Description = "人事相关管理功能", CreateTime = DateTime.Now },
                    new Menu { MenuId = "system", Name = "系统管理", Path = null, Icon = "Setting", Type = "sub-menu", ParentId = null, SortOrder = 8, Status = true, Description = "系统相关管理功能", CreateTime = DateTime.Now }
                };

                // 先添加一级菜单
                _context.Menus.AddRange(initMenus);
                await _context.SaveChangesAsync();

                // 获取一级菜单的ID
                var engineeringMenu = await _context.Menus.FirstAsync(m => m.MenuId == "engineering");
                var productionMenu = await _context.Menus.FirstAsync(m => m.MenuId == "production");
                var qualityMenu = await _context.Menus.FirstAsync(m => m.MenuId == "quality");
                var warehouseMenu = await _context.Menus.FirstAsync(m => m.MenuId == "warehouse");
                var equipmentMenu = await _context.Menus.FirstAsync(m => m.MenuId == "equipment");
                var hrMenu = await _context.Menus.FirstAsync(m => m.MenuId == "hr");
                var systemMenu = await _context.Menus.FirstAsync(m => m.MenuId == "system");

                // 添加子菜单
                var subMenus = new List<Menu>
                {
                    // 工程管理子菜单
                    new Menu { MenuId = "engineering-model", Name = "型号管理", Path = "/home/<USER>/model-management", Icon = "Files", Type = "menu-item", ParentId = engineeringMenu.Id, SortOrder = 1, Status = true, Description = "产品型号管理", CreateTime = DateTime.Now },
                    new Menu { MenuId = "engineering-process", Name = "工艺管理", Path = "/engineering/process", Icon = "Operation", Type = "menu-item", ParentId = engineeringMenu.Id, SortOrder = 2, Status = true, Description = "工艺流程管理", CreateTime = DateTime.Now },
                    new Menu { MenuId = "engineering-bom", Name = "BOM管理", Path = "/engineering/bom", Icon = "List", Type = "menu-item", ParentId = engineeringMenu.Id, SortOrder = 3, Status = true, Description = "物料清单管理", CreateTime = DateTime.Now },
                    new Menu { MenuId = "engineering-wis", Name = "WIS管理", Path = "/home/<USER>/wis-management", Icon = "Notebook", Type = "menu-item", ParentId = engineeringMenu.Id, SortOrder = 4, Status = true, Description = "WIS PDF文件查看和管理", CreateTime = DateTime.Now },

                    // 生产管理子菜单
                    new Menu { MenuId = "production-plan", Name = "生产计划", Path = "/production/plan", Icon = "Calendar", Type = "menu-item", ParentId = productionMenu.Id, SortOrder = 1, Status = true, Description = "生产计划管理", CreateTime = DateTime.Now },
                    new Menu { MenuId = "production-task", Name = "生产任务", Path = "/production/task", Icon = "Document", Type = "menu-item", ParentId = productionMenu.Id, SortOrder = 2, Status = true, Description = "生产任务管理", CreateTime = DateTime.Now },
                    new Menu { MenuId = "production-monitor", Name = "生产监控", Path = "/production/monitor", Icon = "VideoCamera", Type = "menu-item", ParentId = productionMenu.Id, SortOrder = 3, Status = true, Description = "生产过程监控", CreateTime = DateTime.Now },

                    // 质量管理子菜单
                    new Menu { MenuId = "quality-inspection", Name = "质量检验", Path = "/quality/inspection", Icon = "Check", Type = "menu-item", ParentId = qualityMenu.Id, SortOrder = 1, Status = true, Description = "质量检验管理", CreateTime = DateTime.Now },
                    new Menu { MenuId = "quality-report", Name = "质量报表", Path = "/quality/report", Icon = "DataAnalysis", Type = "menu-item", ParentId = qualityMenu.Id, SortOrder = 2, Status = true, Description = "质量报表统计", CreateTime = DateTime.Now },

                    // 仓储管理子菜单
                    new Menu { MenuId = "warehouse-material", Name = "物料管理", Path = "/warehouse/material", Icon = "Goods", Type = "menu-item", ParentId = warehouseMenu.Id, SortOrder = 1, Status = true, Description = "物料信息管理", CreateTime = DateTime.Now },
                    new Menu { MenuId = "warehouse-inventory", Name = "库存管理", Path = "/warehouse/inventory", Icon = "Collection", Type = "menu-item", ParentId = warehouseMenu.Id, SortOrder = 2, Status = true, Description = "库存信息管理", CreateTime = DateTime.Now },

                    // 设备管理子菜单
                    new Menu { MenuId = "equipment-list", Name = "设备台账", Path = "/equipment/list", Icon = "Platform", Type = "menu-item", ParentId = equipmentMenu.Id, SortOrder = 1, Status = true, Description = "设备台账管理", CreateTime = DateTime.Now },
                    new Menu { MenuId = "equipment-maintenance", Name = "设备维护", Path = "/equipment/maintenance", Icon = "Service", Type = "menu-item", ParentId = equipmentMenu.Id, SortOrder = 2, Status = true, Description = "设备维护管理", CreateTime = DateTime.Now },

                    // 人事管理子菜单
                    new Menu { MenuId = "hr-employees", Name = "员工管理", Path = "/home/<USER>/employees", Icon = "User", Type = "menu-item", ParentId = hrMenu.Id, SortOrder = 1, Status = true, Description = "员工信息管理", CreateTime = DateTime.Now },
                    new Menu { MenuId = "hr-attendance", Name = "考勤管理", Path = "/home/<USER>/attendance", Icon = "Calendar", Type = "menu-item", ParentId = hrMenu.Id, SortOrder = 2, Status = true, Description = "员工考勤管理", CreateTime = DateTime.Now },
                    new Menu { MenuId = "hr-salary", Name = "薪资管理", Path = "/home/<USER>/salary", Icon = "Money", Type = "menu-item", ParentId = hrMenu.Id, SortOrder = 3, Status = true, Description = "员工薪资管理", CreateTime = DateTime.Now },
                    new Menu { MenuId = "hr-company-settings", Name = "公司设置", Path = "/home/<USER>/company-settings", Icon = "OfficeBuilding", Type = "menu-item", ParentId = hrMenu.Id, SortOrder = 4, Status = true, Description = "公司信息设置", CreateTime = DateTime.Now },

                    // 系统管理子菜单
                    new Menu { MenuId = "system-users", Name = "用户管理", Path = "/home/<USER>/users", Icon = "Avatar", Type = "menu-item", ParentId = systemMenu.Id, SortOrder = 1, Status = true, Description = "系统用户管理", CreateTime = DateTime.Now },
                    new Menu { MenuId = "system-roles", Name = "角色权限", Path = "/home/<USER>/roles", Icon = "Lock", Type = "menu-item", ParentId = systemMenu.Id, SortOrder = 2, Status = true, Description = "角色权限管理", CreateTime = DateTime.Now },
                    new Menu { MenuId = "system-menus", Name = "菜单管理", Path = "/home/<USER>/menus", Icon = "List", Type = "menu-item", ParentId = systemMenu.Id, SortOrder = 3, Status = true, Description = "系统菜单管理", CreateTime = DateTime.Now }
                };

                _context.Menus.AddRange(subMenus);
                await _context.SaveChangesAsync();

                return Ok(new ApiResponseDto<object>(200, "菜单数据初始化成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponseDto<object>(500, $"菜单数据初始化失败: {ex.Message}"));
            }
        }
    }
} 