using System.ComponentModel.DataAnnotations;

namespace mes_system_server.DTOs
{
    // 菜单列表DTO
    public class MenuListDto
    {
        public int Id { get; set; }
        public string MenuId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Path { get; set; }
        public string? Icon { get; set; }
        public string Type { get; set; } = string.Empty;
        public int? ParentId { get; set; }
        public int SortOrder { get; set; }
        public bool Status { get; set; }
        public string? Description { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime? UpdateTime { get; set; }
        public List<MenuListDto> Children { get; set; } = new List<MenuListDto>();
    }

    // 创建菜单DTO
    public class CreateMenuDto
    {
        [Required(ErrorMessage = "菜单ID不能为空")]
        [StringLength(50, ErrorMessage = "菜单ID长度不能超过50个字符")]
        public string MenuId { get; set; } = string.Empty;

        [Required(ErrorMessage = "菜单名称不能为空")]
        [StringLength(100, ErrorMessage = "菜单名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "菜单路径长度不能超过200个字符")]
        public string? Path { get; set; }

        [StringLength(50, ErrorMessage = "菜单图标长度不能超过50个字符")]
        public string? Icon { get; set; }

        [Required(ErrorMessage = "菜单类型不能为空")]
        [StringLength(20, ErrorMessage = "菜单类型长度不能超过20个字符")]
        public string Type { get; set; } = string.Empty;

        public int? ParentId { get; set; }

        public int SortOrder { get; set; } = 0;

        public bool Status { get; set; } = true;

        [StringLength(200, ErrorMessage = "菜单描述长度不能超过200个字符")]
        public string? Description { get; set; }
    }

    // 更新菜单DTO
    public class UpdateMenuDto
    {
        [Required(ErrorMessage = "菜单名称不能为空")]
        [StringLength(100, ErrorMessage = "菜单名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "菜单路径长度不能超过200个字符")]
        public string? Path { get; set; }

        [StringLength(50, ErrorMessage = "菜单图标长度不能超过50个字符")]
        public string? Icon { get; set; }

        [Required(ErrorMessage = "菜单类型不能为空")]
        [StringLength(20, ErrorMessage = "菜单类型长度不能超过20个字符")]
        public string Type { get; set; } = string.Empty;

        public int? ParentId { get; set; }

        public int SortOrder { get; set; } = 0;

        public bool Status { get; set; } = true;

        [StringLength(200, ErrorMessage = "菜单描述长度不能超过200个字符")]
        public string? Description { get; set; }
    }

    // 保存角色菜单权限DTO
    public class SaveRoleMenusDto
    {
        public List<string> MenuIds { get; set; } = new List<string>();
    }

    // 角色菜单权限响应DTO
    public class RoleMenuPermissionDto
    {
        public int RoleId { get; set; }
        public string RoleName { get; set; } = string.Empty;
        public List<string> MenuIds { get; set; } = new List<string>();
    }
} 