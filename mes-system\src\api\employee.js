import request from '@/utils/request'
import { mockEmployees } from '@/utils/mockData'

// 获取员工列表
export function getEmployees(params) {
  // 使用实际API调用
  return request({
    url: '/api/employees',
    method: 'get',
    params
  })
}

// 获取员工详情
export function getEmployee(id) {
  return request({
    url: `/api/employees/${id}`,
    method: 'get'
  })
}

// 创建员工
export function createEmployee(data) {
  return request({
    url: '/api/employees',
    method: 'post',
    data
  })
}

// 更新员工
export function updateEmployee(id, data) {
  return request({
    url: `/api/employees/${id}`,
    method: 'put',
    data
  })
}

// 删除员工
export function deleteEmployee(id) {
  return request({
    url: `/api/employees/${id}`,
    method: 'delete'
  })
}

// 导入员工数据
export function importEmployees(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/api/employees/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出员工数据
export function exportEmployees(params) {
  return request({
    url: '/api/employees/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
} 