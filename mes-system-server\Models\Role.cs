using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace mes_system_server.Models
{
    public class Role
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string RoleName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string RoleCode { get; set; } = string.Empty;

        [StringLength(200)]
        public string? Description { get; set; }

        public bool Status { get; set; } = true;

        public DateTime CreateTime { get; set; } = DateTime.Now;

        public DateTime? UpdateTime { get; set; }

        // 导航属性 - 角色关联的用户
        public virtual ICollection<User> Users { get; set; } = new List<User>();

        // 导航属性 - 角色关联的菜单
        public virtual ICollection<RoleMenu> RoleMenus { get; set; } = new List<RoleMenu>();
    }
} 