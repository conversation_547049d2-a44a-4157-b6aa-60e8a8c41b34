using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace mes_system_server.Models
{
    /// <summary>
    /// 数据字典主表
    /// </summary>
    public class DataDictionary
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 字典名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 字典编码（唯一）
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 字典类型（system:系统配置, business:业务配置, other:其他配置）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 字典描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 排序序号
        /// </summary>
        public int Sort { get; set; } = 0;

        /// <summary>
        /// 状态（true:启用, false:禁用）
        /// </summary>
        public bool Status { get; set; } = true;

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remark { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public int? CreateBy { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public int? UpdateBy { get; set; }

        // 导航属性
        /// <summary>
        /// 字典项集合
        /// </summary>
        public virtual ICollection<DataDictionaryItem> Items { get; set; } = new List<DataDictionaryItem>();
    }
} 