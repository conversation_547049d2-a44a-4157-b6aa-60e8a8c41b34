# 菜单数据初始化指南

本文档介绍如何将前端菜单管理界面的菜单数据初始化到数据库中，并实现前后端的完整对接。

## 功能概述

系统已实现了完整的菜单管理功能，包括：

1. **后端API**: 提供菜单的增删改查和初始化功能
2. **前端界面**: 菜单管理界面完全对接后端API
3. **数据库脚本**: 支持SQL Server的菜单数据初始化
4. **自动化脚本**: PowerShell脚本自动执行数据库初始化

## 实现的菜单结构

根据附件图片，系统包含以下菜单结构：

### 一级菜单
- 仪表盘 (dashboard) - `/home`
- 工程管理 (engineering) - 子菜单
- 生产管理 (production) - 子菜单  
- 质量管理 (quality) - 子菜单
- 仓储管理 (warehouse) - 子菜单
- 设备管理 (equipment) - 子菜单
- 人事管理 (hr) - 子菜单
- 系统管理 (system) - 子菜单

### 二级菜单
每个子菜单下都包含相应的管理功能，如：
- 系统管理 > 用户管理 (`system-users`)
- 系统管理 > 角色权限 (`system-roles`)
- 系统管理 > 菜单管理 (`system-menus`)

## 初始化方法

### 方法一：通过前端界面初始化（推荐）

1. 启动后端服务（.NET Core API）
2. 启动前端服务（Vue.js）
3. 登录系统，进入 `系统管理 > 菜单管理`
4. 点击页面右上角的 `初始化菜单` 按钮
5. 确认初始化操作
6. 系统将自动创建所有菜单数据

### 方法二：通过SQL脚本初始化

1. 使用SQL Server Management Studio或其他数据库工具
2. 打开 `mes-system-server/Scripts/InitMenus.sql` 文件
3. 在目标数据库中执行该脚本
4. 脚本将自动创建完整的菜单结构

### 方法三：通过PowerShell脚本初始化

使用PowerShell执行自动化脚本：

```powershell
# 使用Windows身份验证
.\mes-system-server\Scripts\InitMenus.ps1 -ServerName "localhost" -DatabaseName "MesSystemDB"

# 使用SQL Server身份验证
.\mes-system-server\Scripts\InitMenus.ps1 -ServerName "localhost" -DatabaseName "MesSystemDB" -Username "sa" -Password "yourpassword"
```

## API接口说明

### 菜单管理相关API

| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/api/menus` | 获取菜单列表 |
| GET | `/api/menus/tree` | 获取菜单树结构 |
| GET | `/api/menus/{id}` | 获取菜单详情 |
| POST | `/api/menus` | 创建菜单 |
| PUT | `/api/menus/{id}` | 更新菜单 |
| DELETE | `/api/menus/{id}` | 删除菜单 |
| POST | `/api/menus/initialize` | 初始化菜单数据 |

### 数据模型

```json
{
  "menuId": "system-users",
  "name": "用户管理",
  "path": "/home/<USER>/users",
  "icon": "Avatar",
  "type": "menu-item",
  "parentId": 8,
  "sortOrder": 1,
  "status": true,
  "description": "系统用户管理"
}
```

## 前端菜单管理功能

### 支持的操作
- ✅ 查看菜单树结构
- ✅ 新增根菜单
- ✅ 添加子菜单  
- ✅ 编辑菜单信息
- ✅ 删除菜单
- ✅ 菜单状态管理
- ✅ 菜单排序
- ✅ 一键初始化菜单数据

### 界面特性
- 树形表格展示菜单层级结构
- 实时状态显示（启用/禁用）
- 图标选择器支持
- 表单验证
- 操作确认对话框
- 加载状态指示

## 数据库结构

菜单数据存储在 `Menus` 表中，字段说明：

| 字段 | 类型 | 说明 |
|------|------|------|
| Id | int | 自增主键 |
| MenuId | string | 菜单唯一标识 |
| Name | string | 菜单名称 |
| Path | string | 菜单路径 |
| Icon | string | 菜单图标 |
| Type | string | 菜单类型 (menu-item/sub-menu) |
| ParentId | int? | 父菜单ID |
| SortOrder | int | 排序序号 |
| Status | bool | 菜单状态 |
| Description | string | 菜单描述 |
| CreateTime | DateTime | 创建时间 |
| UpdateTime | DateTime? | 更新时间 |

## 角色权限集成

菜单系统与角色权限模块完全集成：

1. **RoleMenu表**: 存储角色与菜单的关联关系
2. **权限控制**: 基于用户角色动态显示菜单
3. **菜单权限设置**: 在角色管理中可以为角色分配菜单权限

## 注意事项

1. **数据库备份**: 初始化前请备份现有数据
2. **权限检查**: 确保当前用户有数据库写权限
3. **重复初始化**: 系统会检查是否已有菜单数据，避免重复初始化
4. **依赖关系**: 删除菜单时会检查是否有子菜单或角色关联

## 故障排除

### 常见问题

1. **初始化失败**
   - 检查数据库连接是否正常
   - 确认用户权限是否足够
   - 查看后端日志了解具体错误

2. **菜单不显示**
   - 检查菜单状态是否为启用
   - 确认角色是否有对应菜单权限
   - 查看前端控制台是否有API错误

3. **图标不显示**
   - 确认图标名称是否正确
   - 检查Element Plus图标库是否正确引入

## 技术架构

- **后端**: ASP.NET Core Web API
- **前端**: Vue 3 + Element Plus
- **数据库**: SQL Server
- **ORM**: Entity Framework Core
- **认证**: JWT Bearer Token

## 更新日志

- ✅ 实现菜单CRUD API
- ✅ 完成前端菜单管理界面
- ✅ 添加菜单初始化功能
- ✅ 集成角色权限系统
- ✅ 提供数据库脚本和自动化工具

---

通过以上步骤，您可以成功将前端菜单管理界面中的菜单数据初始化到数据库中，并实现前后端的完整对接。 