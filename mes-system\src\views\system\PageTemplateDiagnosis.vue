<template>
  <div class="diagnosis-container">
    <div class="page-header">
      <h2 class="page-title">PageTemplate 使用诊断</h2>
      <p class="page-subtitle">诊断和解决PageTemplate页面模板使用中的问题</p>
    </div>

    <el-card class="diagnosis-card">
      <template #header>
        <h3>问题诊断结果</h3>
      </template>

      <!-- 问题1：当前页面显示问题 -->
      <el-alert
        title="发现问题：当前访问的页面不是PageTemplate生成的页面"
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="problem-detail">
            <p><strong>问题描述：</strong></p>
            <ul>
              <li>您截图显示的是首页仪表板页面（/home），而不是使用PageTemplate组件的CRUD页面</li>
              <li>这个页面显示的是统计图表和数据看板，不是标准的表格管理页面</li>
              <li>PageTemplate组件是用来生成标准的增删改查页面，不是仪表板页面</li>
            </ul>
            
            <p><strong>解决方案：</strong></p>
            <ol>
              <li>访问正确的PageTemplate演示页面</li>
              <li>在菜单管理中正确配置页面路径</li>
              <li>确保生成的页面文件放在正确的目录中</li>
            </ol>
          </div>
        </template>
      </el-alert>

      <!-- 体验正确的PageTemplate页面 -->
      <el-card class="demo-section" shadow="never">
        <template #header>
          <h3>体验正确的PageTemplate页面</h3>
        </template>
        <div class="demo-content">
          <p>点击下面的按钮，查看正确的PageTemplate页面效果：</p>
          <el-button 
            type="primary" 
            size="large" 
            @click="goToDemo"
            :icon="ArrowRight"
          >
            查看PageTemplate演示页面
          </el-button>
          <p class="demo-note">
            演示页面包含：搜索筛选、数据表格、分页、新增编辑表单、删除确认等完整CRUD功能
          </p>
        </div>
      </el-card>

      <!-- 菜单配置说明 -->
      <el-card class="guide-section" shadow="never">
        <template #header>
          <h3>菜单配置指南</h3>
        </template>
        <div class="guide-content">
          <el-steps :active="currentStep" direction="vertical">
            <el-step title="新增菜单项">
              <template #description>
                <div class="step-content">
                  <p>在菜单管理中点击"新增菜单"，填写以下信息：</p>
                  <ul>
                    <li><strong>菜单名称：</strong>如"用户管理"</li>
                    <li><strong>菜单ID：</strong>如"user-management"</li>
                    <li><strong>菜单类型：</strong>选择"菜单项"</li>
                    <li><strong>菜单路径：</strong>如"/home/<USER>/user-management"</li>
                  </ul>
                  <el-button size="small" @click="goToMenuManagement">前往菜单管理</el-button>
                </div>
              </template>
            </el-step>
            
            <el-step title="启用页面生成">
              <template #description>
                <div class="step-content">
                  <p>在菜单表单中：</p>
                  <ul>
                    <li>开启"自动生成页面文件"开关</li>
                    <li>查看页面预览信息</li>
                    <li>确认生成的文件路径正确</li>
                  </ul>
                </div>
              </template>
            </el-step>
            
            <el-step title="生成页面文件">
              <template #description>
                <div class="step-content">
                  <p>保存菜单后：</p>
                  <ul>
                    <li>系统会显示生成结果对话框</li>
                    <li>下载生成的Vue组件文件</li>
                    <li>下载生成的API文件</li>
                    <li>复制路由配置代码</li>
                  </ul>
                </div>
              </template>
            </el-step>
            
            <el-step title="手动配置路由">
              <template #description>
                <div class="step-content">
                  <p>由于浏览器环境限制，需要手动配置路由：</p>
                  <ul>
                    <li>将下载的Vue文件放到对应目录</li>
                    <li>在router/index.js中添加路由配置</li>
                    <li>刷新页面使路由生效</li>
                  </ul>
                </div>
              </template>
            </el-step>
          </el-steps>
        </div>
      </el-card>

      <!-- 常见问题解答 -->
      <el-card class="faq-section" shadow="never">
        <template #header>
          <h3>常见问题解答</h3>
        </template>
        <el-collapse>
          <el-collapse-item title="为什么生成的页面不能直接访问？" name="1">
            <p>由于浏览器安全限制，前端无法直接创建文件和修改路由配置。需要手动下载文件并配置路由。</p>
            <p><strong>解决方案：</strong>按照生成指南手动配置文件和路由。</p>
          </el-collapse-item>
          
          <el-collapse-item title="PageTemplate和Dashboard有什么区别？" name="2">
            <p><strong>PageTemplate：</strong>用于生成标准的CRUD管理页面，包含表格、表单、搜索等功能。</p>
            <p><strong>Dashboard：</strong>仪表板页面，显示统计图表和数据概览，通常作为首页。</p>
          </el-collapse-item>
          
          <el-collapse-item title="如何自定义PageTemplate生成的页面？" name="3">
            <p>可以通过以下方式自定义：</p>
            <ul>
              <li>修改searchFields配置自定义搜索字段</li>
              <li>修改tableColumns配置自定义表格列</li>
              <li>修改formFields配置自定义表单字段</li>
              <li>使用插槽自定义特殊列的渲染</li>
              <li>添加自定义事件处理逻辑</li>
            </ul>
          </el-collapse-item>
          
          <el-collapse-item title="为什么访问菜单路径显示的是首页？" name="4">
            <p>可能的原因：</p>
            <ul>
              <li>路由配置不正确或缺失</li>
              <li>Vue组件文件路径错误</li>
              <li>菜单路径配置错误</li>
            </ul>
            <p><strong>解决方案：</strong>检查路由配置，确保组件文件存在于正确路径。</p>
          </el-collapse-item>
        </el-collapse>
      </el-card>

      <!-- 系统信息 -->
      <el-card class="system-info" shadow="never">
        <template #header>
          <h3>系统信息</h3>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="当前路由">{{ currentRoute }}</el-descriptions-item>
          <el-descriptions-item label="组件名称">{{ componentName }}</el-descriptions-item>
          <el-descriptions-item label="PageTemplate组件">已安装</el-descriptions-item>
          <el-descriptions-item label="菜单管理">已配置</el-descriptions-item>
          <el-descriptions-item label="演示页面">可访问</el-descriptions-item>
          <el-descriptions-item label="文档说明">已完善</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowRight } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const currentStep = ref(0)

const currentRoute = computed(() => route.path)
const componentName = computed(() => route.name || '未知')

// 前往演示页面
const goToDemo = () => {
  router.push('/home/<USER>/page-template-demo')
}

// 前往菜单管理
const goToMenuManagement = () => {
  router.push('/home/<USER>/menus')
}
</script>

<style scoped>
.diagnosis-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
  text-align: center;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 500;
  color: #303133;
}

.page-subtitle {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.diagnosis-card {
  max-width: 1200px;
  margin: 0 auto;
}

.problem-detail {
  margin-top: 12px;
}

.problem-detail ul,
.problem-detail ol {
  margin: 8px 0;
  padding-left: 20px;
}

.problem-detail li {
  margin-bottom: 4px;
  line-height: 1.6;
}

.demo-section,
.guide-section,
.faq-section,
.system-info {
  margin-top: 20px;
  border: 1px solid #e4e7ed;
}

.demo-content {
  text-align: center;
  padding: 20px;
}

.demo-note {
  margin-top: 12px;
  color: #909399;
  font-size: 14px;
}

.step-content {
  padding: 12px 0;
}

.step-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

.step-content li {
  margin-bottom: 4px;
  line-height: 1.6;
}

.faq-section .el-collapse {
  border: none;
}

.faq-section .el-collapse-item {
  margin-bottom: 8px;
}

.system-info {
  background: #f8f9fa;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .diagnosis-container {
    padding: 12px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .demo-content {
    padding: 16px;
  }
}
</style> 