/**
 * Vue组件文本提取工具
 * 自动从Vue文件中提取中文文本内容
 */

// 中文字符正则表达式
const CHINESE_REGEX = /[\u4e00-\u9fa5]/

// 提取中文文本的正则表达式模式
const TEXT_PATTERNS = {
  // HTML标签内的文本内容
  tagContent: />([^<]*[\u4e00-\u9fa5][^<]*)</g,
  
  // 属性值中的中文文本
  attributes: {
    title: /title\s*=\s*["']([^"']*[\u4e00-\u9fa5][^"']*)["']/g,
    placeholder: /placeholder\s*=\s*["']([^"']*[\u4e00-\u9fa5][^"']*)["']/g,
    label: /label\s*=\s*["']([^"']*[\u4e00-\u9fa5][^"']*)["']/g,
    alt: /alt\s*=\s*["']([^"']*[\u4e00-\u9fa5][^"']*)["']/g
  },
  
  // JavaScript字符串中的中文
  jsStrings: {
    singleQuote: /'([^']*[\u4e00-\u9fa5][^']*)'/g,
    doubleQuote: /"([^"]*[\u4e00-\u9fa5][^"]*)"/g,
    templateLiteral: /`([^`]*[\u4e00-\u9fa5][^`]*)`/g
  },
  
  // Vue指令中的文本
  vueDirectives: {
    vText: /v-text\s*=\s*["']([^"']*[\u4e00-\u9fa5][^"']*)["']/g,
    vHtml: /v-html\s*=\s*["']([^"']*[\u4e00-\u9fa5][^"']*)["']/g
  },
  
  // Element Plus组件的常用属性
  elementPlus: {
    elButton: /<el-button[^>]*>([^<]*[\u4e00-\u9fa5][^<]*)<\/el-button>/g,
    elTag: /<el-tag[^>]*>([^<]*[\u4e00-\u9fa5][^<]*)<\/el-tag>/g,
    elCard: /<el-card[^>]*>[\s\S]*?<template\s+#header[^>]*>[\s\S]*?>([^<]*[\u4e00-\u9fa5][^<]*)<[\s\S]*?<\/el-card>/g,
    elFormItem: /<el-form-item[^>]*label\s*=\s*["']([^"']*[\u4e00-\u9fa5][^"']*)["']/g,
    elMessage: /ElMessage\.(success|error|warning|info)\s*\(\s*["']([^"']*[\u4e00-\u9fa5][^"']*)["']\s*\)/g,
    elMessageBox: /ElMessageBox\.(confirm|alert|prompt)[\s\S]*?["']([^"']*[\u4e00-\u9fa5][^"']*)["']/g
  }
}

// 需要排除的文本模式
const EXCLUDE_PATTERNS = [
  /^[\s]*$/, // 空白文本
  /^[0-9\s\-_\.\/\\:]+$/, // 纯数字、符号
  /^[a-zA-Z\s\-_\.\/\\:]+$/, // 纯英文
  /console\.(log|error|warn|info)/, // 控制台输出
  /import\s+/, // import语句
  /export\s+/, // export语句
  /\$t\s*\(/, // 已经使用了翻译函数
  /\{\{.*\$t.*\}\}/, // 模板中已使用翻译
  /v-model/, // v-model指令
  /ref=/, // ref属性
  /@click/, // 事件处理
  /computed/, // 计算属性
  /methods/, // 方法
  /watch/, // 监听器
]

/**
 * 从Vue文件内容中提取中文文本
 * @param {string} fileContent Vue文件内容
 * @param {string} filePath 文件路径
 * @returns {Array} 提取的文本数组
 */
export function extractChineseTexts(fileContent, filePath) {
  const extractedTexts = []
  const processedTexts = new Set() // 避免重复
  
  // 获取模块名（从文件路径推断）
  const moduleName = getModuleNameFromPath(filePath)
  
  // 1. 提取HTML标签内容
  extractTagContents(fileContent, extractedTexts, processedTexts, moduleName)
  
  // 2. 提取属性值
  extractAttributeValues(fileContent, extractedTexts, processedTexts, moduleName)
  
  // 3. 提取JavaScript字符串
  extractJsStrings(fileContent, extractedTexts, processedTexts, moduleName)
  
  // 4. 提取Element Plus组件内容
  extractElementPlusContents(fileContent, extractedTexts, processedTexts, moduleName)
  
  // 5. 提取特殊模式的文本
  extractSpecialPatterns(fileContent, extractedTexts, processedTexts, moduleName)
  
  return extractedTexts.sort((a, b) => a.key.localeCompare(b.key))
}

/**
 * 提取HTML标签内的文本内容
 */
function extractTagContents(content, results, processed, module) {
  // 移除script和style标签内容
  const cleanContent = content.replace(/<script[\s\S]*?<\/script>/gi, '')
                             .replace(/<style[\s\S]*?<\/style>/gi, '')
  
  let match
  while ((match = TEXT_PATTERNS.tagContent.exec(cleanContent)) !== null) {
    const text = match[1].trim()
    if (isValidChineseText(text) && !processed.has(text)) {
      const textType = determineTextType(text, match[0])
      const key = generateTranslationKey(text, module, textType)
      
      results.push({
        key,
        text,
        type: textType,
        context: match[0].substring(0, 100) + '...'
      })
      
      processed.add(text)
    }
  }
}

/**
 * 提取属性值中的文本
 */
function extractAttributeValues(content, results, processed, module) {
  Object.entries(TEXT_PATTERNS.attributes).forEach(([attrName, pattern]) => {
    let match
    while ((match = pattern.exec(content)) !== null) {
      const text = match[1].trim()
      if (isValidChineseText(text) && !processed.has(text)) {
        const key = generateTranslationKey(text, module, attrName)
        
        results.push({
          key,
          text,
          type: attrName,
          context: match[0]
        })
        
        processed.add(text)
      }
    }
  })
}

/**
 * 提取JavaScript字符串中的文本
 */
function extractJsStrings(content, results, processed, module) {
  // 提取script标签内容
  const scriptMatch = content.match(/<script[\s\S]*?>([\s\S]*?)<\/script>/i)
  if (!scriptMatch) return
  
  const scriptContent = scriptMatch[1]
  
  Object.entries(TEXT_PATTERNS.jsStrings).forEach(([type, pattern]) => {
    let match
    while ((match = pattern.exec(scriptContent)) !== null) {
      const text = match[1].trim()
      if (isValidChineseText(text) && !processed.has(text)) {
        const textType = determineJsTextType(text, scriptContent, match.index)
        const key = generateTranslationKey(text, module, textType)
        
        results.push({
          key,
          text,
          type: textType,
          context: getContextAroundMatch(scriptContent, match.index, 50)
        })
        
        processed.add(text)
      }
    }
  })
}

/**
 * 提取Element Plus组件内容
 */
function extractElementPlusContents(content, results, processed, module) {
  Object.entries(TEXT_PATTERNS.elementPlus).forEach(([componentType, pattern]) => {
    let match
    while ((match = pattern.exec(content)) !== null) {
      const text = (match[2] || match[1]).trim()
      if (isValidChineseText(text) && !processed.has(text)) {
        const textType = getElementPlusTextType(componentType)
        const key = generateTranslationKey(text, module, textType)
        
        results.push({
          key,
          text,
          type: textType,
          context: match[0].substring(0, 100) + '...'
        })
        
        processed.add(text)
      }
    }
  })
}

/**
 * 提取特殊模式的文本
 */
function extractSpecialPatterns(content, results, processed, module) {
  // 提取页面标题
  const titlePatterns = [
    /<h[1-6][^>]*>([^<]*[\u4e00-\u9fa5][^<]*)<\/h[1-6]>/g,
    /class\s*=\s*["'][^"']*title[^"']*["'][^>]*>([^<]*[\u4e00-\u9fa5][^<]*)</g,
    /class\s*=\s*["'][^"']*header[^"']*["'][^>]*>([^<]*[\u4e00-\u9fa5][^<]*)</g
  ]
  
  titlePatterns.forEach(pattern => {
    let match
    while ((match = pattern.exec(content)) !== null) {
      const text = match[1].trim()
      if (isValidChineseText(text) && !processed.has(text)) {
        const key = generateTranslationKey(text, module, 'title')
        
        results.push({
          key,
          text,
          type: 'title',
          context: match[0]
        })
        
        processed.add(text)
      }
    }
  })
}

/**
 * 检查文本是否为有效的中文文本
 */
function isValidChineseText(text) {
  if (!text || typeof text !== 'string') return false
  
  // 必须包含中文字符
  if (!CHINESE_REGEX.test(text)) return false
  
  // 检查排除模式
  for (const pattern of EXCLUDE_PATTERNS) {
    if (pattern.test(text)) return false
  }
  
  // 过滤太短或太长的文本
  if (text.length < 2 || text.length > 100) return false
  
  return true
}

/**
 * 从文件路径推断模块名
 */
function getModuleNameFromPath(filePath) {
  if (!filePath) return 'common'
  
  const pathLower = filePath.toLowerCase()
  
  // 根据路径推断模块
  if (pathLower.includes('/login')) return 'login'
  if (pathLower.includes('/user')) return 'user'
  if (pathLower.includes('/system')) return 'system'
  if (pathLower.includes('/hr')) return 'hr'
  if (pathLower.includes('/production')) return 'production'
  if (pathLower.includes('/quality')) return 'quality'
  if (pathLower.includes('/warehouse')) return 'warehouse'
  if (pathLower.includes('/equipment')) return 'equipment'
  if (pathLower.includes('/engineering')) return 'engineering'
  
  // 从文件名推断
  const fileName = filePath.split('/').pop().replace('.vue', '').toLowerCase()
  return fileName || 'page'
}

/**
 * 确定文本类型
 */
function determineTextType(text, context) {
  const contextLower = context.toLowerCase()
  
  if (contextLower.includes('button') || contextLower.includes('btn')) return 'button'
  if (contextLower.includes('title') || contextLower.includes('h1') || contextLower.includes('h2')) return 'title'
  if (contextLower.includes('label')) return 'label'
  if (contextLower.includes('placeholder')) return 'placeholder'
  if (contextLower.includes('message') || contextLower.includes('alert')) return 'message'
  if (contextLower.includes('tooltip') || contextLower.includes('tip')) return 'tooltip'
  if (contextLower.includes('tab')) return 'tab'
  if (contextLower.includes('menu')) return 'menu'
  
  return 'text'
}

/**
 * 确定JavaScript文本类型
 */
function determineJsTextType(text, scriptContent, matchIndex) {
  const contextBefore = scriptContent.substring(Math.max(0, matchIndex - 50), matchIndex)
  const contextAfter = scriptContent.substring(matchIndex, matchIndex + 50)
  const context = (contextBefore + contextAfter).toLowerCase()
  
  if (context.includes('elmessage') || context.includes('message')) return 'message'
  if (context.includes('title')) return 'title'
  if (context.includes('confirm') || context.includes('alert')) return 'confirm'
  if (context.includes('error')) return 'error'
  if (context.includes('success')) return 'success'
  if (context.includes('warning')) return 'warning'
  
  return 'script'
}

/**
 * 获取Element Plus组件的文本类型
 */
function getElementPlusTextType(componentType) {
  const typeMap = {
    elButton: 'button',
    elTag: 'tag',
    elCard: 'title',
    elFormItem: 'label',
    elMessage: 'message',
    elMessageBox: 'confirm'
  }
  
  return typeMap[componentType] || 'component'
}

/**
 * 生成翻译键名
 */
function generateTranslationKey(text, module, type) {
  // 移除特殊字符，只保留中文、字母、数字
  const cleanText = text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '')
  
  // 生成基于内容的键名
  let keyName = ''
  
  // 如果文本较短，直接使用拼音或简化
  if (cleanText.length <= 4) {
    keyName = convertToPinyin(cleanText)
  } else {
    // 对于较长文本，使用类型 + 简化描述
    const typePrefix = getTypePrefix(type)
    const shortDesc = generateShortDescription(cleanText)
    keyName = typePrefix + shortDesc
  }
  
  return `${module}.${keyName}`
}

/**
 * 获取类型前缀
 */
function getTypePrefix(type) {
  const prefixMap = {
    button: 'btn',
    title: 'title',
    label: 'lbl',
    placeholder: 'ph',
    message: 'msg',
    tooltip: 'tip',
    confirm: 'confirm',
    error: 'err',
    success: 'success',
    warning: 'warn',
    tab: 'tab',
    menu: 'menu',
    tag: 'tag'
  }
  
  return prefixMap[type] || 'text'
}

/**
 * 生成简短描述
 */
function generateShortDescription(text) {
  // 提取关键词
  const keywords = extractKeywords(text)
  if (keywords.length > 0) {
    return convertToPinyin(keywords[0])
  }
  
  // 使用前几个字符
  return convertToPinyin(text.substring(0, 3))
}

/**
 * 提取关键词
 */
function extractKeywords(text) {
  // 常见的业务关键词
  const businessKeywords = [
    '用户', '管理', '系统', '登录', '密码', '菜单', '权限', '角色',
    '员工', '部门', '公司', '考勤', '薪资', '生产', '质量', '设备',
    '仓储', '物料', '库存', '工程', '型号', '工艺', '新增', '编辑',
    '删除', '保存', '取消', '确认', '搜索', '刷新', '导出', '导入'
  ]
  
  const keywords = []
  for (const keyword of businessKeywords) {
    if (text.includes(keyword)) {
      keywords.push(keyword)
    }
  }
  
  return keywords
}

/**
 * 简单的拼音转换（仅处理常见字符）
 */
function convertToPinyin(text) {
  const pinyinMap = {
    '用': 'user', '户': 'user', '管': 'manage', '理': 'manage',
    '系': 'system', '统': 'system', '登': 'login', '录': 'login',
    '密': 'password', '码': 'password', '菜': 'menu', '单': 'menu',
    '权': 'permission', '限': 'permission', '角': 'role', '色': 'role',
    '员': 'employee', '工': 'employee', '部': 'dept', '门': 'dept',
    '公': 'company', '司': 'company', '考': 'attend', '勤': 'attend',
    '薪': 'salary', '资': 'salary', '生': 'product', '产': 'product',
    '质': 'quality', '量': 'quality', '设': 'equipment', '备': 'equipment',
    '仓': 'warehouse', '储': 'warehouse', '物': 'material', '料': 'material',
    '库': 'inventory', '存': 'inventory', '工': 'engineering', '程': 'engineering',
    '型': 'model', '号': 'model', '艺': 'process',
    '新': 'add', '增': 'add', '编': 'edit', '辑': 'edit',
    '删': 'delete', '除': 'delete', '保': 'save', '存': 'save',
    '取': 'cancel', '消': 'cancel', '确': 'confirm', '认': 'confirm',
    '搜': 'search', '索': 'search', '刷': 'refresh', '新': 'refresh',
    '导': 'export', '出': 'export', '入': 'import',
    '标': 'title', '题': 'title', '按': 'button', '钮': 'button',
    '提': 'tip', '示': 'tip', '消': 'message', '息': 'message'
  }
  
  let result = ''
  for (const char of text) {
    if (pinyinMap[char]) {
      result += pinyinMap[char]
    } else if (/[a-zA-Z0-9]/.test(char)) {
      result += char.toLowerCase()
    }
  }
  
  return result || 'text'
}

/**
 * 获取匹配位置周围的上下文
 */
function getContextAroundMatch(content, index, length) {
  const start = Math.max(0, index - length)
  const end = Math.min(content.length, index + length)
  return content.substring(start, end)
}

/**
 * 从远程获取Vue文件内容
 */
export async function fetchVueFileContent(filePath) {
  try {
    // 这里应该调用后端API来读取文件内容
    // 暂时返回模拟数据，实际项目中需要实现真正的文件读取
    const response = await fetch(`/api/files/content?path=${encodeURIComponent(filePath)}`)
    if (!response.ok) {
      throw new Error('文件读取失败')
    }
    return await response.text()
  } catch (error) {
    console.error('获取文件内容失败:', error)
    // 返回模拟的Vue文件内容用于演示
    return getMockVueFileContent(filePath)
  }
}

/**
 * 获取模拟的Vue文件内容（用于演示）
 */
function getMockVueFileContent(filePath) {
  if (filePath.includes('Login.vue')) {
    return `
<template>
  <div class="login-container">
    <h1>MES系统登录</h1>
    <el-form>
      <el-form-item label="用户名">
        <el-input placeholder="请输入用户名" />
      </el-form-item>
      <el-form-item label="密码">
        <el-input type="password" placeholder="请输入密码" />
      </el-form-item>
      <el-button type="primary">登录系统</el-button>
      <el-button>取消</el-button>
    </el-form>
    <p>欢迎使用智能制造执行系统</p>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'

const handleLogin = () => {
  ElMessage.success('登录成功')
}

const handleError = () => {
  ElMessage.error('登录失败，请检查用户名和密码')
}
</script>
    `
  }
  
  if (filePath.includes('UserManagement.vue')) {
    return `
<template>
  <div class="user-management">
    <div class="page-header">
      <h2>用户管理</h2>
      <el-button type="primary">新增用户</el-button>
    </div>
    <el-table>
      <el-table-column label="用户名" />
      <el-table-column label="姓名" />
      <el-table-column label="状态" />
      <el-table-column label="操作">
        <template #default>
          <el-button size="small">编辑</el-button>
          <el-button type="danger" size="small">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'

const handleDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要删除该用户吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
  } catch {
    ElMessage.info('已取消删除')
  }
}
</script>
    `
  }
  
  // 默认返回空内容
  return `
<template>
  <div>
    <h1>页面标题</h1>
    <p>这是一个示例页面</p>
  </div>
</template>
  `
} 