using System.ComponentModel.DataAnnotations;

namespace mes_system_server.DTOs
{
    /// <summary>
    /// 数据字典列表DTO
    /// </summary>
    public class DataDictionaryListDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int Sort { get; set; }
        public bool Status { get; set; }
        public string? Remark { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime? UpdateTime { get; set; }
        public int ItemCount { get; set; } = 0; // 字典项数量
    }

    /// <summary>
    /// 数据字典详情DTO
    /// </summary>
    public class DataDictionaryDetailDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int Sort { get; set; }
        public bool Status { get; set; }
        public string? Remark { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime? UpdateTime { get; set; }
        public List<DataDictionaryItemDto> Items { get; set; } = new List<DataDictionaryItemDto>();
    }

    /// <summary>
    /// 创建数据字典DTO
    /// </summary>
    public class CreateDataDictionaryDto
    {
        [Required(ErrorMessage = "字典名称不能为空")]
        [StringLength(100, ErrorMessage = "字典名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "字典编码不能为空")]
        [StringLength(100, ErrorMessage = "字典编码长度不能超过100个字符")]
        [RegularExpression(@"^[A-Z][A-Z0-9_]*$", ErrorMessage = "字典编码必须以大写字母开头，只能包含大写字母、数字和下划线")]
        public string Code { get; set; } = string.Empty;

        [Required(ErrorMessage = "字典类型不能为空")]
        [StringLength(50, ErrorMessage = "字典类型长度不能超过50个字符")]
        public string Type { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "字典描述长度不能超过500个字符")]
        public string? Description { get; set; }

        public int Sort { get; set; } = 0;

        public bool Status { get; set; } = true;

        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 更新数据字典DTO
    /// </summary>
    public class UpdateDataDictionaryDto
    {
        [Required(ErrorMessage = "字典名称不能为空")]
        [StringLength(100, ErrorMessage = "字典名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "字典类型不能为空")]
        [StringLength(50, ErrorMessage = "字典类型长度不能超过50个字符")]
        public string Type { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "字典描述长度不能超过500个字符")]
        public string? Description { get; set; }

        public int Sort { get; set; } = 0;

        public bool Status { get; set; } = true;

        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 数据字典查询DTO
    /// </summary>
    public class DataDictionaryQueryDto
    {
        /// <summary>
        /// 字典名称（模糊查询）
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 字典编码（模糊查询）
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 字典类型
        /// </summary>
        public string? Type { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public bool? Status { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 排序字段
        /// </summary>
        public string? SortField { get; set; } = "Sort";

        /// <summary>
        /// 排序方向（asc, desc）
        /// </summary>
        public string? SortOrder { get; set; } = "asc";
    }

    /// <summary>
    /// 数据字典项DTO
    /// </summary>
    public class DataDictionaryItemDto
    {
        public int Id { get; set; }
        public int DictId { get; set; }
        public string Label { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public int Sort { get; set; }
        public bool Status { get; set; }
        public string? Description { get; set; }
        public string? ExtProperty1 { get; set; }
        public string? ExtProperty2 { get; set; }
        public string? ExtProperty3 { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime? UpdateTime { get; set; }
    }

    /// <summary>
    /// 创建数据字典项DTO
    /// </summary>
    public class CreateDataDictionaryItemDto
    {
        [Required(ErrorMessage = "字典ID不能为空")]
        public int DictId { get; set; }

        [Required(ErrorMessage = "字典标签不能为空")]
        [StringLength(100, ErrorMessage = "字典标签长度不能超过100个字符")]
        public string Label { get; set; } = string.Empty;

        [Required(ErrorMessage = "字典值不能为空")]
        [StringLength(100, ErrorMessage = "字典值长度不能超过100个字符")]
        public string Value { get; set; } = string.Empty;

        public int Sort { get; set; } = 0;

        public bool Status { get; set; } = true;

        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }

        [StringLength(200, ErrorMessage = "扩展属性1长度不能超过200个字符")]
        public string? ExtProperty1 { get; set; }

        [StringLength(200, ErrorMessage = "扩展属性2长度不能超过200个字符")]
        public string? ExtProperty2 { get; set; }

        [StringLength(200, ErrorMessage = "扩展属性3长度不能超过200个字符")]
        public string? ExtProperty3 { get; set; }
    }

    /// <summary>
    /// 更新数据字典项DTO
    /// </summary>
    public class UpdateDataDictionaryItemDto
    {
        [Required(ErrorMessage = "字典标签不能为空")]
        [StringLength(100, ErrorMessage = "字典标签长度不能超过100个字符")]
        public string Label { get; set; } = string.Empty;

        [Required(ErrorMessage = "字典值不能为空")]
        [StringLength(100, ErrorMessage = "字典值长度不能超过100个字符")]
        public string Value { get; set; } = string.Empty;

        public int Sort { get; set; } = 0;

        public bool Status { get; set; } = true;

        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }

        [StringLength(200, ErrorMessage = "扩展属性1长度不能超过200个字符")]
        public string? ExtProperty1 { get; set; }

        [StringLength(200, ErrorMessage = "扩展属性2长度不能超过200个字符")]
        public string? ExtProperty2 { get; set; }

        [StringLength(200, ErrorMessage = "扩展属性3长度不能超过200个字符")]
        public string? ExtProperty3 { get; set; }
    }

    /// <summary>
    /// 数据字典项查询DTO
    /// </summary>
    public class DataDictionaryItemQueryDto
    {
        /// <summary>
        /// 字典ID
        /// </summary>
        [Required(ErrorMessage = "字典ID不能为空")]
        public int DictId { get; set; }

        /// <summary>
        /// 字典标签（模糊查询）
        /// </summary>
        public string? Label { get; set; }

        /// <summary>
        /// 字典值（模糊查询）
        /// </summary>
        public string? Value { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public bool? Status { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 50;

        /// <summary>
        /// 排序字段
        /// </summary>
        public string? SortField { get; set; } = "Sort";

        /// <summary>
        /// 排序方向（asc, desc）
        /// </summary>
        public string? SortOrder { get; set; } = "asc";
    }



    /// <summary>
    /// 批量操作DTO
    /// </summary>
    public class BatchOperationDto
    {
        [Required(ErrorMessage = "ID列表不能为空")]
        public List<int> Ids { get; set; } = new List<int>();
    }

    /// <summary>
    /// 批量状态更新DTO
    /// </summary>
    public class BatchStatusUpdateDto : BatchOperationDto
    {
        [Required(ErrorMessage = "状态值不能为空")]
        public bool Status { get; set; }
    }
} 