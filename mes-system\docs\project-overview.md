# MES智能制造执行系统 - 项目概述

## 项目简介

MES（Manufacturing Execution System）智能制造执行系统是一个基于Vue 3 + Element Plus + Node.js开发的现代化制造执行管理平台。系统提供了完整的制造过程管理、质量控制、设备管理、人员管理等功能模块。

## 核心特性

### 🚀 技术特性
- **现代化前端框架**: 基于Vue 3 + Vite构建，支持TypeScript
- **组件化开发**: 使用Element Plus UI组件库，提供丰富的交互组件
- **模块化架构**: 采用模块化设计，便于扩展和维护
- **响应式设计**: 支持多种设备尺寸，适配PC、平板、手机
- **国际化支持**: 内置多语言支持机制

### 📊 业务特性
- **动态菜单系统**: 支持可视化菜单配置和权限管理
- **角色权限管理**: 细粒度的权限控制，支持多角色管理
- **通用页面模板**: PageTemplate组件提供标准CRUD页面快速生成
- **实时数据监控**: 提供实时生产数据监控和分析
- **移动端适配**: 支持移动设备访问和操作

## 技术栈

### 前端技术栈
- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **路由管理**: Vue Router 4
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **图表库**: ECharts
- **CSS预处理器**: Sass/SCSS
- **代码规范**: ESLint + Prettier

### 后端技术栈
- **框架**: ASP.NET Core 8
- **数据库**: SQL Server / MySQL
- **ORM**: Entity Framework Core
- **认证授权**: JWT Token
- **API文档**: Swagger/OpenAPI
- **缓存**: Redis（可选）
- **日志**: Serilog

### 开发工具
- **IDE**: Visual Studio Code / Visual Studio
- **版本控制**: Git
- **包管理**: npm / pnpm
- **调试工具**: Vue DevTools
- **API测试**: Postman / Swagger UI

## 系统架构

```
mes-system/                    # 前端项目
├── public/                    # 静态资源
├── src/                       # 源代码
│   ├── api/                   # API接口
│   ├── assets/                # 资源文件
│   ├── components/            # 通用组件
│   │   ├── PageTemplate.vue   # 通用页面模板
│   │   └── Dashboard.vue      # 仪表板组件
│   ├── router/                # 路由配置
│   ├── stores/                # 状态管理
│   ├── utils/                 # 工具函数
│   ├── views/                 # 页面组件
│   │   ├── system/            # 系统管理
│   │   ├── engineering/       # 工程管理
│   │   ├── production/        # 生产管理
│   │   ├── quality/           # 质量管理
│   │   ├── warehouse/         # 仓储管理
│   │   ├── equipment/         # 设备管理
│   │   └── hr/                # 人力资源
│   └── App.vue                # 根组件
├── docs/                      # 文档目录
└── package.json               # 项目配置

mes-system-server/             # 后端项目
├── Controllers/               # 控制器
├── Models/                    # 数据模型
├── Services/                  # 业务服务
├── Data/                      # 数据访问
└── Program.cs                 # 启动文件
```

## 功能模块

### 核心模块
1. **系统管理**: 用户管理、角色权限、菜单配置、数据字典
2. **工程管理**: 产品型号、工艺流程、BOM管理
3. **生产管理**: 生产计划、任务调度、进度跟踪
4. **质量管理**: 质量检验、统计分析、报表生成
5. **仓储管理**: 物料管理、库存控制、出入库管理
6. **设备管理**: 设备台账、维护保养、状态监控
7. **人力资源**: 员工管理、考勤管理、薪资管理

### 特色功能
- **PageTemplate通用模板**: 快速生成标准CRUD页面
- **动态菜单系统**: 可视化配置菜单结构和权限
- **实时监控大屏**: 生产状态实时监控
- **移动端支持**: 响应式设计，支持移动设备

## 快速开始

### 环境要求
- Node.js >= 16
- npm >= 7 或 pnpm >= 6
- Vue CLI >= 5 (可选)

### 安装依赖
```bash
# 使用npm
npm install

# 或使用pnpm (推荐)
pnpm install
```

### 开发模式
```bash
npm run dev
# 或
pnpm dev
```

### 构建生产版本
```bash
npm run build
# 或
pnpm build
```

## 开发指南

详细的开发指南请查看：
- [快速开始](./quick-start.md)
- [开发规范](./development-standards.md)
- [组件库](./components.md)
- [API接口文档](./api-reference.md)

## 许可证

本项目采用 MIT 许可证，详情请查看 [LICENSE](../LICENSE) 文件。

## 联系我们

- 项目维护者: [您的姓名]
- 邮箱: [您的邮箱]
- 项目地址: [GitHub地址]

---

*文档最后更新时间：2024-01-15* 