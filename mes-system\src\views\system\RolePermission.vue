<template>
  <div class="role-permission">
    <el-tabs v-model="activeTab" type="border-card">
      <!-- 角色定义标签页 -->
      <el-tab-pane name="role-definition" :label="$t('role.roleDefinition')">
        <div class="operation-bar">
          <div class="search-area">
            <el-input
              v-model="roleSearchQuery"
              :placeholder="$t('role.searchPlaceholder')"
              class="search-input"
              clearable
              @clear="handleRoleSearch"
              @keyup.enter="handleRoleSearch"
            >
              <template #append>
                <el-button type="primary" :icon="Search" @click="handleRoleSearch">{{ $t('role.search') }}</el-button>
              </template>
            </el-input>
            <el-button type="primary" :icon="Plus" @click="handleAddRole">{{ $t('role.addRole') }}</el-button>
          </div>
        </div>

        <!-- 角色列表 -->
        <el-table
          v-loading="roleLoading"
          :data="roleList"
          border
          style="width: 100%"
        >
          <el-table-column type="index" width="55" align="center" />
          <el-table-column prop="roleName" :label="$t('role.roleName')" min-width="120" align="center" />
          <el-table-column prop="roleCode" :label="$t('role.roleCode')" min-width="120" align="center" />
          <el-table-column prop="description" :label="$t('role.roleDescription')" min-width="180" align="center" />
          <el-table-column prop="status" :label="$t('role.status')" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status ? 'success' : 'danger'">
                {{ row.status ? $t('role.active') : $t('role.inactive') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" :label="$t('role.createTime')" min-width="180" align="center" />
          <el-table-column :label="$t('role.operation')" width="240" align="center" fixed="right">
            <template #default="{ row }">
              <el-button-group class="operation-buttons">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleEditRole(row)"
                >
                  {{ $t('role.edit') }}
                </el-button>
                <el-button
                  :type="row.status ? 'danger' : 'success'"
                  size="small"
                  @click="handleToggleRoleStatus(row)"
                >
                  {{ row.status ? $t('role.disable') : $t('role.enable') }}
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDeleteRole(row)"
                >
                  {{ $t('role.delete') }}
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="roleCurrentPage"
            v-model:page-size="rolePageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="roleTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleRoleSizeChange"
            @current-change="handleRoleCurrentChange"
          />
        </div>

        <!-- 角色表单对话框 -->
        <el-dialog
          v-model="roleDialogVisible"
          :title="roleDialogType === 'add' ? $t('role.addRoleDialog') : $t('role.editRoleDialog')"
          width="500px"
        >
          <el-form
            ref="roleFormRef"
            :model="roleForm"
            :rules="roleRules"
            label-width="100px"
          >
            <el-form-item :label="$t('role.roleName')" prop="roleName">
              <el-input v-model="roleForm.roleName" />
            </el-form-item>
            <el-form-item :label="$t('role.roleCode')" prop="roleCode">
              <el-input v-model="roleForm.roleCode" :disabled="roleDialogType === 'edit'" />
            </el-form-item>
            <el-form-item :label="$t('role.roleDescription')" prop="description">
              <el-input v-model="roleForm.description" type="textarea" :rows="3" />
            </el-form-item>
            <el-form-item :label="$t('role.status')" prop="status">
              <el-switch
                v-model="roleForm.status"
                :active-value="true"
                :inactive-value="false"
                :active-text="$t('role.active')"
                :inactive-text="$t('role.inactive')"
              />
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="roleDialogVisible = false">{{ $t('role.cancel') }}</el-button>
              <el-button type="primary" @click="handleRoleSubmit">{{ $t('role.confirm') }}</el-button>
            </span>
          </template>
        </el-dialog>
      </el-tab-pane>

      <!-- 角色用户设置标签页 -->
      <el-tab-pane name="role-users" :label="$t('role.roleUsers')">
        <div class="role-user-container role-users-tab">
          <div class="role-search-area">
            <el-input
              v-model="roleSearchInput"
              :placeholder="$t('role.searchPlaceholder')"
              style="width: 100%;"
              clearable
              @input="filterRoles"
            />
          </div>
          
          <!-- 角色与用户设置表格 -->
          <div class="role-user-tables">
            <!-- 角色权限设置表格 -->
            <div class="role-permissions-table">
              <el-table 
                :data="filteredRolePermissions" 
                border 
                style="width: 100%"
                highlight-current-row
                @row-click="handleRoleRowClick"
                ref="rolePermissionTableRef"
                header-cell-class-name="custom-table-header"
                class="custom-table"
              >
                <el-table-column prop="roleName" :label="$t('role.roleName')" align="center" min-width="120" header-align="center" />
                <el-table-column prop="roleCode" :label="$t('role.roleCode')" align="center" min-width="120" header-align="center" />
                <el-table-column :label="$t('role.add')" align="center" width="70">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.permissions.add" />
                  </template>
                </el-table-column>
                <el-table-column :label="$t('role.editPermission')" align="center" width="70">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.permissions.edit" />
                  </template>
                </el-table-column>
                <el-table-column :label="$t('role.deletePermission')" align="center" width="70">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.permissions.delete" />
                  </template>
                </el-table-column>
                <el-table-column :label="$t('role.verify')" align="center" width="70">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.permissions.verify" />
                  </template>
                </el-table-column>
                <el-table-column :label="$t('role.import')" align="center" width="70">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.permissions.import" />
                  </template>
                </el-table-column>
                <el-table-column :label="$t('role.export')" align="center" width="70">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.permissions.export" />
                  </template>
                </el-table-column>
                <el-table-column :label="$t('role.query')" align="center" width="70">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.permissions.query" />
                  </template>
                </el-table-column>
                <el-table-column :label="$t('role.report')" align="center" width="100">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.permissions.report" />
                  </template>
                </el-table-column>
              </el-table>
              
              <div class="table-header-blue">
                <span>{{ $t('role.roleUsersTable') }}</span>
              </div>
              
              <el-table 
                v-loading="usersLoading"
                :data="currentRoleUsers" 
                border 
                style="width: 100%" 
                header-cell-class-name="custom-table-header"
                class="custom-table"
                :element-loading-text="$t('role.loadingUsers')"
              >
                <el-table-column prop="username" :label="$t('role.username')" align="center" min-width="120" header-align="center" />
                <el-table-column prop="name" :label="$t('role.name')" align="center" min-width="120" header-align="center" />
                <el-table-column prop="department" :label="$t('role.department')" align="center" min-width="150" header-align="center" />
                <el-table-column prop="position" :label="$t('role.position')" align="center" min-width="120" header-align="center" />
                <el-table-column prop="email" :label="$t('role.email')" align="center" min-width="180" header-align="center" />
                <el-table-column prop="phone" :label="$t('role.phone')" align="center" min-width="130" header-align="center" />
                <template #empty>
                  <el-empty :description="$t('role.noUsersForRole')" />
                </template>
              </el-table>
            </div>
          </div>
          
          <div class="action-buttons">
            <el-button type="primary" @click="saveRolePermissions">{{ $t('role.saveRolePermissions') }}</el-button>
            <el-button @click="resetRolePermissions">{{ $t('role.reset') }}</el-button>
          </div>
        </div>
      </el-tab-pane>

      <!-- 角色菜单设置标签页 -->
      <el-tab-pane name="role-menus" :label="$t('role.roleMenus')">
        <div class="role-menu-container">
          <div class="role-select-area">
            <el-select v-model="selectedRoleForMenu" :placeholder="$t('role.selectRole')" @change="handleRoleChangeForMenu">
              <el-option
                v-for="role in roleList"
                :key="role.id"
                :label="role.roleName"
                :value="role.id"
              />
            </el-select>
            <el-button 
              type="warning" 
              @click="handleInitializePermissions"
              :loading="initializingPermissions"
              style="margin-left: 10px;"
            >
              {{ initializingPermissions ? $t('role.initializingPermissions') : $t('role.initializePermissions') }}
            </el-button>
          </div>

          <div class="menu-tree" v-if="selectedRoleForMenu">
            <div v-loading="menuLoading" :element-loading-text="$t('role.loadingMenus')">
              <el-tree
                ref="menuTreeRef"
                :data="menuTree"
                show-checkbox
                node-key="id"
                check-strictly
                default-expand-all
                :props="{
                  label: 'name',
                  children: 'children'
                }"
              />
            </div>
            <div class="menu-actions">
              <el-button type="primary" @click="saveRoleMenus">{{ $t('role.saveMenuPermissions') }}</el-button>
              <el-button @click="resetRoleMenus">{{ $t('role.reset') }}</el-button>
            </div>
          </div>
          
          <div class="no-role-selected" v-else>
            <el-empty :description="$t('role.noRoleSelected')" />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, nextTick, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  Search,
  Plus,
  Delete
} from '@element-plus/icons-vue'
import { getRoles, createRole, updateRole, deleteRole, toggleRoleStatus, getRoleUsers, getRoleMenus, saveRoleMenus as saveRoleMenusAPI, initializeRoleMenuPermissions } from '@/api/role'
import { getMenuTree } from '@/api/menu'
import { convertMenuDataToTree, filterValidMenuIds } from '@/utils/menuUtils'

const { t } = useI18n()

// 标签页激活状态
const activeTab = ref('role-definition')

// 角色定义部分
const roleLoading = ref(false)
const roleList = ref([])
const roleSearchQuery = ref('')
const roleCurrentPage = ref(1)
const rolePageSize = ref(10)
const roleTotal = ref(0)

// 角色表单对话框
const roleDialogVisible = ref(false)
const roleDialogType = ref('add')
const roleFormRef = ref(null)
const roleForm = reactive({
  roleName: '',
  roleCode: '',
  description: '',
  status: true
})

// 角色表单验证规则
const roleRules = computed(() => ({
  roleName: [
    { required: true, message: t('role.roleNameRequired'), trigger: 'blur' },
    { min: 2, max: 20, message: t('role.roleNameLength'), trigger: 'blur' }
  ],
  roleCode: [
    { required: true, message: t('role.roleCodeRequired'), trigger: 'blur' },
    { min: 2, max: 30, message: t('role.roleCodeLength'), trigger: 'blur' }
  ]
}))

// 角色用户设置部分 - 新的表格布局
const roleSearchInput = ref('')
const rolePermissionTableRef = ref(null)
const selectedRoleId = ref(null)

// 过滤后的角色权限列表
const filteredRolePermissions = ref([])

// 当前选中角色的用户列表
const currentRoleUsers = ref([])

// 角色权限列表数据
const rolePermissionsList = ref([])

// 用户加载状态
const usersLoading = ref(false)

// 角色菜单设置部分
const selectedRoleForMenu = ref(null)
const menuTreeRef = ref(null)
const menuTree = ref([])
const checkedMenus = ref([]) // 默认选中的菜单
const menuLoading = ref(false)
const initializingPermissions = ref(false)

// 监听标签页变化，当切换到角色用户设置标签页时，初始化角色权限列表
watch(() => activeTab.value, (newValue) => {
  if (newValue === 'role-users') {
    initRolePermissionsList()
    
    // 在下一个DOM更新周期后，直接修改表头样式
    setTimeout(() => {
      const tableHeaders = document.querySelectorAll('.el-table__header th');
      tableHeaders.forEach(th => {
        th.style.backgroundColor = '#79a8d0';
        th.style.color = 'white';
        th.style.fontWeight = 'bold';
      });
      
      // 默认选中第一行并加载相关用户信息
      setTimeout(() => {
        if (filteredRolePermissions.value.length > 0) {
          const firstRole = filteredRolePermissions.value[0];
          selectedRoleId.value = firstRole.id;
          
          // 加载该角色关联的用户
          loadRoleUsers(firstRole.id);
          
          // 通过表格ref实例设置当前行为选中状态
          if (rolePermissionTableRef.value) {
            rolePermissionTableRef.value.setCurrentRow(firstRole);
          }
        }
      }, 100);
    }, 100);
  } else if (newValue === 'role-menus') {
    // 角色菜单设置标签页，初始化菜单树并默认选择第一个角色
    initMenuTree().then(() => {
      nextTick(() => {
        if (roleList.value.length > 0) {
          // 设置选中第一个角色
          selectedRoleForMenu.value = roleList.value[0].id;
          
          // 加载该角色关联的菜单
          handleRoleChangeForMenu(roleList.value[0].id);
        }
      });
    }).catch(error => {
      console.error('初始化菜单树失败:', error);
    });
  }
})

// 监听选中菜单变化，更新树形组件的选中状态
watch(checkedMenus, (newCheckedMenus) => {
  if (menuTreeRef.value && newCheckedMenus) {
    nextTick(() => {
      // 清除所有选中状态
      menuTreeRef.value.setCheckedKeys([])
      // 设置新的选中状态
      if (Array.isArray(newCheckedMenus) && newCheckedMenus.length > 0) {
        menuTreeRef.value.setCheckedKeys(newCheckedMenus)
      }
      console.log('更新树形组件选中状态:', newCheckedMenus)
    })
  }
}, { deep: true })

// 初始化菜单树 - 从后端API获取
const initMenuTree = async () => {
  try {
    menuLoading.value = true
    const response = await getMenuTree()
    const responseData = response.data || response
    
    if (responseData.code === 200) {
      // 转换后端菜单数据为前端树形结构
      menuTree.value = convertMenuDataToTree(responseData.data)
      console.log('从后端加载菜单树成功:', menuTree.value)
    } else {
      console.error('获取菜单树失败:', responseData.message)
      ElMessage.error(t('role.getMenuTreeError'))
      menuTree.value = []
    }
  } catch (error) {
    console.error('获取菜单树失败:', error)
    ElMessage.error(t('role.getMenuTreeError'))
    menuTree.value = []
  } finally {
    menuLoading.value = false
  }
}

// 角色菜单设置相关方法
const handleRoleChangeForMenu = async (roleId) => {
  // 加载角色下的已选菜单
  try {
    const response = await getRoleMenus(roleId)
    // 兼容不同的响应格式
    const responseData = response.data || response
    
    if (responseData.code === 200) {
      // 过滤无效的菜单ID（确保菜单ID在当前菜单树中存在）
      const validMenuIds = filterValidMenuIds(menuTree.value, responseData.data || [])
      checkedMenus.value = validMenuIds
      console.log('加载角色菜单:', roleId, '原始数据:', responseData.data, '有效数据:', validMenuIds)
    } else {
      // 如果没有设置菜单权限，则默认为空
      checkedMenus.value = []
      console.warn('角色暂无菜单权限配置')
    }
  } catch (error) {
    console.error('获取角色菜单失败:', error)
    checkedMenus.value = []
    ElMessage.warning(t('role.getRoleMenusError'))
  }
}

const saveRoleMenus = async () => {
  // 保存角色菜单设置
  if (!selectedRoleForMenu.value) {
    ElMessage.warning(t('role.selectRoleFirst'))
    return
  }
  
  try {
    const checkedNodes = menuTreeRef.value.getCheckedNodes()
    const checkedKeys = checkedNodes.map(node => node.id)
    
    const response = await saveRoleMenusAPI({
      roleId: selectedRoleForMenu.value,
      menuIds: checkedKeys
    })
    
    // 兼容不同的响应格式
    const responseData = response.data || response
    
    if (responseData.code === 200) {
      ElMessage.success(responseData.message || t('role.saveMenusSuccess'))
    } else {
      ElMessage.error(responseData.message || t('role.saveMenusError'))
    }
    
    console.log('保存角色菜单:', selectedRoleForMenu.value, checkedKeys)
  } catch (error) {
    console.error('保存角色菜单权限失败:', error)
    ElMessage.error(t('role.saveMenusError'))
  }
}

const resetRoleMenus = () => {
  // 重置角色菜单设置
  if (!selectedRoleForMenu.value) {
    ElMessage.warning(t('role.selectRoleFirst'))
    return
  }
  handleRoleChangeForMenu(selectedRoleForMenu.value)
}

// 角色表单提交
const handleRoleSubmit = async () => {
  if (!roleFormRef.value) return
  
  await roleFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        let response
        if (roleDialogType.value === 'add') {
          // 新增角色
          response = await createRole(roleForm)
        } else {
          // 编辑角色
          response = await updateRole(roleForm.id, roleForm)
        }
        
        // 兼容不同的响应格式
        const responseData = response.data || response
        
        // 检查业务状态码，后端统一返回200表示成功
        if (responseData.code === 200) {
          const successMessage = roleDialogType.value === 'add' ? t('role.addRoleSuccess') : t('role.updateRoleSuccess')
          ElMessage.success(responseData.message || successMessage)
          roleDialogVisible.value = false
          fetchRoleList() // 刷新角色列表
        } else {
          ElMessage.error(responseData.message || t('role.saveRoleError'))
        }
      } catch (error) {
        console.error('保存角色失败:', error)
        ElMessage.error(t('role.saveRoleError'))
      }
    }
  })
}

// 角色搜索
const handleRoleSearch = () => {
  roleCurrentPage.value = 1
  fetchRoleList()
}

// 添加角色
const handleAddRole = () => {
  roleDialogType.value = 'add'
  // 重置表单
  Object.assign(roleForm, {
    roleName: '',
    roleCode: '',
    description: '',
    status: true
  })
  // 清除表单验证状态
  if (roleFormRef.value) {
    roleFormRef.value.clearValidate()
  }
  roleDialogVisible.value = true
}

// 编辑角色
const handleEditRole = (row) => {
  roleDialogType.value = 'edit'
  roleForm.id = row.id
  roleForm.roleName = row.roleName
  roleForm.roleCode = row.roleCode
  roleForm.description = row.description
  roleForm.status = row.status
  roleDialogVisible.value = true
}

// 删除角色
const handleDeleteRole = (row) => {
  ElMessageBox.confirm(
    t('role.confirmDelete', { name: row.roleName }),
    t('role.deleteConfirm'),
    {
      confirmButtonText: t('role.confirm'),
      cancelButtonText: t('role.cancel'),
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await deleteRole(row.id)
      // 兼容不同的响应格式
      const responseData = response.data || response
      
      if (responseData.code === 200) {
        ElMessage.success(responseData.message || t('role.deleteRoleSuccess'))
        fetchRoleList() // 刷新角色列表
      } else {
        ElMessage.error(responseData.message || t('role.deleteRoleError'))
      }
    } catch (error) {
      console.error('删除角色失败:', error)
      ElMessage.error(t('role.deleteRoleError'))
    }
  }).catch(() => {
    // 取消删除
  })
}

// 切换角色状态
const handleToggleRoleStatus = async (row) => {
  try {
    const newStatus = !row.status
    const response = await toggleRoleStatus(row.id, newStatus)
    
    // 兼容不同的响应格式
    const responseData = response.data || response
    
    if (responseData.code === 200) {
      row.status = newStatus
      const successMessage = newStatus ? t('role.enableRoleSuccess') : t('role.disableRoleSuccess')
      ElMessage.success(responseData.message || successMessage)
    } else {
      ElMessage.error(responseData.message || t('role.toggleStatusError'))
    }
  } catch (error) {
    console.error('切换角色状态失败:', error)
    ElMessage.error(t('role.toggleStatusError'))
  }
}

// 角色分页操作
const handleRoleSizeChange = (size) => {
  rolePageSize.value = size
  fetchRoleList()
}

const handleRoleCurrentChange = (page) => {
  roleCurrentPage.value = page
  fetchRoleList()
}

// 获取角色列表
const fetchRoleList = async () => {
  roleLoading.value = true
  try {
    const response = await getRoles({
      page: roleCurrentPage.value,
      pageSize: rolePageSize.value,
      search: roleSearchQuery.value
    })
    
    // 兼容不同的响应格式
    const responseData = response.data || response
    
    if (responseData.code === 200) {
      roleList.value = responseData.data.list
      roleTotal.value = responseData.data.total
      
      // 更新角色权限列表（如果在角色用户设置标签页）
      if (activeTab.value === 'role-users') {
        initRolePermissionsList()
      }
    } else {
      ElMessage.error(responseData.message || t('role.fetchRoleListError'))
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error(t('role.fetchRoleListError'))
  } finally {
    roleLoading.value = false
  }
}

// 初始化角色权限列表
const initRolePermissionsList = () => {
  // 转换角色列表为角色权限列表
  rolePermissionsList.value = roleList.value.map(role => {
    return {
      id: role.id,
      roleName: role.roleName,
      roleCode: role.roleCode,
      permissions: {
        add: false,
        edit: false,
        delete: false,
        verify: false,
        import: false,
        export: false,
        query: false,
        report: false
      }
    }
  })
  
  // 初始化过滤后的列表
  filteredRolePermissions.value = [...rolePermissionsList.value]
  
  // 默认选中第一行并显示关联用户信息
  nextTick(() => {
    if (filteredRolePermissions.value.length > 0) {
      const firstRole = filteredRolePermissions.value[0]
      selectedRoleId.value = firstRole.id
      loadRoleUsers(firstRole.id)
      
      // 确保表格渲染完成后再设置当前行
      setTimeout(() => {
        if (rolePermissionTableRef.value) {
          rolePermissionTableRef.value.setCurrentRow(firstRole)
        }
      }, 100)
    } else {
      // 清空当前选中角色和用户列表
      selectedRoleId.value = null
      currentRoleUsers.value = []
    }
  })
}

// 过滤角色
const filterRoles = () => {
  if (!roleSearchInput.value) {
    filteredRolePermissions.value = [...rolePermissionsList.value]
  } else {
    filteredRolePermissions.value = rolePermissionsList.value.filter(role => 
      role.roleName.includes(roleSearchInput.value) || 
      role.roleCode.includes(roleSearchInput.value)
    )
  }
  
  // 过滤后，如果有结果则默认选中第一行
  nextTick(() => {
    if (filteredRolePermissions.value.length > 0) {
      const firstRole = filteredRolePermissions.value[0]
      selectedRoleId.value = firstRole.id
      loadRoleUsers(firstRole.id)
      
      // 确保表格渲染完成后再设置当前行
      setTimeout(() => {
        if (rolePermissionTableRef.value) {
          rolePermissionTableRef.value.setCurrentRow(firstRole)
        }
      }, 100)
    } else {
      // 清空当前选中角色和用户列表
      selectedRoleId.value = null
      currentRoleUsers.value = []
    }
  })
}

// 处理角色行点击
const handleRoleRowClick = (row) => {
  // 设置当前选中角色
  selectedRoleId.value = row.id
  
  // 加载该角色关联的用户
  loadRoleUsers(row.id)
}

// 加载角色关联的用户
const loadRoleUsers = async (roleId) => {
  if (!roleId) {
    currentRoleUsers.value = []
    return
  }
  
  usersLoading.value = true
  try {
    const response = await getRoleUsers(roleId)
    
    // 兼容不同的响应格式
    const responseData = response.data || response
    
    if (responseData.code === 200) {
      // 处理返回的用户数据，确保包含position字段
      currentRoleUsers.value = (responseData.data || []).map(user => ({
        ...user,
        position: user.role || user.position || '未设置' // 使用role字段作为position显示
      }))
    } else {
      ElMessage.error(responseData.message || t('role.getRoleUsersError'))
      currentRoleUsers.value = []
    }
  } catch (error) {
    console.error('加载角色用户失败:', error)
    ElMessage.error(t('role.loadRoleUsersError'))
    currentRoleUsers.value = []
  } finally {
    usersLoading.value = false
  }
}

// 保存角色权限
const saveRolePermissions = () => {
  if (!selectedRoleId.value) {
    ElMessage.warning(t('role.selectRoleFirst'))
    return
  }
  
  // 获取选中角色的权限信息
  const selectedRole = rolePermissionsList.value.find(r => r.id === selectedRoleId.value)
  
  if (selectedRole) {
    console.log('保存角色权限:', selectedRole)
    ElMessage.success(t('role.savePermissionsSuccess'))
  }
}

// 重置角色权限
const resetRolePermissions = () => {
  if (!selectedRoleId.value) {
    ElMessage.warning(t('role.selectRoleFirst'))
    return
  }
  
  // 获取选中角色的索引
  const index = rolePermissionsList.value.findIndex(r => r.id === selectedRoleId.value)
  
  if (index !== -1) {
    // 重置该角色的权限
    rolePermissionsList.value[index].permissions = {
      add: false,
      edit: false,
      delete: false,
      verify: false,
      import: false,
      export: false,
      query: false,
      report: false
    }
    
    // 更新过滤后的列表
    filteredRolePermissions.value = [...filteredRolePermissions.value]
    
    ElMessage.info(t('role.resetPermissionsSuccess'))
  }
}

// 初始化角色权限
const handleInitializePermissions = async () => {
  try {
    const confirmResult = await ElMessageBox.confirm(
      t('role.initializeConfirmMessage'),
      t('role.initializeConfirmTitle'),
      {
        confirmButtonText: t('role.confirm'),
        cancelButtonText: t('role.cancel'),
        type: 'warning'
      }
    )

    if (confirmResult === 'confirm') {
      initializingPermissions.value = true
      const response = await initializeRoleMenuPermissions()
      
      if (response.data.code === 200) {
        ElMessage.success(response.data.message || t('role.initializeSuccess'))
        // 重新加载当前选中角色的菜单权限
        if (selectedRoleForMenu.value) {
          await handleRoleChangeForMenu(selectedRoleForMenu.value)
        }
      } else {
        ElMessage.error(response.data.message || t('role.initializeError'))
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('初始化角色权限失败:', error)
      ElMessage.error(t('role.initializeError'))
    }
  } finally {
    initializingPermissions.value = false
  }
}

// 组件挂载后执行
onMounted(() => {
  fetchRoleList()
  
  // 初始化菜单树
  initMenuTree()
  
  // 如果默认激活的标签页是角色用户设置，则初始化角色权限列表
  if (activeTab.value === 'role-users') {
    initRolePermissionsList()
    
    // 在下一个DOM更新周期后，直接修改表头样式
    setTimeout(() => {
      const tableHeaders = document.querySelectorAll('.el-table__header th');
      tableHeaders.forEach(th => {
        th.style.backgroundColor = '#79a8d0';
        th.style.color = 'white';
        th.style.fontWeight = 'bold';
      });
      
      // 默认选中第一行并加载相关用户信息
      setTimeout(() => {
        if (filteredRolePermissions.value.length > 0) {
          const firstRole = filteredRolePermissions.value[0];
          selectedRoleId.value = firstRole.id;
          
          // 加载该角色关联的用户
          loadRoleUsers(firstRole.id);
          
          // 通过表格ref实例设置当前行为选中状态
          if (rolePermissionTableRef.value) {
            rolePermissionTableRef.value.setCurrentRow(firstRole);
          }
        }
      }, 100);
    }, 100);
  } else if (activeTab.value === 'role-menus') {
    // 角色菜单设置标签页，默认选择第一个角色
    nextTick(() => {
      if (roleList.value.length > 0) {
        // 设置选中第一个角色
        selectedRoleForMenu.value = roleList.value[0].id;
        
        // 加载该角色关联的菜单
        handleRoleChangeForMenu(roleList.value[0].id);
      }
    });
  }
})
</script>

<style scoped>
.role-permission {
  padding: 20px;
}

.operation-bar {
  margin-bottom: 20px;
}

.search-area {
  display: flex;
  align-items: center;
}

.search-input {
  width: 300px;
  margin-right: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.operation-buttons {
  display: flex;
  justify-content: center;
}

.role-user-container,
.role-menu-container {
  padding: 20px 0;
}

.role-search-area {
  margin-bottom: 20px;
}

.role-user-tables {
  margin-top: 20px;
}

.table-header-blue {
  background-color: #79a8d0;
  color: white;
  padding: 10px;
  font-weight: bold;
  margin-top: 20px;
  margin-bottom: 0;
}

.action-buttons {
  margin-top: 20px;
  text-align: center;
}

.menu-tree {
  margin-top: 20px;
}

.menu-actions {
  margin-top: 20px;
  text-align: center;
}

.custom-table-header {
  background-color: #79a8d0 !important;
  color: white !important;
  font-weight: bold !important;
}

.custom-table {
  width: 100%; /* 添加一个实际的CSS属性，避免空规则集 */
}

/* 强制覆盖表头样式 */
:deep(.custom-table .el-table__header th) {
  background-color: #79a8d0 !important;
  color: white !important;
  font-weight: bold !important;
}

/* 额外尝试直接指定表头的样式 */
:deep(.el-table__header-wrapper .el-table__header thead tr th) {
  background-color: #79a8d0 !important;
  color: white !important;
  font-weight: bold !important;
}
</style>

<style>
/* 全局样式，作用于整个应用 */
.role-users-tab .el-table__header-wrapper .el-table__header thead tr th {
  background-color: #79a8d0 !important;
  color: white !important;
  font-weight: bold !important;
}
</style> 