<template>
  <div class="material-model-tab">
    <div class="tab-header">
      <div class="header-actions">
        <div class="search-area">
          <el-input
            v-model="searchQuery"
            :placeholder="$t('engineering.searchModel')"
            prefix-icon="Search"
            clearable
            class="search-input"
          />
          <span class="model-type-desc">{{ getModelTypeDesc }}</span>
        </div>
        <div class="action-buttons">
          <el-button type="primary" @click="handleQuery" :loading="loading">
            {{ $t('common.query') }}
          </el-button>
        <el-button type="primary" @click="handleAdd">
          {{ $t('engineering.addModel') }}
        </el-button>
        </div>
      </div>
    </div>
    
    <el-row :gutter="16" class="model-grid">
      <template v-if="loading">
        <div class="loading-container">
          <el-skeleton :rows="3" animated />
          <div class="loading-text">{{ $t('engineering.loadingModels') }}</div>
        </div>
      </template>
      <template v-else-if="modelList.length === 0">
        <div class="empty-result">
          <el-empty :description="$t('engineering.noModelsFound')">
            <template #image>
              <el-icon size="60"><Box /></el-icon>
            </template>
            <el-button type="primary" @click="handleQuery">
              {{ $t('common.retry') }}
            </el-button>
          </el-empty>
        </div>
      </template>
      <template v-else>
        <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3" v-for="model in filteredModelList" :key="model.code || model.id">
        <el-card class="model-card" shadow="hover">
          <div class="model-image">
            <el-image 
              :src="model.image" 
              fit="contain"
                :preview-src-list="model.image ? [model.image] : []">
              <template #error>
                <div class="image-placeholder">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
              <div class="model-status" :class="model.status === '启用' || model.status === 'True' ? 'active' : 'inactive'">
                {{ model.status === 'True' ? '启用' : model.status }}
            </div>
          </div>
          <div class="model-info">
            <h3 class="model-name">{{ model.name }}</h3>
              <!-- 编码和单位同行显示 -->
              <div class="model-code-unit">
            <div class="model-code">{{ model.code }}</div>
                <div class="model-unit" v-if="model.unit">
                  {{ $t('engineering.unit') }}: {{ model.unit }}
                </div>
              </div>
              <!-- 只在非成品型号、非组件型号、非零件型号和非辅料包材型号下显示分类 -->
              <div class="model-category" v-if="!isProductType && !isComponentType && !isPartType && !isAuxiliaryType && !isHardwarePlasticType">
              <el-tag size="small">{{ model.category }}</el-tag>
            </div>
              <!-- 只在非成品型号、非组件型号、非零件型号和非辅料包材型号下显示规格参数 -->
              <div class="model-spec" v-if="!isProductType && !isComponentType && !isPartType && !isAuxiliaryType && !isHardwarePlasticType">{{ model.specification }}</div>
              <div class="model-extra" v-if="['component', 'part', 'auxiliary', 'hardwarePlastic', 'components', 'parts', 'REF_SUB', 'REF_COMP', 'REF_ACC', 'REF_MET'].includes(modelType)">
                <div class="model-supplier" v-if="model.supplier && isShowSupplier">
                {{ $t('engineering.supplier') }}: {{ model.supplier }}
              </div>
            </div>
          </div>
          <div class="model-actions">
            <el-button type="primary" link @click="handleEdit(model)">
              {{ $t('common.edit') }}
            </el-button>
            <el-button type="success" link @click="handleView(model)">
              {{ $t('engineering.view') }}
            </el-button>
            <el-button type="danger" link @click="handleDelete(model)">
              {{ $t('common.delete') }}
            </el-button>
          </div>
        </el-card>
      </el-col>
      </template>
    </el-row>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[12, 24, 36, 48]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑型号对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? $t('engineering.addModel') : $t('engineering.editModel')"
      width="1200px"
      :close-on-click-modal="false"
      class="model-edit-dialog"
    >
      <el-tabs v-model="activeFormTab" type="border-card" class="model-form-tabs">
        <!-- 基本信息视图 -->
        <el-tab-pane :label="$t('engineering.basicInfo')" name="basic">
          <el-form :model="modelForm" :rules="formRules" ref="formRef" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.modelName')" prop="name">
                  <el-input v-model="modelForm.name" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.modelCode')" prop="code">
                  <el-input v-model="modelForm.code" :disabled="dialogType === 'edit'" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.category')" prop="category">
                  <el-input 
                    v-model="modelForm.category" 
                    :placeholder="$t('engineering.selectCategory')" 
                    readonly
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.unit')" prop="unit">
                  <el-select v-model="modelForm.unit" :placeholder="$t('engineering.selectUnit')" style="width: 100%">
                    <el-option label="个" value="个" />
                    <el-option label="套" value="套" />
                    <el-option label="件" value="件" />
                    <el-option label="kg" value="kg" />
                    <el-option label="g" value="g" />
                    <el-option label="m" value="m" />
                    <el-option label="cm" value="cm" />
                    <el-option label="mm" value="mm" />
                    <el-option label="张" value="张" />
                    <el-option label="卷" value="卷" />
                    <el-option label="盒" value="盒" />
                    <el-option label="包" value="包" />
                    <el-option label="米" value="米" />
                    <el-option label="平方米" value="平方米" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="$t('engineering.specification')" prop="specification">
                  <el-input v-model="modelForm.specification" type="textarea" :rows="3" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20" v-if="['component', 'part', 'auxiliary', 'hardwarePlastic'].includes(modelType)">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.supplier')">
                  <el-input v-model="modelForm.supplier" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="['part', 'auxiliary', 'hardwarePlastic'].includes(modelType)">
                <el-form-item :label="$t('engineering.material')" prop="material">
                  <el-input v-model="modelForm.material" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20" v-if="['part', 'auxiliary', 'hardwarePlastic'].includes(modelType)">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.size')" prop="size">
                  <el-input v-model="modelForm.size" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="$t('engineering.status')" prop="status">
                <el-switch
                  v-model="modelForm.status"
                  :active-value="'启用'"
                  :inactive-value="'停用'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('engineering.image')">
                <el-upload
                  class="model-upload"
                  action="#"
                  :auto-upload="false"
                  :show-file-list="false"
                  :on-change="handleImageChange"
                >
                  <el-image
                    v-if="modelForm.image"
                    :src="modelForm.image"
                    class="upload-image"
                    fit="contain"
                  />
                  <el-icon v-else class="upload-icon"><Plus /></el-icon>
                </el-upload>
              </el-form-item>
              <el-form-item :label="$t('engineering.imageSaveToCloudServer')" prop="saveToCloudServer" style="margin-top: 10px;" class="save-to-cloud-checkbox">
                <el-checkbox v-model="modelForm.saveToCloudServer" class="save-to-cloud-checkbox-inner" />
              </el-form-item>
            </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- 销售视图 -->
        <el-tab-pane :label="$t('engineering.salesView')" name="sales">
          <el-form :model="modelForm" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.salesPrice')">
                  <el-input v-model="modelForm.salesPrice" type="number">
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.salesUnit')">
                  <el-select v-model="modelForm.salesUnit" style="width: 100%">
                    <el-option label="个" value="个" />
                    <el-option label="套" value="套" />
                    <el-option label="件" value="件" />
                    <el-option label="kg" value="kg" />
                    <el-option label="g" value="g" />
                    <el-option label="张" value="张" />
                    <el-option label="卷" value="卷" />
                    <el-option label="盒" value="盒" />
                    <el-option label="包" value="包" />
                    <el-option label="米" value="米" />
                    <el-option label="平方米" value="平方米" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.minOrderQty')">
                  <el-input v-model="modelForm.minOrderQty" type="number" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.maxOrderQty')">
                  <el-input v-model="modelForm.maxOrderQty" type="number" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.leadTime')">
                  <el-input v-model="modelForm.leadTime" type="number">
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.warranty')">
                  <el-input v-model="modelForm.warranty" type="number">
                    <template #append>月</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- 仓库视图 -->
        <el-tab-pane :label="$t('engineering.warehouseView')" name="warehouse">
          <el-form :model="modelForm" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.storageLocation')">
                  <el-input v-model="modelForm.storageLocation" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.safetyStock')">
                  <el-input v-model="modelForm.safetyStock" type="number" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.maxStock')">
                  <el-input v-model="modelForm.maxStock" type="number" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.reorderPoint')">
                  <el-input v-model="modelForm.reorderPoint" type="number" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.storageCondition')">
                  <el-select v-model="modelForm.storageCondition" style="width: 100%">
                    <el-option label="常温" value="常温" />
                    <el-option label="低温" value="低温" />
                    <el-option label="干燥" value="干燥" />
                    <el-option label="防潮" value="防潮" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.shelfLife')">
                  <el-input v-model="modelForm.shelfLife" type="number">
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- 财务视图 -->
        <el-tab-pane :label="$t('engineering.financeView')" name="finance">
          <el-form :model="modelForm" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.standardCost')">
                  <el-input v-model="modelForm.standardCost" type="number">
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.averageCost')">
                  <el-input v-model="modelForm.averageCost" type="number">
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.valuationMethod')">
                  <el-select v-model="modelForm.valuationMethod" style="width: 100%">
                    <el-option label="先进先出" value="FIFO" />
                    <el-option label="后进先出" value="LIFO" />
                    <el-option label="加权平均" value="AVERAGE" />
                    <el-option label="标准成本" value="STANDARD" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.taxRate')">
                  <el-input v-model="modelForm.taxRate" type="number">
                    <template #append>%</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.accountSubject')">
                  <el-input v-model="modelForm.accountSubject" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.costCenter')">
                  <el-input v-model="modelForm.costCenter" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- 生产视图 -->
        <el-tab-pane :label="$t('engineering.productionView')" name="production">
          <el-form :model="modelForm" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.productionType')">
                  <el-select v-model="modelForm.productionType" style="width: 100%">
                    <el-option label="自制" value="MAKE" />
                    <el-option label="外购" value="BUY" />
                    <el-option label="委外" value="OUTSOURCE" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.productionLeadTime')">
                  <el-input v-model="modelForm.productionLeadTime" type="number">
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.setupTime')">
                  <el-input v-model="modelForm.setupTime" type="number">
                    <template #append>分钟</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.cycleTime')">
                  <el-input v-model="modelForm.cycleTime" type="number">
                    <template #append>分钟</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.batchSize')">
                  <el-input v-model="modelForm.batchSize" type="number" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.workCenter')">
                  <el-input v-model="modelForm.workCenter" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="$t('engineering.qualityStandard')">
                  <el-input v-model="modelForm.qualityStandard" type="textarea" :rows="3" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- 采购视图 -->
        <el-tab-pane :label="$t('engineering.purchaseView')" name="purchase">
          <el-form :model="modelForm" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.supplier')" prop="supplier">
                  <el-input v-model="modelForm.supplier" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.purchasePrice')">
                  <el-input v-model="modelForm.purchasePrice" type="number">
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.purchaseUnit')">
                  <el-select v-model="modelForm.purchaseUnit" style="width: 100%">
                    <el-option label="个" value="个" />
                    <el-option label="套" value="套" />
                    <el-option label="件" value="件" />
                    <el-option label="kg" value="kg" />
                    <el-option label="g" value="g" />
                    <el-option label="张" value="张" />
                    <el-option label="卷" value="卷" />
                    <el-option label="盒" value="盒" />
                    <el-option label="包" value="包" />
                    <el-option label="米" value="米" />
                    <el-option label="平方米" value="平方米" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.minPurchaseQty')">
                  <el-input v-model="modelForm.minPurchaseQty" type="number" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.purchaseLeadTime')">
                  <el-input v-model="modelForm.purchaseLeadTime" type="number">
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.qualityLevel')">
                  <el-select v-model="modelForm.qualityLevel" style="width: 100%">
                    <el-option label="A级" value="A" />
                    <el-option label="B级" value="B" />
                    <el-option label="C级" value="C" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="$t('engineering.purchaseNote')">
                  <el-input v-model="modelForm.purchaseNote" type="textarea" :rows="3" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- 进出口视图 -->
        <el-tab-pane :label="$t('engineering.importExportView')" name="importExport">
          <el-form :model="modelForm" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.hsCode')">
                  <el-input v-model="modelForm.hsCode" placeholder="请输入海关编码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.originCountry')">
                  <el-select v-model="modelForm.originCountry" style="width: 100%" filterable>
                    <el-option label="中国" value="CN" />
                    <el-option label="美国" value="US" />
                    <el-option label="德国" value="DE" />
                    <el-option label="日本" value="JP" />
                    <el-option label="韩国" value="KR" />
                    <el-option label="其他" value="OTHER" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.importTaxRate')">
                  <el-input v-model="modelForm.importTaxRate" type="number">
                    <template #append>%</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.exportTaxRefund')">
                  <el-input v-model="modelForm.exportTaxRefund" type="number">
                    <template #append>%</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.isDangerous')">
                  <el-switch
                    v-model="modelForm.isDangerous"
                    :active-value="true"
                    :inactive-value="false"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.transportMode')">
                  <el-select v-model="modelForm.transportMode" style="width: 100%">
                    <el-option label="海运" value="SEA" />
                    <el-option label="空运" value="AIR" />
                    <el-option label="陆运" value="LAND" />
                    <el-option label="铁路" value="RAIL" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.packingRequirement')">
                  <el-input v-model="modelForm.packingRequirement" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.inspectionRequired')">
                  <el-switch
                    v-model="modelForm.inspectionRequired"
                    :active-value="true"
                    :inactive-value="false"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="$t('engineering.licenseRequirement')">
                  <el-input v-model="modelForm.licenseRequirement" type="textarea" :rows="3" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- MRP计划视图 -->
        <el-tab-pane :label="$t('engineering.mrpView')" name="mrp">
          <el-form :model="modelForm" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.planningStrategy')">
                  <el-select v-model="modelForm.planningStrategy" style="width: 100%">
                    <el-option label="按库存生产" value="MTS" />
                    <el-option label="按订单生产" value="MTO" />
                    <el-option label="按订单设计" value="ETO" />
                    <el-option label="混合模式" value="HYBRID" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.planningCycle')">
                  <el-input v-model="modelForm.planningCycle" type="number">
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.forecastMethod')">
                  <el-select v-model="modelForm.forecastMethod" style="width: 100%">
                    <el-option label="移动平均" value="MOVING_AVERAGE" />
                    <el-option label="指数平滑" value="EXPONENTIAL_SMOOTHING" />
                    <el-option label="线性回归" value="LINEAR_REGRESSION" />
                    <el-option label="人工预测" value="MANUAL" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.abcCategory')">
                  <el-select v-model="modelForm.abcCategory" style="width: 100%">
                    <el-option label="A类物料" value="A" />
                    <el-option label="B类物料" value="B" />
                    <el-option label="C类物料" value="C" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.demandSource')">
                  <el-select v-model="modelForm.demandSource" style="width: 100%">
                    <el-option label="销售订单" value="SALES_ORDER" />
                    <el-option label="生产订单" value="PRODUCTION_ORDER" />
                    <el-option label="预测需求" value="FORECAST" />
                    <el-option label="安全库存" value="SAFETY_STOCK" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.planner')">
                  <el-input v-model="modelForm.planner" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.safetyStockDays')">
                  <el-input v-model="modelForm.safetyStockDays" type="number">
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.lotSizeRule')">
                  <el-select v-model="modelForm.lotSizeRule" style="width: 100%">
                    <el-option label="最小批量" value="MIN_LOT" />
                    <el-option label="固定批量" value="FIXED_LOT" />
                    <el-option label="经济批量" value="EOQ" />
                    <el-option label="批量倍数" value="LOT_MULTIPLE" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.demandTimeFence')">
                  <el-input v-model="modelForm.demandTimeFence" type="number">
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.supplyTimeFence')">
                  <el-input v-model="modelForm.supplyTimeFence" type="number">
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="$t('engineering.planningNote')">
                  <el-input v-model="modelForm.planningNote" type="textarea" :rows="3" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="handleSave" :loading="saving">
            {{ saving ? $t('common.saving') : $t('common.save') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看型号对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      :title="$t('engineering.viewModel')"
      width="1200px"
      :close-on-click-modal="false"
      class="model-view-dialog"
    >
      <el-tabs v-model="activeViewTab" type="border-card" class="model-form-tabs">
        <!-- 基本信息视图 -->
        <el-tab-pane :label="$t('engineering.basicInfo')" name="basic">
          <el-form :model="viewModelForm" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.modelName')">
                  <el-input v-model="viewModelForm.name" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.modelCode')">
                  <el-input v-model="viewModelForm.code" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.category')">
                  <el-input v-model="viewModelForm.category" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.unit')">
                  <el-input v-model="viewModelForm.unit" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="$t('engineering.specification')">
                  <el-input v-model="viewModelForm.specification" type="textarea" :rows="3" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20" v-if="['component', 'part', 'auxiliary', 'hardwarePlastic'].includes(modelType)">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.supplier')">
                  <el-input v-model="viewModelForm.supplier" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="['part', 'auxiliary', 'hardwarePlastic'].includes(modelType)">
                <el-form-item :label="$t('engineering.material')">
                  <el-input v-model="viewModelForm.material" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20" v-if="['part', 'auxiliary', 'hardwarePlastic'].includes(modelType)">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.size')">
                  <el-input v-model="viewModelForm.size" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.status')">
                  <el-input :value="viewModelForm.status" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.image')">
                  <el-image
                    v-if="viewModelForm.image"
                    :src="viewModelForm.image"
                    class="view-image"
                    fit="contain"
                    :preview-src-list="[viewModelForm.image]"
                  >
                    <template #error>
                      <div class="image-placeholder">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                  <div v-else class="no-image">无图片</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- 销售视图 -->
        <el-tab-pane :label="$t('engineering.salesView')" name="sales">
          <el-form :model="viewModelForm" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.salesPrice')">
                  <el-input v-model="viewModelForm.salesPrice" readonly>
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.salesUnit')">
                  <el-input v-model="viewModelForm.salesUnit" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.minOrderQty')">
                  <el-input v-model="viewModelForm.minOrderQty" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.maxOrderQty')">
                  <el-input v-model="viewModelForm.maxOrderQty" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.leadTime')">
                  <el-input v-model="viewModelForm.leadTime" readonly>
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.warranty')">
                  <el-input v-model="viewModelForm.warranty" readonly>
                    <template #append>月</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- 仓库视图 -->
        <el-tab-pane :label="$t('engineering.warehouseView')" name="warehouse">
          <el-form :model="viewModelForm" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.storageLocation')">
                  <el-input v-model="viewModelForm.storageLocation" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.safetyStock')">
                  <el-input v-model="viewModelForm.safetyStock" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.maxStock')">
                  <el-input v-model="viewModelForm.maxStock" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.reorderPoint')">
                  <el-input v-model="viewModelForm.reorderPoint" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.storageCondition')">
                  <el-input v-model="viewModelForm.storageCondition" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.shelfLife')">
                  <el-input v-model="viewModelForm.shelfLife" readonly>
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- 财务视图 -->
        <el-tab-pane :label="$t('engineering.financeView')" name="finance">
          <el-form :model="viewModelForm" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.standardCost')">
                  <el-input v-model="viewModelForm.standardCost" readonly>
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.averageCost')">
                  <el-input v-model="viewModelForm.averageCost" readonly>
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.valuationMethod')">
                  <el-input v-model="viewModelForm.valuationMethod" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.taxRate')">
                  <el-input v-model="viewModelForm.taxRate" readonly>
                    <template #append>%</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.accountSubject')">
                  <el-input v-model="viewModelForm.accountSubject" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.costCenter')">
                  <el-input v-model="viewModelForm.costCenter" readonly />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- 生产视图 -->
        <el-tab-pane :label="$t('engineering.productionView')" name="production">
          <el-form :model="viewModelForm" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.productionType')">
                  <el-input v-model="viewModelForm.productionType" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.productionLeadTime')">
                  <el-input v-model="viewModelForm.productionLeadTime" readonly>
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.setupTime')">
                  <el-input v-model="viewModelForm.setupTime" readonly>
                    <template #append>分钟</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.cycleTime')">
                  <el-input v-model="viewModelForm.cycleTime" readonly>
                    <template #append>分钟</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.batchSize')">
                  <el-input v-model="viewModelForm.batchSize" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.workCenter')">
                  <el-input v-model="viewModelForm.workCenter" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="$t('engineering.qualityStandard')">
                  <el-input v-model="viewModelForm.qualityStandard" type="textarea" :rows="3" readonly />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- 采购视图 -->
        <el-tab-pane :label="$t('engineering.purchaseView')" name="purchase">
          <el-form :model="viewModelForm" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.supplier')">
                  <el-input v-model="viewModelForm.supplier" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.purchasePrice')">
                  <el-input v-model="viewModelForm.purchasePrice" readonly>
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.purchaseUnit')">
                  <el-input v-model="viewModelForm.purchaseUnit" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.minPurchaseQty')">
                  <el-input v-model="viewModelForm.minPurchaseQty" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.purchaseLeadTime')">
                  <el-input v-model="viewModelForm.purchaseLeadTime" readonly>
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.qualityLevel')">
                  <el-input v-model="viewModelForm.qualityLevel" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="$t('engineering.purchaseNote')">
                  <el-input v-model="viewModelForm.purchaseNote" type="textarea" :rows="3" readonly />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- 进出口视图 -->
        <el-tab-pane :label="$t('engineering.importExportView')" name="importExport">
          <el-form :model="viewModelForm" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.hsCode')">
                  <el-input v-model="viewModelForm.hsCode" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.originCountry')">
                  <el-input v-model="viewModelForm.originCountry" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.importTaxRate')">
                  <el-input v-model="viewModelForm.importTaxRate" readonly>
                    <template #append>%</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.exportTaxRefund')">
                  <el-input v-model="viewModelForm.exportTaxRefund" readonly>
                    <template #append>%</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.isDangerous')">
                  <el-input :value="viewModelForm.isDangerous ? '是' : '否'" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.transportMode')">
                  <el-input v-model="viewModelForm.transportMode" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.packingRequirement')">
                  <el-input v-model="viewModelForm.packingRequirement" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.inspectionRequired')">
                  <el-input :value="viewModelForm.inspectionRequired ? '是' : '否'" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="$t('engineering.licenseRequirement')">
                  <el-input v-model="viewModelForm.licenseRequirement" type="textarea" :rows="3" readonly />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- MRP计划视图 -->
        <el-tab-pane :label="$t('engineering.mrpView')" name="mrp">
          <el-form :model="viewModelForm" label-width="120px" class="form-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.planningStrategy')">
                  <el-input v-model="viewModelForm.planningStrategy" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.planningCycle')">
                  <el-input v-model="viewModelForm.planningCycle" readonly>
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.forecastMethod')">
                  <el-input v-model="viewModelForm.forecastMethod" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.abcCategory')">
                  <el-input v-model="viewModelForm.abcCategory" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.demandSource')">
                  <el-input v-model="viewModelForm.demandSource" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.planner')">
                  <el-input v-model="viewModelForm.planner" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.safetyStockDays')">
                  <el-input v-model="viewModelForm.safetyStockDays" readonly>
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.lotSizeRule')">
                  <el-input v-model="viewModelForm.lotSizeRule" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('engineering.demandTimeFence')">
                  <el-input v-model="viewModelForm.demandTimeFence" readonly>
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('engineering.supplyTimeFence')">
                  <el-input v-model="viewModelForm.supplyTimeFence" readonly>
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="$t('engineering.planningNote')">
                  <el-input v-model="viewModelForm.planningNote" type="textarea" :rows="3" readonly />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="viewDialogVisible = false">{{ $t('common.close') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { Search, Picture, Plus, Box } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { productModelApiNew, componentModelApiNew, partModelApiNew, auxiliaryModelApiNew, hardwarePlasticModelApi } from '@/api/model'
import request from '@/utils/request'

const props = defineProps({
  modelType: {
    type: String,
    required: true
  },
  api: {
    type: Object,
    required: true
  },
  categories: {
    type: Array,
    default: () => []
  },
  categoryConfig: {
    type: Object,
    default: () => ({})
  }
})

const { t, locale } = useI18n()
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)
const dialogVisible = ref(false)
const dialogType = ref('add')
const saving = ref(false)
const formRef = ref()
const activeFormTab = ref('basic')

// 查看对话框相关变量
const viewDialogVisible = ref(false)
const activeViewTab = ref('basic')

const modelForm = ref({
  // 基本信息
  name: '',
  code: '',
  category: '',
  specification: '',
  status: '启用',
  image: '',
  unit: '',
  material: '',
  size: '',
  saveToCloudServer: true,
  
  // 销售信息
  salesPrice: '0.00',
  salesUnit: '',
  minOrderQty: '0.00',
  maxOrderQty: '0.00',
  leadTime: '',
  warranty: '',
  
  // 仓库信息
  storageLocation: '',
  safetyStock: '0.00',
  maxStock: '0.00',
  reorderPoint: '0.00',
  storageCondition: '',
  shelfLife: '',
  
  // 财务信息
  standardCost: '0.00',
  averageCost: '0.00',
  valuationMethod: '',
  taxRate: '0.00',
  accountSubject: '',
  costCenter: '',
  
  // 生产信息
  productionType: '',
  productionLeadTime: '',
  setupTime: '',
  cycleTime: '',
  batchSize: '',
  workCenter: '',
  qualityStandard: '',
  
  // 采购信息
  supplier: '',
  purchasePrice: '0.00',
  purchaseUnit: '',
  minPurchaseQty: '0.00',
  purchaseLeadTime: '',
  qualityLevel: '',
  purchaseNote: '',
  
  // 进出口信息
  hsCode: '',
  originCountry: '',
  importTaxRate: '0.00',
  exportTaxRefund: '0.00',
  isDangerous: false,
  transportMode: '',
  packingRequirement: '',
  inspectionRequired: false,
  licenseRequirement: '',
  
  // MRP计划信息
  planningStrategy: '',
  planningCycle: '',
  forecastMethod: '',
  abcCategory: '',
  demandSource: '',
  planner: '',
  safetyStockDays: '',
  lotSizeRule: '',
  demandTimeFence: '',
  supplyTimeFence: '',
  planningNote: ''
})

const viewModelForm = ref({
  // 基本信息
  name: '',
  code: '',
  category: '',
  specification: '',
  status: '启用',
  image: '',
  unit: '',
  material: '',
  size: '',
  
  // 销售信息
  salesPrice: '',
  salesUnit: '',
  minOrderQty: '',
  maxOrderQty: '',
  leadTime: '',
  warranty: '',
  
  // 仓库信息
  storageLocation: '',
  safetyStock: '',
  maxStock: '',
  reorderPoint: '',
  storageCondition: '',
  shelfLife: '',
  
  // 财务信息
  standardCost: '',
  averageCost: '',
  valuationMethod: '',
  taxRate: '',
  accountSubject: '',
  costCenter: '',
  
  // 生产信息
  productionType: '',
  productionLeadTime: '',
  setupTime: '',
  cycleTime: '',
  batchSize: '',
  workCenter: '',
  qualityStandard: '',
  
  // 采购信息
  supplier: '',
  purchasePrice: '',
  purchaseUnit: '',
  minPurchaseQty: '',
  purchaseLeadTime: '',
  qualityLevel: '',
  purchaseNote: '',
  
  // 进出口信息
  hsCode: '',
  originCountry: '',
  importTaxRate: '',
  exportTaxRefund: '',
  isDangerous: false,
  transportMode: '',
  packingRequirement: '',
  inspectionRequired: false,
  licenseRequirement: '',
  
  // MRP计划信息
  planningStrategy: '',
  planningCycle: '',
  forecastMethod: '',
  abcCategory: '',
  demandSource: '',
  planner: '',
  safetyStockDays: '',
  lotSizeRule: '',
  demandTimeFence: '',
  supplyTimeFence: '',
  planningNote: ''
})

const modelList = ref([])

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入型号名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入型号编码', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  specification: [
    { required: true, message: '请输入规格参数', trigger: 'blur' }
  ]
}

// 计算属性
const filteredModelList = computed(() => {
  if (!searchQuery.value) {
    return modelList.value
  }
  
  const query = searchQuery.value.toLowerCase()
  return modelList.value.filter(model => {
    return model.name?.toLowerCase().includes(query) || 
           model.code?.toLowerCase().includes(query) || 
           model.specification?.toLowerCase().includes(query)
  })
})

// 检查是否为组件型号类型
const isComponentType = computed(() => {
  return props.modelType === 'REF_SUB' || props.modelType === 'components'
})

// 检查是否为成品型号类型
const isProductType = computed(() => {
  return props.modelType === 'REF_FG' || props.modelType === 'products'
})

// 检查是否为零件型号类型
const isPartType = computed(() => {
  return props.modelType === 'REF_COMP' || props.modelType === 'parts'
})

// 检查是否为辅料包材型号类型
const isAuxiliaryType = computed(() => {
  return props.modelType === 'REF_ACC' || props.modelType === 'auxiliary'
})

// 计算是否是塑胶五金型号
const isHardwarePlasticType = computed(() => {
  return props.modelType === 'REF_MET' || props.modelType === 'hardware-plastic'
})

// 检查是否显示供应商信息
const isShowSupplier = computed(() => {
  // 只在非成品型号、非组件型号、非零件型号、非辅料包材型号和非塑胶五金型号时显示供应商信息
  return !isComponentType.value && !isProductType.value && !isPartType.value && !isAuxiliaryType.value && !isHardwarePlasticType.value
})

// 型号类型描述
const getModelTypeDesc = computed(() => {
  // 明确依赖 locale 以确保响应性
  const currentLocale = locale.value
  
  // 优先使用国际化的物料分类编码映射，不使用 categoryConfig.description（因为它可能是固定的中文）
  const newDescMap = {
    'REF_FG': t('engineering.finishedProductDesc'),
    'REF_SUB': t('engineering.subAssemblyDesc'),
    'REF_COMP': t('engineering.componentPartDesc'),
    'REF_ACC': t('engineering.auxiliaryPackagingDesc'),
    'REF_MET': t('engineering.plasticMetalDesc'),
    'REF_ADMIN': t('engineering.administrativeDesc'),
    'REF_VIRTUAL': t('engineering.virtualDesc')
  }
  
  // 检查是否是新的编码格式
  if (newDescMap[props.modelType]) {
    return newDescMap[props.modelType]
  }
  
  // 回退到旧的描述映射
  const oldDescMap = {
    'product': t('engineering.productModelDesc'),
    'component': t('engineering.componentModelDesc'),
    'part': t('engineering.partModelDesc'),
    'auxiliary': t('engineering.auxiliaryModelDesc'),
    'hardwarePlastic': t('engineering.hardwarePlasticModelDesc')
  }
  
  return oldDescMap[props.modelType] || ''
})

// 分类选项 - 如果props.categories为空，使用默认选项
const categoryOptions = computed(() => {
  if (props.categories && props.categories.length > 0) {
    return props.categories
  }
  
  // 提供默认的分类选项
  return [
    { value: 'REF_FG', label: t('engineering.finishedProductModel') },
    { value: 'REF_SUB', label: t('engineering.subAssemblyModel') },
    { value: 'REF_COMP', label: t('engineering.componentPartModel') },
    { value: 'REF_ACC', label: t('engineering.auxiliaryPackagingModel') },
    { value: 'REF_MET', label: t('engineering.plasticMetalModel') },
    { value: 'REF_ADMIN', label: t('engineering.administrativeMaterial') },
    { value: 'REF_VIRTUAL', label: t('engineering.virtualMaterial') }
  ]
})

// 加载型号列表
const loadModelList = async () => {
  try {
    loading.value = true
    // 检查API是否可用
    if (!props.api || typeof props.api.getList !== 'function') {
      console.info('API未配置，使用模拟数据:', props.modelType)
      loadMockData()
      loading.value = false
      return
    }
    
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      search: searchQuery.value
    }
    
    console.info(`开始加载${props.modelType}型号列表，参数:`, params)
    
    let response
    
    // 对于组件型号，特别处理以确保调用正确的API
    if (props.modelType === 'REF_SUB' || props.modelType === 'components') {
      console.info('使用组件型号专用API加载列表数据')
      // 使用组件型号专用API，URL为 /api/models/components
      response = await request({
        url: '/api/models/components',
        method: 'get',
        params: {
          page: params.page,
          pageSize: params.pageSize,
          search: params.search || ''
        }
      })
    }
    // 对于零件型号，特别处理以确保调用正确的API
    else if (props.modelType === 'REF_COMP' || props.modelType === 'parts') {
      console.info('使用零件型号专用API加载列表数据')
      // 使用零件型号专用API，URL为 /api/models/parts
      response = await request({
        url: '/api/models/parts',
        method: 'get',
        params: {
          page: params.page,
          pageSize: params.pageSize,
          search: params.search || ''
        }
      })
    }
    // 对于辅料包材型号，特别处理以确保调用正确的API
    else if (props.modelType === 'REF_ACC' || props.modelType === 'auxiliary') {
      console.info('使用辅料包材型号专用API加载列表数据')
      // 使用辅料包材型号专用API，URL为 /api/models/auxiliary
      response = await request({
        url: '/api/models/auxiliary',
        method: 'get',
        params: {
          page: params.page,
          pageSize: params.pageSize,
          search: params.search || ''
        }
      })
    }
    // 对于塑胶五金型号，特别处理以确保调用正确的API
    else if (props.modelType === 'REF_MET' || props.modelType === 'hardware-plastic') {
      console.info('================================')
      console.info('📦 使用塑胶五金型号专用API加载列表数据')
      const requestParams = {
        page: params.page,
        pageSize: params.pageSize,
        search: params.search || ''
      }
      console.info('📋 塑胶五金型号(REF_MET)列表请求参数:', JSON.stringify(requestParams))
      console.info('🔗 塑胶五金型号(REF_MET)请求URL: /api/models/hardware-plastic')
      
      try {
        // 使用塑胶五金型号API
        console.info('⏳ 正在发送塑胶五金型号(REF_MET)请求...')
        response = await hardwarePlasticModelApi.getList(requestParams)
        console.info('✅ 塑胶五金型号(REF_MET)响应状态:', response.status)
        console.info('📄 塑胶五金型号(REF_MET)响应数据:', JSON.stringify(response.data))
        console.info('================================')
  } catch (error) {
        console.error('❌ 塑胶五金型号(REF_MET)列表加载失败:', error.message)
        console.error('❌ 错误详情:', error)
        console.info('================================')
        throw error
      }
    } else {
      // 其他类型使用通用API
      response = await props.api.getList(params)
    }
    
    console.info(`${props.modelType}型号列表API响应:`, response)
    
    if (response && response.data) {
      // 处理后端API返回的数据格式：{ code: 200, data: { list: [...], total: n } }
      if (response.data.code === 200 && response.data.data) {
        const list = response.data.data.list || []
        if (Array.isArray(list) && list.length > 0) {
          // 格式化decimal字段为显示格式
          const formattedList = list.map(item => {
            if (props.modelType === 'REF_SUB' || props.modelType === 'components' ||
                props.modelType === 'REF_COMP' || props.modelType === 'parts' ||
                props.modelType === 'REF_ACC' || props.modelType === 'auxiliary' ||
                props.modelType === 'REF_MET' || props.modelType === 'hardware-plastic') {
              return formatDecimalFields(item)
            }
            return item
          })
          
          modelList.value = formattedList
          total.value = response.data.data.total || list.length
          console.info(`成功加载 ${list.length} 条${props.modelType}数据, 总数: ${total.value}`)
          return
        } else {
          console.info('API返回空列表，列表数据已清空')
          modelList.value = []
          total.value = 0
        }
      } else {
        // 兼容旧的数据格式：直接在response.data中
        const list = response.data.list || response.data || []
        if (Array.isArray(list) && list.length > 0) {
          // 格式化decimal字段为显示格式
          const formattedList = list.map(item => {
            if (props.modelType === 'REF_SUB' || props.modelType === 'components' ||
                props.modelType === 'REF_COMP' || props.modelType === 'parts' ||
                props.modelType === 'REF_ACC' || props.modelType === 'auxiliary' ||
                props.modelType === 'REF_MET' || props.modelType === 'hardware-plastic') {
              return formatDecimalFields(item)
            }
            return item
          })
          
          modelList.value = formattedList
          total.value = response.data.total || list.length
          console.info(`成功加载 ${list.length} 条${props.modelType}数据（兼容格式）, 总数: ${total.value}`)
          return
        } else {
          console.info('API返回空数据，列表数据已清空')
          modelList.value = []
          total.value = 0
        }
      }
    } else {
      console.info('API响应格式异常，使用模拟数据:', props.modelType)
    loadMockData()
    }
  } catch (error) {
    console.info('API调用失败，使用模拟数据:', props.modelType, error.message)
    console.error('型号列表加载详细错误:', error)
    loadMockData()
  } finally {
    loading.value = false
  }
}

// 加载模拟数据
const loadMockData = () => {
  const mockData = {
    product: [
      {
        id: 1,
        code: 'MC001',
        name: '断路器A型',
        category: '断路器',
        specification: '220V/10A',
        status: '启用',
        image: 'https://img.alicdn.com/imgextra/i4/2206684561233/O1CN01Z5pLmM1TfMpXhXGXk_!!2206684561233.jpg',
        createTime: '2024-02-20'
      },
      {
        id: 2,
        code: 'MC002',
        name: '继电器B型',
        category: '继电器',
        specification: '12V/5A',
        status: '启用',
        image: 'https://img.alicdn.com/imgextra/i2/2206684561233/O1CN01X5pLmM1TfMpXhXGXk_!!2206684561233.jpg',
        createTime: '2024-02-19'
      }
    ],
    components: [
      {
        id: 1,
        code: 'CMP001',
        name: '电容器C1',
        category: '电子元件',
        specification: '10μF/25V',
        status: '启用',
        supplier: '电子元件公司',
        unit: '个',
        image: '',
        createTime: '2024-02-20'
      },
      {
        id: 2,
        code: 'CMP002',
        name: '电阻器R1',
        category: '电子元件',
        specification: '1kΩ/0.25W',
        status: '启用',
        supplier: '电子元件公司',
        unit: '个',
        image: '',
        createTime: '2024-02-19'
      },
      {
        id: 3,
        code: 'CMP003',
        name: '电机控制板',
        category: 'PCB组件',
        specification: '电机驱动控制电路',
        status: '启用',
        supplier: '科技电子有限公司',
        unit: '块',
        image: '',
        createTime: '2024-03-15',
        salesPrice: '45.00',
        purchasePrice: '28.50'
      },
      {
        id: 4,
        code: 'CMP004',
        name: '触摸屏模组',
        category: '显示模组',
        specification: '7英寸IPS触摸屏',
        status: '启用',
        supplier: '显示技术有限公司',
        unit: '个',
        image: '',
        createTime: '2024-03-22',
        salesPrice: '120.00',
        purchasePrice: '80.00'
      }
    ],
    component: [
      {
        id: 1,
        code: 'CMP001',
        name: '电容器C1',
        category: '电子元件',
        specification: '10μF/25V',
        status: '启用',
        supplier: '电子元件公司',
        unit: '个',
        image: '',
        createTime: '2024-02-20'
      },
      {
        id: 2,
        code: 'CMP002',
        name: '电阻器R1',
        category: '电子元件',
        specification: '1kΩ/0.25W',
        status: '启用',
        supplier: '电子元件公司',
        unit: '个',
        image: '',
        createTime: '2024-02-19'
      }
    ],
    REF_SUB: [
      {
        id: 1,
        code: 'SUB001',
        name: '电源模块',
        category: 'REF_SUB',
        specification: '开关电源模块 12V 5A',
        status: '启用',
        supplier: '电源技术有限公司',
        unit: '个',
        image: '',
        createTime: '2024-04-01',
        salesPrice: '35.00',
        purchasePrice: '22.50'
      },
      {
        id: 2,
        code: 'SUB002',
        name: '控制面板组件',
        category: 'REF_SUB',
        specification: '带LED指示灯控制面板',
        status: '启用',
        supplier: '控制系统有限公司',
        unit: '套',
        image: '',
        createTime: '2024-04-05',
        salesPrice: '58.00',
        purchasePrice: '37.60'
      },
      {
        id: 3,
        code: 'SUB003',
        name: '通信模块',
        category: 'REF_SUB',
        specification: '支持RS485通信协议',
        status: '启用',
        supplier: '通信技术有限公司',
        unit: '个',
        image: '',
        createTime: '2024-04-10',
        salesPrice: '42.50',
        purchasePrice: '28.00'
      }
    ],
    part: [
      {
        id: 1,
        code: 'PRT001',
        name: '螺丝M3×10',
        category: '螺丝',
        specification: 'M3×10mm 十字头螺丝',
        status: '启用',
        supplier: '紧固件供应商',
        unit: '个',
        material: '不锈钢',
        size: 'M3×10',
        image: '',
        createTime: '2024-02-20'
      },
      {
        id: 2,
        code: 'PRT002',
        name: '弹簧垫片',
        category: '垫片',
        specification: 'M3弹簧垫片',
        status: '启用',
        supplier: '紧固件供应商',
        unit: '个',
        material: '弹簧钢',
        size: 'M3',
        image: '',
        createTime: '2024-02-19'
      }
    ],
    auxiliary: [
      {
        id: 1,
        code: 'AUX001',
        name: '彩色包装盒',
        category: '包装材料',
        specification: '300×200×100mm 彩印包装盒',
        status: '启用',
        supplier: '包装材料公司',
        unit: '个',
        image: '',
        createTime: '2024-02-20'
      },
      {
        id: 2,
        code: 'AUX002',
        name: '产品标签',
        category: '标签贴纸',
        specification: '50×30mm 防水标签',
        status: '启用',
        supplier: '印刷材料公司',
        unit: '张',
        image: '',
        createTime: '2024-02-19'
      }
    ],
    hardwarePlastic: [
      {
        id: 1,
        code: 'HP001',
        name: '不锈钢支架',
        category: '五金件',
        specification: '304不锈钢 L型支架',
        status: '启用',
        supplier: '五金制品公司',
        unit: '个',
        material: '304不锈钢',
        size: '100×80×5mm',
        image: '',
        createTime: '2024-02-20'
      },
      {
        id: 2,
        code: 'HP002',
        name: 'ABS外壳',
        category: '塑料件',
        specification: 'ABS阻燃外壳',
        status: '启用',
        supplier: '塑料制品公司',
        unit: '个',
        material: 'ABS',
        size: '120×80×40mm',
        image: '',
        createTime: '2024-02-19'
      }
    ]
  }
  
  // 为components类型也使用REF_SUB的模拟数据
  mockData.components = mockData.REF_SUB.map(item => ({
    ...item,
    category: 'components'
  }))
  
  // 为parts类型也使用REF_COMP的模拟数据
  mockData.parts = mockData.REF_COMP.map(item => ({
    ...item,
    category: 'parts'
  }))
  
  // 为auxiliary类型也使用REF_ACC的模拟数据
  mockData.auxiliary = mockData.REF_ACC.map(item => ({
    ...item,
    category: 'auxiliary'
  }))
  
  // 为hardware-plastic类型使用REF_MET的模拟数据
  mockData['hardware-plastic'] = mockData.hardwarePlastic.map(item => ({
    ...item,
    category: 'hardware-plastic'
  }))
  
  // 判断是否存在对应类型的模拟数据
  if (mockData[props.modelType]) {
    console.info(`使用${props.modelType}类型的模拟数据, ${mockData[props.modelType].length}条`)
    modelList.value = mockData[props.modelType]
  total.value = modelList.value.length
  } else {
    console.warn(`找不到${props.modelType}类型的模拟数据，使用空数组`)
    modelList.value = []
    total.value = 0
  }
}

const handleAdd = () => {
  dialogType.value = 'add'
  resetForm()
  // 设置默认分类为当前标签页的分类
  setDefaultCategory()
  dialogVisible.value = true
}

const handleEdit = async (model) => {
  try {
  dialogType.value = 'edit'
  activeFormTab.value = 'basic'
    
    // 检查是否是成品型号，如果是则获取完整详情
    if (props.modelType === 'REF_FG' || props.modelType === 'products') {
      console.info('获取成品型号详情，ID:', model.id)
      const response = await productModelApiNew.getDetail(model.id)
      
      if (response && response.data) {
        // 处理后端API返回的数据格式
        let detailData = response.data
        if (response.data.code === 200 && response.data.data) {
          detailData = response.data.data
        }
        
        // 格式化decimal类型字段为"0.00"格式
        detailData = formatDecimalFields(detailData)
        
        // 将详细数据赋值到表单
        modelForm.value = { ...detailData }
        console.info('成功加载成品型号详情:', detailData)
      } else {
        // 如果获取详情失败，使用卡片数据作为后备方案
        console.info('获取详情失败，使用卡片数据')
        // 格式化decimal类型字段为"0.00"格式
        const formattedModel = formatDecimalFields(model)
        modelForm.value = { ...formattedModel }
      }
    } 
    // 检查是否是组件型号
    else if (props.modelType === 'REF_SUB' || props.modelType === 'components') {
      console.info('获取组件型号详情，ID:', model.id)
      const response = await componentModelApiNew.getDetail(model.id)
      
      if (response && response.data) {
        // 处理后端API返回的数据格式
        let detailData = response.data
        if (response.data.code === 200 && response.data.data) {
          detailData = response.data.data
        }
        
        // 格式化decimal类型字段为"0.00"格式
        detailData = formatDecimalFields(detailData)
        
        // 将详细数据赋值到表单
        modelForm.value = { ...detailData }
        console.info('成功加载组件型号详情:', detailData)
      } else {
        // 如果获取详情失败，使用卡片数据作为后备方案
        console.info('获取详情失败，使用卡片数据')
        // 格式化decimal类型字段为"0.00"格式
        const formattedModel = formatDecimalFields(model)
        modelForm.value = { ...formattedModel }
      }
    }
    // 检查是否是零件型号
    else if (props.modelType === 'REF_COMP' || props.modelType === 'parts') {
      console.info('获取零件型号详情，ID:', model.id)
      const response = await partModelApiNew.getDetail(model.id)
      
      if (response && response.data) {
        // 处理后端API返回的数据格式
        let detailData = response.data
        if (response.data.code === 200 && response.data.data) {
          detailData = response.data.data
        }
        
        // 格式化decimal类型字段为"0.00"格式
        detailData = formatDecimalFields(detailData)
        
        // 将详细数据赋值到表单
        modelForm.value = { ...detailData }
        console.info('成功加载零件型号详情:', detailData)
      } else {
        // 如果获取详情失败，使用卡片数据作为后备方案
        console.info('获取详情失败，使用卡片数据')
        // 格式化decimal类型字段为"0.00"格式
        const formattedModel = formatDecimalFields(model)
        modelForm.value = { ...formattedModel }
      }
    }
    // 检查是否是辅料包材型号
    else if (props.modelType === 'REF_ACC' || props.modelType === 'auxiliary') {
      console.info('获取辅料包材型号详情，ID:', model.id)
      const response = await auxiliaryModelApiNew.getDetail(model.id)
      
      if (response && response.data) {
        // 处理后端API返回的数据格式
        let detailData = response.data
        if (response.data.code === 200 && response.data.data) {
          detailData = response.data.data
        }
        
        // 格式化decimal类型字段为"0.00"格式
        detailData = formatDecimalFields(detailData)
        
        // 将详细数据赋值到表单
        modelForm.value = { ...detailData }
        console.info('成功加载辅料包材型号详情:', detailData)
      } else {
        // 如果获取详情失败，使用卡片数据作为后备方案
        console.info('获取详情失败，使用卡片数据')
        // 格式化decimal类型字段为"0.00"格式
        const formattedModel = formatDecimalFields(model)
        modelForm.value = { ...formattedModel }
      }
    }
    // 检查是否是塑胶五金型号
    else if (props.modelType === 'REF_MET' || props.modelType === 'hardware-plastic') {
      console.info('================================')
      console.info(`📦 准备编辑塑胶五金型号(REF_MET)，ID: ${model.id}, 代码: ${model.code}, 名称: ${model.name}`)
      console.info(`🔗 塑胶五金型号(REF_MET)详情请求URL: /api/models/hardware-plastic/${model.id}`)
      
      try {
        console.info('⏳ 正在发送塑胶五金型号(REF_MET)详情请求...')
        const response = await hardwarePlasticModelApi.getDetail(model.id)
        console.info('✅ 塑胶五金型号(REF_MET)详情响应状态:', response.status)
        console.info('📄 塑胶五金型号(REF_MET)详情响应数据:', JSON.stringify(response.data))
        
        if (response && response.data) {
          // 处理后端API返回的数据格式
          let detailData = response.data
          if (response.data.code === 200 && response.data.data) {
            detailData = response.data.data
          }
          
          // 将详细数据赋值到编辑表单
          modelForm.value = { ...detailData }
          console.info('📋 成功加载塑胶五金型号(REF_MET)编辑详情')
          console.info('================================')
        } else {
          // 如果获取详情失败，使用卡片数据作为后备方案
          console.warn('⚠️ 获取编辑详情失败，使用卡片数据')
  modelForm.value = { ...model }
          console.info('================================')
        }
      } catch (error) {
        console.error('❌ 塑胶五金型号(REF_MET)详情获取失败:', error)
        console.error('❌ 错误详情:', {
          message: error.message,
          response: error.response ? {
            status: error.response.status,
            data: JSON.stringify(error.response.data || {})
          } : '无响应数据'
        })
        // 如果获取详情失败，使用卡片数据作为后备方案
        console.warn('⚠️ 获取编辑详情失败，使用卡片数据')
        modelForm.value = { ...model }
        console.info('================================')
      }
    }
    else {
      // 其他类型型号直接使用卡片数据
      console.info('使用卡片数据作为其他类型型号的编辑数据', model)
      const formattedModel = formatDecimalFields(model)
      modelForm.value = { ...formattedModel }
    }
    
  dialogVisible.value = true
  } catch (error) {
    console.info('编辑操作准备失败:', error.message || error)
    ElMessage.error('加载详情失败: ' + (error.message || '未知错误'))
  }
}

// 添加新函数，用于格式化decimal类型字段
const formatDecimalFields = (data) => {
  const result = { ...data }
  
  // 销售信息字段
  if (result.salesPrice !== undefined) result.salesPrice = formatToDecimal(result.salesPrice)
  if (result.minOrderQty !== undefined) result.minOrderQty = formatToDecimal(result.minOrderQty)
  if (result.maxOrderQty !== undefined) result.maxOrderQty = formatToDecimal(result.maxOrderQty)
  
  // 仓库信息字段
  if (result.safetyStock !== undefined) result.safetyStock = formatToDecimal(result.safetyStock)
  if (result.maxStock !== undefined) result.maxStock = formatToDecimal(result.maxStock)
  if (result.reorderPoint !== undefined) result.reorderPoint = formatToDecimal(result.reorderPoint)
  
  // 财务信息字段
  if (result.standardCost !== undefined) result.standardCost = formatToDecimal(result.standardCost)
  if (result.averageCost !== undefined) result.averageCost = formatToDecimal(result.averageCost)
  if (result.taxRate !== undefined) result.taxRate = formatToDecimal(result.taxRate)
  
  // 采购信息字段
  if (result.purchasePrice !== undefined) result.purchasePrice = formatToDecimal(result.purchasePrice)
  if (result.minPurchaseQty !== undefined) result.minPurchaseQty = formatToDecimal(result.minPurchaseQty)
  
  // 进出口信息字段
  if (result.importTaxRate !== undefined) result.importTaxRate = formatToDecimal(result.importTaxRate)
  if (result.exportTaxRefund !== undefined) result.exportTaxRefund = formatToDecimal(result.exportTaxRefund)
  
  return result
}

// 格式化为两位小数的字符串
const formatToDecimal = (value) => {
  if (value === '' || value === null || value === undefined) {
    return '0.00'
  }
  
  // 尝试将值转换为数字
  const numValue = parseFloat(value)
  if (isNaN(numValue)) {
    return '0.00'
  }
  
  // 格式化为两位小数的字符串
  return numValue.toFixed(2)
}

const handleView = async (model) => {
  console.log('点击了查看按钮，显示型号详情:', model)
  console.log('点击查看前，viewDialogVisible =', viewDialogVisible.value)
  
  // 简化处理，只设置对话框显示
  viewDialogVisible.value = true
  console.log('设置后，viewDialogVisible =', viewDialogVisible.value)
  
  // 模拟基本数据
  viewModelForm.value = {
    name: model.name,
    code: model.code,
    category: model.category,
    unit: model.unit,
    specification: model.specification,
    image: model.image,
    status: model.status
  }
  
  // 其他数据处理暂时注释掉
  /*
  viewMode.value = true
  activeViewTab.value = 'basic'
  loading.value = true
  
  try {
    console.info('查看型号详情:', model.id, model.code, model.name)
    
    // 如果是成品型号，使用专用API获取详细数据
    if (props.modelType === 'REF_FG' || props.modelType === 'products') {
      const response = await productModelApiNew.getDetail(model.id)
      viewModelForm.value = response.data
    }
    // 如果是组件型号，使用专用API获取详细数据
    else if (props.modelType === 'REF_SUB' || props.modelType === 'components') {
      const response = await componentModelApiNew.getDetail(model.id)
      viewModelForm.value = response.data
    }
    // 如果是零件型号，使用专用API获取详细数据
    else if (props.modelType === 'REF_COMP' || props.modelType === 'parts') {
      const response = await partModelApiNew.getDetail(model.id)
      viewModelForm.value = response.data
    }
    // 如果是辅料包材型号，使用专用API获取详细数据
    else if (props.modelType === 'REF_ACC' || props.modelType === 'auxiliary') {
      const response = await auxiliaryModelApiNew.getDetail(model.id)
      viewModelForm.value = response.data
    }
    // 如果是塑胶五金型号，使用专用API获取详细数据
    else if (props.modelType === 'REF_MET' || props.modelType === 'hardware-plastic') {
      console.info('================================')
      console.info(`📦 准备查看塑胶五金型号(REF_MET)详情，ID: ${model.id}, 代码: ${model.code}, 名称: ${model.name}`)
      console.info(`🔗 塑胶五金型号(REF_MET)详情请求URL: /api/models/hardware-plastic/${model.id}`)
      try {
        console.info('⏳ 正在发送塑胶五金型号(REF_MET)详情请求...')
        const response = await hardwarePlasticModelApi.getDetail(model.id)
        console.info('✅ 塑胶五金型号(REF_MET)详情响应状态:', response.status)
        console.info('📄 塑胶五金型号(REF_MET)详情响应数据:', JSON.stringify(response.data))
        console.info('================================')
        viewModelForm.value = response.data
      } catch (error) {
        console.error('❌ 塑胶五金型号(REF_MET)详情获取失败:', error)
        console.error('❌ 错误详情:', {
          message: error.message,
          response: error.response ? {
            status: error.response.status,
            data: JSON.stringify(error.response.data || {})
          } : '无响应数据'
        })
        console.info('================================')
        throw error
      }
    }
    // 其他类型使用通用API
    else {
      const response = await props.api.getDetail(model.id)
      viewModelForm.value = response.data
    }
    
    // 确保decimal字段正确显示
    if (viewModelForm.value) {
      viewModelForm.value = formatDecimalFields(viewModelForm.value)
    }
    
    console.log('数据加载完成，viewDialogVisible =', viewDialogVisible.value)
  } catch (error) {
    console.error('获取型号详情失败:', error)
    ElMessage.error('获取详情失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
    console.log('finally执行完毕，viewDialogVisible =', viewDialogVisible.value)
  }
  */
}

const handleDelete = async (model) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除型号"${model.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await props.api.delete(model.id)
    ElMessage.success('删除成功')
    loadModelList()
  } catch (error) {
    if (error !== 'cancel') {
      console.info('删除操作失败:', error.message || error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saving.value = true
    
    if (dialogType.value === 'add') {
      // 检查是否是成品型号，如果是则使用新的API
      if (props.modelType === 'REF_FG' || props.modelType === 'products') {
        console.info('准备保存成品型号，使用专用API:', modelForm.value)
        const response = await productModelApiNew.create(modelForm.value)
        console.info('成品型号保存响应:', response)
        ElMessage.success('成品型号新增成功')
      }
      // 检查是否是组件型号
      else if (props.modelType === 'REF_SUB' || props.modelType === 'components') {
        console.info('准备保存组件型号，使用专用API:', modelForm.value)
        // 使用已导入的API对象，而不是require
        const response = await componentModelApiNew.create(modelForm.value)
        console.info('组件型号保存响应:', response)
        ElMessage.success('组件型号新增成功')
      }
      // 检查是否是零件型号
      else if (props.modelType === 'REF_COMP' || props.modelType === 'parts') {
        console.info('准备保存零件型号，使用专用API:', modelForm.value)
        const response = await partModelApiNew.create(modelForm.value)
        console.info('零件型号保存响应:', response)
        ElMessage.success('零件型号新增成功')
      }
      // 检查是否是辅料包材型号
      else if (props.modelType === 'REF_ACC' || props.modelType === 'auxiliary') {
        console.info('准备保存辅料包材型号，使用专用API:', modelForm.value)
        const response = await auxiliaryModelApiNew.create(modelForm.value)
        console.info('辅料包材型号保存响应:', response)
        ElMessage.success('辅料包材型号新增成功')
      }
      // 检查是否是塑胶五金型号
      else if (props.modelType === 'REF_MET' || props.modelType === 'hardware-plastic') {
        console.info('================================')
        console.info('📦 准备保存塑胶五金型号(REF_MET)，原始数据:', JSON.stringify(modelForm.value))
        // 确保先处理数据格式，特别是decimal字段
        let formattedData = formatModelDataForSave(modelForm.value, 'hardware-plastic')
        // 确保category字段值为REF_MET
        formattedData.category = 'REF_MET'
        console.info('📋 塑胶五金型号(REF_MET)格式化后数据:', JSON.stringify(formattedData))
        console.info('🔗 塑胶五金型号(REF_MET)请求URL: /api/models/hardware-plastic')
        
        try {
          // 使用硬件塑料型号API
          console.info('⏳ 正在发送塑胶五金型号(REF_MET)保存请求...')
          const response = await hardwarePlasticModelApi.create(formattedData)
          console.info('✅ 塑胶五金型号(REF_MET)保存响应状态:', response.status)
          console.info('📄 塑胶五金型号(REF_MET)保存响应数据:', JSON.stringify(response.data))
          console.info('================================')
          ElMessage.success('塑胶五金型号新增成功')
        } catch (error) {
          console.error('❌ 塑胶五金型号(REF_MET)保存失败:', error)
          console.error('❌ 错误详情:', {
            message: error.message,
            response: error.response ? {
              status: error.response.status,
              data: JSON.stringify(error.response.data || {})
            } : '无响应数据'
          })
          console.info('================================')
          throw error
        }
      }
      else {
        console.info('准备保存其他类型型号:', modelForm.value)
        const response = await props.api.create(modelForm.value)
        console.info('型号保存响应:', response)
      ElMessage.success('新增成功')
      }
    } else {
      // 检查是否是成品型号，如果是则使用新的API
      if (props.modelType === 'REF_FG' || props.modelType === 'products') {
        console.info('准备更新成品型号，使用专用API:', modelForm.value)
        const response = await productModelApiNew.update(modelForm.value.id, modelForm.value)
        console.info('成品型号更新响应:', response)
        ElMessage.success('成品型号更新成功')
      }
      // 检查是否是组件型号
      else if (props.modelType === 'REF_SUB' || props.modelType === 'components') {
        console.info('准备更新组件型号，使用专用API:', modelForm.value)
        // 使用已导入的API对象，而不是require
        const response = await componentModelApiNew.update(modelForm.value.id, modelForm.value)
        console.info('组件型号更新响应:', response)
        ElMessage.success('组件型号更新成功')
      }
      // 检查是否是零件型号
      else if (props.modelType === 'REF_COMP' || props.modelType === 'parts') {
        console.info('准备更新零件型号，使用专用API:', modelForm.value)
        const response = await partModelApiNew.update(modelForm.value.id, modelForm.value)
        console.info('零件型号更新响应:', response)
        ElMessage.success('零件型号更新成功')
      }
      // 检查是否是辅料包材型号
      else if (props.modelType === 'REF_ACC' || props.modelType === 'auxiliary') {
        console.info('准备更新辅料包材型号，使用专用API:', modelForm.value)
        const response = await auxiliaryModelApiNew.update(modelForm.value.id, modelForm.value)
        console.info('辅料包材型号更新响应:', response)
        ElMessage.success('辅料包材型号更新成功')
      }
      // 检查是否是塑胶五金型号
      else if (props.modelType === 'REF_MET' || props.modelType === 'hardware-plastic') {
        console.info('================================')
        console.info('📦 准备更新塑胶五金型号(REF_MET)，原始数据:', JSON.stringify(modelForm.value))
        // 确保先处理数据格式
        let formattedData = formatModelDataForSave(modelForm.value, 'hardware-plastic')
        // 确保category字段值为REF_MET
        formattedData.category = 'REF_MET'
        console.info('📋 塑胶五金型号(REF_MET)格式化后数据:', JSON.stringify(formattedData))
        console.info(`🔗 塑胶五金型号(REF_MET)更新请求URL: /api/models/hardware-plastic/${modelForm.value.id}`)
        
        try {
          // 使用硬件塑料型号API
          console.info('⏳ 正在发送塑胶五金型号(REF_MET)更新请求...')
          const response = await hardwarePlasticModelApi.update(modelForm.value.id, formattedData)
          console.info('✅ 塑胶五金型号(REF_MET)更新响应状态:', response.status)
          console.info('📄 塑胶五金型号(REF_MET)更新响应数据:', JSON.stringify(response.data))
          console.info('================================')
          ElMessage.success('塑胶五金型号更新成功')
        } catch (error) {
          console.error('❌ 塑胶五金型号(REF_MET)更新失败:', error)
          console.error('❌ 错误详情:', {
            message: error.message,
            response: error.response ? {
              status: error.response.status,
              data: JSON.stringify(error.response.data || {})
            } : '无响应数据'
          })
          console.info('================================')
          throw error
        }
      }
      else {
        console.info('准备更新其他类型型号:', modelForm.value)
        const response = await props.api.update(modelForm.value.id, modelForm.value)
        console.info('型号更新响应:', response)
      ElMessage.success('更新成功')
      }
    }
    
    dialogVisible.value = false
    
    // 保存完成后重新加载列表数据
    console.info('保存成功，准备重新加载型号列表...')
    
    // 确保在组件更新后再加载数据
    nextTick(() => {
      // 特别处理组件型号、零件型号、辅料包材型号和塑胶五金型号，确保数据刷新
      if (props.modelType === 'REF_SUB' || props.modelType === 'components' || 
          props.modelType === 'REF_COMP' || props.modelType === 'parts' ||
          props.modelType === 'REF_ACC' || props.modelType === 'auxiliary' ||
          props.modelType === 'REF_MET' || props.modelType === 'hardware-plastic') {
        console.info('型号数据已保存，立即刷新列表数据')
        // 重置到第一页，确保能看到新增的数据
        currentPage.value = 1
      }
      
    loadModelList()
    })
  } catch (error) {
    console.info('保存操作失败:', error.message || error)
    console.error('详细错误信息:', error)
    ElMessage.error('保存失败: ' + (error.response?.data?.message || error.message || '未知错误'))
  } finally {
    saving.value = false
  }
}

const handleImageChange = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    modelForm.value.image = e.target.result
  }
  reader.readAsDataURL(file.raw)
}

const resetForm = () => {
  activeFormTab.value = 'basic'
  modelForm.value = {
    // 基本信息
    name: '',
    code: '',
    category: '',
    specification: '',
    status: '启用',
    image: '',
    unit: '',
    material: '',
    size: '',
    saveToCloudServer: true,
    
    // 销售信息
    salesPrice: '0.00',
    salesUnit: '',
    minOrderQty: '0.00',
    maxOrderQty: '0.00',
    leadTime: '',
    warranty: '',
    
    // 仓库信息
    storageLocation: '',
    safetyStock: '0.00',
    maxStock: '0.00',
    reorderPoint: '0.00',
    storageCondition: '',
    shelfLife: '',
    
    // 财务信息
    standardCost: '0.00',
    averageCost: '0.00',
    valuationMethod: '',
    taxRate: '0.00',
    accountSubject: '',
    costCenter: '',
    
    // 生产信息
    productionType: '',
    productionLeadTime: '',
    setupTime: '',
    cycleTime: '',
    batchSize: '',
    workCenter: '',
    qualityStandard: '',
    
    // 采购信息
    supplier: '',
    purchasePrice: '0.00',
    purchaseUnit: '',
    minPurchaseQty: '0.00',
    purchaseLeadTime: '',
    qualityLevel: '',
    purchaseNote: '',
    
    // 进出口信息
    hsCode: '',
    originCountry: '',
    importTaxRate: '0.00',
    exportTaxRefund: '0.00',
    isDangerous: false,
    transportMode: '',
    packingRequirement: '',
    inspectionRequired: false,
    licenseRequirement: '',
    
    // MRP计划信息
    planningStrategy: '',
    planningCycle: '',
    forecastMethod: '',
    abcCategory: '',
    demandSource: '',
    planner: '',
    safetyStockDays: '',
    lotSizeRule: '',
    demandTimeFence: '',
    supplyTimeFence: '',
    planningNote: ''
  }
}

// 设置默认分类
const setDefaultCategory = () => {
  // 根据当前物料类型设置默认分类
  const categoryMap = {
    'REF_FG': 'REF_FG',        // 成品型号
    'REF_SUB': 'REF_SUB',      // 组件型号
    'REF_COMP': 'REF_COMP',    // 零件型号
    'REF_ACC': 'REF_ACC',      // 辅料包材型号
    'REF_MET': 'REF_MET',      // 塑胶五金型号
    'REF_ADMIN': 'REF_ADMIN',  // 行政物料
    'REF_VIRTUAL': 'REF_VIRTUAL', // 虚拟物料型号
    // 兼容旧编码
    'product': 'REF_FG',
    'component': 'REF_SUB',
    'part': 'REF_COMP',
    'auxiliary': 'REF_ACC',
    'hardwarePlastic': 'REF_MET'
  }
  
  // 设置默认分类
  if (categoryMap[props.modelType]) {
    modelForm.value.category = categoryMap[props.modelType]
  }
}

// 状态变量
const loading = ref(false)

// 处理查询
const handleQuery = async () => {
  console.info('执行型号查询')
  loading.value = true
  try {
    // 重置到第一页
    currentPage.value = 1
    
    // 如果是组件型号，使用API查询
    if (props.modelType === 'REF_SUB' || props.modelType === 'components') {
      await loadModelList()
    } 
    // 如果是零件型号，使用API查询
    else if (props.modelType === 'REF_COMP' || props.modelType === 'parts') {
      await loadModelList()
    }
    // 如果是辅料包材型号，使用API查询
    else if (props.modelType === 'REF_ACC' || props.modelType === 'auxiliary') {
      await loadModelList()
    }
    // 如果是塑胶五金型号，使用API查询
    else if (props.modelType === 'REF_MET' || props.modelType === 'hardware-plastic') {
      console.info('================================')
      console.info('📦 执行塑胶五金型号(REF_MET)查询')
      console.info('🔍 搜索关键词:', searchQuery.value)
      console.info('🔗 查询URL: /api/models/hardware-plastic')
      console.info('⏳ 开始加载塑胶五金型号(REF_MET)数据...')
      await loadModelList()
      console.info('✅ 塑胶五金型号(REF_MET)查询完成')
      console.info('================================')
    }
    else {
      // 其他类型
      await loadModelList()
    }
    
    ElMessage.success('查询成功')
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 监听搜索查询变化
watch(searchQuery, () => {
  currentPage.value = 1
})

// 监听分页变化
watch([currentPage, pageSize], () => {
  loadModelList()
})

// 监听属性变化
watch(() => props.categoryConfig, (newConfig) => {
  if (newConfig && Object.keys(newConfig).length > 0) {
    console.info('分类配置已更新:', newConfig)
  }
}, { immediate: true, deep: true })

// 监听modelType变化，切换标签页时重新加载数据
watch(() => props.modelType, (newModelType, oldModelType) => {
  if (newModelType && newModelType !== oldModelType) {
    console.info('标签页切换:', oldModelType, '->', newModelType)
    // 重置分页
    currentPage.value = 1
    searchQuery.value = ''
    // 重新加载数据
    nextTick(() => {
      loadModelList()
    })
  }
}, { immediate: false })

onMounted(() => {
  // 延迟一下再加载数据，避免初始化时的API调用冲突
  nextTick(() => {
    // 如果是组件型号、零件型号、辅料包材型号或塑胶五金型号，立即执行查询
    if (props.modelType === 'REF_SUB' || props.modelType === 'components' ||
        props.modelType === 'REF_COMP' || props.modelType === 'parts' ||
        props.modelType === 'REF_ACC' || props.modelType === 'auxiliary' ||
        props.modelType === 'REF_MET' || props.modelType === 'hardware-plastic') {
      console.info('组件/零件/辅料包材/塑胶五金型号标签页挂载，立即执行查询')
      handleQuery()
    } else {
      // 其他类型正常加载
  loadModelList()
    }
})
})

// 处理分页变化
const handleSizeChange = (val) => {
  pageSize.value = val
  console.info('页大小变更为:', val)
  loadModelList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  console.info('当前页变更为:', val)
  loadModelList()
}

// 格式化模型数据用于保存到后端
const formatModelDataForSave = (data, modelType) => {
  const result = { ...data }
  
  // 将字符串形式的decimal字段转换为数字形式
  // 销售信息字段
  if (result.salesPrice !== undefined) result.salesPrice = parseFloat(result.salesPrice)
  if (result.minOrderQty !== undefined) result.minOrderQty = parseFloat(result.minOrderQty)
  if (result.maxOrderQty !== undefined) result.maxOrderQty = parseFloat(result.maxOrderQty)
  
  // 仓库信息字段
  if (result.safetyStock !== undefined) result.safetyStock = parseFloat(result.safetyStock)
  if (result.maxStock !== undefined) result.maxStock = parseFloat(result.maxStock)
  if (result.reorderPoint !== undefined) result.reorderPoint = parseFloat(result.reorderPoint)
  
  // 财务信息字段
  if (result.standardCost !== undefined) result.standardCost = parseFloat(result.standardCost)
  if (result.averageCost !== undefined) result.averageCost = parseFloat(result.averageCost)
  if (result.taxRate !== undefined) result.taxRate = parseFloat(result.taxRate)
  
  // 采购信息字段
  if (result.purchasePrice !== undefined) result.purchasePrice = parseFloat(result.purchasePrice)
  if (result.minPurchaseQty !== undefined) result.minPurchaseQty = parseFloat(result.minPurchaseQty)
  
  // 进出口信息字段
  if (result.importTaxRate !== undefined) result.importTaxRate = parseFloat(result.importTaxRate)
  if (result.exportTaxRefund !== undefined) result.exportTaxRefund = parseFloat(result.exportTaxRefund)
  
  // 确保Boolean字段为真正的boolean类型
  if (result.isDangerous !== undefined) result.isDangerous = result.isDangerous === true || result.isDangerous === 'true'
  if (result.inspectionRequired !== undefined) result.inspectionRequired = result.inspectionRequired === true || result.inspectionRequired === 'true'
  
  // 根据模型类型设置正确的分类
  const categoryMap = {
    'products': 'REF_FG',
    'components': 'REF_SUB',
    'parts': 'REF_COMP',
    'auxiliary': 'REF_ACC',
    'hardware-plastic': 'REF_MET'
  }
  
  if (categoryMap[modelType]) {
    result.category = categoryMap[modelType]
  }
  
  // 确保status字段格式一致
  if (result.status === '启用' || result.status === true || result.status === 'true') {
    result.status = true
  } else {
    result.status = false
  }
  
  return result
}
</script>

<style scoped>
.material-model-tab {
  min-height: 600px;
}

.tab-header {
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.search-area {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.search-input {
  width: 300px;
}

.model-type-desc {
  color: #909399;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.model-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
  margin-bottom: 20px;
  min-height: 200px;
}

.model-grid .el-col {
  padding: 0 8px;
}

.loading-container {
  width: 100%;
  padding: 40px 0;
  text-align: center;
}

.loading-text {
  margin-top: 20px;
  color: #909399;
  font-size: 14px;
}

.empty-result {
  width: 100%;
  padding: 40px 0;
  display: flex;
  justify-content: center;
}

.model-card {
  margin-bottom: 16px;
  transition: all 0.3s;
  height: 200px;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.model-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
}

.model-card .el-card__body {
  padding: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.model-image {
  height: 100px;
  position: relative;
  overflow: hidden;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.model-image .el-image {
  width: 100%;
  height: 100%;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  font-size: 20px;
}

.model-status {
  position: absolute;
  top: 3px;
  right: 3px;
  font-size: 9px;
  padding: 0px 4px;
  border-radius: 8px;
  background-color: #67c23a;
  color: white;
}

.model-status.active {
  background-color: #67c23a;
}

.model-status.inactive {
  background-color: #909399;
}

.model-info {
  padding: 5px 6px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.model-name {
  margin: 0 0 1px 0;
  font-size: 11px;
  font-weight: bold;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
}

.model-code {
  color: #909399;
  font-size: 10px;
  margin-bottom: 1px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
}

.model-category {
  margin-bottom: 1px;
}

.model-category .el-tag {
  font-size: 9px;
  height: 16px;
  line-height: 14px;
  padding: 0 3px;
}

.model-spec {
  color: #606266;
  font-size: 10px;
  margin-bottom: 1px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
}

.model-extra {
  margin-top: 1px;
  padding-top: 1px;
  border-top: 1px solid #f0f0f0;
}

.model-supplier,
.model-unit {
  color: #909399;
  font-size: 9px;
  margin-bottom: 1px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
}

.model-actions {
  padding: 3px 5px;
  display: flex;
  justify-content: space-around;
  gap: 2px;
  margin-top: auto;
  border-top: 1px solid #f0f0f0;
}

.model-actions .el-button {
  font-size: 10px;
  padding: 1px 3px;
  height: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.model-upload {
  width: 148px;
  height: 148px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.model-upload:hover {
  border-color: #409EFF;
}

.upload-image {
  width: 100%;
  height: 100%;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
}

/* 查看对话框中的图片样式 */
.view-image {
  width: 148px;
  height: 148px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
}

.no-image {
  width: 148px;
  height: 148px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8c939d;
  font-size: 14px;
}

/* 对话框样式 */
.model-edit-dialog,
.model-view-dialog {
  max-height: 90vh;
}

.model-edit-dialog :deep(.el-dialog__body),
.model-view-dialog :deep(.el-dialog__body) {
  padding: 0;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

.model-form-tabs {
  border: none;
}

.model-form-tabs :deep(.el-tabs__content) {
  padding: 20px;
}

.model-form-tabs :deep(.el-tabs__header) {
  margin: 0;
  background-color: #f8f9fa;
}

.form-content {
  max-height: calc(90vh - 200px);
  overflow-y: auto;
  padding-right: 10px;
}

.form-content .el-form-item {
  margin-bottom: 20px;
}

.form-content .el-form-item__label {
  color: #606266;
  font-weight: 500;
}

/* 响应式布局 */
@media screen and (max-width: 992px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-area {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .model-type-desc {
    text-align: center;
    font-size: 13px;
  }
  
  .model-edit-dialog,
  .model-view-dialog {
    width: 95% !important;
  }
  
  .form-content .el-col {
    width: 100% !important;
  }
}

@media screen and (max-width: 768px) {
  .model-edit-dialog,
  .model-view-dialog {
    width: 98% !important;
    margin: 5vh auto;
  }
  
  /* 在小屏幕上进一步调整卡片图片高度 */
  .model-image {
    height: 90px;
  }
  
  /* 在小屏幕上调整字体大小 */
  .model-name {
    font-size: 11px;
  }
  
  .model-code,
  .model-spec {
    font-size: 9px;
  }
  
  /* 在小屏幕上调整卡片高度 */
  .model-card {
    height: 180px;
  }
  
  .model-actions .el-button {
    font-size: 9px;
    padding: 1px 2px;
  }
}

/* 编码和单位同行显示的样式 */
.model-code-unit {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1px;
  color: #909399;
  font-size: 10px;
}

.model-code-unit .model-code {
  margin-bottom: 0;
}

@media screen and (max-width: 992px) and (min-width: 769px) {
  .model-grid {
    margin: 0 -8px;
  }
  
  .el-col {
    padding: 0 8px;
  }
  
  .model-card {
    height: 190px;
  }
  
  .model-image {
    height: 95px;
  }
}

@media screen and (min-width: 1200px) {
  .model-grid {
    margin: 0 -6px;
  }
  
  .model-grid .el-col {
    padding: 0 6px;
  }
  
  .model-card {
    margin-bottom: 12px;
  }
}

@media screen and (min-width: 1600px) {
  .model-grid {
    margin: 0 -5px;
  }
  
  .model-grid .el-col {
    padding: 0 5px;
  }
  
  .model-card {
    margin-bottom: 10px;
    height: 190px;
  }
  
  .model-image {
    height: 95px;
  }
}
.save-to-cloud-checkbox .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #1f3a8a !important; /* 深蓝色背景 */
  border-color: #1f3a8a !important; /* 深蓝色边框 */
}

.save-to-cloud-checkbox .el-checkbox__input.is-checked:hover .el-checkbox__inner {
  background-color: #1e40af !important; /* 更深的蓝色悬停 */
  border-color: #1e40af !important;
}

.save-to-cloud-checkbox .el-checkbox__inner {
  border-color: #1a2e66 !important; /* 更深的默认边框颜色 */
  border-width: 2px !important; /* 加粗边框 */
  border-style: solid !important;
}

.save-to-cloud-checkbox .el-checkbox__label {
  color: #1e3a8a !important; /* 标签文字颜色 */
  font-weight: 600;
  font-size: 10px !important; /* 字体更小 */
  white-space: nowrap !important; /* 不换行 */
  line-height: 1.2 !important; /* 减小行高，避免换行 */
}
</style>
