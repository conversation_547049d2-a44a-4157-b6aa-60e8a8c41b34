﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace mes_system_server.Migrations
{
    /// <inheritdoc />
    public partial class AddEmployeeSeedData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3222), new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3222) });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3329));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3437));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3444));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3449));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3454));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3459));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3510));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 4, 58, 952, DateTimeKind.Utc).AddTicks(3516));

            migrationBuilder.InsertData(
                table: "Employees",
                columns: new[] { "Id", "Age", "BaseSalary", "Birthday", "ContractNo", "CreateTime", "DepartmentId", "Education", "Email", "EmployeeId", "EntryDate", "FactoryAddress", "FactoryContact", "FactoryName", "FactoryPhone", "Gender", "IdCard", "IdCardIssueDate", "IdCardIssuePlace", "Level", "Name", "PerformanceLevel", "Phone", "Position", "SalaryType", "SkillLevel", "Status", "Team", "UpdateTime", "WorkYears" },
                values: new object[,]
                {
                    { 1, 33, "15000", new DateTime(1990, 5, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "HT2022001", new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3569), 1, "本科", "<EMAIL>", "EMP001", new DateTime(2022, 1, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), "上海市浦东新区张江高科技园区", "王经理", "总部研发中心", "021-12345678", "男", "310101199005103215", null, "", "3", "张大明", "A", "13800138001", "软件工程师", "月薪", "高级", "active", "后端开发组", new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3570), 8 },
                    { 2, 31, "12000", new DateTime(1992, 8, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), "HT2022015", new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3581), 6, "本科", "<EMAIL>", "EMP002", new DateTime(2022, 3, 20, 0, 0, 0, 0, DateTimeKind.Unspecified), "上海市浦东新区张江高科技园区", "王经理", "总部研发中心", "021-12345678", "女", "310101199208153624", null, "", "2", "李小红", "B+", "13900139002", "前端开发工程师", "月薪", "中级", "active", "前端开发组", new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3582), 5 },
                    { 3, 38, "10000", new DateTime(1985, 12, 20, 0, 0, 0, 0, DateTimeKind.Unspecified), "HT2021052", new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3591), 2, "大专", "<EMAIL>", "EMP003", new DateTime(2021, 5, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "上海市松江区新桥镇新茸路888号", "赵经理", "松江生产基地", "021-87654321", "男", "310101198512204871", null, "", "2", "王强", "A", "13700137003", "生产主管", "月薪", "中级", "active", "生产一组", new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3592), 10 },
                    { 4, 30, "11000", new DateTime(1993, 3, 25, 0, 0, 0, 0, DateTimeKind.Unspecified), "HT2022089", new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3601), 5, "硕士", "<EMAIL>", "EMP004", new DateTime(2022, 8, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "上海市浦东新区张江高科技园区", "钱经理", "总部", "021-12345678", "女", "310101199303251234", null, "", "2", "赵静", "A-", "13600136004", "人力资源专员", "月薪", "中级", "active", "招聘组", new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3601), 3 },
                    { 5, 35, "8000", new DateTime(1988, 7, 8, 0, 0, 0, 0, DateTimeKind.Unspecified), "HT2022035", new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3610), 3, "大专", "<EMAIL>", "EMP005", new DateTime(2022, 4, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), "上海市松江区新桥镇新茸路888号", "赵经理", "松江生产基地", "021-87654321", "男", "310101198807085678", null, "", "1", "陈明", "B", "13500135005", "质检专员", "月薪", "初级", "active", "质检一组", new DateTime(2025, 4, 24, 21, 4, 58, 952, DateTimeKind.Local).AddTicks(3611), 5 }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "Employees",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4500), new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4500) });

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 1,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4611));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 2,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4741));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 3,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4748));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 4,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4753));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 5,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4762));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 6,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4766));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 7,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4771));

            migrationBuilder.UpdateData(
                table: "Departments",
                keyColumn: "Id",
                keyValue: 8,
                column: "UpdateTime",
                value: new DateTime(2025, 4, 24, 13, 3, 21, 0, DateTimeKind.Utc).AddTicks(4775));
        }
    }
}
