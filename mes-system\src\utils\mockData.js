// 员工模拟数据
export const mockEmployees = [
  {
    id: 1,
    employeeId: 'EMP001',
    name: '张三',
    department: '研发部',
    departmentId: 1,
    position: '高级工程师',
    phone: '13800138001',
    email: '<PERSON><PERSON><PERSON>@example.com',
    entryDate: '2020-01-01',
    status: true
  },
  {
    id: 2,
    employeeId: 'EMP002',
    name: '李四',
    department: '生产部',
    departmentId: 2,
    position: '生产主管',
    phone: '13800138002',
    email: '<EMAIL>',
    entryDate: '2020-02-15',
    status: true
  },
  {
    id: 3,
    employeeId: 'EMP003',
    name: '王五',
    department: '质检部',
    departmentId: 3,
    position: '质检员',
    phone: '13800138003',
    email: '<EMAIL>',
    entryDate: '2020-03-20',
    status: true
  },
  {
    id: 4,
    employeeId: 'EMP004',
    name: '赵六',
    department: '研发部',
    departmentId: 1,
    position: '初级工程师',
    phone: '13800138004',
    email: 'z<PERSON><PERSON><PERSON>@example.com',
    entryDate: '2021-05-10',
    status: true
  },
  {
    id: 5,
    employeeId: 'EMP005',
    name: '钱七',
    department: '行政部',
    departmentId: 4,
    position: '行政专员',
    phone: '13800138005',
    email: '<EMAIL>',
    entryDate: '2021-06-18',
    status: true
  },
  {
    id: 6,
    employeeId: 'EMP006',
    name: '孙八',
    department: '研发部',
    departmentId: 1,
    position: '技术总监',
    phone: '13800138006',
    email: '<EMAIL>',
    entryDate: '2019-08-01',
    status: true
  },
  {
    id: 7,
    employeeId: 'EMP007',
    name: '周九',
    department: '生产部',
    departmentId: 2,
    position: '操作员',
    phone: '13800138007',
    email: '<EMAIL>',
    entryDate: '2022-01-10',
    status: true
  },
  {
    id: 8,
    employeeId: 'EMP008',
    name: '吴十',
    department: '质检部',
    departmentId: 3,
    position: '质检主管',
    phone: '13800138008',
    email: '<EMAIL>',
    entryDate: '2020-11-05',
    status: true
  }
];

// 部门模拟数据
export const mockDepartments = [
  {
    id: 1,
    name: '研发部'
  },
  {
    id: 2,
    name: '生产部'
  },
  {
    id: 3,
    name: '质检部'
  },
  {
    id: 4,
    name: '行政部'
  },
  {
    id: 5,
    name: '市场部'
  }
]; 