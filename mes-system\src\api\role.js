import request from '@/utils/request'

/**
 * 获取角色列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页记录数
 * @param {string} params.search - 搜索关键词
 * @returns {Promise} - 返回角色列表数据
 */
export function getRoles(params) {
  return request({
    url: '/api/roles',
    method: 'get',
    params
  })
}

/**
 * 获取角色详情
 * @param {number} id - 角色ID
 * @returns {Promise} - 返回角色详情
 */
export function getRole(id) {
  return request({
    url: `/api/roles/${id}`,
    method: 'get'
  })
}

/**
 * 创建角色
 * @param {Object} data - 角色数据
 * @param {string} data.roleName - 角色名称
 * @param {string} data.roleCode - 角色编码
 * @param {string} data.description - 角色描述
 * @param {boolean} data.status - 角色状态
 * @returns {Promise} - 返回创建结果
 */
export function createRole(data) {
  return request({
    url: '/api/roles',
    method: 'post',
    data
  })
}

/**
 * 更新角色
 * @param {number} id - 角色ID
 * @param {Object} data - 角色数据
 * @param {string} data.roleName - 角色名称
 * @param {string} data.description - 角色描述
 * @param {boolean} data.status - 角色状态
 * @returns {Promise} - 返回更新结果
 */
export function updateRole(id, data) {
  return request({
    url: `/api/roles/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除角色
 * @param {number} id - 角色ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteRole(id) {
  return request({
    url: `/api/roles/${id}`,
    method: 'delete'
  })
}

/**
 * 切换角色状态
 * @param {number} id - 角色ID
 * @param {boolean} status - 新状态
 * @returns {Promise} - 返回操作结果
 */
export function toggleRoleStatus(id, status) {
  return request({
    url: `/api/roles/${id}/status`,
    method: 'put',
    data: { status }
  })
}

/**
 * 获取角色的用户列表
 * @param {number} roleId - 角色ID
 * @returns {Promise} - 返回与角色关联的用户列表
 */
export function getRoleUsers(roleId) {
  return request({
    url: `/api/roles/${roleId}/users`,
    method: 'get'
  })
}

/**
 * 获取角色菜单权限
 * @param {number} roleId - 角色ID
 * @returns {Promise} - 返回角色菜单权限
 */
export function getRoleMenus(roleId) {
  return request({
    url: `/api/roles/${roleId}/menus`,
    method: 'get'
  })
}

/**
 * 保存角色菜单权限
 * @param {Object} data - 角色菜单权限数据
 * @param {number} data.roleId - 角色ID
 * @param {Array} data.menuIds - 菜单ID列表
 * @returns {Promise} - 返回保存结果
 */
export function saveRoleMenus(data) {
  return request({
    url: `/api/roles/${data.roleId}/menus`,
    method: 'put',
    data: {
      menuIds: data.menuIds
    }
  })
}

/**
 * 初始化角色菜单权限数据
 * @returns {Promise} - 返回初始化结果
 */
export function initializeRoleMenuPermissions() {
  return request({
    url: '/api/roles/initialize-permissions',
    method: 'post'
  })
} 