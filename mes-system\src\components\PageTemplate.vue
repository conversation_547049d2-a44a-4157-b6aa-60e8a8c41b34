<template>
  <div class="page-template">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">{{ pageTitle }}</h2>
      <div class="page-actions">
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          {{ addButtonText }}
        </el-button>
        <el-button type="success" :icon="RefreshRight" @click="refreshData">
          刷新
        </el-button>
        <slot name="extra-actions"></slot>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <el-card>
        <!-- 搜索区域 -->
        <div class="search-area" v-if="showSearch">
          <el-form :inline="true" :model="searchForm">
            <el-form-item 
              v-for="field in searchFields" 
              :key="field.prop" 
              :label="field.label"
            >
              <el-input 
                v-if="field.type === 'input'"
                v-model="searchForm[field.prop]" 
                :placeholder="field.placeholder || `请输入${field.label}`" 
                clearable
                @keyup.enter="handleSearch"
              />
              <el-select 
                v-else-if="field.type === 'select'"
                v-model="searchForm[field.prop]" 
                :placeholder="field.placeholder || `请选择${field.label}`"
                clearable
              >
                <el-option 
                  v-for="option in field.options" 
                  :key="option.value" 
                  :label="option.label" 
                  :value="option.value" 
                />
              </el-select>
              <el-date-picker
                v-else-if="field.type === 'date'"
                v-model="searchForm[field.prop]"
                type="date"
                :placeholder="field.placeholder || `请选择${field.label}`"
                clearable
              />
              <el-date-picker
                v-else-if="field.type === 'daterange'"
                v-model="searchForm[field.prop]"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 数据表格 -->
        <el-table
          v-loading="loading"
          :data="dataList"
          border
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column 
            v-if="showSelection" 
            type="selection" 
            width="55" 
            align="center" 
          />
          <el-table-column 
            v-if="showIndex" 
            type="index" 
            label="序号" 
            width="80" 
            align="center" 
          />
          <el-table-column
            v-for="column in tableColumns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :align="column.align || 'center'"
            :fixed="column.fixed"
          >
            <template #default="{ row }" v-if="column.slot">
              <slot :name="column.slot" :row="row"></slot>
            </template>
          </el-table-column>
          <el-table-column 
            v-if="showActions" 
            label="操作" 
            :width="actionWidth" 
            align="center" 
            fixed="right"
          >
            <template #default="{ row }">
              <el-button-group>
                <el-button 
                  v-if="actions.includes('edit')"
                  type="primary" 
                  size="small" 
                  @click="handleEdit(row)"
                >
                  编辑
                </el-button>
                <el-button 
                  v-if="actions.includes('view')"
                  type="info" 
                  size="small" 
                  @click="handleView(row)"
                >
                  查看
                </el-button>
                <el-button 
                  v-if="actions.includes('delete')"
                  type="danger" 
                  size="small" 
                  @click="handleDelete(row)"
                >
                  删除
                </el-button>
                <slot name="custom-actions" :row="row"></slot>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="pagination-area" v-if="showPagination">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="pageSizes"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? `新增${entityName}` : `编辑${entityName}`"
      :width="dialogWidth"
      @closed="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item
          v-for="field in formFields"
          :key="field.prop"
          :label="field.label"
          :prop="field.prop"
        >
          <el-input
            v-if="field.type === 'input'"
            v-model="form[field.prop]"
            :placeholder="field.placeholder || `请输入${field.label}`"
            :disabled="field.disabled"
          />
          <el-input
            v-else-if="field.type === 'textarea'"
            v-model="form[field.prop]"
            type="textarea"
            :rows="field.rows || 3"
            :placeholder="field.placeholder || `请输入${field.label}`"
          />
          <el-select
            v-else-if="field.type === 'select'"
            v-model="form[field.prop]"
            :placeholder="field.placeholder || `请选择${field.label}`"
            clearable
          >
            <el-option
              v-for="option in field.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <el-switch
            v-else-if="field.type === 'switch'"
            v-model="form[field.prop]"
            :active-text="field.activeText || '启用'"
            :inactive-text="field.inactiveText || '禁用'"
          />
          <el-date-picker
            v-else-if="field.type === 'date'"
            v-model="form[field.prop]"
            type="date"
            :placeholder="field.placeholder || `请选择${field.label}`"
          />
          <el-input-number
            v-else-if="field.type === 'number'"
            v-model="form[field.prop]"
            :min="field.min"
            :max="field.max"
            :step="field.step"
            :placeholder="field.placeholder || `请输入${field.label}`"
          />
          <!-- 自定义表单项插槽 -->
          <slot :name="`form-${field.prop}`" :field="field" :form="form" v-else></slot>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ submitting ? '保存中...' : '确定' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, RefreshRight } from '@element-plus/icons-vue'

// 定义属性
const props = defineProps({
  // 页面标题
  pageTitle: {
    type: String,
    default: '数据管理'
  },
  // 实体名称（用于对话框标题）
  entityName: {
    type: String,
    default: '数据'
  },
  // 新增按钮文本
  addButtonText: {
    type: String,
    default: '新增'
  },
  // 是否显示搜索区域
  showSearch: {
    type: Boolean,
    default: true
  },
  // 搜索字段配置
  searchFields: {
    type: Array,
    default: () => []
  },
  // 表格列配置
  tableColumns: {
    type: Array,
    default: () => []
  },
  // 表单字段配置
  formFields: {
    type: Array,
    default: () => []
  },
  // 表单验证规则
  formRules: {
    type: Object,
    default: () => ({})
  },
  // 是否显示选择列
  showSelection: {
    type: Boolean,
    default: false
  },
  // 是否显示序号列
  showIndex: {
    type: Boolean,
    default: true
  },
  // 是否显示操作列
  showActions: {
    type: Boolean,
    default: true
  },
  // 操作列宽度
  actionWidth: {
    type: String,
    default: '200'
  },
  // 可用操作
  actions: {
    type: Array,
    default: () => ['edit', 'delete']
  },
  // 是否显示分页
  showPagination: {
    type: Boolean,
    default: true
  },
  // 分页大小选项
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  },
  // 对话框宽度
  dialogWidth: {
    type: String,
    default: '600px'
  }
})

// 定义事件
const emit = defineEmits([
  'search',
  'reset',
  'add',
  'edit',
  'view',
  'delete',
  'submit',
  'refresh',
  'selection-change',
  'size-change',
  'current-change'
])

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dataList = ref([])
const dialogVisible = ref(false)
const dialogType = ref('add') // add, edit
const formRef = ref(null)
const selectedRows = ref([])

// 搜索表单
const searchForm = reactive({})

// 分页配置
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 表单数据
const form = reactive({})

// 初始化搜索表单
const initSearchForm = () => {
  props.searchFields.forEach(field => {
    searchForm[field.prop] = field.defaultValue || ''
  })
}

// 初始化表单
const initForm = () => {
  props.formFields.forEach(field => {
    form[field.prop] = field.defaultValue || (field.type === 'switch' ? false : '')
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  initForm()
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  emit('search', { ...searchForm })
}

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    const field = props.searchFields.find(f => f.prop === key)
    searchForm[key] = field?.defaultValue || ''
  })
  handleSearch()
}

// 新增处理
const handleAdd = () => {
  dialogType.value = 'add'
  resetForm()
  dialogVisible.value = true
  emit('add')
}

// 编辑处理
const handleEdit = (row) => {
  dialogType.value = 'edit'
  // 将行数据复制到表单
  Object.keys(form).forEach(key => {
    if (row.hasOwnProperty(key)) {
      form[key] = row[key]
    }
  })
  dialogVisible.value = true
  emit('edit', row)
}

// 查看处理
const handleView = (row) => {
  emit('view', row)
}

// 删除处理
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除这条${props.entityName}吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    emit('delete', row)
  }).catch(() => {
    // 用户取消删除
  })
}

// 表单提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid) => {
    if (valid) {
      submitting.value = true
      emit('submit', {
        type: dialogType.value,
        data: { ...form }
      })
    }
  })
}

// 选择变化处理
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
  emit('selection-change', selection)
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  emit('size-change', size)
}

// 当前页变化
const handleCurrentChange = (page) => {
  pagination.page = page
  emit('current-change', page)
}

// 刷新数据
const refreshData = () => {
  emit('refresh')
}

// 暴露方法给父组件
const setLoading = (value) => {
  loading.value = value
}

const setSubmitting = (value) => {
  submitting.value = value
}

const setDataList = (list) => {
  dataList.value = list
}

const setPagination = (page, size, total) => {
  pagination.page = page
  pagination.size = size
  pagination.total = total
}

const closeDialog = () => {
  dialogVisible.value = false
}

const showSuccess = (message) => {
  ElMessage.success(message)
}

const showError = (message) => {
  ElMessage.error(message)
}

// 暴露给父组件的方法
defineExpose({
  setLoading,
  setSubmitting,
  setDataList,
  setPagination,
  closeDialog,
  showSuccess,
  showError,
  selectedRows,
  searchForm,
  form,
  pagination
})

// 组件挂载时初始化
onMounted(() => {
  initSearchForm()
  initForm()
})
</script>

<style scoped>
.page-template {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: #303133;
}

.page-actions {
  display: flex;
  gap: 12px;
}

.content-area {
  background: #fff;
  border-radius: 8px;
}

.search-area {
  padding: 16px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;
}

.search-area .el-form {
  margin-bottom: 0;
}

.pagination-area {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .page-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .search-area .el-form {
    flex-direction: column;
  }

  .search-area .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
</style> 