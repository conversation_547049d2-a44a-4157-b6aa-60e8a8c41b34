/**
 * 菜单名称翻译映射工具
 * 将后端返回的菜单名称映射到i18n翻译键
 */

// 菜单名称到翻译键的映射表
const menuNameMap = {
  // 主要菜单
  '仪表盘': 'menu.dashboard',
  '工程管理': 'menu.engineering',
  '生产管理': 'menu.production',
  '质量管理': 'menu.quality',
  '仓储管理': 'menu.warehouse',
  '设备管理': 'menu.equipment',
  '人事管理': 'menu.hr',
  '系统管理': 'menu.system',
  '首页': 'menu.home',
  
  // 工程管理子菜单
  '物料主数据': 'menu.modelManagement',
  
  // 生产管理子菜单
  '工作中心设置': 'menu.workCenterSettings',
  
  // 人事管理子菜单
  '员工管理': 'menu.employeeManagement',
  '公司设置': 'menu.companySettings',
  '考勤管理': 'menu.attendanceManagement',
  
  // 系统管理子菜单
  '用户管理': 'menu.userManagement',
  '角色权限': 'system.rolePermission',
  '菜单管理': 'menu.menuManagement',
  '多语言管理': 'menu.i18nManagement',
  '数据字典': 'menu.dataDictionary',
  '数据字典管理': 'dataDict.management',
  
  // WIS相关
  'WIS查询': 'menu.wisQuery',
  'WIS PDF查看器': 'menu.wisPDFViewer',
  
  // 英文菜单名称映射（兼容性）
  'Dashboard': 'menu.dashboard',
  'Engineering': 'menu.engineering',
  'Production': 'menu.production',
  'Quality': 'menu.quality',
  'Warehouse': 'menu.warehouse',
  'Equipment': 'menu.equipment',
  'Human Resources': 'menu.hr',
  'System': 'menu.system',
  'Home': 'menu.home',
  'Material Master Data': 'menu.modelManagement',
  'Work Center Settings': 'menu.workCenterSettings',
  'Employee Management': 'menu.employeeManagement',
  'Company Settings': 'menu.companySettings',
  'Attendance Management': 'menu.attendanceManagement',
  'User Management': 'menu.userManagement',
  'Role Permission': 'system.rolePermission',
  'Role Management': 'system.rolePermission',
  'Menu Management': 'menu.menuManagement',
  'I18n Management': 'menu.i18nManagement',
  'Data Dictionary': 'menu.dataDictionary',
  'Data Dictionary Management': 'dataDict.management',
  'WIS Query': 'menu.wisQuery',
  'WIS PDF Viewer': 'menu.wisPDFViewer'
}

// 路径到翻译键的映射表（备用方案）
const menuPathMap = {
  '/home': 'menu.home',
  '/dashboard': 'menu.dashboard',
  '/engineering': 'menu.engineering',
  '/engineering/model': 'menu.modelManagement',
  '/production': 'menu.production',
  '/production/work-center': 'menu.workCenterSettings',
  '/hr': 'menu.hr',
  '/hr/employees': 'menu.employeeManagement',
  '/hr/company': 'menu.companySettings',
  '/hr/attendance': 'menu.attendanceManagement',
  '/system': 'menu.system',
  '/system/users': 'menu.userManagement',
  '/system/roles': 'menu.roleManagement',
  '/system/menus': 'menu.menuManagement',
  '/system/language': 'menu.i18nManagement',
  '/system/dict': 'menu.dataDictionary',
  '/wis/query': 'menu.wisQuery',
  '/wis/pdf': 'menu.wisPDFViewer'
}

/**
 * 获取菜单名称的翻译键
 * @param {string} menuName - 菜单名称
 * @param {string} menuPath - 菜单路径（可选）
 * @returns {string} 翻译键，如果没有找到则返回原始名称
 */
export function getMenuTranslationKey(menuName, menuPath = '') {
  // 首先尝试通过菜单名称获取翻译键
  if (menuNameMap[menuName]) {
    return menuNameMap[menuName]
  }
  
  // 如果名称映射失败，尝试通过路径获取翻译键
  if (menuPath && menuPathMap[menuPath]) {
    return menuPathMap[menuPath]
  }
  
  // 如果都没有找到，返回原始菜单名称
  return menuName
}

/**
 * 获取翻译后的菜单名称
 * @param {Function} t - Vue i18n的翻译函数
 * @param {string} menuName - 菜单名称
 * @param {string} menuPath - 菜单路径（可选）
 * @returns {string} 翻译后的菜单名称
 */
export function getTranslatedMenuName(t, menuName, menuPath = '') {
  const translationKey = getMenuTranslationKey(menuName, menuPath)
  
  // 如果翻译键就是原始名称，直接返回
  if (translationKey === menuName) {
    return menuName
  }
  
  // 尝试获取翻译，如果翻译不存在则返回原始名称
  try {
    const translated = t(translationKey)
    return translated !== translationKey ? translated : menuName
  } catch (error) {
    console.warn(`菜单翻译失败: ${translationKey}`, error)
    return menuName
  }
}

/**
 * 添加新的菜单名称映射
 * @param {string} menuName - 菜单名称
 * @param {string} translationKey - 翻译键
 */
export function addMenuTranslation(menuName, translationKey) {
  menuNameMap[menuName] = translationKey
}

/**
 * 批量添加菜单名称映射
 * @param {Object} mappings - 菜单名称到翻译键的映射对象
 */
export function addMenuTranslations(mappings) {
  Object.assign(menuNameMap, mappings)
}

export default {
  getMenuTranslationKey,
  getTranslatedMenuName,
  addMenuTranslation,
  addMenuTranslations
} 