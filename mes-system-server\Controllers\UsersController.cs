using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using mes_system_server.Data;
using mes_system_server.Models;
using mes_system_server.DTOs;
using BCrypt.Net;
using Microsoft.Extensions.Logging;

namespace mes_system_server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UsersController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<UsersController> _logger;
        private const string DefaultPassword = "123456";

        public UsersController(ApplicationDbContext context, ILogger<UsersController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: api/users
        [HttpGet]
        public async Task<ActionResult<IEnumerable<UserListDto>>> GetUsers(
            [FromQuery] string? username,
            [FromQuery] string? name,
            [FromQuery] string? department,
            [FromQuery] string? role,
            [FromQuery] bool? status,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            var query = _context.Users.AsQueryable();

            if (!string.IsNullOrEmpty(username))
                query = query.Where(u => u.Username.Contains(username));

            if (!string.IsNullOrEmpty(name))
                query = query.Where(u => u.Name.Contains(name));

            if (!string.IsNullOrEmpty(department))
                query = query.Where(u => u.Department.Contains(department));

            if (!string.IsNullOrEmpty(role))
                query = query.Where(u => u.Role.Contains(role));

            if (status.HasValue)
                query = query.Where(u => u.Status == status.Value);

            var totalCount = await query.CountAsync();
            var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

            var users = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(u => new UserListDto
                {
                    Id = u.Id,
                    Username = u.Username,
                    Name = u.Name,
                    Department = u.Department,
                    Role = u.Role,
                    RoleId = u.RoleId,
                    Email = u.Email,
                    Phone = u.Phone,
                    Status = u.Status,
                    CreateTime = u.CreateTime
                })
                .ToListAsync();

            Response.Headers["X-Total-Count"] = totalCount.ToString();
            Response.Headers["X-Total-Pages"] = totalPages.ToString();

            return Ok(users);
        }

        // POST: api/users
        [HttpPost]
        public async Task<ActionResult<User>> CreateUser(CreateUserDto createUserDto)
        {
            try
            {
                _logger.LogInformation("开始创建用户: {@CreateUserDto}", createUserDto);

                if (await _context.Users.AnyAsync(u => u.Username == createUserDto.Username))
                {
                    _logger.LogWarning("用户名已存在: {Username}", createUserDto.Username);
                    return BadRequest("用户名已存在");
                }

                if (await _context.Users.AnyAsync(u => u.Email == createUserDto.Email))
                {
                    _logger.LogWarning("邮箱已存在: {Email}", createUserDto.Email);
                    return BadRequest("邮箱已存在");
                }

                // 根据角色名称查找对应的RoleId
                var role = await _context.Roles.FirstOrDefaultAsync(r => r.RoleName == createUserDto.Role);
                int? roleId = role?.Id;

                if (role == null)
                {
                    _logger.LogWarning("指定的角色不存在: {Role}", createUserDto.Role);
                    return BadRequest($"指定的角色 '{createUserDto.Role}' 不存在");
                }

                var user = new User
                {
                    Username = createUserDto.Username,
                    Name = createUserDto.Name,
                    Department = createUserDto.Department,
                    Role = createUserDto.Role,
                    RoleId = roleId,
                    Email = createUserDto.Email,
                    Phone = createUserDto.Phone,
                    Password = BCrypt.Net.BCrypt.HashPassword(createUserDto.Password),
                    Status = true,
                    CreateTime = DateTime.UtcNow
                };

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                _logger.LogInformation("用户创建成功: {Username}, RoleId: {RoleId}", user.Username, user.RoleId);
                
                // 创建包含RoleId的响应对象
                var userResponse = new UserListDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Name = user.Name,
                    Department = user.Department,
                    Role = user.Role,
                    RoleId = user.RoleId,
                    Email = user.Email ?? string.Empty,
                    Phone = user.Phone ?? string.Empty,
                    Status = user.Status,
                    CreateTime = user.CreateTime
                };

                return CreatedAtAction(nameof(GetUsers), new { id = user.Id }, userResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建用户时发生错误: {@CreateUserDto}", createUserDto);
                return StatusCode(500, "创建用户时发生内部错误");
            }
        }

        // PUT: api/users/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateUser(int id, UpdateUserDto updateUserDto)
        {
            var user = await _context.Users.FindAsync(id);

            if (user == null)
                return NotFound();

            if (updateUserDto.Email != user.Email && 
                await _context.Users.AnyAsync(u => u.Email == updateUserDto.Email))
                return BadRequest("邮箱已存在");

            // 如果角色发生变化，需要更新RoleId
            if (updateUserDto.Role != user.Role)
            {
                var role = await _context.Roles.FirstOrDefaultAsync(r => r.RoleName == updateUserDto.Role);
                if (role == null)
                {
                    return BadRequest($"指定的角色 '{updateUserDto.Role}' 不存在");
                }
                user.RoleId = role.Id;
            }

            user.Name = updateUserDto.Name;
            user.Department = updateUserDto.Department;
            user.Role = updateUserDto.Role;
            user.Email = updateUserDto.Email;
            user.Phone = updateUserDto.Phone;

            await _context.SaveChangesAsync();

            return NoContent();
        }

        // PUT: api/users/{id}/status
        [HttpPut("{id}/status")]
        public async Task<IActionResult> UpdateUserStatus(int id, [FromBody] bool status)
        {
            var user = await _context.Users.FindAsync(id);

            if (user == null)
                return NotFound();

            user.Status = status;
            await _context.SaveChangesAsync();

            return NoContent();
        }

        // POST: api/users/{id}/reset-password
        [HttpPost("{id}/reset-password")]
        public async Task<IActionResult> ResetPassword(int id)
        {
            var user = await _context.Users.FindAsync(id);

            if (user == null)
                return NotFound();

            user.Password = BCrypt.Net.BCrypt.HashPassword(DefaultPassword);
            await _context.SaveChangesAsync();

            return NoContent();
        }
    }
} 