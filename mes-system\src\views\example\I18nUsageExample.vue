<template>
  <div class="i18n-usage-example">
    <div class="page-header">
      <!-- 使用翻译函数显示标题 -->
      <h2 class="page-title">{{ $t('i18n.management') }}</h2>
      <div class="language-switcher">
        <el-radio-group v-model="currentLanguage" @change="handleLanguageChange">
          <el-radio-button label="zh">中文</el-radio-button>
          <el-radio-button label="en">English</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <span>{{ $t('i18n.management') }}使用示例</span>
        </div>
      </template>

      <div class="demo-content">
        <h3>基本用法</h3>
        <p>在组件中使用多语言翻译：</p>
        
        <div class="code-example">
          <h4>在模板中使用：</h4>
          <pre><code>&lt;!-- 基本用法 --&gt;
{{ $t('common.add') }}

&lt;!-- 带参数的用法 --&gt;
{{ $t('common.welcome', { name: 'User' }) }}

&lt;!-- 按钮中使用 --&gt;
&lt;el-button type="primary"&gt;{{ $t('common.save') }}&lt;/el-button&gt;</code></pre>
        </div>

        <div class="code-example">
          <h4>在脚本中使用：</h4>
          <pre><code>import { useI18n } from 'vue-i18n'

// 在 setup 中使用
const { t } = useI18n()
const message = t('common.success')

// 或者使用全局实例
import i18n from '@/i18n'
const message = i18n.global.t('common.error')</code></pre>
        </div>

        <h3>实际效果演示</h3>
        <div class="demo-buttons">
          <el-button type="primary">{{ $t('common.add') }}</el-button>
          <el-button type="success">{{ $t('common.edit') }}</el-button>
          <el-button type="warning">{{ $t('common.delete') }}</el-button>
          <el-button type="info">{{ $t('common.search') }}</el-button>
          <el-button type="default">{{ $t('common.refresh') }}</el-button>
        </div>

        <h3>菜单翻译示例</h3>
        <div class="menu-demo">
          <ul class="demo-menu">
            <li>{{ $t('menu.dashboard') }}</li>
            <li>{{ $t('menu.engineering') }}</li>
            <li>{{ $t('menu.production') }}</li>
            <li>{{ $t('menu.quality') }}</li>
            <li>{{ $t('menu.warehouse') }}</li>
            <li>{{ $t('menu.equipment') }}</li>
            <li>{{ $t('menu.hr') }}</li>
            <li>{{ $t('menu.system') }}</li>
          </ul>
        </div>

        <h3>表单翻译示例</h3>
        <el-form :model="demoForm" label-width="120px" style="max-width: 500px;">
          <el-form-item :label="$t('i18n.translationKey')">
            <el-input v-model="demoForm.key" :placeholder="$t('i18n.translationKey')" />
          </el-form-item>
          <el-form-item :label="$t('i18n.module')">
            <el-input v-model="demoForm.module" :placeholder="$t('i18n.module')" />
          </el-form-item>
          <el-form-item :label="$t('i18n.description')">
            <el-input v-model="demoForm.description" type="textarea" :placeholder="$t('i18n.description')" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary">{{ $t('common.save') }}</el-button>
            <el-button>{{ $t('common.cancel') }}</el-button>
          </el-form-item>
        </el-form>

        <h3>状态翻译示例</h3>
        <div class="status-demo">
          <el-tag type="success">{{ $t('i18n.status.completed') }}</el-tag>
          <el-tag type="warning">{{ $t('i18n.status.incomplete') }}</el-tag>
          <el-tag type="danger">{{ $t('i18n.status.missing') }}</el-tag>
        </div>

        <h3>消息提示翻译示例</h3>
        <div class="message-demo">
          <el-button @click="showSuccess">{{ $t('common.success') }}消息</el-button>
          <el-button @click="showError">{{ $t('common.error') }}消息</el-button>
          <el-button @click="showWarning">{{ $t('common.warning') }}消息</el-button>
          <el-button @click="showInfo">{{ $t('common.info') }}消息</el-button>
        </div>

        <div class="tips">
          <h3>使用提示</h3>
          <ul>
            <li>所有的文本内容都应该使用 $t() 函数进行翻译</li>
            <li>翻译键使用点号分隔的层级结构，如：common.add, system.userManagement</li>
            <li>在多语言管理页面中可以统一管理所有翻译内容</li>
            <li>支持从页面中自动提取文本并生成翻译键</li>
            <li>可以导入导出翻译数据，便于翻译人员处理</li>
            <li>切换语言后，页面内容会自动更新</li>
          </ul>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'

const { t, locale } = useI18n()

// 当前语言
const currentLanguage = ref(locale.value)

// 演示表单数据
const demoForm = ref({
  key: '',
  module: '',
  description: ''
})

// 切换语言
const handleLanguageChange = (lang) => {
  locale.value = lang
  localStorage.setItem('language', lang)
  ElMessage.success(`语言已切换为 ${lang === 'zh' ? '中文' : 'English'}`)
}

// 消息提示示例
const showSuccess = () => {
  ElMessage.success(t('common.success'))
}

const showError = () => {
  ElMessage.error(t('common.error'))
}

const showWarning = () => {
  ElMessage.warning(t('common.warning'))
}

const showInfo = () => {
  ElMessage.info(t('common.info'))
}
</script>

<style scoped>
.i18n-usage-example {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  color: #303133;
}

.language-switcher {
  display: flex;
  align-items: center;
}

.demo-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-content {
  line-height: 1.6;
}

.demo-content h3 {
  margin: 30px 0 15px 0;
  color: #409eff;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}

.demo-content h4 {
  margin: 20px 0 10px 0;
  color: #606266;
}

.code-example {
  margin: 20px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 5px;
  border-left: 4px solid #409eff;
}

.code-example pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #666;
  overflow-x: auto;
}

.demo-buttons {
  margin: 20px 0;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.menu-demo {
  margin: 20px 0;
}

.demo-menu {
  list-style: none;
  padding: 0;
  background: #f8f9fa;
  border-radius: 5px;
  padding: 15px;
}

.demo-menu li {
  padding: 8px 0;
  border-bottom: 1px solid #e4e7ed;
  color: #606266;
}

.demo-menu li:last-child {
  border-bottom: none;
}

.status-demo {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.message-demo {
  margin: 20px 0;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.tips {
  margin-top: 30px;
  padding: 20px;
  background: #f0f9ff;
  border-radius: 5px;
  border-left: 4px solid #409eff;
}

.tips h3 {
  margin-top: 0;
  color: #409eff;
}

.tips ul {
  margin: 15px 0;
  padding-left: 20px;
}

.tips li {
  margin: 8px 0;
  color: #606266;
}
</style> 