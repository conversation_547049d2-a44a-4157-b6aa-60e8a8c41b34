# 登录页面多语言切换功能使用指南

## 功能概述

本指南介绍了如何在MES系统登录页面使用多语言切换功能。该功能允许用户在登录界面右上角选择不同的语言（中文/英文），系统会自动切换界面语言并保存用户的语言偏好。

## 功能特性

### 1. 多语言支持
- **中文**：简体中文界面
- **英文**：English界面
- **自动检测**：根据浏览器语言自动设置默认语言
- **记忆功能**：保存用户的语言选择，下次访问时自动应用

### 2. 界面元素翻译
- 页面标题和口号
- 登录表单标签和占位符
- 按钮文本
- 验证提示信息
- 版权信息

### 3. 响应式设计
- 在不同屏幕尺寸下都能正常显示
- 移动端优化布局

## 使用方法

### 切换语言
1. 打开登录页面
2. 在右上角找到语言切换按钮（显示当前语言和国旗图标）
3. 点击语言切换按钮，打开语言选择下拉菜单
4. 选择需要的语言（中文/English）
5. 系统会立即切换到选定的语言

### 语言持久化
- 用户选择的语言会自动保存到浏览器本地存储
- 下次访问时会自动应用上次选择的语言
- 如果是首次访问，系统会根据浏览器语言设置自动选择合适的语言

## 实现细节

### 翻译键映射
系统使用了与Language.json文件兼容的翻译键：

```javascript
// 中文翻译键
login: {
  welcome: '欢迎登录',
  welcomeText: 'MES智能制造执行系统',
  slogan: '智能制造执行系统',
  subSlogan: '打造智能工厂，引领工业4.0',
  phuseruser: '请输入用户名',
  phpasswordpassword: '请输入密码',
  rememberMe: '记住我',
  forgotPassword: '忘记密码？',
  loginButton: '登录',
  loginSuccess: '登录成功',
  // ...更多翻译键
}
```

### 组件结构
- `LanguageSwitcher.vue`：语言切换组件
- `Login.vue`：登录页面（已修改支持多语言）
- `i18n/index.js`：多语言配置文件

## 技术要点

### 1. Vue I18n集成
```javascript
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
// 使用 t('login.welcome') 获取翻译文本
```

### 2. 响应式翻译
```javascript
// 表单验证规则使用computed确保翻译更新
const rules = computed(() => ({
  username: [
    { required: true, message: t('login.usernameRequired'), trigger: 'blur' }
  ]
}))
```

### 3. 样式适配
```css
/* 语言切换按钮在不同屏幕尺寸下的适配 */
.language-switcher-wrapper {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

@media screen and (max-width: 768px) {
  .language-switcher-wrapper {
    position: fixed;
    top: 15px;
    right: 15px;
  }
}
```

## 扩展功能

### 添加新语言
1. 在`i18n/index.js`中添加新语言的翻译内容
2. 在`LanguageSwitcher.vue`中添加新语言到`supportedLanguages`数组
3. 更新`getDefaultLanguage`函数以支持新语言检测

### 自定义翻译键
如果需要修改翻译键名以匹配您的Language.json文件格式：
1. 修改`i18n/index.js`中的翻译键
2. 更新Login.vue中对应的`$t()`调用

## 注意事项

1. **首次使用**：确保已经在`main.js`中正确导入和使用i18n插件
2. **翻译键一致性**：确保翻译键与Language.json文件中的键名保持一致
3. **浏览器兼容性**：语言切换功能支持现代浏览器
4. **本地存储**：用户的语言偏好保存在localStorage中

## 常见问题

### Q: 语言切换后页面没有更新？
A: 检查是否在main.js中正确导入并使用了i18n插件。

### Q: 如何添加更多语言？
A: 在i18n配置文件中添加新语言的翻译内容，并更新LanguageSwitcher组件的支持语言列表。

### Q: 翻译文本没有显示？
A: 确保翻译键名正确，并且在i18n配置中存在对应的翻译内容。

## 文件结构

```
mes-system/
├── src/
│   ├── components/
│   │   └── LanguageSwitcher.vue          # 语言切换组件
│   ├── i18n/
│   │   └── index.js                      # 多语言配置
│   ├── views/
│   │   └── Login.vue                     # 登录页面
│   └── main.js                           # 应用入口
└── Language.json                         # 原始翻译文件
```

通过以上配置，您的MES系统登录页面现在支持完整的多语言切换功能，用户可以方便地在中英文之间切换，提升了系统的国际化水平。 