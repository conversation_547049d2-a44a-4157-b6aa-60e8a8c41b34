using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using mes_system_server.Data;
using mes_system_server.DTOs;
using mes_system_server.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace mes_system_server.Controllers
{
    [Route("api/positions")]
    [ApiController]
    [AllowAnonymous]
    public class PositionsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public PositionsController(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取职位列表
        /// </summary>
        /// <param name="page">页码，默认为1</param>
        /// <param name="pageSize">每页记录数，默认为10</param>
        /// <param name="search">搜索关键词</param>
        /// <returns>职位列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetPositions([FromQuery] int page = 1, [FromQuery] int pageSize = 10, [FromQuery] string search = "")
        {
            try
            {
                var query = _context.Positions
                    .Include(p => p.Department)
                    .AsQueryable();

                // 应用搜索条件
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(p => 
                        p.Name.Contains(search) || 
                        p.Department.Name.Contains(search)
                    );
                }

                // 计算总记录数
                var total = await query.CountAsync();

                // 应用分页
                var positions = await query
                    .OrderBy(p => p.Id)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // 构建响应数据
                var positionDtos = positions.Select(p => new PositionDto
                {
                    Id = p.Id,
                    Name = p.Name,
                    DepartmentId = p.DepartmentId,
                    DepartmentName = p.Department.Name,
                    Level = p.Level,
                    Description = p.Description,
                    CreateTime = p.CreateTime
                }).ToList();

                var response = new PositionResponseDto
                {
                    Total = total,
                    List = positionDtos
                };

                return Ok(ApiResponseDto<PositionResponseDto>.Success(response));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取职位列表失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 根据ID获取职位详情
        /// </summary>
        /// <param name="id">职位ID</param>
        /// <returns>职位详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetPosition(int id)
        {
            try
            {
                var position = await _context.Positions
                    .Include(p => p.Department)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (position == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("职位不存在", 404));
                }

                var positionDto = new PositionDto
                {
                    Id = position.Id,
                    Name = position.Name,
                    DepartmentId = position.DepartmentId,
                    DepartmentName = position.Department.Name,
                    Level = position.Level,
                    Description = position.Description,
                    CreateTime = position.CreateTime
                };

                return Ok(ApiResponseDto<PositionDto>.Success(positionDto));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取职位详情失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 创建职位
        /// </summary>
        /// <param name="positionDto">职位信息</param>
        /// <returns>创建的职位</returns>
        [HttpPost]
        public async Task<IActionResult> CreatePosition([FromBody] CreatePositionDto positionDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                // 检查部门是否存在
                var department = await _context.Departments.FindAsync(positionDto.DepartmentId);
                if (department == null)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("所选部门不存在"));
                }

                var position = new Position
                {
                    Name = positionDto.Name,
                    DepartmentId = positionDto.DepartmentId,
                    Level = positionDto.Level,
                    Description = positionDto.Description,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };

                _context.Positions.Add(position);
                await _context.SaveChangesAsync();

                // 重新加载职位信息，包括部门名称
                position = await _context.Positions
                    .Include(p => p.Department)
                    .FirstOrDefaultAsync(p => p.Id == position.Id);

                var createdPositionDto = new PositionDto
                {
                    Id = position.Id,
                    Name = position.Name,
                    DepartmentId = position.DepartmentId,
                    DepartmentName = position.Department.Name,
                    Level = position.Level,
                    Description = position.Description,
                    CreateTime = position.CreateTime
                };

                return CreatedAtAction(nameof(GetPosition), new { id = position.Id }, 
                    ApiResponseDto<PositionDto>.Success(createdPositionDto, "添加成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"创建职位失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 更新职位
        /// </summary>
        /// <param name="id">职位ID</param>
        /// <param name="positionDto">职位更新信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdatePosition(int id, [FromBody] UpdatePositionDto positionDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                var position = await _context.Positions.FindAsync(id);
                if (position == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("职位不存在", 404));
                }

                // 检查部门是否存在
                var department = await _context.Departments.FindAsync(positionDto.DepartmentId);
                if (department == null)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("所选部门不存在"));
                }

                // 更新职位信息
                position.Name = positionDto.Name;
                position.DepartmentId = positionDto.DepartmentId;
                position.Level = positionDto.Level;
                position.Description = positionDto.Description;
                position.UpdateTime = DateTime.Now;

                _context.Entry(position).State = EntityState.Modified;
                await _context.SaveChangesAsync();

                return Ok(ApiResponseDto<string>.Success("更新成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"更新职位失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 删除职位
        /// </summary>
        /// <param name="id">职位ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeletePosition(int id)
        {
            try
            {
                var position = await _context.Positions.FindAsync(id);
                if (position == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("职位不存在", 404));
                }

                _context.Positions.Remove(position);
                await _context.SaveChangesAsync();

                return Ok(ApiResponseDto<string>.Success("删除成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"删除职位失败: {ex.Message}", 500));
            }
        }
    }
} 