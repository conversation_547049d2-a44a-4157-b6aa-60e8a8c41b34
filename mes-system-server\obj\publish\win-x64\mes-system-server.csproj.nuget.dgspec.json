{"format": 1, "restore": {"D:\\开发\\For Cursor\\AMPER\\mes-system-server\\mes-system-server.csproj": {}}, "projects": {"D:\\开发\\For Cursor\\AMPER\\mes-system-server\\mes-system-server.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\开发\\For Cursor\\AMPER\\mes-system-server\\mes-system-server.csproj", "projectName": "mes-system-server", "projectPath": "D:\\开发\\For Cursor\\AMPER\\mes-system-server\\mes-system-server.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\开发\\For Cursor\\AMPER\\mes-system-server\\obj\\publish\\win-x64\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.15, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.15, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}, "System.Linq.Dynamic.Core": {"target": "Package", "version": "[1.3.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[8.0.8, 8.0.8]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}