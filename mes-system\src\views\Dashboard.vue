<template>
  <div class="dashboard-container">
    <div class="page-header">
      <h2>仪表盘</h2>
      <p>欢迎使用MES智能制造执行系统，以下是今日生产概况</p>
    </div>

    <!-- 数据卡片区域 -->
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="data-card bg-blue">
          <div class="card-header">
            <span>今日产量</span>
            <el-icon><DataLine /></el-icon>
          </div>
          <div class="card-content">
            <div class="main-num">1,286</div>
            <div class="sub-info">
              <span>计划：1,500</span>
              <div class="status up">
                <el-icon><ArrowUp /></el-icon>
                <span>5.8%</span>
              </div>
            </div>
          </div>
          <el-progress :percentage="85" color="#409EFF" />
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="data-card bg-green">
          <div class="card-header">
            <span>设备运行</span>
            <el-icon><Monitor /></el-icon>
          </div>
          <div class="card-content">
            <div class="main-num">92%</div>
            <div class="sub-info">
              <span>运行：46/50台</span>
              <div class="status up">
                <el-icon><ArrowUp /></el-icon>
                <span>2.3%</span>
              </div>
            </div>
          </div>
          <el-progress :percentage="92" color="#67C23A" />
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="data-card bg-orange">
          <div class="card-header">
            <span>质量监控</span>
            <el-icon><Warning /></el-icon>
          </div>
          <div class="card-content">
            <div class="main-num">98.5%</div>
            <div class="sub-info">
              <span>不良品：19</span>
              <div class="status down">
                <el-icon><ArrowDown /></el-icon>
                <span>0.3%</span>
              </div>
            </div>
          </div>
          <el-progress :percentage="98.5" color="#E6A23C" />
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="data-card bg-red">
          <div class="card-header">
            <span>能耗监控</span>
            <el-icon><Lightning /></el-icon>
          </div>
          <div class="card-content">
            <div class="main-num">256.4</div>
            <div class="sub-info">
              <span>单位：kW·h</span>
              <div class="status down">
                <el-icon><ArrowDown /></el-icon>
                <span>1.7%</span>
              </div>
            </div>
          </div>
          <el-progress :percentage="75" color="#F56C6C" />
        </div>
      </el-col>
    </el-row>

    <!-- 图表卡片区域 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <div class="chart-card">
          <div class="chart-header">
            <h3>生产趋势分析</h3>
            <div class="chart-actions">
              <el-radio-group v-model="timeRange" size="small">
                <el-radio-button label="daily">日</el-radio-button>
                <el-radio-button label="weekly">周</el-radio-button>
                <el-radio-button label="monthly">月</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div class="chart-content" ref="trendChartRef"></div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>产品类型分布</h3>
          </div>
          <div class="chart-content" ref="distributionChartRef"></div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { DataLine, ArrowUp, ArrowDown, Monitor, Warning, Lightning } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 时间范围选择
const timeRange = ref('daily')

// 图表引用
const trendChartRef = ref(null)
const distributionChartRef = ref(null)

// 图表实例
let trendChart = null
let distributionChart = null

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value) return
  
  trendChart = echarts.init(trendChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['计划产量', '实际产量', '合格率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1日', '2日', '3日', '4日', '5日', '6日', '7日']
    },
    yAxis: [
      {
        type: 'value',
        name: '产量',
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '合格率',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '计划产量',
        type: 'bar',
        data: [320, 332, 301, 334, 390, 330, 320]
      },
      {
        name: '实际产量',
        type: 'bar',
        data: [300, 310, 290, 330, 380, 320, 310]
      },
      {
        name: '合格率',
        type: 'line',
        yAxisIndex: 1,
        data: [95.5, 96.8, 97.2, 98.1, 98.5, 97.8, 98.3]
      }
    ]
  }
  
  trendChart.setOption(option)
}

// 初始化分布图表
const initDistributionChart = () => {
  if (!distributionChartRef.value) return
  
  distributionChart = echarts.init(distributionChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      right: 'right'
    },
    series: [
      {
        name: '产品类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 1048, name: '断路器' },
          { value: 735, name: '继电器' },
          { value: 580, name: '接触器' },
          { value: 484, name: '漏电保护器' },
          { value: 300, name: '其他' }
        ]
      }
    ]
  }
  
  distributionChart.setOption(option)
}

// 窗口大小变化时重置图表大小
const handleResize = () => {
  trendChart?.resize()
  distributionChart?.resize()
}

// 监听时间范围变化
const watchTimeRange = () => {
  console.log('时间范围变更为:', timeRange.value)
  // 这里可以添加根据时间范围获取新数据的逻辑
}

onMounted(() => {
  initTrendChart()
  initDistributionChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  trendChart?.dispose()
  distributionChart?.dispose()
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  font-size: 24px;
  margin-bottom: 8px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  color: #606266;
  font-size: 14px;
}

.data-card {
  height: 200px;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
  position: relative;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.card-content {
  padding: 10px 0 15px;
}

.main-num {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 10px;
}

.sub-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-size: 14px;
}

.status {
  display: flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.2;
}

.status.up {
  color: #67C23A;
  background-color: rgba(103, 194, 58, 0.1);
}

.status.down {
  color: #F56C6C;
  background-color: rgba(245, 108, 108, 0.1);
}

.bg-blue .card-header {
  color: #409EFF;
}

.bg-green .card-header {
  color: #67C23A;
}

.bg-orange .card-header {
  color: #E6A23C;
}

.bg-red .card-header {
  color: #F56C6C;
}

.chart-row {
  margin-top: 10px;
}

.chart-card {
  min-height: 320px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.chart-content {
  height: 280px;
}
</style> 