import request from '@/utils/request'

/**
 * 上传PDF图片到服务器
 * @param {Object} data - 包含PDF图片信息的对象
 * @param {string} data.fileName - PDF文件名（不含扩展名）
 * @param {string} data.bookmarkName - 书签名称
 * @param {number} data.pageNumber - 页码
 * @param {string} data.imageBase64 - 图片Base64数据
 * @param {number} data.imageWidth - 图片宽度
 * @param {number} data.imageHeight - 图片高度
 * @param {string} data.remarks - 备注
 * @returns {Promise} - 返回上传结果
 */
export function uploadPDFImage(data) {
  return request({
    url: '/api/wis/uploadPDFImage',
    method: 'post',
    data: data,
    timeout: 600000 // 10分钟超时，适合单个大图片上传
  })
}

/**
 * 上传PDF图片到云服务器文件夹
 * @param {Object} data - 包含PDF图片信息的对象
 * @param {string} data.fileName - PDF文件名（不含扩展名）
 * @param {string} data.bookmarkName - 书签名称
 * @param {number} data.pageNumber - 页码
 * @param {string} data.imageBase64 - 图片Base64数据
 * @param {number} data.imageWidth - 图片宽度
 * @param {number} data.imageHeight - 图片高度
 * @param {string} data.remarks - 备注
 * @param {boolean} data.saveBase64 - 是否同时保存Base64到数据库
 * @returns {Promise} - 返回上传结果
 */
export function uploadPDFImageToCloud(data) {
  return request({
    url: '/api/wis/uploadPDFImageToCloud',
    method: 'post',
    data: data,
    timeout: 600000 // 10分钟超时，适合大图片文件保存
  })
}

/**
 * 批量上传PDF图片到云服务器（控制并发数量）
 * @param {Array} imageList - 图片数据列表
 * @param {number} concurrency - 并发数量，默认2
 * @param {Function} onProgress - 进度回调函数
 * @param {boolean} saveBase64 - 是否同时保存Base64到数据库
 * @returns {Promise} - 返回上传结果
 */
export function batchUploadPDFImagesToCloud(imageList, concurrency = 2, onProgress, saveBase64 = false) {
  return new Promise(async (resolve, reject) => {
    try {
      const results = []
      const total = imageList.length
      let completed = 0
      let failed = 0

      // 分批处理
      for (let i = 0; i < imageList.length; i += concurrency) {
        const batch = imageList.slice(i, i + concurrency)
        
        // 并发上传当前批次
        const batchPromises = batch.map(async (imageData) => {
          try {
            // 添加saveBase64参数
            const uploadData = {
              ...imageData,
              saveBase64: saveBase64
            }
            
            const result = await uploadPDFImageToCloud(uploadData)
            completed++
            
            if (onProgress) {
              onProgress({
                completed,
                total,
                failed,
                current: imageData,
                percentage: Math.round((completed / total) * 100)
              })
            }
            
            return { success: true, data: result, imageData }
          } catch (error) {
            failed++
            console.error(`上传到云服务器失败 - ${imageData.fileName} 第${imageData.pageNumber}页:`, error)
            
            if (onProgress) {
              onProgress({
                completed,
                total,
                failed,
                current: imageData,
                percentage: Math.round(((completed + failed) / total) * 100),
                error: error.message
              })
            }
            
            return { success: false, error, imageData }
          }
        })

        // 等待当前批次完成
        const batchResults = await Promise.all(batchPromises)
        results.push(...batchResults)
        
        // 添加小延迟，避免服务器压力过大
        if (i + concurrency < imageList.length) {
          await new Promise(resolve => setTimeout(resolve, 200))
        }
      }

      resolve({
        total,
        completed: completed,
        failed: failed,
        results
      })
    } catch (error) {
      reject(error)
    }
  })
}

/**
 * 批量上传PDF图片（控制并发数量）
 * @param {Array} imageList - 图片数据列表
 * @param {number} concurrency - 并发数量，默认3
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise} - 返回上传结果
 */
export function batchUploadPDFImagesWithConcurrency(imageList, concurrency = 3, onProgress) {
  return new Promise(async (resolve, reject) => {
    try {
      const results = []
      const total = imageList.length
      let completed = 0
      let failed = 0

      // 分批处理
      for (let i = 0; i < imageList.length; i += concurrency) {
        const batch = imageList.slice(i, i + concurrency)
        
        // 并发上传当前批次
        const batchPromises = batch.map(async (imageData, index) => {
          try {
            const result = await uploadPDFImage(imageData)
            completed++
            
            if (onProgress) {
              onProgress({
                completed,
                total,
                failed,
                current: imageData,
                percentage: Math.round((completed / total) * 100)
              })
            }
            
            return { success: true, data: result, imageData }
          } catch (error) {
            failed++
            console.error(`上传失败 - ${imageData.fileName} 第${imageData.pageNumber}页:`, error)
            
            if (onProgress) {
              onProgress({
                completed,
                total,
                failed,
                current: imageData,
                percentage: Math.round(((completed + failed) / total) * 100),
                error: error.message
              })
            }
            
            return { success: false, error, imageData }
          }
        })

        // 等待当前批次完成
        const batchResults = await Promise.all(batchPromises)
        results.push(...batchResults)
        
        // 添加小延迟，避免服务器压力过大
        if (i + concurrency < imageList.length) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }

      resolve({
        total,
        completed: completed,
        failed: failed,
        results
      })
    } catch (error) {
      reject(error)
    }
  })
}

/**
 * 获取PDF图片
 * @param {string} fileName - PDF文件名
 * @param {number} pageNumber - 页码
 * @returns {Promise} - 返回图片信息
 */
export function getPDFImage(fileName, pageNumber) {
  return request({
    url: '/api/wis/getPDFImage',
    method: 'get',
    params: {
      fileName,
      pageNumber
    }
  })
}

/**
 * 删除PDF相关的所有图片
 * @param {string} fileName - PDF文件名
 * @param {boolean} deleteImageFiles - 是否同时删除图片文件
 * @returns {Promise} - 返回删除结果
 */
export function deletePDFImages(fileName, deleteImageFiles = true) {
  return request({
    url: '/api/wis/deletePDFImages',
    method: 'delete',
    data: {
      fileName,
      deleteImageFiles
    }
  })
}

/**
 * 保存PDF书签数据
 * @param {Object} data - 书签数据
 * @param {string} data.fileName - PDF文件名
 * @param {Array} data.bookmarks - 书签列表
 * @returns {Promise} - 返回保存结果
 */
export function savePDFBookmarks(data) {
  return request({
    url: '/api/wis/savePDFBookmarks',
    method: 'post',
    data: data,
    timeout: 120000 // 2分钟超时，适合批量书签保存
  })
}

/**
 * 获取PDF书签数据
 * @param {string} fileName - PDF文件名
 * @returns {Promise} - 返回书签列表
 */
export function getPDFBookmarks(fileName) {
  return request({
    url: '/api/wis/getPDFBookmarks',
    method: 'get',
    params: {
      fileName
    }
  })
}

/**
 * 删除PDF书签数据
 * @param {string} fileName - PDF文件名
 * @param {boolean} deleteImageFiles - 是否同时删除图片文件
 * @returns {Promise} - 返回删除结果
 */
export function deletePDFBookmarks(fileName, deleteImageFiles = true) {
  return request({
    url: '/api/wis/deletePDFBookmarks',
    method: 'delete',
    data: {
      fileName,
      deleteImageFiles
    }
  })
}

/**
 * 批量上传PDF图片
 * @param {Object} data - 批量上传数据
 * @param {string} data.fileName - PDF文件名
 * @param {Array} data.images - 图片列表
 * @returns {Promise} - 返回上传结果
 */
export function batchUploadPDFImages(data) {
  return request({
    url: '/api/wis/batchUploadPDFImages',
    method: 'post',
    data: data
  })
}

/**
 * 获取PDF文件列表
 * @returns {Promise} - 返回PDF文件列表
 */
export function getPDFList() {
  return request({
    url: '/api/wis/pdf-list',
    method: 'get',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  }).then(response => {
    // 直接返回response.data，因为request拦截器已经处理了响应
    return response.data
  })
}

/**
 * 查询PDF书签
 * @param {Object} params - 查询参数
 * @param {string} params.keyword - 查询关键词
 * @param {number} params.pageSize - 返回结果数量限制
 * @param {number} params.pageNumber - 页码（可选）
 * @param {boolean} params.loadImages - 是否加载图片数据（可选）
 * @returns {Promise} - 返回查询结果
 */
export function queryPDFBookmarks(params) {
  return request({
    url: '/api/wis/bookmarks/search',
    method: 'get',
    params: params
  })
}

/**
 * 根据PDF文件名获取书签列表
 * @param {string} fileName - PDF文件名
 * @param {Object} options - 查询选项
 * @param {boolean} options.loadImages - 是否加载图片数据，默认false
 * @param {number} options.pageSize - 每页数量，默认100
 * @param {number} options.pageNumber - 页码，默认1
 * @returns {Promise} - 返回书签列表
 */
export function getPDFBookmarksByFileName(fileName, options = {}) {
  const { 
    loadImages = false, 
    pageSize = 100, 
    pageNumber = 1 
  } = options;
  
  return request({
    url: '/api/wis/bookmarks/by-filename',
    method: 'get',
    params: {
      fileName: fileName,
      loadImages: loadImages,
      pageSize: pageSize,
      pageNumber: pageNumber
    },
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  }).then(response => {
    // 直接返回response.data，因为request拦截器已经处理了响应
    return response.data
  })
}

/**
 * 获取PDF文件基本信息
 * @param {string} fileName - PDF文件名
 * @returns {Promise} - 返回PDF基本信息
 */
export function getPDFInfo(fileName) {
  return request({
    url: '/api/wis/pdf/info',
    method: 'get',
    params: {
      fileName: fileName
    }
  })
}

/**
 * 按需获取PDF图片数据
 * @param {string} fileName - PDF文件名
 * @param {number} pageNumber - 页码
 * @returns {Promise} - 返回图片数据
 */
export function getPDFImageOnDemand(fileName, pageNumber) {
  return request({
    url: '/api/wis/getPDFImage',
    method: 'get',
    params: {
      fileName,
      pageNumber
    }
  })
} 