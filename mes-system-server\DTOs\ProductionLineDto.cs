using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace mes_system_server.DTOs
{
    public class ProductionLineDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime UpdateTime { get; set; }
        public List<WorkCenterDto> WorkCenters { get; set; } = new();
    }

    public class CreateProductionLineDto
    {
        [Required(ErrorMessage = "产线名称不能为空")]
        [StringLength(100, ErrorMessage = "产线名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "产线编码不能为空")]
        [StringLength(50, ErrorMessage = "产线编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }
    }

    public class UpdateProductionLineDto
    {
        [Required(ErrorMessage = "产线名称不能为空")]
        [StringLength(100, ErrorMessage = "产线名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "产线编码不能为空")]
        [StringLength(50, ErrorMessage = "产线编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;
    }

    public class ProductionLineResponseDto
    {
        public int Total { get; set; }
        public List<ProductionLineDto> List { get; set; } = new();
    }
} 