# 角色权限控制系统使用指南

## 概述

本系统实现了完整的基于角色的访问控制（RBAC）功能，用户登录后系统会根据其角色自动加载相应的菜单权限，动态控制左侧导航栏的菜单显示。

## 功能特性

### ✅ 已实现的功能

1. **用户角色关联**
   - 用户登录时自动获取角色信息
   - 支持用户与角色的多对一关系

2. **动态菜单权限**
   - 根据用户角色动态加载菜单权限
   - 左侧导航栏根据权限过滤显示菜单
   - 支持层级菜单的权限控制

3. **权限管理界面**
   - 角色菜单设置界面
   - 可视化的树形菜单权限配置
   - 一键初始化默认权限数据

4. **安全控制**
   - 路由守卫防止未授权访问
   - 登录状态检查
   - 权限验证工具类

## 系统架构

### 数据库表结构

```sql
-- 用户表
Users {
  Id: int (主键)
  Username: string (用户名)
  Name: string (姓名)
  Role: string (角色名称)
  RoleId: int (角色ID外键)
  -- 其他字段...
}

-- 角色表
Roles {
  Id: int (主键)
  RoleName: string (角色名称)
  RoleCode: string (角色编码)
  -- 其他字段...
}

-- 菜单表
Menus {
  Id: int (主键)
  MenuId: string (菜单唯一标识)
  Name: string (菜单名称)
  Path: string (菜单路径)
  Type: string (菜单类型)
  ParentId: int (父菜单ID)
  -- 其他字段...
}

-- 角色菜单关联表
RoleMenus {
  Id: int (主键)
  RoleId: int (角色ID)
  MenuId: int (菜单ID)
  CreateTime: datetime (创建时间)
}
```

### 核心文件说明

#### 后端文件
- `Controllers/AuthController.cs` - 登录认证控制器
- `Controllers/RolesController.cs` - 角色管理控制器
- `Controllers/MenusController.cs` - 菜单管理控制器
- `Models/User.cs` - 用户模型
- `Models/Role.cs` - 角色模型
- `Models/Menu.cs` - 菜单模型
- `Models/RoleMenu.cs` - 角色菜单关联模型

#### 前端文件
- `src/utils/permission.js` - 权限工具类
- `src/views/Home.vue` - 主页面（含权限控制的导航菜单）
- `src/views/system/RolePermission.vue` - 角色权限管理页面
- `src/views/system/PermissionTest.vue` - 权限测试页面
- `src/router/index.js` - 路由配置（含路由守卫）
- `src/api/role.js` - 角色相关API

## 使用流程

### 1. 用户登录权限控制流程

```mermaid
graph TD
    A[用户登录] --> B[验证用户名密码]
    B --> C{验证成功?}
    C -->|否| D[登录失败]
    C -->|是| E[返回用户信息含RoleId]
    E --> F[前端保存用户信息到localStorage]
    F --> G[跳转到主页]
    G --> H[获取用户角色ID]
    H --> I[调用API获取角色菜单权限]
    I --> J[获取所有菜单树]
    J --> K[根据权限过滤菜单]
    K --> L[渲染左侧导航栏]
```

### 2. 权限数据初始化

#### 方法一：使用管理界面初始化
1. 登录系统（使用管理员账号）
2. 访问"系统管理 -> 角色权限"
3. 切换到"角色菜单设置"标签页
4. 点击"初始化角色权限"按钮
5. 确认初始化操作

#### 方法二：API调用初始化
```http
POST /api/roles/initialize-permissions
Content-Type: application/json
Authorization: Bearer {token}
```

#### 方法三：直接执行SQL脚本
运行 `mes-system-server/Scripts/InsertRoleMenuData.sql` 脚本

### 3. 角色菜单权限配置

1. 访问"系统管理 -> 角色权限"页面
2. 切换到"角色菜单设置"标签页
3. 从下拉框选择要配置的角色
4. 在菜单树中勾选该角色可访问的菜单
5. 点击"保存菜单权限"按钮

### 4. 权限测试

1. 访问"系统管理 -> 权限测试"页面（需要添加到菜单中）
2. 查看当前用户信息和权限
3. 测试特定菜单的权限
4. 查看过滤后的菜单树

## API接口说明

### 角色菜单权限API

#### 获取角色菜单权限
```http
GET /api/roles/{roleId}/menus
```

响应示例：
```json
{
  "code": 200,
  "message": "获取角色菜单权限成功",
  "data": ["system-users", "system-roles", "warehouse-in", "warehouse-out"]
}
```

#### 保存角色菜单权限
```http
PUT /api/roles/{roleId}/menus
Content-Type: application/json

{
  "menuIds": ["system-users", "system-roles", "warehouse-in"]
}
```

#### 初始化角色权限
```http
POST /api/roles/initialize-permissions
```

### 菜单管理API

#### 获取菜单树
```http
GET /api/menus/tree
```

#### 获取菜单列表
```http
GET /api/menus
```

## 前端权限工具类使用

### 导入权限工具类
```javascript
import { 
  getCurrentUser, 
  getCurrentUserMenuPermissions, 
  hasMenuPermission, 
  filterMenusByPermissions,
  isUserLoggedIn,
  isSuperAdmin,
  logout 
} from '@/utils/permission'
```

### 获取当前用户信息
```javascript
const userInfo = getCurrentUser()
console.log('当前用户:', userInfo)
```

### 获取用户菜单权限
```javascript
const menuPermissions = await getCurrentUserMenuPermissions()
console.log('用户菜单权限:', menuPermissions)
```

### 检查菜单权限
```javascript
const hasPermission = hasMenuPermission('system-users', menuPermissions)
console.log('是否有用户管理权限:', hasPermission)
```

### 过滤菜单
```javascript
const filteredMenus = filterMenusByPermissions(allMenus, userMenuPermissions)
console.log('过滤后的菜单:', filteredMenus)
```

## 默认角色权限配置

### 系统管理员 (RoleId: 1)
- 拥有所有菜单权限
- 可以管理用户、角色、菜单等系统功能

### 生产经理 (RoleId: 2)
- 生产计划、生产订单、排产管理
- 生产监控、生产任务
- 质量检验相关功能

### 质量检验员 (RoleId: 3)
- 质量检验
- 质量报告

## 扩展指南

### 添加新的权限检查

1. **在组件中检查权限**
```javascript
<template>
  <div>
    <el-button v-if="hasDeletePermission" @click="handleDelete">删除</el-button>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { getCurrentUserMenuPermissions, hasMenuPermission } from '@/utils/permission'

const userPermissions = await getCurrentUserMenuPermissions()
const hasDeletePermission = computed(() => 
  hasMenuPermission('system-users', userPermissions)
)
</script>
```

2. **在路由中检查权限**
```javascript
// 在路由守卫中添加更细粒度的权限检查
router.beforeEach(async (to, from, next) => {
  if (to.meta.requiredPermission) {
    const permissions = await getCurrentUserMenuPermissions()
    const hasPermission = hasMenuPermission(to.meta.requiredPermission, permissions)
    
    if (!hasPermission) {
      ElMessage.error('没有权限访问此页面')
      next(false)
      return
    }
  }
  next()
})
```

### 添加新的权限类型

除了菜单权限外，还可以扩展其他权限类型：

1. **操作权限**（增删改查）
2. **数据权限**（部门、区域）
3. **功能权限**（导入导出、审核）

## 常见问题

### Q1: 用户登录后看不到任何菜单？
**A:** 检查以下几点：
- 用户是否有分配角色（RoleId字段）
- 角色是否有配置菜单权限
- 菜单数据是否正确初始化

### Q2: 权限修改后不生效？
**A:** 可能的原因：
- 浏览器缓存，建议刷新页面
- 权限数据未正确保存到数据库
- 前端权限缓存未更新

### Q3: 如何为新角色配置权限？
**A:** 操作步骤：
1. 先在角色管理中创建新角色
2. 在角色菜单设置中为新角色分配菜单权限
3. 将用户分配到新角色

### Q4: 如何添加新的菜单项？
**A:** 操作步骤：
1. 在菜单管理中添加新菜单项
2. 在角色菜单设置中为相应角色分配新菜单权限
3. 确保前端路由配置正确

## 安全注意事项

1. **前端权限控制只是用户体验优化**，真正的安全控制必须在后端实现
2. **所有敏感API都应该有权限验证**
3. **定期审查用户权限分配**
4. **权限变更应该有日志记录**
5. **测试不同角色的权限边界**

## 测试建议

1. **创建不同角色的测试用户**
2. **测试权限边界情况**
3. **验证权限修改的即时生效性**
4. **测试路由守卫的有效性**
5. **验证登录登出的权限清理**

---

## 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 实现基础的角色权限控制
- ✅ 完成菜单权限动态过滤
- ✅ 添加权限管理界面
- ✅ 实现路由守卫
- ✅ 创建权限工具类
- ✅ 添加权限测试页面

### 计划中的功能
- 🔄 操作级权限控制
- 🔄 数据级权限控制
- 🔄 权限变更日志
- �� 权限导入导出
- 🔄 角色继承机制 