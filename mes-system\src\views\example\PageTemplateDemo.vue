<template>
  <PageTemplate
    ref="pageTemplateRef"
    page-title="演示管理"
    entity-name="演示数据"
    add-button-text="新增演示"
    :search-fields="searchFields"
    :table-columns="tableColumns"
    :form-fields="formFields"
    :form-rules="formRules"
    :actions="['edit', 'view', 'delete']"
    @search="handleSearch"
    @add="handleAdd"
    @edit="handleEdit"
    @view="handleView"
    @delete="handleDelete"
    @submit="handleSubmit"
    @refresh="loadData"
  >
    <!-- 状态列插槽 -->
    <template #status="{ row }">
      <el-tag :type="row.status ? 'success' : 'danger'">
        {{ row.status ? '启用' : '禁用' }}
      </el-tag>
    </template>
    
    <!-- 类型列插槽 -->
    <template #type="{ row }">
      <el-tag :type="getTypeTagType(row.type)">
        {{ getTypeLabel(row.type) }}
      </el-tag>
    </template>
    
    <!-- 额外操作按钮 -->
    <template #extra-actions>
      <el-button type="warning" :icon="Download" @click="handleExport">
        导出数据
      </el-button>
      <el-button type="info" :icon="Upload" @click="handleImport">
        导入数据
      </el-button>
    </template>
    
    <!-- 自定义操作列 -->
    <template #custom-actions="{ row }">
      <el-button 
        type="warning" 
        size="small" 
        @click="handleCopy(row)"
      >
        复制
      </el-button>
    </template>
  </PageTemplate>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Upload } from '@element-plus/icons-vue'
import PageTemplate from '@/components/PageTemplate.vue'

// 页面模板引用
const pageTemplateRef = ref(null)

// 搜索字段配置
const searchFields = [
  {
    prop: 'name',
    label: '名称',
    type: 'input',
    placeholder: '请输入演示名称'
  },
  {
    prop: 'type',
    label: '类型',
    type: 'select',
    options: [
      { label: '类型A', value: 'A' },
      { label: '类型B', value: 'B' },
      { label: '类型C', value: 'C' }
    ]
  },
  {
    prop: 'status',
    label: '状态',
    type: 'select',
    options: [
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ]
  },
  {
    prop: 'createTime',
    label: '创建时间',
    type: 'daterange'
  }
]

// 表格列配置
const tableColumns = [
  {
    prop: 'id',
    label: 'ID',
    width: '80'
  },
  {
    prop: 'name',
    label: '名称',
    minWidth: '150'
  },
  {
    prop: 'code',
    label: '编码',
    minWidth: '120'
  },
  {
    prop: 'type',
    label: '类型',
    width: '100',
    slot: 'type'
  },
  {
    prop: 'status',
    label: '状态',
    width: '100',
    slot: 'status'
  },
  {
    prop: 'score',
    label: '评分',
    width: '100'
  },
  {
    prop: 'description',
    label: '描述',
    minWidth: '200'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: '180'
  }
]

// 表单字段配置
const formFields = [
  {
    prop: 'name',
    label: '名称',
    type: 'input',
    placeholder: '请输入演示名称'
  },
  {
    prop: 'code',
    label: '编码',
    type: 'input',
    placeholder: '请输入演示编码'
  },
  {
    prop: 'type',
    label: '类型',
    type: 'select',
    options: [
      { label: '类型A', value: 'A' },
      { label: '类型B', value: 'B' },
      { label: '类型C', value: 'C' }
    ]
  },
  {
    prop: 'score',
    label: '评分',
    type: 'number',
    min: 0,
    max: 100,
    defaultValue: 60
  },
  {
    prop: 'status',
    label: '状态',
    type: 'switch',
    defaultValue: true
  },
  {
    prop: 'description',
    label: '描述',
    type: 'textarea',
    placeholder: '请输入演示描述'
  }
]

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入演示名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入演示编码', trigger: 'blur' },
    { 
      pattern: /^[A-Z][A-Z0-9_]*$/, 
      message: '编码必须以大写字母开头，只能包含大写字母、数字和下划线', 
      trigger: 'blur' 
    }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ],
  score: [
    { required: true, message: '请输入评分', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '评分范围为0-100', trigger: 'blur' }
  ]
}

// 获取类型标签样式
const getTypeTagType = (type) => {
  const typeMap = {
    'A': 'primary',
    'B': 'success',
    'C': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取类型标签文本
const getTypeLabel = (type) => {
  const labelMap = {
    'A': '类型A',
    'B': '类型B',
    'C': '类型C'
  }
  return labelMap[type] || '未知'
}

// 模拟数据
const mockData = [
  {
    id: 1,
    name: '演示数据1',
    code: 'DEMO_001',
    type: 'A',
    status: true,
    score: 85,
    description: '这是第一个演示数据，用于展示PageTemplate的功能',
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    name: '演示数据2',
    code: 'DEMO_002',
    type: 'B',
    status: false,
    score: 72,
    description: '这是第二个演示数据，展示不同状态和类型',
    createTime: '2024-01-14 15:20:00'
  },
  {
    id: 3,
    name: '演示数据3',
    code: 'DEMO_003',
    type: 'C',
    status: true,
    score: 90,
    description: '这是第三个演示数据，评分最高的示例',
    createTime: '2024-01-13 09:45:00'
  },
  {
    id: 4,
    name: '演示数据4',
    code: 'DEMO_004',
    type: 'A',
    status: true,
    score: 68,
    description: '第四个演示数据，用于展示分页功能',
    createTime: '2024-01-12 14:30:00'
  },
  {
    id: 5,
    name: '演示数据5',
    code: 'DEMO_005',
    type: 'B',
    status: false,
    score: 55,
    description: '第五个演示数据，禁用状态示例',
    createTime: '2024-01-11 11:15:00'
  }
]

// 加载数据
const loadData = async (params = {}) => {
  try {
    pageTemplateRef.value?.setLoading(true)
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟搜索过滤
    let filteredData = [...mockData]
    
    if (params.name) {
      filteredData = filteredData.filter(item => 
        item.name.includes(params.name)
      )
    }
    
    if (params.type) {
      filteredData = filteredData.filter(item => item.type === params.type)
    }
    
    if (params.status !== undefined && params.status !== '') {
      filteredData = filteredData.filter(item => item.status === params.status)
    }
    
    // 模拟分页
    const page = pageTemplateRef.value?.pagination.page || 1
    const size = pageTemplateRef.value?.pagination.size || 20
    const total = filteredData.length
    const start = (page - 1) * size
    const end = start + size
    const pageData = filteredData.slice(start, end)
    
    pageTemplateRef.value?.setDataList(pageData)
    pageTemplateRef.value?.setPagination(page, size, total)
    
    // 显示加载结果
    ElMessage.success(`加载完成，共找到 ${total} 条数据`)
    
  } catch (error) {
    console.error('加载数据失败:', error)
    pageTemplateRef.value?.showError('加载数据失败')
  } finally {
    pageTemplateRef.value?.setLoading(false)
  }
}

// 搜索处理
const handleSearch = (searchParams) => {
  console.log('搜索参数:', searchParams)
  loadData(searchParams)
}

// 新增处理
const handleAdd = () => {
  console.log('新增演示数据')
  ElMessage.info('新增功能：这里可以预填充一些默认值')
}

// 编辑处理
const handleEdit = (row) => {
  console.log('编辑演示数据:', row)
  ElMessage.info(`编辑功能：正在编辑 ${row.name}`)
}

// 查看处理
const handleView = (row) => {
  ElMessage.info(`查看详情：${row.name} - ${row.description}`)
}

// 删除处理
const handleDelete = async (row) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 从模拟数据中移除
    const index = mockData.findIndex(item => item.id === row.id)
    if (index > -1) {
      mockData.splice(index, 1)
    }
    
    pageTemplateRef.value?.showSuccess(`成功删除：${row.name}`)
    loadData()
  } catch (error) {
    console.error('删除失败:', error)
    pageTemplateRef.value?.showError('删除失败')
  }
}

// 表单提交处理
const handleSubmit = async ({ type, data }) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (type === 'add') {
      // 模拟新增
      const newId = Math.max(...mockData.map(item => item.id)) + 1
      const newItem = {
        ...data,
        id: newId,
        createTime: new Date().toLocaleString()
      }
      mockData.unshift(newItem)
      pageTemplateRef.value?.showSuccess(`成功新增：${data.name}`)
    } else {
      // 模拟编辑
      const index = mockData.findIndex(item => item.id === data.id)
      if (index > -1) {
        mockData[index] = { ...mockData[index], ...data }
      }
      pageTemplateRef.value?.showSuccess(`成功编辑：${data.name}`)
    }
    
    pageTemplateRef.value?.closeDialog()
    loadData()
  } catch (error) {
    console.error('保存失败:', error)
    pageTemplateRef.value?.showError('保存失败')
  } finally {
    pageTemplateRef.value?.setSubmitting(false)
  }
}

// 复制处理
const handleCopy = (row) => {
  const newItem = {
    ...row,
    id: Math.max(...mockData.map(item => item.id)) + 1,
    name: row.name + ' (副本)',
    code: row.code + '_COPY',
    createTime: new Date().toLocaleString()
  }
  mockData.unshift(newItem)
  ElMessage.success(`复制成功：${newItem.name}`)
  loadData()
}

// 导出处理
const handleExport = () => {
  const data = pageTemplateRef.value?.selectedRows || []
  if (data.length === 0) {
    ElMessage.warning('请先选择要导出的数据')
    return
  }
  
  const csvContent = [
    ['ID', '名称', '编码', '类型', '状态', '评分', '描述', '创建时间'].join(','),
    ...data.map(row => [
      row.id,
      row.name,
      row.code,
      row.type,
      row.status ? '启用' : '禁用',
      row.score,
      row.description,
      row.createTime
    ].join(','))
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = '演示数据.csv'
  link.click()
  
  ElMessage.success(`导出完成，共 ${data.length} 条数据`)
}

// 导入处理
const handleImport = () => {
  ElMessage.info('导入功能开发中，可以通过文件上传组件实现')
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
/* 自定义样式 */
.demo-info {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.demo-info h3 {
  margin: 0 0 8px 0;
  color: #409eff;
}

.demo-info p {
  margin: 4px 0;
  color: #606266;
}
</style> 