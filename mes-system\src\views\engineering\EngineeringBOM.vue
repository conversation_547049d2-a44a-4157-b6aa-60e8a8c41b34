<template>
  <el-card class="engineering-bom">
    <div class="header-bar">
      <div class="title">
        <el-icon><Setting /></el-icon>
        <span>研发BOM管理</span>
      </div>
      <div class="actions">
        <el-button type="primary" size="small" @click="refreshData">刷新数据</el-button>
        <el-button type="success" size="small" @click="exportBOM">导出BOM</el-button>
      </div>
    </div>
    <div class="layout">
      <!-- 左侧型号列表 -->
      <div class="left-panel">
        <div class="search-container">
        <el-input
          v-model="searchModel"
          placeholder="请输入型号进行搜索"
          clearable
          @clear="handleSearchClear"
          @input="handleSearchInput"
          size="small"
          prefix-icon="Search"
          class="search-input"
        />
          <div class="model-type-filter">
            <el-radio-group v-model="modelTypeFilter" size="small">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="product">成品</el-radio-button>
              <el-radio-button label="component">组件</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <el-scrollbar class="model-list">
          <div v-if="modelsLoading" class="loading-container">
            <el-skeleton :rows="3" animated />
            <div class="loading-text">加载型号列表中...</div>
          </div>
          <el-empty v-else-if="filteredModels.length === 0" description="暂无型号数据" />
          <div v-else class="model-item-container">
            <div
              v-for="model in filteredModels"
              :key="model.id"
              :class="['model-item', { active: model.id === selectedModel?.id }]"
              @click="selectModel(model)"
            >
              <div class="model-info">
                <div class="model-name">{{ model.name }}</div>
                <div class="model-details">
                  <span class="model-code">{{ model.code }}</span>
                  <el-tag size="small" :type="model.type === '成品' ? tagTypes.primary : tagTypes.success" class="model-type-tag">
                    {{ model.type }}
                  </el-tag>
                </div>
              </div>
              <div class="model-status" :class="model.status === '启用' ? 'enabled' : 'disabled'">
                {{ model.status }}
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>

      <!-- 右侧标签页 -->
      <div class="right-panel">
        <el-tabs v-model="activeTab" type="card" stretch>
          <!-- BOM数据标签页 -->
          <el-tab-pane name="bom">
            <template #label>
              <span class="tab-label">
                <el-icon><Document /></el-icon>
                <span>BOM数据</span>
              </span>
            </template>
            <div class="tab-content">
              <div v-if="!selectedModel" class="no-selection">
                <el-empty description="请先选择一个型号查看BOM数据">
                  <template #image>
                    <el-icon size="60"><Box /></el-icon>
                  </template>
                </el-empty>
              </div>
              <div v-else class="bom-content">
                <!-- BOM工具栏 -->
                <div class="bom-toolbar">
                  <div class="toolbar-left">
                    <el-button type="primary" size="small" @click="handleAddBomItem">
                      <el-icon><Plus /></el-icon>
                      新增BOM项
                    </el-button>
                    <el-button type="success" size="small" @click="handleImportBom">
                      <el-icon><Upload /></el-icon>
                      导入BOM
                    </el-button>
                    <el-button type="warning" size="small" @click="handleExportBom">
                      <el-icon><Download /></el-icon>
                      导出BOM
                    </el-button>
                    <el-button size="small" @click="handleExpandAll">
                      <el-icon><Expand /></el-icon>
                      {{ expandAll ? '收起全部' : '展开全部' }}
                    </el-button>
                    <el-button type="info" size="small" @click="handleReorderLineNumbers">
                      <el-icon><Sort /></el-icon>
                      重新排序
                    </el-button>
                  </div>
                  <div class="toolbar-middle">
                    <el-tag :type="tagTypes.info" class="version-tag">
                      当前版本号：{{ currentVersion }}
                    </el-tag>
                  </div>
                  <div class="toolbar-right">
                    <el-button 
                      type="primary" 
                      size="small" 
                      plain
                      class="toggle-operation-btn" 
                      @click="toggleOperationColumn"
                    >
                      <el-icon>
                        <View v-if="showOperationColumn" />
                        <Hide v-else />
                      </el-icon>
                      {{ showOperationColumn ? '隐藏操作列' : '显示操作列' }}
                    </el-button>
                    <el-input
                      v-model="bomSearchText"
                      placeholder="搜索BOM项目"
                      size="small"
                      prefix-icon="Search"
                      clearable
                      style="width: 200px; margin-left: 10px;"
                    />
                  </div>
                </div>

                <!-- BOM树形表格 -->
                <el-table
                  ref="bomTableRef"
                  :data="filteredBomData"
                  row-key="id"
                  :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
                  :default-expand-all="expandAll"
                  v-loading="bomLoading"
                  border
                  stripe
                  class="bom-table"
                  height="400px"
                >
                  <el-table-column label="行号" width="100" align="center" fixed="left">
                    <template #default="{ row }">
                      <el-input-number 
                        v-model="row.lineNumber" 
                        :min="1" 
                        :controls="false"
                        size="small"
                        @change="handleLineNumberChange(row)"
                        style="width: 70px"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="materialCode" label="物料编码" width="120" fixed="left">
                    <template #default="{ row }">
                      <span class="material-code">{{ row.materialCode }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="materialName" label="物料名称" min-width="150">
                    <template #default="{ row }">
                      <div class="material-info">
                        <span class="material-name">{{ row.materialName }}</span>
                        <el-tag v-if="row.isAlternative" size="small" type="warning">替代料</el-tag>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="specification" label="规格型号" min-width="120" show-overflow-tooltip />
                  <el-table-column prop="quantity" label="用量" width="80" align="right">
                    <template #default="{ row }">
                      <span class="quantity">{{ row.quantity }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="unit" label="单位" width="60" align="center" />
                  <el-table-column prop="lossRate" label="损耗率(%)" width="90" align="right">
                    <template #default="{ row }">
                      <span class="loss-rate">{{ row.lossRate }}%</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="unitCost" label="单价(元)" width="90" align="right">
                    <template #default="{ row }">
                      <span class="unit-cost">¥{{ row.unitCost }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="totalCost" label="总成本(元)" width="100" align="right">
                    <template #default="{ row }">
                      <span class="total-cost">¥{{ row.totalCost }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="supplier" label="供应商" min-width="120" show-overflow-tooltip />
                  <el-table-column prop="effectiveDate" label="生效日期" width="100" align="center">
                    <template #default="{ row }">
                      <span class="effective-date">{{ row.effectiveDate }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="80" align="center">
                    <template #default="{ row }">
                      <el-tag :type="row.status === '启用' ? 'success' : 'danger'" size="small">
                        {{ row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column v-if="showOperationColumn" label="操作" width="240" align="center" fixed="right">
                    <template #default="{ row }">
                      <el-button type="primary" link size="small" @click="handleEditBomItem(row)">
                        编辑
                      </el-button>
                      <el-button type="success" link size="small" @click="handleAddChildBomItem(row)">
                        添加子项
                      </el-button>
                      <el-button type="danger" link size="small" @click="handleDeleteBomItem(row)">
                        删除
                      </el-button>
                      <el-button type="info" link size="small" @click="handleMoveUp(row)">
                        <el-icon><ArrowUp /></el-icon>
                      </el-button>
                      <el-button type="info" link size="small" @click="handleMoveDown(row)">
                        <el-icon><ArrowDown /></el-icon>
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- BOM统计信息 -->
                <div class="bom-summary">
                  <el-row :gutter="20">
                    <el-col :span="6">
                      <el-statistic title="BOM项目总数" :value="bomStatistics.totalItems" />
                    </el-col>
                    <el-col :span="6">
                      <el-statistic title="总成本" :value="bomStatistics.totalCost" prefix="¥" :precision="2" />
                    </el-col>
                    <el-col :span="6">
                      <el-statistic title="物料种类" :value="bomStatistics.materialTypes" />
                    </el-col>
                    <el-col :span="6">
                      <el-statistic title="最后更新" :value="bomStatistics.lastUpdate" />
                    </el-col>
                  </el-row>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 设备参数标签页 -->
          <el-tab-pane name="device">
            <template #label>
              <span class="tab-label">
                <el-icon><Setting /></el-icon>
                <span>设备参数</span>
              </span>
            </template>
            <div class="tab-content">
              <div v-if="!selectedModel" class="no-selection">
                <el-empty description="请先选择一个型号查看设备参数">
                  <template #image>
                    <el-icon size="60"><Setting /></el-icon>
                  </template>
                </el-empty>
              </div>
              <div v-else class="device-content">
                <!-- 设备参数工具栏 -->
                <div class="device-toolbar">
                  <div class="toolbar-left">
                    <el-button type="primary" size="small" @click="handleAddDeviceParam">
                      <el-icon><Plus /></el-icon>
                      新增参数
                    </el-button>
                    <el-button type="success" size="small" @click="handleImportDeviceParams">
                      <el-icon><Upload /></el-icon>
                      导入参数
                    </el-button>
                    <el-button type="warning" size="small" @click="handleExportDeviceParams">
                      <el-icon><Download /></el-icon>
                      导出参数
                    </el-button>
                  </div>
                  <div class="toolbar-right">
                    <el-select v-model="deviceParamCategory" placeholder="参数分类" size="small" style="width: 120px; margin-right: 10px;">
                      <el-option label="全部" value="" />
                      <el-option label="技术参数" value="technical" />
                      <el-option label="质量参数" value="quality" />
                      <el-option label="维护参数" value="maintenance" />
                      <el-option label="工艺参数" value="process" />
                    </el-select>
                    <el-input
                      v-model="deviceSearchText"
                      placeholder="搜索设备参数"
                      size="small"
                      prefix-icon="Search"
                      clearable
                      style="width: 200px;"
                    />
                  </div>
                </div>

                <!-- 设备参数表格 -->
                <el-table
                  :data="filteredDeviceParams"
                  v-loading="deviceLoading"
                  border
                  stripe
                  class="device-table"
                  height="400px"
                >
                  <el-table-column prop="paramCode" label="参数编码" width="120" />
                  <el-table-column prop="paramName" label="参数名称" min-width="150" />
                  <el-table-column prop="category" label="参数分类" width="100" align="center">
                    <template #default="{ row }">
                      <el-tag :type="getParamCategoryType(row.category)" size="small">
                        {{ getParamCategoryLabel(row.category) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="paramValue" label="参数值" width="120" />
                  <el-table-column prop="unit" label="单位" width="80" align="center" />
                  <el-table-column prop="minValue" label="最小值" width="80" align="right" />
                  <el-table-column prop="maxValue" label="最大值" width="80" align="right" />
                  <el-table-column prop="standardValue" label="标准值" width="80" align="right" />
                  <el-table-column prop="tolerance" label="公差" width="80" align="center" />
                  <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
                  <el-table-column prop="status" label="状态" width="80" align="center">
                    <template #default="{ row }">
                      <el-tag :type="row.status === '启用' ? 'success' : 'danger'" size="small">
                        {{ row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150" align="center" fixed="right">
                    <template #default="{ row }">
                      <el-button type="primary" link size="small" @click="handleEditDeviceParam(row)">
                        编辑
                      </el-button>
                      <el-button type="danger" link size="small" @click="handleDeleteDeviceParam(row)">
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>

                      <!-- 设计图纸标签页 -->
          <el-tab-pane name="quality">
            <template #label>
              <span class="tab-label">
                <el-icon><Picture /></el-icon>
                <span>设计图纸</span>
              </span>
            </template>
            <div class="tab-content">
              <div v-if="!selectedModel" class="no-selection">
                <el-empty description="请先选择一个型号查看设计图纸">
                  <template #image>
                    <el-icon size="60"><Medal /></el-icon>
                  </template>
                </el-empty>
              </div>
              <div v-else class="drawing-content">
                <!-- 工具栏 -->
                <div class="drawing-toolbar">
                  <div class="toolbar-left">
                    <el-button type="primary" size="small" @click="handleUploadDrawing">
                      <el-icon><Plus /></el-icon>
                      上传图纸
                    </el-button>
                    <el-button type="success" size="small" @click="handleBatchImport">
                      <el-icon><Upload /></el-icon>
                      批量导入
                    </el-button>
                    <el-button type="warning" size="small" @click="handleExportDrawings">
                      <el-icon><Download /></el-icon>
                      导出
                    </el-button>
                    <el-radio-group v-model="drawingViewMode" size="small">
                      <el-radio-button label="table">
                        <el-icon><Operation /></el-icon>
                        列表视图
                      </el-radio-button>
                      <el-radio-button label="card">
                        <el-icon><Document /></el-icon>
                        卡片视图
                      </el-radio-button>
                    </el-radio-group>
                  </div>
                  <div class="toolbar-right">
                    <el-select 
                      v-model="selectedDrawingCategory" 
                      placeholder="图纸分类" 
                      size="small" 
                      clearable
                      style="width: 140px; margin-right: 10px;"
                    >
                      <el-option 
                        v-for="category in drawingCategories" 
                        :key="category.value" 
                        :label="category.label" 
                        :value="category.value" 
                      />
                    </el-select>
                    <el-input
                      v-model="drawingSearchText"
                      placeholder="搜索图纸"
                      size="small"
                      prefix-icon="Search"
                      clearable
                      style="width: 200px;"
                  />
                </div>
              </div>

                <!-- 表格视图 -->
                <el-table
                  v-if="drawingViewMode === 'table'"
                  :data="filteredDrawings"
                  v-loading="drawingLoading"
                  border
                  stripe
                  class="drawing-table"
                  height="400px"
                >
                  <el-table-column prop="fileName" label="文件名称" min-width="150" />
                  <el-table-column prop="fileType" label="文件类型" width="100" align="center" />
                  <el-table-column prop="category" label="图纸分类" width="120">
                    <template #default="{ row }">
                      <span>{{ getDrawingCategoryLabel(row.category) }}</span>
                  </template>
                  </el-table-column>
                  <el-table-column prop="version" label="版本" width="80" align="center" />
                  <el-table-column prop="uploadDate" label="上传日期" width="100" align="center" />
                  <el-table-column prop="fileSize" label="文件大小" width="100" align="center" />
                  <el-table-column prop="status" label="状态" width="100" align="center">
                    <template #default="{ row }">
                      <el-tag :type="getDrawingStatusInfo(row.status).type" size="small">
                        {{ getDrawingStatusInfo(row.status).label }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
                  <el-table-column label="操作" width="240" align="center" fixed="right">
                    <template #default="{ row }">
                      <el-button type="primary" link size="small" @click="handlePreviewDrawing(row)">
                        <el-icon><View /></el-icon>
                        预览
                      </el-button>
                      <el-button type="success" link size="small" @click="handleDownloadDrawing">
                        <el-icon><Download /></el-icon>
                        下载
                      </el-button>
                      <el-button type="warning" link size="small" @click="handleEditDrawing(row)">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-button>
                      <el-button type="danger" link size="small" @click="handleDeleteDrawing(row)">
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 卡片视图 -->
                <div v-else class="drawing-cards">
                  <el-empty v-if="filteredDrawings.length === 0" description="暂无图纸" />
                  <el-row v-else :gutter="20">
                    <el-col 
                      v-for="drawing in filteredDrawings"
                      :key="drawing.id"
                      :xs="24"
                      :sm="12"
                      :md="8"
                      :lg="6"
                      :xl="4"
                    >
                      <el-card class="drawing-card" shadow="hover">
                        <div class="drawing-card-header">
                          <div class="drawing-card-title">
                            <el-tooltip :content="drawing.fileName" placement="top">
                              <span>{{ drawing.fileName }}</span>
                            </el-tooltip>
              </div>
                          <el-tag size="small" :type="getDrawingStatusInfo(drawing.status).type">
                            {{ getDrawingStatusInfo(drawing.status).label }}
                          </el-tag>
                </div>
                        <div class="drawing-card-thumbnail">
                          <!-- 图纸缩略图 -->
                          <el-image 
                            :src="drawing.thumbnailUrl" 
                            fit="cover"
                            :preview-src-list="[drawing.url]"
                            alt="图纸缩略图"
                          >
                            <template #error>
                              <div class="drawing-thumbnail-placeholder">
                                <el-icon size="32"><Document /></el-icon>
                                <span>{{ drawing.fileType }}</span>
                              </div>
                            </template>
                          </el-image>
                        </div>
                        <div class="drawing-card-info">
                          <div class="info-item">
                            <span class="info-label">分类:</span>
                            <span class="info-value">{{ getDrawingCategoryLabel(drawing.category) }}</span>
                          </div>
                          <div class="info-item">
                            <span class="info-label">版本:</span>
                            <span class="info-value">{{ drawing.version }}</span>
                          </div>
                          <div class="info-item">
                            <span class="info-label">上传:</span>
                            <span class="info-value">{{ drawing.uploadDate }}</span>
                          </div>
                        </div>
                        <div class="drawing-card-actions">
                          <el-button size="small" type="primary" circle @click="handlePreviewDrawing(drawing)">
                            <el-icon><View /></el-icon>
                          </el-button>
                          <el-button size="small" type="success" circle @click="handleDownloadDrawing">
                            <el-icon><Download /></el-icon>
                          </el-button>
                          <el-button size="small" type="warning" circle @click="handleEditDrawing(drawing)">
                            <el-icon><Edit /></el-icon>
                          </el-button>
                          <el-button size="small" type="danger" circle @click="handleDeleteDrawing(drawing)">
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </div>
                      </el-card>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 成本分析标签页 -->
          <el-tab-pane name="cost">
            <template #label>
              <span class="tab-label">
                <el-icon><TrendCharts /></el-icon>
                <span>成本分析</span>
              </span>
            </template>
            <div class="tab-content">
              <div v-if="!selectedModel" class="no-selection">
                <el-empty description="请先选择一个型号查看成本分析">
                  <template #image>
                    <el-icon size="60"><TrendCharts /></el-icon>
                  </template>
                </el-empty>
              </div>
              <div v-else class="cost-content">
                <!-- 成本概览 -->
                <el-row :gutter="20" class="cost-overview">
                  <el-col :span="6">
                    <el-card class="cost-card" shadow="hover">
                      <template #header>
                        <div class="cost-card-header">
                          <span>总成本</span>
                          <el-tag :type="tagTypes.danger" size="small">元</el-tag>
                        </div>
                      </template>
                      <div class="cost-card-content">
                        <el-statistic :value="bomStatistics.totalCost" :precision="2" />
                        <div class="cost-trend">
                          <span>较上期</span>
                          <el-tag :type="tagTypes.success" size="small">-2.5%</el-tag>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="6">
                    <el-card class="cost-card" shadow="hover">
                      <template #header>
                        <div class="cost-card-header">
                          <span>物料成本</span>
                          <el-tag :type="tagTypes.warning" size="small">元</el-tag>
                        </div>
                      </template>
                      <div class="cost-card-content">
                        <el-statistic :value="costAnalysis.materialCost" :precision="2" />
                        <div class="cost-percentage">
                          <el-progress :percentage="costAnalysis.materialCostPercentage" :color="'#F56C6C'" />
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="6">
                    <el-card class="cost-card" shadow="hover">
                      <template #header>
                        <div class="cost-card-header">
                          <span>人工成本</span>
                          <el-tag :type="tagTypes.primary" size="small">元</el-tag>
                        </div>
                      </template>
                      <div class="cost-card-content">
                        <el-statistic :value="costAnalysis.laborCost" :precision="2" />
                        <div class="cost-percentage">
                          <el-progress :percentage="costAnalysis.laborCostPercentage" :color="'#409EFF'" />
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="6">
                    <el-card class="cost-card" shadow="hover">
                      <template #header>
                        <div class="cost-card-header">
                          <span>制造费用</span>
                          <el-tag :type="tagTypes.info" size="small">元</el-tag>
                        </div>
                      </template>
                      <div class="cost-card-content">
                        <el-statistic :value="costAnalysis.manufacturingCost" :precision="2" />
                        <div class="cost-percentage">
                          <el-progress :percentage="costAnalysis.manufacturingCostPercentage" :color="'#909399'" />
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>

                <!-- 成本分析图表区域 -->
                <el-row :gutter="20" class="cost-charts">
                  <el-col :span="12">
                    <el-card class="chart-card" shadow="hover">
                      <template #header>
                        <div class="chart-card-header">
                          <span>成本构成比例</span>
                          <el-select v-model="costChartTimeRange" size="small" style="width: 120px;">
                            <el-option label="最近一月" value="month" />
                            <el-option label="最近一季" value="quarter" />
                            <el-option label="最近一年" value="year" />
                          </el-select>
                        </div>
                      </template>
                      <div class="chart-placeholder pie-chart">
                        <div class="chart-text">
                          <el-icon size="40"><TrendCharts /></el-icon>
                          <p>成本构成饼图</p>
                          <p class="chart-desc">在实际应用中，这里将集成饼图组件</p>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="12">
                    <el-card class="chart-card" shadow="hover">
                      <template #header>
                        <div class="chart-card-header">
                          <span>成本趋势变化</span>
                          <el-radio-group v-model="costTrendType" size="small">
                            <el-radio-button label="total">总成本</el-radio-button>
                            <el-radio-button label="material">物料成本</el-radio-button>
                          </el-radio-group>
                        </div>
                      </template>
                      <div class="chart-placeholder line-chart">
                        <div class="chart-text">
                          <el-icon size="40"><TrendCharts /></el-icon>
                          <p>成本趋势折线图</p>
                          <p class="chart-desc">在实际应用中，这里将集成折线图组件</p>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>

                <!-- 成本明细表格 -->
                <el-card class="cost-detail-card" shadow="hover">
                  <template #header>
                    <div class="cost-detail-header">
                      <span class="cost-detail-title">成本明细</span>
                      <div class="cost-detail-actions">
                        <el-select v-model="costDetailFilter" size="small" style="width: 150px; margin-right: 10px;">
                          <el-option label="所有类型" value="" />
                          <el-option label="核心部件" value="core" />
                          <el-option label="外壳组件" value="case" />
                          <el-option label="电子元件" value="electronic" />
                          <el-option label="辅助部件" value="auxiliary" />
                        </el-select>
                        <el-input
                          v-model="costDetailSearchText"
                          placeholder="搜索物料"
                          size="small"
                          prefix-icon="Search"
                          clearable
                          style="width: 200px;"
                  />
                </div>
                    </div>
                  </template>
                  <el-table
                    :data="filteredCostDetails"
                    border
                    stripe
                    style="width: 100%"
                    height="350px"
                  >
                    <el-table-column prop="materialCode" label="物料编码" width="120" />
                    <el-table-column prop="materialName" label="物料名称" width="150" />
                    <el-table-column prop="category" label="物料分类" width="120">
                      <template #default="{ row }">
                        <el-tag :type="getMaterialCategoryType(row.category)" size="small">
                          {{ getMaterialCategoryLabel(row.category) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="specification" label="规格型号" width="120" />
                    <el-table-column prop="quantity" label="用量" width="80" align="right">
                      <template #default="{ row }">
                        <span>{{ row.quantity }}</span>
                        <span class="unit-text">{{ row.unit }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="unitCost" label="单价(元)" width="100" align="right">
                      <template #default="{ row }">
                        <span>{{ row.unitCost.toFixed(2) }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="lossRate" label="损耗率(%)" width="100" align="right">
                      <template #default="{ row }">
                        <span>{{ row.lossRate.toFixed(2) }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="totalCost" label="总成本(元)" width="120" align="right">
                      <template #default="{ row }">
                        <span class="total-cost">{{ row.totalCost.toFixed(2) }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="costPercentage" label="成本占比" width="120" align="center">
                      <template #default="{ row }">
                        <el-progress :percentage="row.costPercentage" :color="getCostPercentageColor(row.costPercentage)" />
                      </template>
                    </el-table-column>
                    <el-table-column prop="supplier" label="供应商" min-width="150" />
                    <el-table-column label="优化建议" width="120" align="center" fixed="right">
                      <template #default="{ row }">
                        <el-tooltip :content="getCostOptimizationTip(row)" placement="left">
                          <el-button v-if="hasCostOptimization(row)" :type="tagTypes.warning" link size="small">
                            <el-icon><Operation /></el-icon> 查看建议
                          </el-button>
                          <span v-else class="no-optimization">无建议</span>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-card>

                <!-- 成本优化分析 -->
                <el-row :gutter="20" class="cost-optimization">
                  <el-col :span="12">
                    <el-card class="optimization-card" shadow="hover">
                      <template #header>
                        <div class="optimization-header">
                          <span>成本优化机会</span>
                        </div>
                      </template>
                      <el-empty v-if="costOptimizationList.length === 0" description="暂无优化建议" />
                      <div v-else class="optimization-list">
                        <div v-for="(item, index) in costOptimizationList" :key="index" class="optimization-item">
                          <div class="optimization-icon">
                            <el-icon :size="24" :color="getOptimizationIconColor(item.type)">
                              <Box v-if="getOptimizationIcon(item.type) === 'Box'" />
                              <Operation v-else-if="getOptimizationIcon(item.type) === 'Operation'" />
                              <Setting v-else-if="getOptimizationIcon(item.type) === 'Setting'" />
                              <InfoFilled v-else />
                            </el-icon>
                          </div>
                          <div class="optimization-content">
                            <div class="optimization-title">{{ item.title }}</div>
                            <div class="optimization-desc">{{ item.description }}</div>
                            <div class="optimization-benefit">
                              预计节省: <span class="benefit-amount">{{ item.savingAmount.toFixed(2) }}</span> 元
                              (<span class="benefit-percentage">{{ item.savingPercentage.toFixed(1) }}%</span>)
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="12">
                    <el-card class="benchmark-card" shadow="hover">
                      <template #header>
                        <div class="benchmark-header">
                          <span>成本对标分析</span>
                          <el-select v-model="benchmarkTarget" size="small" style="width: 180px;">
                            <el-option label="同类竞品平均水平" value="average" />
                            <el-option label="行业最优水平" value="best" />
                            <el-option label="上一版本" value="previous" />
                          </el-select>
                        </div>
                      </template>
                      <div class="chart-placeholder bar-chart">
                        <div class="chart-text">
                          <el-icon size="40"><TrendCharts /></el-icon>
                          <p>成本对标条形图</p>
                          <p class="chart-desc">在实际应用中，这里将集成条形图组件</p>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- BOM项目编辑对话框 -->
    <el-dialog
      v-model="bomDialogVisible"
      :title="bomDialogType === 'add' ? '新增BOM项目' : '编辑BOM项目'"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="bomFormRef"
        :model="bomForm"
        :rules="bomFormRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物料编码" prop="materialCode">
              <el-input v-model="bomForm.materialCode" placeholder="请输入物料编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料名称" prop="materialName">
              <el-input v-model="bomForm.materialName" placeholder="请输入物料名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="规格型号" prop="specification">
              <el-input v-model="bomForm.specification" placeholder="请输入规格型号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplier">
              <el-input v-model="bomForm.supplier" placeholder="请输入供应商" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="用量" prop="quantity">
              <el-input-number v-model="bomForm.quantity" :min="0" :precision="4" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单位" prop="unit">
              <el-select v-model="bomForm.unit" placeholder="请选择单位" style="width: 100%">
                <el-option label="个" value="个" />
                <el-option label="套" value="套" />
                <el-option label="件" value="件" />
                <el-option label="kg" value="kg" />
                <el-option label="g" value="g" />
                <el-option label="m" value="m" />
                <el-option label="cm" value="cm" />
                <el-option label="mm" value="mm" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="损耗率(%)" prop="lossRate">
              <el-input-number v-model="bomForm.lossRate" :min="0" :max="100" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="单价(元)" prop="unitCost">
              <el-input-number v-model="bomForm.unitCost" :min="0" :precision="4" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效日期" prop="effectiveDate">
              <el-date-picker
                v-model="bomForm.effectiveDate"
                type="date"
                placeholder="请选择生效日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否替代料">
              <el-switch v-model="bomForm.isAlternative" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="bomForm.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="启用" value="启用" />
                <el-option label="停用" value="停用" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input v-model="bomForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="bomDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveBomItem" :loading="bomSaving">
            {{ bomSaving ? '保存中...' : '确定' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 图纸预览对话框 -->
    <el-dialog
      v-model="drawingPreviewVisible"
      title="图纸预览"
      width="80%"
      :close-on-click-modal="false"
      class="drawing-preview-dialog"
    >
      <div v-if="currentDrawing" class="drawing-preview-content">
        <div class="drawing-info-header">
          <h3>{{ currentDrawing.fileName }}</h3>
          <div class="drawing-info-tags">
            <el-tag size="small">{{ currentDrawing.fileType }}</el-tag>
            <el-tag size="small">{{ getDrawingCategoryLabel(currentDrawing.category) }}</el-tag>
            <el-tag :type="getDrawingStatusInfo(currentDrawing.status).type" size="small">
              {{ getDrawingStatusInfo(currentDrawing.status).label }}
            </el-tag>
            <el-tag type="success" size="small">{{ currentDrawing.version }}</el-tag>
          </div>
        </div>

        <div class="drawing-preview-main">
          <!-- 对于PDF文件展示PDF预览器 -->
          <div v-if="currentDrawing.fileType === 'PDF'" class="pdf-preview">
            <div class="pdf-placeholder">
              <el-icon size="60"><Document /></el-icon>
              <div class="pdf-placeholder-text">PDF预览区域</div>
              <div class="pdf-placeholder-subtext">在实际应用中，这里将集成PDF.js或其他PDF预览组件</div>
            </div>
          </div>
          
          <!-- 对于CAD文件，展示专用CAD预览器 -->
          <div v-else-if="['DWG', 'DXF'].includes(currentDrawing.fileType)" class="cad-preview">
            <div class="cad-placeholder">
              <el-icon size="60"><Box /></el-icon>
              <div class="cad-placeholder-text">CAD预览区域</div>
              <div class="cad-placeholder-subtext">在实际应用中，这里将集成CAD预览组件</div>
            </div>
          </div>
          
          <!-- 对于3D模型，展示3D预览器 -->
          <div v-else-if="['STEP', 'STL', 'IGES'].includes(currentDrawing.fileType)" class="model-preview">
            <div class="model-placeholder">
              <el-icon size="60"><Box /></el-icon>
              <div class="model-placeholder-text">3D模型预览区域</div>
              <div class="model-placeholder-subtext">在实际应用中，这里将集成Three.js或其他3D预览组件</div>
            </div>
          </div>
          
          <!-- 对于其他类型文件，显示通用预览 -->
          <div v-else class="generic-preview">
            <div class="generic-placeholder">
              <el-icon size="60"><Document /></el-icon>
              <div class="generic-placeholder-text">{{ currentDrawing.fileType }}文件预览</div>
              <div class="generic-placeholder-subtext">请下载后查看详情</div>
            </div>
          </div>
        </div>
        
        <div class="drawing-info-footer">
          <div class="drawing-meta">
            <div class="meta-item">
              <div class="meta-label">上传日期:</div>
              <div class="meta-value">{{ currentDrawing.uploadDate }}</div>
            </div>
            <div class="meta-item">
              <div class="meta-label">上传人员:</div>
              <div class="meta-value">{{ currentDrawing.uploadBy }}</div>
            </div>
            <div class="meta-item">
              <div class="meta-label">文件大小:</div>
              <div class="meta-value">{{ currentDrawing.fileSize }}</div>
            </div>
          </div>
          
          <div class="drawing-description">
            <div class="description-label">描述:</div>
            <div class="description-content">{{ currentDrawing.description }}</div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="drawingPreviewVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleDownloadDrawing">
            <el-icon><Download /></el-icon>
            下载图纸
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 图纸表单对话框 -->
    <el-dialog
      v-model="drawingFormVisible"
      :title="drawingDialogType === 'add' ? '上传新图纸' : '编辑图纸'"
      width="700px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="drawingFormRef"
        :model="drawingForm"
        :rules="drawingFormRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="文件名称" prop="fileName">
              <el-input v-model="drawingForm.fileName" placeholder="请输入文件名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文件类型" prop="fileType">
              <el-select v-model="drawingForm.fileType" placeholder="请选择文件类型" style="width: 100%">
                <el-option label="PDF" value="PDF" />
                <el-option label="DWG" value="DWG" />
                <el-option label="DXF" value="DXF" />
                <el-option label="STEP" value="STEP" />
                <el-option label="STL" value="STL" />
                <el-option label="IGES" value="IGES" />
                <el-option label="Gerber" value="Gerber" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="图纸分类" prop="category">
              <el-select v-model="drawingForm.category" placeholder="请选择图纸分类" style="width: 100%">
                <el-option 
                  v-for="category in drawingCategories" 
                  :key="category.value" 
                  :label="category.label" 
                  :value="category.value" 
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="版本号" prop="version">
              <el-input v-model="drawingForm.version" placeholder="请输入版本号，如v1.0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="上传日期">
              <el-date-picker
                v-model="drawingForm.uploadDate"
                type="date"
                placeholder="选择日期"
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上传人员">
              <el-input v-model="drawingForm.uploadBy" placeholder="上传人员" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="文件大小">
              <el-input v-model="drawingForm.fileSize" placeholder="如: 1.2MB" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-select v-model="drawingForm.status" placeholder="请选择状态" style="width: 100%">
                <el-option 
                  v-for="option in drawingStatusOptions" 
                  :key="option.value" 
                  :label="option.label" 
                  :value="option.value" 
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="文件上传" v-if="drawingDialogType === 'add'">
          <el-upload
            action="#"
            :auto-upload="false"
            :limit="1"
            accept=".pdf,.dwg,.dxf,.step,.stl,.iges,.zip"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                请选择图纸文件，支持PDF、DWG、DXF、STEP、STL、IGES等格式
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="drawingForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入图纸描述信息" 
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="drawingFormVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveDrawing" :loading="drawingSaving">
            {{ drawingSaving ? '保存中...' : '确定' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 设备参数编辑对话框 -->
    <el-dialog
      v-model="deviceDialogVisible"
      :title="deviceDialogType === 'add' ? '新增设备参数' : '编辑设备参数'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="deviceFormRef"
        :model="deviceForm"
        :rules="deviceFormRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="参数编码" prop="paramCode">
              <el-input v-model="deviceForm.paramCode" placeholder="请输入参数编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="参数名称" prop="paramName">
              <el-input v-model="deviceForm.paramName" placeholder="请输入参数名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="参数分类" prop="category">
              <el-select v-model="deviceForm.category" placeholder="请选择参数分类" style="width: 100%">
                <el-option label="技术参数" value="technical" />
                <el-option label="质量参数" value="quality" />
                <el-option label="维护参数" value="maintenance" />
                <el-option label="工艺参数" value="process" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-input v-model="deviceForm.unit" placeholder="请输入单位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="参数值" prop="paramValue">
              <el-input v-model="deviceForm.paramValue" placeholder="请输入参数值" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标准值" prop="standardValue">
              <el-input v-model="deviceForm.standardValue" placeholder="请输入标准值" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="最小值" prop="minValue">
              <el-input v-model="deviceForm.minValue" placeholder="最小值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大值" prop="maxValue">
              <el-input v-model="deviceForm.maxValue" placeholder="最大值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="公差" prop="tolerance">
              <el-input v-model="deviceForm.tolerance" placeholder="公差" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="deviceForm.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="启用" value="启用" />
                <el-option label="停用" value="停用" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述">
          <el-input v-model="deviceForm.description" type="textarea" :rows="3" placeholder="请输入参数描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deviceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveDeviceParam" :loading="deviceSaving">
            {{ deviceSaving ? '保存中...' : '确定' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
  Setting, Plus, Upload, Download, Search, Refresh, Expand, 
  Box, Medal, TrendCharts, Operation, Document, Picture,
  ArrowUp, ArrowDown, Sort, Hide, View, Edit, Delete, InfoFilled
} from '@element-plus/icons-vue'
import { productModelApiNew, componentModelApiNew } from '../../api/model.js'
import { 
  ElMessage, 
  ElMessageBox, 
  ElTag,
  ElCard,
  ElIcon,
  ElButton,
  ElInput,
  ElScrollbar,
  ElSkeleton,
  ElEmpty,
  ElTabs,
  ElTabPane,
  ElRadioGroup,
  ElRadioButton
} from 'element-plus'

interface Model {
  id: number
  name: string
  code: string
  status: string
  type: string  // 型号类型：成品/组件
  _raw?: any    // 原始数据，用于调试
}

interface BomItem {
  id: number
  materialCode: string
  materialName: string
  specification: string
  quantity: number
  unit: string
  lossRate: number
  unitCost: number
  totalCost: number
  supplier: string
  effectiveDate: string
  status: string
  isAlternative: boolean
  remark?: string
  children?: BomItem[]
  hasChildren?: boolean
  lineNumber?: number // 新增行号
  versionNumber?: number // 当前版本号
}

interface DeviceParam {
  id: number
  paramCode: string
  paramName: string
  category: string
  paramValue: string
  unit: string
  minValue: string
  maxValue: string
  standardValue: string
  tolerance: string
  description: string
  status: string
}

interface DrawingDocument {
  id: number
  fileName: string
  fileType: string  // 文件类型，如PDF、DWG、DXF等
  category: string  // 图纸分类，如电气原理图、PCB布局图等
  version: string   // 版本号，如v1.0
  uploadDate: string
  uploadBy: string
  fileSize: string
  description: string
  status: string    // 状态，如"正式"、"审核中"、"草稿"
  url: string       // 文件URL
  thumbnailUrl: string // 缩略图URL
}

// Element Plus类型常量
const tagTypes = {
  info: 'info' as const,
  success: 'success' as const,
  warning: 'warning' as const,
  danger: 'danger' as const,
  primary: 'primary' as const
}

// 基础数据
const searchModel = ref('')
const selectedModel = ref<Model | null>(null)
const activeTab = ref('bom')
const modelsLoading = ref(false)

// 型号数据
const models = ref<Model[]>([])

// 从物料主数据模块获取型号
const loadModels = async () => {
  console.error('🚀 ==> loadModels函数开始执行')
  modelsLoading.value = true
  
  try {
    console.error('🔄 开始加载真实数据...')
    
    // 并行获取成品和组件型号
    const [productRes, componentRes] = await Promise.all([
      productModelApiNew.getList({ status: '启用' }),
      componentModelApiNew.getList({ status: '启用' })
    ])
    
    console.error('📦 成品型号API响应:', productRes)
    console.error('📦 组件型号API响应:', componentRes)
    
    let allModels = []
    
    // 处理成品数据
    if (productRes?.data?.list && Array.isArray(productRes.data.list)) {
      const productModels = productRes.data.list.map(item => ({
        id: item.id,
        name: item.name || '未知成品',
        code: item.code || '未知编码', 
        status: item.status || '启用',
        type: '成品'
      }))
      allModels.push(...productModels)
      console.error('✅ 成品型号处理完成:', productModels.length, '个')
    }
    
    // 处理组件数据  
    if (componentRes?.data?.list && Array.isArray(componentRes.data.list)) {
      const componentModels = componentRes.data.list.map(item => ({
        id: item.id + 10000, // 避免ID冲突，加大偏移量
        name: item.name || '未知组件',
        code: item.code || '未知编码',
        status: item.status || '启用', 
        type: '组件'
      }))
      allModels.push(...componentModels)
      console.error('✅ 组件型号处理完成:', componentModels.length, '个')
    }
    
    if (allModels.length > 0) {
      models.value = allModels
      console.error('🎉 数据加载完成:', allModels.length, '个型号')
      ElMessage.success(`成功加载 ${allModels.length} 个型号`)
    } else {
      console.error('❌ 未获取到数据，使用备用数据')
      // 如果API没有数据，使用少量测试数据
      models.value = [
        { id: 1, name: '示例成品型号', code: 'DEMO001', status: '启用', type: '成品' },
        { id: 2, name: '示例组件型号', code: 'DEMO002', status: '启用', type: '组件' }
      ]
      ElMessage.warning('未获取到API数据，显示示例数据')
    }
    
  } catch (error) {
    console.error('❌ 数据加载失败:', error)
    ElMessage.error('数据加载失败，显示示例数据')
    
    // 错误时使用备用数据
    models.value = [
      { id: 1, name: '备用成品型号', code: 'BACKUP001', status: '启用', type: '成品' },
      { id: 2, name: '备用组件型号', code: 'BACKUP002', status: '启用', type: '组件' }
    ]
  } finally {
    modelsLoading.value = false
  }
}

// BOM相关数据
const bomSearchText = ref('')
const bomLoading = ref(false)
const expandAll = ref(false)
const bomDialogVisible = ref(false)
const bomDialogType = ref<'add' | 'edit'>('add')
const bomSaving = ref(false)
const bomFormRef = ref()
const currentVersion = ref(1) // 当前BOM版本号
const showOperationColumn = ref(false) // 控制操作列显示/隐藏，默认隐藏

// 设计图纸相关数据
const drawingSearchText = ref('')
const drawingLoading = ref(false)
const drawingDialogVisible = ref(false)
const drawingDialogType = ref<'add' | 'edit' | 'view'>('add')
const drawingSaving = ref(false)
const drawingFormRef = ref()
const selectedDrawingCategory = ref('')
const drawingPreviewVisible = ref(false)
const currentDrawing = ref<DrawingDocument | null>(null)
const drawingFormVisible = ref(false)

// 成本分析相关数据
const costDetailSearchText = ref('')
const costDetailFilter = ref('')
const costChartTimeRange = ref('month')
const costTrendType = ref('total')
const benchmarkTarget = ref('average')
const costLoading = ref(false)
const drawingForm = ref<DrawingDocument>({
  id: 0,
  fileName: '',
  fileType: 'PDF',
  category: '',
  version: 'v1.0',
  uploadDate: '',
  uploadBy: '',
  fileSize: '',
  description: '',
  status: 'draft',
  url: '',
  thumbnailUrl: ''
})

// 图纸表单验证规则
const drawingFormRules = {
  fileName: [
    { required: true, message: '请输入文件名称', trigger: 'blur' },
    { min: 3, max: 50, message: '长度在3到50个字符', trigger: 'blur' }
  ],
  fileType: [
    { required: true, message: '请选择文件类型', trigger: 'change' }
  ],
  category: [
    { required: true, message: '请选择图纸分类', trigger: 'change' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ]
}

// 设计图纸分类
const drawingCategories = [
  { value: 'electrical-schematic', label: '电气原理图' },
  { value: 'panel-layout', label: '面板布置图' },
  { value: 'assembly-drawing', label: '装配图' },
  { value: 'wiring-diagram', label: '接线图' },
  { value: '3d-model', label: '3D模型' },
  { value: 'pcb-design', label: 'PCB设计图' },
  { value: 'structural-drawing', label: '结构图纸' },
  { value: 'spec-sheet', label: '技术规格书' },
  { value: 'test-report', label: '测试报告' }
]

// 图纸状态
const drawingStatusOptions = [
  { value: 'draft', label: '草稿', type: 'info' },
  { value: 'reviewing', label: '审核中', type: 'warning' },
  { value: 'approved', label: '已批准', type: 'success' },
  { value: 'obsolete', label: '已废弃', type: 'danger' }
]

// 图纸视图模式
const drawingViewMode = ref<'table' | 'card'>('table')

// 示例图纸数据
const drawingsData = ref<DrawingDocument[]>([
  {
    id: 1,
    fileName: 'MC001-原理图',
    fileType: 'PDF',
    category: 'electrical-schematic',
    version: 'v1.2',
    uploadDate: '2024-05-10',
    uploadBy: '张工',
    fileSize: '2.5MB',
    description: '断路器A型电气原理图，包含主回路和控制回路',
    status: 'approved',
    url: 'https://example.com/drawings/MC001-schematic.pdf',
    thumbnailUrl: 'https://example.com/thumbnails/MC001-schematic.jpg'
  },
  {
    id: 2,
    fileName: 'MC001-面板图',
    fileType: 'DWG',
    category: 'panel-layout',
    version: 'v1.1',
    uploadDate: '2024-05-08',
    uploadBy: '李工',
    fileSize: '3.8MB',
    description: '断路器A型面板布置图，标注了面板开孔和元器件安装位置',
    status: 'approved',
    url: 'https://example.com/drawings/MC001-panel.dwg',
    thumbnailUrl: 'https://example.com/thumbnails/MC001-panel.jpg'
  },
  {
    id: 3,
    fileName: 'MC001-PCB设计',
    fileType: 'Gerber',
    category: 'pcb-design',
    version: 'v2.0',
    uploadDate: '2024-05-15',
    uploadBy: '王工',
    fileSize: '4.2MB',
    description: '断路器A型控制板PCB设计文件，包含元件布局和走线',
    status: 'reviewing',
    url: 'https://example.com/drawings/MC001-pcb.zip',
    thumbnailUrl: 'https://example.com/thumbnails/MC001-pcb.jpg'
  },
  {
    id: 4,
    fileName: 'MC001-装配图',
    fileType: 'PDF',
    category: 'assembly-drawing',
    version: 'v1.0',
    uploadDate: '2024-05-05',
    uploadBy: '刘工',
    fileSize: '5.1MB',
    description: '断路器A型整体装配图，包含装配顺序和注意事项',
    status: 'approved',
    url: 'https://example.com/drawings/MC001-assembly.pdf',
    thumbnailUrl: 'https://example.com/thumbnails/MC001-assembly.jpg'
  },
  {
    id: 5,
    fileName: 'MC001-3D模型',
    fileType: 'STEP',
    category: '3d-model',
    version: 'v1.3',
    uploadDate: '2024-05-12',
    uploadBy: '赵工',
    fileSize: '12.8MB',
    description: '断路器A型3D模型文件，用于结构设计和干涉检查',
    status: 'approved',
    url: 'https://example.com/drawings/MC001-3d.step',
    thumbnailUrl: 'https://example.com/thumbnails/MC001-3d.jpg'
  },
  {
    id: 6,
    fileName: 'MC001-接线图',
    fileType: 'PDF',
    category: 'wiring-diagram',
    version: 'v1.1',
    uploadDate: '2024-05-09',
    uploadBy: '张工',
    fileSize: '1.8MB',
    description: '断路器A型接线图，标注了各端子连接关系',
    status: 'approved',
    url: 'https://example.com/drawings/MC001-wiring.pdf',
    thumbnailUrl: 'https://example.com/thumbnails/MC001-wiring.jpg'
  },
  {
    id: 7,
    fileName: 'MC001-技术规格书',
    fileType: 'PDF',
    category: 'spec-sheet',
    version: 'v2.1',
    uploadDate: '2024-05-18',
    uploadBy: '陈工',
    fileSize: '3.2MB',
    description: '断路器A型技术规格书，包含电气参数和安装说明',
    status: 'approved',
    url: 'https://example.com/drawings/MC001-specs.pdf',
    thumbnailUrl: 'https://example.com/thumbnails/MC001-specs.jpg'
  }
])

const bomData = ref<BomItem[]>([
  {
    id: 1,
    lineNumber: 1,
    materialCode: 'M001',
    materialName: '主控制板',
    specification: 'PCB-001',
    quantity: 1,
    unit: '块',
    lossRate: 2,
    unitCost: 150.00,
    totalCost: 153.00,
    supplier: '电子科技有限公司',
    effectiveDate: '2024-01-01',
    status: '启用',
    isAlternative: false,
    hasChildren: true,
    children: [
      {
        id: 11,
        lineNumber: 2,
        materialCode: 'M011',
        materialName: '电阻R1',
        specification: '1kΩ/0.25W',
        quantity: 10,
        unit: '个',
        lossRate: 1,
        unitCost: 0.05,
        totalCost: 0.505,
        supplier: '电子元件公司',
        effectiveDate: '2024-01-01',
        status: '启用',
        isAlternative: false
      },
      {
        id: 12,
        lineNumber: 3,
        materialCode: 'M012',
        materialName: '电容C1',
        specification: '10μF/25V',
        quantity: 5,
        unit: '个',
        lossRate: 1,
        unitCost: 0.10,
        totalCost: 0.505,
        supplier: '电子元件公司',
        effectiveDate: '2024-01-01',
        status: '启用',
        isAlternative: false
      }
    ]
  },
  {
    id: 2,
    lineNumber: 4,
    materialCode: 'M002',
    materialName: '次控制板',
    specification: 'PCB-002',
    quantity: 2,
    unit: '块',
    lossRate: 1,
    unitCost: 80.00,
    totalCost: 160.00,
    supplier: '电子科技有限公司',
    effectiveDate: '2024-02-01',
    status: '启用',
    isAlternative: true,
    hasChildren: true,
    children: [
      {
        id: 21,
        lineNumber: 5,
        materialCode: 'M021',
        materialName: '电阻R2',
        specification: '2kΩ/0.25W',
        quantity: 20,
        unit: '个',
        lossRate: 0.5,
        unitCost: 0.03,
        totalCost: 0.60,
        supplier: '电子元件公司',
        effectiveDate: '2024-02-01',
        status: '启用',
        isAlternative: false
      },
      {
        id: 22,
        lineNumber: 6,
        materialCode: 'M022',
        materialName: '电容C2',
        specification: '20μF/25V',
        quantity: 10,
        unit: '个',
        lossRate: 0.5,
        unitCost: 0.05,
        totalCost: 0.50,
        supplier: '电子元件公司',
        effectiveDate: '2024-02-01',
        status: '启用',
        isAlternative: false
      }
    ]
  },
  {
    id: 3,
    lineNumber: 7,
    materialCode: 'M003',
    materialName: '电源模块',
    specification: 'PS-001',
    quantity: 1,
    unit: '个',
    lossRate: 0.5,
    unitCost: 200.00,
    totalCost: 200.50,
    supplier: '电子科技有限公司',
    effectiveDate: '2024-03-01',
    status: '启用',
    isAlternative: false,
    hasChildren: false
  },
  {
    id: 4,
    lineNumber: 8,
    materialCode: 'M004',
    materialName: '散热器',
    specification: 'FAN-001',
    quantity: 2,
    unit: '个',
    lossRate: 1,
    unitCost: 50.00,
    totalCost: 100.00,
    supplier: '电子科技有限公司',
    effectiveDate: '2024-03-10',
    status: '启用',
    isAlternative: false,
    hasChildren: false
  },
  {
    id: 5,
    lineNumber: 9,
    materialCode: 'M005',
    materialName: '连接线',
    specification: 'CABLE-001',
    quantity: 5,
    unit: '米',
    lossRate: 0.5,
    unitCost: 1.00,
    totalCost: 5.00,
    supplier: '电子元件公司',
    effectiveDate: '2024-03-15',
    status: '启用',
    isAlternative: false,
    hasChildren: false
  },
  {
    id: 6,
    lineNumber: 10,
    materialCode: 'M006',
    materialName: '螺丝',
    specification: 'SCREW-001',
    quantity: 100,
    unit: '个',
    lossRate: 0.1,
    unitCost: 0.01,
    totalCost: 1.00,
    supplier: '五金工具公司',
    effectiveDate: '2024-03-20',
    status: '启用',
    isAlternative: false,
    hasChildren: false
  },
  {
    id: 7,
    lineNumber: 11,
    materialCode: 'M007',
    materialName: '导热硅脂',
    specification: 'THERMAL-001',
    quantity: 1,
    unit: '瓶',
    lossRate: 0.5,
    unitCost: 20.00,
    totalCost: 20.00,
    supplier: '电子元件公司',
    effectiveDate: '2024-03-25',
    status: '启用',
    isAlternative: false,
    hasChildren: false
  },
  {
    id: 8,
    lineNumber: 12,
    materialCode: 'M008',
    materialName: '绝缘胶带',
    specification: 'INSULATOR-001',
    quantity: 10,
    unit: '卷',
    lossRate: 0.2,
    unitCost: 5.00,
    totalCost: 50.00,
    supplier: '电子元件公司',
    effectiveDate: '2024-04-01',
    status: '启用',
    isAlternative: false,
    hasChildren: false
  },
  {
    id: 9,
    lineNumber: 13,
    materialCode: 'M009',
    materialName: '热缩管',
    specification: 'HEAT-SHRINK-001',
    quantity: 20,
    unit: '个',
    lossRate: 0.3,
    unitCost: 0.50,
    totalCost: 10.00,
    supplier: '电子元件公司',
    effectiveDate: '2024-04-05',
    status: '启用',
    isAlternative: false,
    hasChildren: false
  },
  {
    id: 10,
    lineNumber: 14,
    materialCode: 'M010',
    materialName: '焊锡丝',
    specification: 'SOLDER-WIRE-001',
    quantity: 100,
    unit: '卷',
    lossRate: 0.1,
    unitCost: 10.00,
    totalCost: 1000.00,
    supplier: '电子元件公司',
    effectiveDate: '2024-04-10',
    status: '启用',
    isAlternative: false,
    hasChildren: false
  },
  {
    id: 11,
    lineNumber: 15,
    materialCode: 'M011',
    materialName: '电阻R1',
    specification: '1kΩ/0.25W',
    quantity: 10,
    unit: '个',
    lossRate: 1,
    unitCost: 0.05,
    totalCost: 0.505,
    supplier: '电子元件公司',
    effectiveDate: '2024-01-01',
    status: '启用',
    isAlternative: false
  },
  {
    id: 12,
    lineNumber: 16,
    materialCode: 'M012',
    materialName: '电容C1',
    specification: '10μF/25V',
    quantity: 5,
    unit: '个',
    lossRate: 1,
    unitCost: 0.10,
    totalCost: 0.505,
    supplier: '电子元件公司',
    effectiveDate: '2024-01-01',
    status: '启用',
    isAlternative: false
  },
  {
    id: 13,
    lineNumber: 17,
    materialCode: 'M013',
    materialName: '显示屏',
    specification: 'DISPLAY-001',
    quantity: 1,
    unit: '个',
    lossRate: 0.5,
    unitCost: 350.00,
    totalCost: 351.75,
    supplier: '显示器科技有限公司',
    effectiveDate: '2024-04-15',
    status: '启用',
    isAlternative: false,
    hasChildren: true,
    children: [
      {
        id: 131,
        lineNumber: 18,
        materialCode: 'M0131',
        materialName: '液晶面板',
        specification: 'LCD-001',
        quantity: 1,
        unit: '片',
        lossRate: 0.8,
        unitCost: 280.00,
        totalCost: 282.24,
        supplier: '面板科技有限公司',
        effectiveDate: '2024-04-15',
        status: '启用',
        isAlternative: false
      },
      {
        id: 132,
        lineNumber: 19,
        materialCode: 'M0132',
        materialName: '触摸屏',
        specification: 'TOUCH-001',
        quantity: 1,
        unit: '片',
        lossRate: 1.0,
        unitCost: 70.00,
        totalCost: 70.70,
        supplier: '面板科技有限公司',
        effectiveDate: '2024-04-15',
        status: '启用',
        isAlternative: false
      }
    ]
  },
  {
    id: 14,
    lineNumber: 20,
    materialCode: 'M014',
    materialName: '电池模块',
    specification: 'BATTERY-001',
    quantity: 2,
    unit: '个',
    lossRate: 0.2,
    unitCost: 120.00,
    totalCost: 240.48,
    supplier: '电池科技有限公司',
    effectiveDate: '2024-04-20',
    status: '启用',
    isAlternative: false
  },
  {
    id: 15,
    lineNumber: 21,
    materialCode: 'M015',
    materialName: '外壳组件',
    specification: 'CASE-001',
    quantity: 1,
    unit: '套',
    lossRate: 0.3,
    unitCost: 80.00,
    totalCost: 80.24,
    supplier: '五金制造有限公司',
    effectiveDate: '2024-04-25',
    status: '启用',
    isAlternative: false,
    hasChildren: true,
    children: [
      {
        id: 151,
        lineNumber: 22,
        materialCode: 'M0151',
        materialName: '上盖',
        specification: 'TOP-001',
        quantity: 1,
        unit: '个',
        lossRate: 0.3,
        unitCost: 40.00,
        totalCost: 40.12,
        supplier: '五金制造有限公司',
        effectiveDate: '2024-04-25',
        status: '启用',
        isAlternative: false
      },
      {
        id: 152,
        lineNumber: 23,
        materialCode: 'M0152',
        materialName: '下壳',
        specification: 'BOTTOM-001',
        quantity: 1,
        unit: '个',
        lossRate: 0.3,
        unitCost: 40.00,
        totalCost: 40.12,
        supplier: '五金制造有限公司',
        effectiveDate: '2024-04-25',
        status: '启用',
        isAlternative: false
      }
    ]
  },
  {
    id: 16,
    lineNumber: 24,
    materialCode: 'M016',
    materialName: '接口模块',
    specification: 'INTERFACE-001',
    quantity: 3,
    unit: '个',
    lossRate: 0.5,
    unitCost: 25.00,
    totalCost: 75.38,
    supplier: '接口科技有限公司',
    effectiveDate: '2024-05-01',
    status: '启用',
    isAlternative: false
  },
  {
    id: 17,
    lineNumber: 25,
    materialCode: 'M017',
    materialName: '传感器套件',
    specification: 'SENSOR-KIT-001',
    quantity: 1,
    unit: '套',
    lossRate: 0.8,
    unitCost: 180.00,
    totalCost: 181.44,
    supplier: '传感科技有限公司',
    effectiveDate: '2024-05-05',
    status: '启用',
    isAlternative: false,
    hasChildren: true,
    children: [
      {
        id: 171,
        lineNumber: 26,
        materialCode: 'M0171',
        materialName: '温度传感器',
        specification: 'TEMP-001',
        quantity: 2,
        unit: '个',
        lossRate: 0.5,
        unitCost: 35.00,
        totalCost: 70.35,
        supplier: '传感科技有限公司',
        effectiveDate: '2024-05-05',
        status: '启用',
        isAlternative: false
      },
      {
        id: 172,
        lineNumber: 27,
        materialCode: 'M0172',
        materialName: '湿度传感器',
        specification: 'HUMID-001',
        quantity: 2,
        unit: '个',
        lossRate: 0.5,
        unitCost: 30.00,
        totalCost: 60.30,
        supplier: '传感科技有限公司',
        effectiveDate: '2024-05-05',
        status: '启用',
        isAlternative: false
      },
      {
        id: 173,
        lineNumber: 28,
        materialCode: 'M0173',
        materialName: '压力传感器',
        specification: 'PRESS-001',
        quantity: 1,
        unit: '个',
        lossRate: 0.5,
        unitCost: 50.00,
        totalCost: 50.25,
        supplier: '传感科技有限公司',
        effectiveDate: '2024-05-05',
        status: '启用',
        isAlternative: false
      }
    ]
  },
  {
    id: 18,
    lineNumber: 29,
    materialCode: 'M018',
    materialName: '包装材料',
    specification: 'PACK-001',
    quantity: 1,
    unit: '套',
    lossRate: 0.1,
    unitCost: 15.00,
    totalCost: 15.02,
    supplier: '包装材料有限公司',
    effectiveDate: '2024-05-10',
    status: '启用',
    isAlternative: false,
    hasChildren: true,
    children: [
      {
        id: 181,
        lineNumber: 30,
        materialCode: 'M0181',
        materialName: '纸箱',
        specification: 'BOX-001',
        quantity: 1,
        unit: '个',
        lossRate: 0.1,
        unitCost: 5.00,
        totalCost: 5.01,
        supplier: '包装材料有限公司',
        effectiveDate: '2024-05-10',
        status: '启用',
        isAlternative: false
      },
      {
        id: 182,
        lineNumber: 31,
        materialCode: 'M0182',
        materialName: '泡沫',
        specification: 'FOAM-001',
        quantity: 2,
        unit: '片',
        lossRate: 0.1,
        unitCost: 3.00,
        totalCost: 6.01,
        supplier: '包装材料有限公司',
        effectiveDate: '2024-05-10',
        status: '启用',
        isAlternative: false
      },
      {
        id: 183,
        lineNumber: 32,
        materialCode: 'M0183',
        materialName: '说明书',
        specification: 'MANUAL-001',
        quantity: 1,
        unit: '本',
        lossRate: 0,
        unitCost: 2.00,
        totalCost: 2.00,
        supplier: '印刷厂',
        effectiveDate: '2024-05-10',
        status: '启用',
        isAlternative: false
      },
      {
        id: 184,
        lineNumber: 33,
        materialCode: 'M0184',
        materialName: '保修卡',
        specification: 'WARRANTY-001',
        quantity: 1,
        unit: '张',
        lossRate: 0,
        unitCost: 0.50,
        totalCost: 0.50,
        supplier: '印刷厂',
        effectiveDate: '2024-05-10',
        status: '启用',
        isAlternative: false
      }
    ]
  },
  {
    id: 19,
    lineNumber: 34,
    materialCode: 'M019',
    materialName: '钢化玻璃',
    specification: 'GLASS-001',
    quantity: 1,
    unit: '片',
    lossRate: 1.5,
    unitCost: 18.00,
    totalCost: 18.27,
    supplier: '玻璃制品有限公司',
    effectiveDate: '2024-05-15',
    status: '启用',
    isAlternative: false
  },
  {
    id: 20,
    lineNumber: 35,
    materialCode: 'M020',
    materialName: '散热风扇',
    specification: 'FAN-002',
    quantity: 2,
    unit: '个',
    lossRate: 0.5,
    unitCost: 12.00,
    totalCost: 24.12,
    supplier: '电子科技有限公司',
    effectiveDate: '2024-05-20',
    status: '启用',
    isAlternative: false
  }
])

const bomForm = ref<BomItem>({
  id: 0,
  lineNumber: 0,
  materialCode: '',
  materialName: '',
  specification: '',
  quantity: 1,
  unit: '个',
  lossRate: 0,
  unitCost: 0,
  totalCost: 0,
  supplier: '',
  effectiveDate: '',
  status: '启用',
  isAlternative: false,
  remark: ''
})

const bomFormRules = {
  materialCode: [{ required: true, message: '请输入物料编码', trigger: 'blur' }],
  materialName: [{ required: true, message: '请输入物料名称', trigger: 'blur' }],
  quantity: [{ required: true, message: '请输入用量', trigger: 'blur' }],
  unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 设备参数相关数据
const deviceSearchText = ref('')
const deviceParamCategory = ref('')
const deviceLoading = ref(false)
const deviceDialogVisible = ref(false)
const deviceDialogType = ref<'add' | 'edit'>('add')
const deviceSaving = ref(false)
const deviceFormRef = ref()

const deviceParams = ref<DeviceParam[]>([
  {
    id: 1,
    paramCode: 'P001',
    paramName: '额定电压',
    category: 'technical',
    paramValue: '220',
    unit: 'V',
    minValue: '200',
    maxValue: '240',
    standardValue: '220',
    tolerance: '±10%',
    description: '设备正常运行时的额定电压',
    status: '启用'
  },
  {
    id: 2,
    paramCode: 'P002',
    paramName: '额定电流',
    category: 'technical',
    paramValue: '5',
    unit: 'A',
    minValue: '0',
    maxValue: '10',
    standardValue: '5',
    tolerance: '±0.5A',
    description: '设备正常运行时的额定电流',
    status: '启用'
  }
])

const deviceForm = ref<DeviceParam>({
  id: 0,
  paramCode: '',
  paramName: '',
  category: 'technical',
  paramValue: '',
  unit: '',
  minValue: '',
  maxValue: '',
  standardValue: '',
  tolerance: '',
  description: '',
  status: '启用'
})

const deviceFormRules = {
  paramCode: [{ required: true, message: '请输入参数编码', trigger: 'blur' }],
  paramName: [{ required: true, message: '请输入参数名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择参数分类', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 添加型号类型过滤
const modelTypeFilter = ref('all') // 'all', 'product' (成品), 'component' (组件)

// 计算属性
const filteredModels = computed(() => {
  let result = models.value
  
  // 按类型过滤
  if (modelTypeFilter.value === 'product') {
    result = result.filter(model => model.type === '成品')
  } else if (modelTypeFilter.value === 'component') {
    result = result.filter(model => model.type === '组件')
  }
  
  // 按搜索关键词过滤
  if (searchModel.value) {
    const search = searchModel.value.toLowerCase()
    result = result.filter(model => 
      model.name.toLowerCase().includes(search) || 
      model.code.toLowerCase().includes(search)
    )
  }
  
  return result
})

const filteredBomData = computed(() => {
  if (!bomSearchText.value) {
    return bomData.value
  }
  
  const search = bomSearchText.value.toLowerCase()
  return filterBomItems(bomData.value, search)
})

const bomStatistics = computed(() => {
  // 计算统计数据
  return {
    totalItems: countBomItems(bomData.value),
    totalCost: calculateTotalCost(bomData.value),
    materialTypes: countMaterialTypes(bomData.value),
    lastUpdate: '2024-05-15'
  }
})

// 成本分析数据计算
const costAnalysis = computed(() => {
  const totalCost = bomStatistics.value.totalCost
  // 按固定比例计算各项成本，实际应用中应从真实数据获取
  const materialCost = totalCost * 0.68
  const laborCost = totalCost * 0.22
  const manufacturingCost = totalCost * 0.10
  
  return {
    materialCost,
    materialCostPercentage: 68,
    laborCost,
    laborCostPercentage: 22,
    manufacturingCost,
    manufacturingCostPercentage: 10
  }
})

// 成本明细数据（基于BOM数据构建）
const costDetails = computed(() => {
  return bomData.value.flatMap(item => flattenBomItems(item)).map(item => {
    const totalBomCost = bomStatistics.value.totalCost
    const percentage = totalBomCost > 0 ? (item.totalCost / totalBomCost * 100) : 0
    
    // 按物料编码前缀分类
    let category = 'other'
    if (item.materialCode.startsWith('E')) {
      category = 'electronic'
    } else if (item.materialCode.startsWith('M')) {
      category = 'core'
    } else if (item.materialCode.startsWith('C')) {
      category = 'case'
    } else if (item.materialCode.startsWith('A')) {
      category = 'auxiliary'
    }
    
    return {
      ...item,
      category,
      costPercentage: Math.min(percentage, 100),
      hasOptimization: percentage > 5 || item.unitCost > 100 // 成本占比高或单价高的物料有优化空间
    }
  })
})

// 过滤后的成本明细
const filteredCostDetails = computed(() => {
  let result = costDetails.value
  
  // 按物料分类筛选
  if (costDetailFilter.value) {
    result = result.filter(item => item.category === costDetailFilter.value)
  }
  
  // 按关键字搜索
  if (costDetailSearchText.value) {
    const search = costDetailSearchText.value.toLowerCase()
    result = result.filter(item => 
      item.materialCode.toLowerCase().includes(search) ||
      item.materialName.toLowerCase().includes(search) ||
      item.specification.toLowerCase().includes(search) ||
      item.supplier.toLowerCase().includes(search)
    )
  }
  
  return result
})

// 成本优化建议列表
const costOptimizationList = computed(() => {
  const highCostItems = costDetails.value
    .filter(item => item.hasOptimization)
    .sort((a, b) => b.totalCost - a.totalCost)
    .slice(0, 3)
  
  return [
    ...highCostItems.map(item => ({
      type: 'material',
      title: `优化 ${item.materialName} 采购成本`,
      description: `建议寻找替代供应商或议价，当前单价${item.unitCost.toFixed(2)}元/件偏高`,
      savingAmount: item.totalCost * 0.15,
      savingPercentage: 15
    })),
    {
      type: 'process',
      title: '优化生产工艺流程',
      description: '通过精益生产方式减少制造环节损耗，提高生产效率',
      savingAmount: costAnalysis.value.manufacturingCost * 0.2,
      savingPercentage: 20
    },
    {
      type: 'design',
      title: '产品结构优化',
      description: '简化产品结构设计，减少零部件数量，降低装配复杂度',
      savingAmount: costAnalysis.value.laborCost * 0.1 + costAnalysis.value.materialCost * 0.05,
      savingPercentage: 7.5
    }
  ]
})

const filteredDeviceParams = computed(() => {
  if (!deviceSearchText.value && !deviceParamCategory.value) {
    return deviceParams.value
  }
  
  let result = deviceParams.value
  
  if (deviceParamCategory.value) {
    result = result.filter(param => param.category === deviceParamCategory.value)
  }
  
  if (deviceSearchText.value) {
    const search = deviceSearchText.value.toLowerCase()
    result = result.filter(param => 
      param.paramCode.toLowerCase().includes(search) || 
      param.paramName.toLowerCase().includes(search) ||
      param.description.toLowerCase().includes(search)
    )
  }
  
  return result
})

// 设计图纸相关计算属性
const filteredDrawings = computed(() => {
  let result = drawingsData.value
  
  // 按分类过滤
  if (selectedDrawingCategory.value) {
    result = result.filter(drawing => drawing.category === selectedDrawingCategory.value)
  }
  
  // 按搜索文本过滤
  if (drawingSearchText.value) {
    const search = drawingSearchText.value.toLowerCase()
    result = result.filter(drawing => 
      drawing.fileName.toLowerCase().includes(search) || 
      drawing.description.toLowerCase().includes(search) || 
      drawing.fileType.toLowerCase().includes(search) ||
      drawing.version.toLowerCase().includes(search)
    )
  }
  
  // 只显示当前选中型号的图纸
  if (selectedModel.value !== null && selectedModel.value.code) {
    const modelCode = selectedModel.value.code
    result = result.filter(drawing => 
      drawing.fileName.includes(modelCode)
    )
  }
  
  return result
})

// 获取图纸分类标签
function getDrawingCategoryLabel(category: string): string {
  const found = drawingCategories.find(cat => cat.value === category)
  return found ? found.label : category
}

// 获取图纸状态标签和类型
function getDrawingStatusInfo(status: string): { label: string, type: 'info' | 'success' | 'warning' | 'danger' | 'primary' } {
  const found = drawingStatusOptions.find(opt => opt.value === status)
  return found 
    ? { label: found.label, type: found.type as 'info' | 'success' | 'warning' | 'danger' | 'primary' } 
    : { label: status, type: 'info' }
}

// 处理图纸预览
function handlePreviewDrawing(drawing: DrawingDocument) {
  currentDrawing.value = drawing
  drawingPreviewVisible.value = true
}

// 处理图纸下载
function handleDownloadDrawing() {
  if (currentDrawing.value) {
    ElMessage.success(`正在下载: ${currentDrawing.value.fileName}`)
    // 实际项目中这里会调用API发起下载
  }
}

// 处理上传图纸
function handleUploadDrawing() {
  drawingDialogType.value = 'add'
  drawingFormVisible.value = true
  // 初始化表单数据
  drawingForm.value = {
    id: 0,
    fileName: '',
    fileType: 'PDF',
    category: '',
    version: 'v1.0',
    uploadDate: new Date().toISOString().split('T')[0],
    uploadBy: '当前用户',
    fileSize: '',
    description: '',
    status: 'draft',
    url: '',
    thumbnailUrl: ''
  }
}

// 处理批量导入
function handleBatchImport() {
  ElMessage.success('批量导入功能待实现')
  // 实际项目中会打开文件选择对话框
}

// 处理导出图纸
function handleExportDrawings() {
  ElMessage.success('图纸导出功能待实现')
  // 实际项目中会导出当前筛选的图纸列表
}

// 处理编辑图纸
function handleEditDrawing(drawing: DrawingDocument) {
  drawingDialogType.value = 'edit'
  currentDrawing.value = drawing
  drawingFormVisible.value = true
  // 使用当前图纸数据初始化表单
  drawingForm.value = JSON.parse(JSON.stringify(drawing))
}

// 处理删除图纸
function handleDeleteDrawing(drawing: DrawingDocument) {
  ElMessageBox.confirm(
    `确定要删除图纸"${drawing.fileName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 实际项目中会调用API删除图纸
    const index = drawingsData.value.findIndex(item => item.id === drawing.id)
    if (index !== -1) {
      drawingsData.value.splice(index, 1)
      ElMessage.success('图纸已成功删除')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 处理保存图纸
function handleSaveDrawing() {
  drawingFormRef.value.validate((valid: boolean) => {
    if (valid) {
      drawingSaving.value = true
      
      // 模拟API请求延迟
      setTimeout(() => {
        if (drawingDialogType.value === 'add') {
          // 添加新图纸
          const newId = Math.max(0, ...drawingsData.value.map(item => item.id)) + 1
          drawingForm.value.id = newId
          drawingsData.value.unshift(drawingForm.value)
          ElMessage.success('图纸上传成功')
        } else {
          // 更新现有图纸
          const index = drawingsData.value.findIndex(item => item.id === drawingForm.value.id)
          if (index !== -1) {
            drawingsData.value[index] = drawingForm.value
            ElMessage.success('图纸信息已更新')
          }
        }
        
        drawingSaving.value = false
        drawingFormVisible.value = false
      }, 600)
    }
  })
}

// 方法
function selectModel(model: Model) {
  selectedModel.value = model
  // 加载BOM和设备参数数据
  loadBomData()
  loadDeviceParams()
}

function handleSearchClear() {
  searchModel.value = ''
}

function handleSearchInput() {
  // 当输入变化时的处理逻辑
}

function refreshData() {
  ElMessage.success('数据已刷新')
  loadBomData()
  loadDeviceParams()
}

function exportBOM() {
  ElMessage.success('BOM导出成功')
}

function loadBomData() {
  bomLoading.value = true
  // 模拟API请求延迟
  setTimeout(() => {
    bomLoading.value = false
  }, 500)
}

function loadDeviceParams() {
  deviceLoading.value = true
  // 模拟API请求延迟
  setTimeout(() => {
    deviceLoading.value = false
  }, 500)
}

function handleExpandAll() {
  expandAll.value = !expandAll.value
}

function handleAddBomItem() {
  bomDialogType.value = 'add'
  
  // 计算最大行号
  let maxLineNumber = 0;
  const findMaxLineNumber = (items: BomItem[]) => {
    for (const item of items) {
      if (item.lineNumber && item.lineNumber > maxLineNumber) {
        maxLineNumber = item.lineNumber;
      }
      if (item.children && item.children.length > 0) {
        findMaxLineNumber(item.children);
      }
    }
  };
  findMaxLineNumber(bomData.value);
  
  bomForm.value = {
    id: 0,
    lineNumber: maxLineNumber + 1,
    materialCode: '',
    materialName: '',
    specification: '',
    quantity: 1,
    unit: '个',
    lossRate: 0,
    unitCost: 0,
    totalCost: 0,
    supplier: '',
    effectiveDate: '',
    status: '启用',
    isAlternative: false,
    remark: ''
  }
  bomDialogVisible.value = true
}

function handleEditBomItem(item: BomItem) {
  bomDialogType.value = 'edit'
  bomForm.value = JSON.parse(JSON.stringify(item))
  bomDialogVisible.value = true
}

function handleAddChildBomItem(parent: BomItem) {
  bomDialogType.value = 'add'
  
  // 计算最大行号
  let maxLineNumber = 0;
  const findMaxLineNumber = (items: BomItem[]) => {
    for (const item of items) {
      if (item.lineNumber && item.lineNumber > maxLineNumber) {
        maxLineNumber = item.lineNumber;
      }
      if (item.children && item.children.length > 0) {
        findMaxLineNumber(item.children);
      }
    }
  };
  findMaxLineNumber(bomData.value);
  
  bomForm.value = {
    id: 0,
    lineNumber: maxLineNumber + 1,
    materialCode: '',
    materialName: '',
    specification: '',
    quantity: 1,
    unit: '个',
    lossRate: 0,
    unitCost: 0,
    totalCost: 0,
    supplier: '',
    effectiveDate: '',
    status: '启用',
    isAlternative: false,
    remark: ''
  }
  bomDialogVisible.value = true
  // 标记为子项
  // 实际场景中应该有一个parentId属性
}

function handleDeleteBomItem(item: BomItem) {
  ElMessageBox.confirm(
    `确认删除物料 "${item.materialName}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 执行删除逻辑
    
    // 更新版本号
    currentVersion.value += 1;
    
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消
  })
}

function handleImportBom() {
  ElMessage.info('导入BOM功能开发中')
}

function handleExportBom() {
  ElMessage.success('BOM导出成功')
}

function handleSaveBomItem() {
  bomFormRef.value.validate((valid: boolean) => {
    if (valid) {
      bomSaving.value = true
      // 计算总成本
      bomForm.value.totalCost = bomForm.value.quantity * bomForm.value.unitCost * (1 + bomForm.value.lossRate / 100)
      
      // 更新版本号
      if (bomDialogType.value === 'add' || bomDialogType.value === 'edit') {
        currentVersion.value += 1; // 每次编辑或添加BOM项时，版本号加1
      }
      
      // 模拟API请求延迟
      setTimeout(() => {
        bomSaving.value = false
        bomDialogVisible.value = false
        ElMessage.success(bomDialogType.value === 'add' ? 'BOM项添加成功' : 'BOM项更新成功')
      }, 500)
    }
  })
}

function handleAddDeviceParam() {
  deviceDialogType.value = 'add'
  deviceForm.value = {
    id: 0,
    paramCode: '',
    paramName: '',
    category: 'technical',
    paramValue: '',
    unit: '',
    minValue: '',
    maxValue: '',
    standardValue: '',
    tolerance: '',
    description: '',
    status: '启用'
  }
  deviceDialogVisible.value = true
}

function handleEditDeviceParam(param: DeviceParam) {
  deviceDialogType.value = 'edit'
  deviceForm.value = JSON.parse(JSON.stringify(param))
  deviceDialogVisible.value = true
}

function handleDeleteDeviceParam(param: DeviceParam) {
  ElMessageBox.confirm(
    `确认删除参数 "${param.paramName}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 执行删除逻辑
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消
  })
}

function handleImportDeviceParams() {
  ElMessage.info('导入设备参数功能开发中')
}

function handleExportDeviceParams() {
  ElMessage.success('设备参数导出成功')
}

function handleSaveDeviceParam() {
  deviceFormRef.value.validate((valid: boolean) => {
    if (valid) {
      deviceSaving.value = true
      
      // 模拟API请求延迟
      setTimeout(() => {
        deviceSaving.value = false
        deviceDialogVisible.value = false
        ElMessage.success(deviceDialogType.value === 'add' ? '参数添加成功' : '参数更新成功')
      }, 500)
    }
  })
}

// 行号相关函数
function handleLineNumberChange(changedRow: BomItem) {
  // 当行号改变时，检查是否有重复的行号
  const allRows = getAllBomItems();
  const duplicates = allRows.filter(row => 
    row.id !== changedRow.id && row.lineNumber === changedRow.lineNumber
  );
  
  if (duplicates.length > 0) {
    // 添加更详细的提示信息，显示哪些项与当前行号冲突
    const conflictNames = duplicates.map(item => item.materialName).join(', ');
    ElMessage.warning(`行号${changedRow.lineNumber}已被以下项目使用: ${conflictNames}，但系统仍允许使用重复行号`);
  }
}

function handleMoveUp(row: BomItem) {
  // 找到当前行和上一行
  const allItems = getAllBomItems();
  const currentIndex = allItems.findIndex(item => item.id === row.id);
  
  if (currentIndex > 0) {
    const prevRow = allItems[currentIndex - 1];
    // 交换行号
    const temp = row.lineNumber;
    row.lineNumber = prevRow.lineNumber;
    prevRow.lineNumber = temp;
    
    ElMessage.success('行号调整成功');
  } else {
    ElMessage.info('已经是第一行，无法上移');
  }
}

function handleMoveDown(row: BomItem) {
  // 找到当前行和下一行
  const allItems = getAllBomItems();
  const currentIndex = allItems.findIndex(item => item.id === row.id);
  
  if (currentIndex < allItems.length - 1) {
    const nextRow = allItems[currentIndex + 1];
    // 交换行号
    const temp = row.lineNumber;
    row.lineNumber = nextRow.lineNumber;
    nextRow.lineNumber = temp;
    
    ElMessage.success('行号调整成功');
  } else {
    ElMessage.info('已经是最后一行，无法下移');
  }
}

function handleReorderLineNumbers() {
  // 获取所有项目并按现有行号排序
  const allItems = getAllBomItems();
  allItems.sort((a, b) => (a.lineNumber || 0) - (b.lineNumber || 0));
  
  // 重新分配行号，从1开始
  let newLineNumber = 1;
  for (const item of allItems) {
    item.lineNumber = newLineNumber++;
  }
  
  ElMessage.success('行号已重新排序');
}

// 切换操作列显示/隐藏
function toggleOperationColumn() {
  showOperationColumn.value = !showOperationColumn.value;
  const status = showOperationColumn.value ? '显示' : '隐藏';
  ElMessage.success(`操作列已${status}`);
}

// 辅助函数，获取所有BOM项（包括子项）
function getAllBomItems(): BomItem[] {
  const allItems: BomItem[] = [];
  
  function collectItems(items: BomItem[]) {
    for (const item of items) {
      allItems.push(item);
      if (item.children && item.children.length > 0) {
        collectItems(item.children);
      }
    }
  }
  
  collectItems(bomData.value);
  return allItems;
}

// 扁平化BOM树形结构
function flattenBomItems(item: BomItem): BomItem[] {
  const result: BomItem[] = [item];
  if (item.children && item.children.length > 0) {
    item.children.forEach(child => {
      result.push(...flattenBomItems(child));
    });
  }
  return result;
}

// 获取物料分类类型
function getMaterialCategoryType(category: string): 'primary' | 'warning' | 'info' | 'success' | 'danger' {
  switch (category) {
    case 'core': return 'danger'
    case 'electronic': return 'primary'
    case 'case': return 'warning'
    case 'auxiliary': return 'info'
    default: return 'info'
  }
}

// 获取物料分类标签
function getMaterialCategoryLabel(category: string): string {
  switch (category) {
    case 'core': return '核心部件'
    case 'electronic': return '电子元件'
    case 'case': return '外壳组件'
    case 'auxiliary': return '辅助部件'
    default: return '其他'
  }
}

// 获取成本占比颜色
function getCostPercentageColor(percentage: number): string {
  if (percentage > 20) return '#F56C6C' // 高占比，红色
  if (percentage > 10) return '#E6A23C' // 中占比，橙色
  if (percentage > 5) return '#67C23A' // 低占比，绿色
  return '#909399' // 很低占比，灰色
}

// 检查物料是否有优化建议
function hasCostOptimization(row: any): boolean {
  return row.hasOptimization || false
}

// 获取优化建议提示
function getCostOptimizationTip(row: any): string {
  if (!row.hasOptimization) return '无优化建议'
  
  const tips: string[] = [] // 明确指定为字符串数组
  if (row.costPercentage > 5) {
    tips.push(`该物料占总成本${row.costPercentage.toFixed(1)}%，寻找替代供应商可节省约15%`)
  }
  if (row.unitCost > 100) {
    tips.push(`物料单价较高(${row.unitCost.toFixed(2)}元)，建议与供应商重新谈判`)
  }
  
  return tips.join('\n')
}

// 获取优化类型图标
function getOptimizationIcon(type: string): string {
  switch (type) {
    case 'material': return 'Box'
    case 'process': return 'Operation'
    case 'design': return 'Setting'
    default: return 'InfoFilled'
  }
}

// 获取优化类型图标颜色
function getOptimizationIconColor(type: string): string {
  switch (type) {
    case 'material': return '#E6A23C'
    case 'process': return '#409EFF'
    case 'design': return '#67C23A'
    default: return '#909399'
  }
}

function getParamCategoryType(category: string): 'primary' | 'warning' | 'info' | 'success' | 'danger' {
  switch (category) {
    case 'technical': return 'primary'
    case 'quality': return 'warning'
    case 'maintenance': return 'info'
    case 'process': return 'success' // 新增工艺参数类型
    default: return 'info' // 默认使用info而不是default，因为default不是有效的类型
  }
}

function getParamCategoryLabel(category: string): string {
  switch (category) {
    case 'technical': return '技术参数'
    case 'quality': return '质量参数'
    case 'maintenance': return '维护参数'
    case 'process': return '工艺参数' // 新增工艺参数标签
    default: return '未分类'
  }
}

// 辅助函数
function filterBomItems(items: BomItem[], search: string): BomItem[] {
  return items.filter(item => {
    const match = item.materialCode.toLowerCase().includes(search) ||
                  item.materialName.toLowerCase().includes(search) ||
                  item.specification.toLowerCase().includes(search) ||
                  item.supplier.toLowerCase().includes(search)
    
    if (match) return true
    
    if (item.children && item.children.length > 0) {
      const filteredChildren = filterBomItems(item.children, search)
      if (filteredChildren.length > 0) {
        // 创建新对象，避免修改原数据
        const newItem = { ...item, children: filteredChildren }
        return true
      }
    }
    
    return false
  })
}

function countBomItems(items: BomItem[]): number {
  let count = items.length
  
  for (const item of items) {
    if (item.children && item.children.length > 0) {
      count += countBomItems(item.children)
    }
  }
  
  return count
}

function calculateTotalCost(items: BomItem[]): number {
  let total = 0
  
  for (const item of items) {
    total += item.totalCost
    
    if (item.children && item.children.length > 0) {
      // 避免重复计算子项的成本，实际情况中可能需要更复杂的计算逻辑
      // total += calculateTotalCost(item.children)
    }
  }
  
  return total
}

function countMaterialTypes(items: BomItem[]): number {
  // 使用Set去重
  const materialCodes = new Set<string>()
  
  function collectMaterialCodes(items: BomItem[]) {
    for (const item of items) {
      materialCodes.add(item.materialCode)
      
      if (item.children && item.children.length > 0) {
        collectMaterialCodes(item.children)
      }
    }
  }
  
  collectMaterialCodes(items)
  return materialCodes.size
}

onMounted(() => {
  console.error('🏁 === EngineeringBOM组件已挂载 ===')
  console.error('🚀 开始执行onMounted钩子')
  
  // 加载物料主数据模块中的型号数据
  console.error('📝 准备调用loadModels函数')
  loadModels()
  console.error('✨ loadModels函数调用完成')
  
  // 默认选择第一个型号（等待型号加载完成）
  setTimeout(() => {
    console.error('⏰ setTimeout回调执行，当前models.value:', models.value)
    // 默认选择第一个启用状态的型号
    const enabledModel = models.value.find(m => m.status === '启用')
    if (enabledModel) {
      console.error('✅ 找到启用的型号:', enabledModel)
      selectModel(enabledModel)
    } else if (models.value.length > 0) {
      console.error('📌 使用第一个型号:', models.value[0])
      selectModel(models.value[0])
    } else {
      console.error('❌ 没有找到任何型号数据')
    }
  }, 800)
})
</script>

<style scoped>
.engineering-bom {
  min-height: calc(100vh - 140px);
  overflow: hidden;
}

.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.title .el-icon {
  margin-right: 8px;
  font-size: 20px;
}

.layout {
  display: flex;
  height: calc(100% - 60px);
  gap: 20px;
}

.left-panel {
  width: 240px;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.search-container {
  padding: 10px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.search-input {
  width: 100%;
}

.model-type-filter {
  display: flex;
  justify-content: center;
  margin-top: 5px;
}

.model-type-filter :deep(.el-radio-button__inner) {
  padding: 5px 10px;
  font-size: 12px;
}

.model-list {
  flex: 1;
  height: 0;
}

.loading-container {
  padding: 20px;
}

.loading-text {
  text-align: center;
  margin-top: 10px;
  color: #909399;
}

.model-item-container {
  padding: 10px;
}

.model-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.model-item:hover {
  background-color: #f5f7fa;
}

.model-item.active {
  background-color: #ecf5ff;
  border-left: 3px solid #409eff;
}

.model-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.model-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 3px;
}

.model-code {
  font-size: 12px;
  color: #909399;
}

.model-type-tag {
  font-size: 10px;
  height: 18px;
  line-height: 16px;
  margin-left: 5px;
}

.model-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
}

.model-status.enabled {
  background-color: #f0f9eb;
  color: #67c23a;
}

.model-status.disabled {
  background-color: #fef0f0;
  color: #f56c6c;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.tab-content {
  padding: 20px;
  min-height: 650px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.no-selection {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.bom-toolbar,
.device-toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-middle {
  display: flex;
  align-items: center;
  margin: 0 15px;
}

.version-tag {
  font-size: 14px;
  padding: 8px 15px;
}

.toggle-operation-btn {
  display: flex;
  align-items: center;
  gap: 5px;
}

.bom-content,
.device-content {
  min-height: 600px;
  display: flex;
  flex-direction: column;
}

.bom-table,
.device-table {
  margin-bottom: 20px;
  flex: 1;
  overflow: auto;
}

.bom-summary {
  margin-top: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  flex-shrink: 0;
}

.material-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.material-name {
  font-weight: bold;
}

.material-code {
  font-family: monospace;
}

/* 确保行号列在最左侧显示 */
:deep(.el-table__fixed) .el-table__cell[data-property="lineNumber"] {
  left: 0 !important;
  z-index: 3;
}

:deep(.el-table__fixed) .el-table__cell[data-property="materialCode"] {
  left: 100px !important; /* 行号列的宽度 */
  z-index: 2;
}

.quantity,
.loss-rate,
.unit-cost,
.total-cost {
  font-family: monospace;
}

.effective-date {
  color: #606266;
}

.quality-placeholder {
  padding: 40px;
}

/* 成本分析页样式 */
.cost-overview {
  margin-bottom: 20px;
}

.cost-card {
  height: 100%;
}

.cost-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cost-card-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.cost-trend {
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.cost-percentage {
  width: 100%;
  margin-top: 15px;
}

.cost-charts {
  margin-bottom: 20px;
}

.chart-card {
  height: 100%;
}

.chart-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.chart-text {
  text-align: center;
  color: #909399;
}

.chart-desc {
  font-size: 14px;
  margin-top: 10px;
  color: #c0c4cc;
}

.cost-detail-card {
  margin-bottom: 20px;
}

.cost-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cost-detail-title {
  font-size: 16px;
  font-weight: bold;
}

.unit-text {
  color: #909399;
  margin-left: 3px;
  font-size: 12px;
}

.total-cost {
  font-weight: bold;
  color: #F56C6C;
}

.no-optimization {
  color: #c0c4cc;
  font-size: 12px;
}

.cost-optimization {
  margin-bottom: 20px;
}

.optimization-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.optimization-item:last-child {
  border-bottom: none;
}

.optimization-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f5f7fa;
}

.optimization-content {
  flex: 1;
}

.optimization-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.optimization-desc {
  color: #606266;
  font-size: 14px;
  margin-bottom: 10px;
}

.optimization-benefit {
  font-size: 13px;
  color: #909399;
}

.benefit-amount {
  color: #F56C6C;
  font-weight: bold;
}

.benefit-percentage {
  color: #67C23A;
  font-weight: bold;
}

.benchmark-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 标签页样式 */
.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.tab-label .el-icon {
  font-size: 16px;
}

:deep(.el-tabs__item.is-active) .tab-label .el-icon {
  color: var(--el-color-primary);
}

:deep(.el-tabs__item:hover) .tab-label .el-icon {
  color: var(--el-color-primary-light-3);
}

/* 设计图纸模块样式 */
.drawing-content {
  min-height: 600px;
  display: flex;
  flex-direction: column;
}

.drawing-toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.drawing-table {
  margin-bottom: 20px;
  flex: 1;
  overflow: auto;
}

.drawing-cards {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  overflow-y: auto;
  max-height: 600px;
}

.drawing-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.drawing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.drawing-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.drawing-card-title {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.drawing-card-thumbnail {
  height: 150px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
}

.drawing-thumbnail-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  color: #909399;
  gap: 10px;
}

.drawing-card-info {
  margin-bottom: 10px;
}

.info-item {
  display: flex;
  margin-bottom: 5px;
  font-size: 12px;
}

.info-label {
  color: #909399;
  margin-right: 5px;
}

.info-value {
  color: #606266;
}

.drawing-card-actions {
  display: flex;
  justify-content: space-between;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}

/* 图纸预览对话框样式 */
.drawing-preview-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.drawing-preview-content {
  padding: 20px;
}

.drawing-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 15px;
}

.drawing-info-tags {
  display: flex;
  gap: 10px;
}

.drawing-preview-main {
  min-height: 500px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 20px;
  overflow: hidden;
}

.pdf-placeholder,
.cad-placeholder,
.model-placeholder,
.generic-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  color: #909399;
  gap: 10px;
  padding: 40px;
  text-align: center;
}

.pdf-placeholder-text,
.cad-placeholder-text,
.model-placeholder-text,
.generic-placeholder-text {
  font-size: 24px;
  margin-top: 20px;
}

.pdf-placeholder-subtext,
.cad-placeholder-subtext,
.model-placeholder-subtext,
.generic-placeholder-subtext {
  font-size: 14px;
  color: #c0c4cc;
}

.drawing-info-footer {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.drawing-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
}

.meta-label {
  font-weight: bold;
  margin-right: 10px;
  color: #606266;
}

.meta-value {
  color: #303133;
}

.drawing-description {
  margin-top: 15px;
}

.description-label {
  font-weight: bold;
  margin-bottom: 5px;
  color: #606266;
}

.description-content {
  color: #303133;
  line-height: 1.5;
}
</style>
