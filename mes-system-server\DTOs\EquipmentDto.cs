using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace mes_system_server.DTOs
{
    public class EquipmentDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int WorkCenterId { get; set; }
        public int SortOrder { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime UpdateTime { get; set; }
    }

    public class CreateEquipmentDto
    {
        [Required(ErrorMessage = "设备名称不能为空")]
        [StringLength(100, ErrorMessage = "设备名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "设备编码不能为空")]
        [StringLength(50, ErrorMessage = "设备编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }

        public int SortOrder { get; set; } = 0;
    }

    public class UpdateEquipmentDto
    {
        [Required(ErrorMessage = "设备名称不能为空")]
        [StringLength(100, ErrorMessage = "设备名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "设备编码不能为空")]
        [StringLength(50, ErrorMessage = "设备编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }

        public int SortOrder { get; set; } = 0;

        public bool IsActive { get; set; } = true;
    }

    public class EquipmentResponseDto
    {
        public int Total { get; set; }
        public List<EquipmentDto> List { get; set; } = new();
    }

    public class BatchEquipmentItemDto
    {
        public int? Id { get; set; } // null表示新增
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int SortOrder { get; set; }
    }
} 