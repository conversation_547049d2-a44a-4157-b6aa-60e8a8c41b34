using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using mes_system_server.Data;
using mes_system_server.DTOs;
using mes_system_server.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace mes_system_server.Controllers
{
    [Route("api/equipment")]
    [ApiController]
    [AllowAnonymous]
    public class EquipmentController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public EquipmentController(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取指定工作中心的设备列表
        /// </summary>
        /// <param name="workCenterId">工作中心ID</param>
        /// <returns>设备列表</returns>
        [HttpGet("by-work-center/{workCenterId}")]
        public async Task<IActionResult> GetEquipmentByWorkCenter(int workCenterId)
        {
            try
            {
                // 验证工作中心是否存在
                var workCenterExists = await _context.WorkCenters
                    .AnyAsync(wc => wc.Id == workCenterId && wc.IsActive);

                if (!workCenterExists)
                {
                    return NotFound(ApiResponseDto<string>.Fail("工作中心不存在", 404));
                }

                var equipment = await _context.Equipment
                    .Where(eq => eq.WorkCenterId == workCenterId && eq.IsActive)
                    .OrderBy(eq => eq.SortOrder)
                    .ThenBy(eq => eq.Id)
                    .ToListAsync();

                var equipmentDtos = equipment.Select(eq => new EquipmentDto
                {
                    Id = eq.Id,
                    Name = eq.Name,
                    Code = eq.Code,
                    Description = eq.Description,
                    WorkCenterId = eq.WorkCenterId,
                    SortOrder = eq.SortOrder,
                    IsActive = eq.IsActive,
                    CreateTime = eq.CreateTime,
                    UpdateTime = eq.UpdateTime
                }).ToList();

                var response = new EquipmentResponseDto
                {
                    Total = equipmentDtos.Count,
                    List = equipmentDtos
                };

                return Ok(ApiResponseDto<EquipmentResponseDto>.Success(response));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取设备列表失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 根据ID获取设备详情
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns>设备详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetEquipment(int id)
        {
            try
            {
                var equipment = await _context.Equipment
                    .FirstOrDefaultAsync(eq => eq.Id == id && eq.IsActive);

                if (equipment == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("设备不存在", 404));
                }

                var equipmentDto = new EquipmentDto
                {
                    Id = equipment.Id,
                    Name = equipment.Name,
                    Code = equipment.Code,
                    Description = equipment.Description,
                    WorkCenterId = equipment.WorkCenterId,
                    SortOrder = equipment.SortOrder,
                    IsActive = equipment.IsActive,
                    CreateTime = equipment.CreateTime,
                    UpdateTime = equipment.UpdateTime
                };

                return Ok(ApiResponseDto<EquipmentDto>.Success(equipmentDto));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取设备详情失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 创建设备
        /// </summary>
        /// <param name="workCenterId">工作中心ID</param>
        /// <param name="equipmentDto">设备信息</param>
        /// <returns>创建的设备</returns>
        [HttpPost("work-center/{workCenterId}")]
        public async Task<IActionResult> CreateEquipment(int workCenterId, [FromBody] CreateEquipmentDto equipmentDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                // 验证工作中心是否存在
                var workCenterExists = await _context.WorkCenters
                    .AnyAsync(wc => wc.Id == workCenterId && wc.IsActive);

                if (!workCenterExists)
                {
                    return NotFound(ApiResponseDto<string>.Fail("工作中心不存在", 404));
                }

                // 检查设备编码在该工作中心内是否已存在
                var existingEquipment = await _context.Equipment
                    .FirstOrDefaultAsync(eq => eq.WorkCenterId == workCenterId && 
                                             eq.Code == equipmentDto.Code && eq.IsActive);

                if (existingEquipment != null)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("设备编码在该工作中心内已存在"));
                }

                var equipment = new Equipment
                {
                    Name = equipmentDto.Name,
                    Code = equipmentDto.Code,
                    Description = equipmentDto.Description,
                    WorkCenterId = workCenterId,
                    SortOrder = equipmentDto.SortOrder,
                    IsActive = true,
                    CreateTime = DateTime.UtcNow,
                    UpdateTime = DateTime.UtcNow
                };

                _context.Equipment.Add(equipment);
                await _context.SaveChangesAsync();

                var createdEquipmentDto = new EquipmentDto
                {
                    Id = equipment.Id,
                    Name = equipment.Name,
                    Code = equipment.Code,
                    Description = equipment.Description,
                    WorkCenterId = equipment.WorkCenterId,
                    SortOrder = equipment.SortOrder,
                    IsActive = equipment.IsActive,
                    CreateTime = equipment.CreateTime,
                    UpdateTime = equipment.UpdateTime
                };

                return CreatedAtAction(nameof(GetEquipment), new { id = equipment.Id }, 
                    ApiResponseDto<EquipmentDto>.Success(createdEquipmentDto, "添加成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"创建设备失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 更新设备
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <param name="equipmentDto">设备信息</param>
        /// <returns>更新的设备</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateEquipment(int id, [FromBody] UpdateEquipmentDto equipmentDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                var equipment = await _context.Equipment
                    .FirstOrDefaultAsync(eq => eq.Id == id && eq.IsActive);

                if (equipment == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("设备不存在", 404));
                }

                // 检查设备编码在该工作中心内是否与其他设备冲突
                var existingEquipment = await _context.Equipment
                    .FirstOrDefaultAsync(eq => eq.WorkCenterId == equipment.WorkCenterId && 
                                             eq.Code == equipmentDto.Code && eq.Id != id && eq.IsActive);

                if (existingEquipment != null)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("设备编码在该工作中心内已存在"));
                }

                // 更新设备信息
                equipment.Name = equipmentDto.Name;
                equipment.Code = equipmentDto.Code;
                equipment.Description = equipmentDto.Description;
                equipment.SortOrder = equipmentDto.SortOrder;
                equipment.IsActive = equipmentDto.IsActive;
                equipment.UpdateTime = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                var updatedEquipmentDto = new EquipmentDto
                {
                    Id = equipment.Id,
                    Name = equipment.Name,
                    Code = equipment.Code,
                    Description = equipment.Description,
                    WorkCenterId = equipment.WorkCenterId,
                    SortOrder = equipment.SortOrder,
                    IsActive = equipment.IsActive,
                    CreateTime = equipment.CreateTime,
                    UpdateTime = equipment.UpdateTime
                };

                return Ok(ApiResponseDto<EquipmentDto>.Success(updatedEquipmentDto, "更新成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"更新设备失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 删除设备
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEquipment(int id)
        {
            try
            {
                var equipment = await _context.Equipment
                    .FirstOrDefaultAsync(eq => eq.Id == id && eq.IsActive);

                if (equipment == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("设备不存在", 404));
                }

                // 软删除
                equipment.IsActive = false;
                equipment.UpdateTime = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(ApiResponseDto<string>.Success(null, "删除成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"删除设备失败: {ex.Message}", 500));
            }
        }
    }
} 