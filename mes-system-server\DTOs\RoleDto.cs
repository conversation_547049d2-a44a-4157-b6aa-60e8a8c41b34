using System.ComponentModel.DataAnnotations;

namespace mes_system_server.DTOs
{
    // 角色列表DTO
    public class RoleListDto
    {
        public int Id { get; set; }
        public string RoleName { get; set; } = string.Empty;
        public string RoleCode { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool Status { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime? UpdateTime { get; set; }
    }

    // 创建角色DTO
    public class CreateRoleDto
    {
        [Required(ErrorMessage = "角色名称不能为空")]
        [StringLength(50, ErrorMessage = "角色名称长度不能超过50个字符")]
        public string RoleName { get; set; } = string.Empty;

        [Required(ErrorMessage = "角色编码不能为空")]
        [StringLength(50, ErrorMessage = "角色编码长度不能超过50个字符")]
        public string RoleCode { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "角色描述长度不能超过200个字符")]
        public string? Description { get; set; }

        public bool Status { get; set; } = true;
    }

    // 更新角色DTO
    public class UpdateRoleDto
    {
        [Required(ErrorMessage = "角色名称不能为空")]
        [StringLength(50, ErrorMessage = "角色名称长度不能超过50个字符")]
        public string RoleName { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "角色描述长度不能超过200个字符")]
        public string? Description { get; set; }

        public bool Status { get; set; } = true;
    }

    // 角色状态更新DTO
    public class RoleStatusDto
    {
        public bool Status { get; set; }
    }

    // 角色详情DTO
    public class RoleDetailDto
    {
        public int Id { get; set; }
        public string RoleName { get; set; } = string.Empty;
        public string RoleCode { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool Status { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime? UpdateTime { get; set; }
        public List<UserListDto> Users { get; set; } = new List<UserListDto>();
    }

    // 分页查询结果DTO
    public class PagedRoleResultDto
    {
        public List<RoleListDto> List { get; set; } = new List<RoleListDto>();
        public int Total { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
    }
} 