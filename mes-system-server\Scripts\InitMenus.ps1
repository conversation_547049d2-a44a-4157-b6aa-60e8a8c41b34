# 菜单初始化PowerShell脚本
# 用于在SQL Server中执行菜单数据初始化

param(
    [Parameter(Mandatory=$false)]
    [string]$ServerName = "localhost",
    
    [Parameter(Mandatory=$false)]
    [string]$DatabaseName = "MesSystemDB",
    
    [Parameter(Mandatory=$false)]
    [string]$Username = "",
    
    [Parameter(Mandatory=$false)]
    [string]$Password = ""
)

$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
$sqlScriptPath = Join-Path $scriptPath "InitMenus.sql"

Write-Host "菜单数据初始化脚本" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green
Write-Host "SQL脚本路径: $sqlScriptPath" -ForegroundColor Yellow
Write-Host "服务器: $ServerName" -ForegroundColor Yellow
Write-Host "数据库: $DatabaseName" -ForegroundColor Yellow

# 检查SQL脚本文件是否存在
if (-not (Test-Path $sqlScriptPath)) {
    Write-Host "错误: 找不到SQL脚本文件 $sqlScriptPath" -ForegroundColor Red
    exit 1
}

try {
    # 构建连接字符串
    if ($Username -and $Password) {
        # SQL Server身份验证
        $connectionString = "Server=$ServerName;Database=$DatabaseName;User Id=$Username;Password=$Password;TrustServerCertificate=true;"
    } else {
        # Windows身份验证
        $connectionString = "Server=$ServerName;Database=$DatabaseName;Integrated Security=true;TrustServerCertificate=true;"
    }

    Write-Host "正在连接到数据库..." -ForegroundColor Yellow
    
    # 使用sqlcmd执行SQL脚本
    if ($Username -and $Password) {
        sqlcmd -S $ServerName -d $DatabaseName -U $Username -P $Password -i $sqlScriptPath
    } else {
        sqlcmd -S $ServerName -d $DatabaseName -E -i $sqlScriptPath
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "菜单数据初始化成功完成！" -ForegroundColor Green
    } else {
        Write-Host "菜单数据初始化失败，错误代码: $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
} catch {
    Write-Host "执行失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "脚本执行完毕。" -ForegroundColor Green 