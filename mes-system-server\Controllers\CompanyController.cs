using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using mes_system_server.Data;
using mes_system_server.DTOs;
using mes_system_server.Models;
using System;
using System.Threading.Tasks;

namespace mes_system_server.Controllers
{
    [Route("api/company")]
    [ApiController]
    public class CompanyController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public CompanyController(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取公司信息
        /// </summary>
        /// <returns>公司基本信息</returns>
        [HttpGet("info")]
        [AllowAnonymous]
        public async Task<IActionResult> GetCompanyInfo()
        {
            try
            {
                var company = await _context.Companies.FirstOrDefaultAsync();
                if (company == null)
                {
                    return NotFound(ApiResponseDto<CompanyDto>.Fail("公司信息不存在", 404));
                }

                var companyDto = new CompanyDto
                {
                    Id = company.Id,
                    Name = company.Name,
                    CreditCode = company.CreditCode,
                    LegalRepresentative = company.LegalRepresentative,
                    RegisteredCapital = company.RegisteredCapital,
                    Address = company.Address,
                    Phone = company.Phone,
                    Email = company.Email,
                    CompanyType = company.CompanyType,
                    RegistrationDate = company.RegistrationDate,
                    Description = company.Description
                };

                return Ok(ApiResponseDto<CompanyDto>.Success(companyDto));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取公司信息失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 更新公司信息
        /// </summary>
        /// <param name="updateDto">更新的公司信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("info")]
        [AllowAnonymous]
        public async Task<IActionResult> UpdateCompanyInfo([FromBody] UpdateCompanyDto updateDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                var company = await _context.Companies.FirstOrDefaultAsync();
                if (company == null)
                {
                    // 如果不存在则创建新公司记录
                    company = new Company
                    {
                        Name = updateDto.Name,
                        CreditCode = updateDto.CreditCode,
                        LegalRepresentative = updateDto.LegalRepresentative,
                        RegisteredCapital = updateDto.RegisteredCapital,
                        Address = updateDto.Address,
                        Phone = updateDto.Phone,
                        Email = updateDto.Email,
                        CompanyType = updateDto.CompanyType,
                        RegistrationDate = updateDto.RegistrationDate,
                        Description = updateDto.Description,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };
                    _context.Companies.Add(company);
                }
                else
                {
                    // 否则更新现有记录
                    company.Name = updateDto.Name;
                    company.CreditCode = updateDto.CreditCode;
                    company.LegalRepresentative = updateDto.LegalRepresentative;
                    company.RegisteredCapital = updateDto.RegisteredCapital;
                    company.Address = updateDto.Address;
                    company.Phone = updateDto.Phone;
                    company.Email = updateDto.Email;
                    company.CompanyType = updateDto.CompanyType;
                    company.RegistrationDate = updateDto.RegistrationDate;
                    company.Description = updateDto.Description;
                    company.UpdatedAt = DateTime.UtcNow;
                    _context.Companies.Update(company);
                }

                await _context.SaveChangesAsync();
                return Ok(ApiResponseDto<object>.Success(null, "更新成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"更新公司信息失败: {ex.Message}", 500));
            }
        }
    }
} 