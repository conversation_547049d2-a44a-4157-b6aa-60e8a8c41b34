<template>
  <div class="login-container">
    <!-- 语言切换按钮 -->
    <div class="language-switcher-wrapper">
      <LanguageSwitcher />
    </div>
    
    <div class="login-content">
      <!-- 左侧图片区域 -->
      <div class="login-left">
        <div class="image-container">
          <img src="../assets/smart-factory.svg" alt="智能工厂" class="main-image" />
          <div class="slogan-container">
            <h2 class="slogan">{{ $t('login.slogan') }}</h2>
            <p class="sub-slogan">{{ $t('login.subSlogan') }}</p>
          </div>
        </div>
      </div>

      <!-- 右侧登录框 -->
      <div class="login-right">
        <el-card class="login-card">
          <template #header>
            <div class="card-header">
              <img src="../assets/logo.svg" alt="logo" class="logo" />
              <h2>{{ $t('login.welcome') }}</h2>
              <p class="welcome-text">{{ $t('login.welcomeText') }}</p>
            </div>
          </template>
          
          <el-form :model="loginForm" :rules="rules" ref="loginFormRef" class="login-form">
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                :placeholder="$t('login.phuseruser')"
                :prefix-icon="User"
                size="large"
              />
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                :placeholder="$t('login.phpasswordpassword')"
                :prefix-icon="Lock"
                show-password
                size="large"
              />
            </el-form-item>

            <div class="login-options">
              <el-checkbox v-model="rememberMe">{{ $t('login.rememberMe') }}</el-checkbox>
              <el-link type="primary" :underline="false">{{ $t('login.forgotPassword') }}</el-link>
            </div>
            
            <el-form-item>
              <el-button type="primary" @click="handleLogin" class="login-button" size="large" :loading="loading">
                {{ $t('login.loginButton') }}
              </el-button>
            </el-form-item>
          </el-form>

          <div class="login-footer">
            <p class="copyright">{{ $t('login.copyright') }}</p>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { login } from '@/api/auth'
import LanguageSwitcher from '@/components/LanguageSwitcher.vue'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const loginFormRef = ref(null)
const rememberMe = ref(false)
const loading = ref(false)

const loginForm = reactive({
  username: '',
  password: ''
})

// 使用翻译函数的表单验证规则
const rules = computed(() => ({
  username: [
    { required: true, message: t('login.usernameRequired'), trigger: 'blur' }
  ],
  password: [
    { required: true, message: t('login.passwordRequired'), trigger: 'blur' }
  ]
}))

const handleLogin = () => {
  loginFormRef.value?.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const response = await login(loginForm)
        
        // 登录成功，保存token和用户信息
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('isLoggedIn', 'true')
        localStorage.setItem('userInfo', JSON.stringify(response.data.user))
        
        if (rememberMe.value) {
          localStorage.setItem('username', loginForm.username)
        }
        
        ElMessage.success(t('login.loginSuccess'))
        
        // 获取重定向地址或默认跳转到首页
        const redirect = route.query.redirect || '/home'
        router.push(redirect)
      } catch (error) {
        // 登录失败的错误处理已在request.js中的拦截器中统一处理
        console.error('登录失败:', error)
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7ed 100%);
  position: relative;
}

/* 语言切换按钮样式 */
.language-switcher-wrapper {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.language-switcher-wrapper :deep(.language-button) {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.language-switcher-wrapper :deep(.language-button:hover) {
  background: rgba(255, 255, 255, 1);
  border-color: #409eff;
}

.login-content {
  display: flex;
  width: 90%;
  max-width: 1400px;
  min-height: 600px;
  max-height: 90vh;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  margin: 40px 0;
  overflow: hidden;
}

.login-left {
  flex: 1.2;
  position: relative;
  background: linear-gradient(135deg, #409EFF 0%, #1890ff 100%);
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.main-image {
  width: 80%;
  max-width: 500px;
  height: auto;
  margin-bottom: 40px;
}

.slogan-container {
  text-align: center;
  color: #ffffff;
}

.slogan {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 16px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sub-slogan {
  font-size: 18px;
  opacity: 0.9;
}

.login-right {
  flex: 0.8;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: transparent;
  box-shadow: none;
  border: none;
}

.card-header {
  text-align: center;
  padding: 0 0 40px 0;
}

.logo {
  width: 80px;
  height: 80px;
  margin-bottom: 24px;
}

.card-header h2 {
  margin: 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.welcome-text {
  margin: 12px 0 0 0;
  color: #909399;
  font-size: 16px;
}

.login-form {
  margin: 0;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  border-radius: 22px;
}

.login-footer {
  text-align: center;
  margin-top: 40px;
}

.copyright {
  color: #909399;
  font-size: 14px;
}

/* 响应式设计 */
@media screen and (max-width: 1024px) {
  .login-content {
    width: 100%;
    margin: 0;
    border-radius: 0;
    max-height: 100vh;
  }
  
  .language-switcher-wrapper {
    top: 10px;
    right: 10px;
  }
}

@media screen and (max-width: 768px) {
  .login-content {
    flex-direction: column;
  }
  
  .login-left {
    display: none;
  }
  
  .login-right {
    flex: 1;
    padding: 40px 20px;
  }
  
  .language-switcher-wrapper {
    position: fixed;
    top: 15px;
    right: 15px;
  }
}
</style> 