using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic; // Added for List

namespace mes_system_server.DTOs
{
    /// <summary>
    /// 物料型号列表DTO
    /// </summary>
    public class MaterialModelListDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Specification { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Image { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;
        public string Material { get; set; } = string.Empty;
        public string Size { get; set; } = string.Empty;
        public DateTime CreateTime { get; set; }
        public DateTime UpdateTime { get; set; }
    }

    /// <summary>
    /// 物料型号详细信息DTO
    /// </summary>
    public class MaterialModelDetailDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Specification { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Image { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;
        public string Material { get; set; } = string.Empty;
        public string Size { get; set; } = string.Empty;

        // 销售信息
        public decimal? SalesPrice { get; set; }
        public string SalesUnit { get; set; } = string.Empty;
        public decimal? MinOrderQty { get; set; }
        public decimal? MaxOrderQty { get; set; }
        public string LeadTime { get; set; } = string.Empty;
        public string Warranty { get; set; } = string.Empty;

        // 仓库信息
        public string StorageLocation { get; set; } = string.Empty;
        public decimal? SafetyStock { get; set; }
        public decimal? MaxStock { get; set; }
        public decimal? ReorderPoint { get; set; }
        public string StorageCondition { get; set; } = string.Empty;
        public string ShelfLife { get; set; } = string.Empty;

        // 财务信息
        public decimal? StandardCost { get; set; }
        public decimal? AverageCost { get; set; }
        public string ValuationMethod { get; set; } = string.Empty;
        public decimal? TaxRate { get; set; }
        public string AccountSubject { get; set; } = string.Empty;
        public string CostCenter { get; set; } = string.Empty;

        // 生产信息
        public string ProductionType { get; set; } = string.Empty;
        public string ProductionLeadTime { get; set; } = string.Empty;
        public string SetupTime { get; set; } = string.Empty;
        public string CycleTime { get; set; } = string.Empty;
        public string BatchSize { get; set; } = string.Empty;
        public string WorkCenter { get; set; } = string.Empty;
        public string QualityStandard { get; set; } = string.Empty;

        // 采购信息
        public string Supplier { get; set; } = string.Empty;
        public decimal? PurchasePrice { get; set; }
        public string PurchaseUnit { get; set; } = string.Empty;
        public decimal? MinPurchaseQty { get; set; }
        public string PurchaseLeadTime { get; set; } = string.Empty;
        public string QualityLevel { get; set; } = string.Empty;
        public string PurchaseNote { get; set; } = string.Empty;

        // 进出口信息
        public string HsCode { get; set; } = string.Empty;
        public string OriginCountry { get; set; } = string.Empty;
        public decimal? ImportTaxRate { get; set; }
        public decimal? ExportTaxRefund { get; set; }
        public bool IsDangerous { get; set; }
        public string TransportMode { get; set; } = string.Empty;
        public string PackingRequirement { get; set; } = string.Empty;
        public bool InspectionRequired { get; set; }
        public string LicenseRequirement { get; set; } = string.Empty;

        // MRP计划信息
        public string PlanningStrategy { get; set; } = string.Empty;
        public string PlanningCycle { get; set; } = string.Empty;
        public string ForecastMethod { get; set; } = string.Empty;
        public string AbcCategory { get; set; } = string.Empty;
        public string DemandSource { get; set; } = string.Empty;
        public string Planner { get; set; } = string.Empty;
        public string SafetyStockDays { get; set; } = string.Empty;
        public string LotSizeRule { get; set; } = string.Empty;
        public string DemandTimeFence { get; set; } = string.Empty;
        public string SupplyTimeFence { get; set; } = string.Empty;
        public string PlanningNote { get; set; } = string.Empty;

        public DateTime CreateTime { get; set; }
        public DateTime UpdateTime { get; set; }
        public int CreateBy { get; set; }
        public int? UpdateBy { get; set; }
    }

    /// <summary>
    /// 创建物料型号DTO
    /// </summary>
    public class CreateMaterialModelDto
    {
        [Required(ErrorMessage = "型号名称不能为空")]
        [StringLength(100, ErrorMessage = "型号名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "型号编码不能为空")]
        [StringLength(50, ErrorMessage = "型号编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        [Required(ErrorMessage = "分类不能为空")]
        [StringLength(50, ErrorMessage = "分类长度不能超过50个字符")]
        public string Category { get; set; } = string.Empty;

        [Required(ErrorMessage = "规格参数不能为空")]
        [StringLength(500, ErrorMessage = "规格参数长度不能超过500个字符")]
        public string Specification { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "状态长度不能超过20个字符")]
        public string Status { get; set; } = "启用";

        [StringLength(500, ErrorMessage = "图片URL长度不能超过500个字符")]
        public string Image { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "单位长度不能超过20个字符")]
        public string Unit { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "材质长度不能超过100个字符")]
        public string Material { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "尺寸长度不能超过100个字符")]
        public string Size { get; set; } = string.Empty;

        // 销售信息
        public decimal? SalesPrice { get; set; }
        public string SalesUnit { get; set; } = string.Empty;
        public decimal? MinOrderQty { get; set; }
        public decimal? MaxOrderQty { get; set; }
        public string LeadTime { get; set; } = string.Empty;
        public string Warranty { get; set; } = string.Empty;

        // 仓库信息
        public string StorageLocation { get; set; } = string.Empty;
        public decimal? SafetyStock { get; set; }
        public decimal? MaxStock { get; set; }
        public decimal? ReorderPoint { get; set; }
        public string StorageCondition { get; set; } = string.Empty;
        public string ShelfLife { get; set; } = string.Empty;

        // 财务信息
        public decimal? StandardCost { get; set; }
        public decimal? AverageCost { get; set; }
        public string ValuationMethod { get; set; } = string.Empty;
        public decimal? TaxRate { get; set; }
        public string AccountSubject { get; set; } = string.Empty;
        public string CostCenter { get; set; } = string.Empty;

        // 生产信息
        public string ProductionType { get; set; } = string.Empty;
        public string ProductionLeadTime { get; set; } = string.Empty;
        public string SetupTime { get; set; } = string.Empty;
        public string CycleTime { get; set; } = string.Empty;
        public string BatchSize { get; set; } = string.Empty;
        public string WorkCenter { get; set; } = string.Empty;
        public string QualityStandard { get; set; } = string.Empty;

        // 采购信息
        public string Supplier { get; set; } = string.Empty;
        public decimal? PurchasePrice { get; set; }
        public string PurchaseUnit { get; set; } = string.Empty;
        public decimal? MinPurchaseQty { get; set; }
        public string PurchaseLeadTime { get; set; } = string.Empty;
        public string QualityLevel { get; set; } = string.Empty;
        public string PurchaseNote { get; set; } = string.Empty;

        // 进出口信息
        public string HsCode { get; set; } = string.Empty;
        public string OriginCountry { get; set; } = string.Empty;
        public decimal? ImportTaxRate { get; set; }
        public decimal? ExportTaxRefund { get; set; }
        public bool IsDangerous { get; set; } = false;
        public string TransportMode { get; set; } = string.Empty;
        public string PackingRequirement { get; set; } = string.Empty;
        public bool InspectionRequired { get; set; } = false;
        public string LicenseRequirement { get; set; } = string.Empty;

        // MRP计划信息
        public string PlanningStrategy { get; set; } = string.Empty;
        public string PlanningCycle { get; set; } = string.Empty;
        public string ForecastMethod { get; set; } = string.Empty;
        public string AbcCategory { get; set; } = string.Empty;
        public string DemandSource { get; set; } = string.Empty;
        public string Planner { get; set; } = string.Empty;
        public string SafetyStockDays { get; set; } = string.Empty;
        public string LotSizeRule { get; set; } = string.Empty;
        public string DemandTimeFence { get; set; } = string.Empty;
        public string SupplyTimeFence { get; set; } = string.Empty;
        public string PlanningNote { get; set; } = string.Empty;
    }

    /// <summary>
    /// 更新物料型号DTO
    /// </summary>
    public class UpdateMaterialModelDto
    {
        [Required(ErrorMessage = "型号名称不能为空")]
        [StringLength(100, ErrorMessage = "型号名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "型号编码不能为空")]
        [StringLength(50, ErrorMessage = "型号编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        [Required(ErrorMessage = "分类不能为空")]
        [StringLength(50, ErrorMessage = "分类长度不能超过50个字符")]
        public string Category { get; set; } = string.Empty;

        [Required(ErrorMessage = "规格参数不能为空")]
        [StringLength(500, ErrorMessage = "规格参数长度不能超过500个字符")]
        public string Specification { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "状态长度不能超过20个字符")]
        public string Status { get; set; } = "启用";

        [StringLength(500, ErrorMessage = "图片URL长度不能超过500个字符")]
        public string Image { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "单位长度不能超过20个字符")]
        public string Unit { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "材质长度不能超过100个字符")]
        public string Material { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "尺寸长度不能超过100个字符")]
        public string Size { get; set; } = string.Empty;

        // 销售信息
        public decimal? SalesPrice { get; set; }
        public string SalesUnit { get; set; } = string.Empty;
        public decimal? MinOrderQty { get; set; }
        public decimal? MaxOrderQty { get; set; }
        public string LeadTime { get; set; } = string.Empty;
        public string Warranty { get; set; } = string.Empty;

        // 仓库信息
        public string StorageLocation { get; set; } = string.Empty;
        public decimal? SafetyStock { get; set; }
        public decimal? MaxStock { get; set; }
        public decimal? ReorderPoint { get; set; }
        public string StorageCondition { get; set; } = string.Empty;
        public string ShelfLife { get; set; } = string.Empty;

        // 财务信息
        public decimal? StandardCost { get; set; }
        public decimal? AverageCost { get; set; }
        public string ValuationMethod { get; set; } = string.Empty;
        public decimal? TaxRate { get; set; }
        public string AccountSubject { get; set; } = string.Empty;
        public string CostCenter { get; set; } = string.Empty;

        // 生产信息
        public string ProductionType { get; set; } = string.Empty;
        public string ProductionLeadTime { get; set; } = string.Empty;
        public string SetupTime { get; set; } = string.Empty;
        public string CycleTime { get; set; } = string.Empty;
        public string BatchSize { get; set; } = string.Empty;
        public string WorkCenter { get; set; } = string.Empty;
        public string QualityStandard { get; set; } = string.Empty;

        // 采购信息
        public string Supplier { get; set; } = string.Empty;
        public decimal? PurchasePrice { get; set; }
        public string PurchaseUnit { get; set; } = string.Empty;
        public decimal? MinPurchaseQty { get; set; }
        public string PurchaseLeadTime { get; set; } = string.Empty;
        public string QualityLevel { get; set; } = string.Empty;
        public string PurchaseNote { get; set; } = string.Empty;

        // 进出口信息
        public string HsCode { get; set; } = string.Empty;
        public string OriginCountry { get; set; } = string.Empty;
        public decimal? ImportTaxRate { get; set; }
        public decimal? ExportTaxRefund { get; set; }
        public bool IsDangerous { get; set; } = false;
        public string TransportMode { get; set; } = string.Empty;
        public string PackingRequirement { get; set; } = string.Empty;
        public bool InspectionRequired { get; set; } = false;
        public string LicenseRequirement { get; set; } = string.Empty;

        // MRP计划信息
        public string PlanningStrategy { get; set; } = string.Empty;
        public string PlanningCycle { get; set; } = string.Empty;
        public string ForecastMethod { get; set; } = string.Empty;
        public string AbcCategory { get; set; } = string.Empty;
        public string DemandSource { get; set; } = string.Empty;
        public string Planner { get; set; } = string.Empty;
        public string SafetyStockDays { get; set; } = string.Empty;
        public string LotSizeRule { get; set; } = string.Empty;
        public string DemandTimeFence { get; set; } = string.Empty;
        public string SupplyTimeFence { get; set; } = string.Empty;
        public string PlanningNote { get; set; } = string.Empty;
    }

    /// <summary>
    /// 物料型号分页结果DTO
    /// </summary>
    public class PagedMaterialModelResultDto
    {
        public List<MaterialModelListDto> List { get; set; } = new List<MaterialModelListDto>();
        public int Total { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    /// <summary>
    /// 物料分类DTO
    /// </summary>
    public class MaterialCategoryDto
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Sort { get; set; }
    }
} 