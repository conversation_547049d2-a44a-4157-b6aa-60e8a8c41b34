# 多语言管理功能使用指南

## 概述

多语言管理功能为MES系统提供了完整的国际化解决方案，支持多种语言的翻译管理、文本提取、导入导出等功能。

## 功能特性

### 1. 核心功能
- ✅ 多语言翻译键值对管理
- ✅ 可视化的翻译编辑界面
- ✅ 支持中文、英文等多种语言
- ✅ 翻译完成度统计
- ✅ 按模块分类管理

### 2. 文本提取功能
- ✅ 自动从页面组件中提取文本
- ✅ 支持多种文本类型（标题、按钮、标签、占位符等）
- ✅ 自动生成翻译键名
- ✅ 批量添加到翻译列表

### 3. 导入导出功能
- ✅ 支持JSON格式的导入导出
- ✅ 支持Excel格式的导入导出（规划中）
- ✅ 支持CSV格式的导入导出（规划中）
- ✅ 多语言数据批量处理

### 4. 搜索和筛选
- ✅ 按关键词搜索翻译内容
- ✅ 按模块筛选
- ✅ 按翻译状态筛选
- ✅ 分页显示

## 使用方法

### 1. 访问多语言管理页面

通过左侧导航菜单：**系统管理** > **多语言管理** 进入管理页面。

### 2. 基本操作

#### 新增翻译
1. 点击页面顶部的"新增翻译"按钮
2. 填写翻译键（如：`login.title`）
3. 选择所属模块
4. 填写各语言的翻译内容
5. 点击确定保存

#### 编辑翻译
1. 在翻译列表中找到要编辑的项目
2. 可以直接在表格中编辑翻译内容
3. 或点击"编辑"按钮打开编辑对话框
4. 修改后自动保存

#### 删除翻译
1. 点击翻译项目的"删除"按钮
2. 确认删除操作

### 3. 文本提取功能

#### 使用文本提取
1. 点击"提取文本"按钮
2. 选择要提取文本的页面
3. 选择提取规则（标题、按钮、标签等）
4. 设置模块前缀
5. 点击"开始提取"
6. 预览提取结果
7. 点击"批量添加到翻译列表"

#### 提取规则说明
- **titles**: 提取页面标题（h1, h2, h3等）
- **buttons**: 提取按钮文本
- **labels**: 提取标签文本
- **placeholders**: 提取输入框占位符文本
- **messages**: 提取消息提示文本
- **tooltips**: 提取工具提示文本

### 4. 导入导出功能

#### 导出翻译数据
1. 点击"导入/导出"按钮
2. 选择"导出"标签页
3. 选择导出格式（JSON/Excel/CSV）
4. 选择要导出的语言
5. 点击"开始导出"

#### 导入翻译数据
1. 点击"导入/导出"按钮
2. 选择"导入"标签页
3. 选择翻译文件
4. 点击"开始导入"

### 5. 搜索和筛选

- **关键词搜索**: 在搜索框中输入翻译键或翻译内容
- **模块筛选**: 选择特定模块查看相关翻译
- **状态筛选**: 
  - 已完成: 所有语言都有翻译
  - 未完成: 部分语言有翻译
  - 缺失翻译: 没有任何语言翻译

## 在组件中使用多语言

### 1. 模板中使用

```vue
<template>
  <div>
    <!-- 基本用法 -->
    <h1>{{ $t('login.title') }}</h1>
    
    <!-- 按钮中使用 -->
    <el-button type="primary">{{ $t('common.save') }}</el-button>
    
    <!-- 带参数的用法 -->
    <p>{{ $t('common.welcome', { name: userName }) }}</p>
    
    <!-- 表单标签 -->
    <el-form-item :label="$t('user.username')">
      <el-input v-model="form.username" :placeholder="$t('user.usernamePlaceholder')" />
    </el-form-item>
  </div>
</template>
```

### 2. 脚本中使用

```javascript
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'

export default {
  setup() {
    const { t } = useI18n()
    
    // 消息提示
    const showSuccess = () => {
      ElMessage.success(t('common.success'))
    }
    
    // 确认对话框
    const handleDelete = async () => {
      try {
        await ElMessageBox.confirm(
          t('common.confirmDelete'),
          t('common.warning'),
          {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: 'warning'
          }
        )
        // 执行删除操作
      } catch {
        // 用户取消
      }
    }
    
    return {
      showSuccess,
      handleDelete
    }
  }
}
```

### 3. 全局使用

```javascript
import i18n from '@/i18n'

// 在非组件代码中使用
const message = i18n.global.t('common.error')
```

## 翻译键命名规范

### 1. 命名格式
使用点号分隔的层级结构：`模块.功能.具体项`

### 2. 模块分类
- `common`: 通用翻译（按钮、状态、消息等）
- `login`: 登录相关
- `menu`: 菜单项
- `system`: 系统管理
- `user`: 用户管理
- `role`: 角色管理
- `hr`: 人事管理
- `production`: 生产管理
- 等等...

### 3. 命名示例
```
common.add              // 新增
common.edit             // 编辑
common.delete           // 删除
login.title             // 登录页标题
user.management         // 用户管理
user.addUser            // 新增用户
user.editUser           // 编辑用户
system.menuManagement   // 菜单管理
```

## 数据结构

### 翻译数据格式
```json
{
  "key": "login.title",
  "module": "login",
  "description": "登录页面的标题",
  "translations": {
    "zh": "MES系统登录",
    "en": "MES System Login"
  }
}
```

### 导出格式示例
```json
{
  "zh": {
    "login": {
      "title": "MES系统登录",
      "username": "请输入用户名"
    },
    "common": {
      "add": "新增",
      "edit": "编辑"
    }
  },
  "en": {
    "login": {
      "title": "MES System Login",
      "username": "Please enter username"
    },
    "common": {
      "add": "Add",
      "edit": "Edit"
    }
  }
}
```

## 最佳实践

### 1. 翻译键管理
- 使用有意义的键名，避免过于简化
- 保持层级结构清晰
- 相关功能的翻译放在同一模块下

### 2. 翻译内容
- 保持翻译内容的一致性
- 注意上下文的准确性
- 考虑文本长度对界面的影响

### 3. 开发流程
1. 开发时使用翻译键而不是硬编码文本
2. 完成功能后使用文本提取功能生成翻译键
3. 找专业翻译人员完善翻译内容
4. 使用导入导出功能处理翻译数据

### 4. 性能优化
- 合理使用懒加载
- 避免在循环中频繁调用翻译函数
- 考虑缓存常用翻译内容

## 故障排除

### 1. 翻译不显示
- 检查翻译键是否正确
- 确认对应语言的翻译是否存在
- 检查i18n配置是否正确

### 2. 语言切换无效
- 确认locale设置是否正确
- 检查localStorage中的语言设置
- 验证组件是否正确使用响应式翻译

### 3. 文本提取失败
- 确认页面路径是否正确
- 检查提取规则设置
- 验证页面组件结构

## 扩展功能

### 1. 添加新语言
1. 在`supportedLanguages`数组中添加新语言配置
2. 在i18n配置中添加对应的消息对象
3. 为新语言添加翻译内容

### 2. 自定义提取规则
可以根据项目需要扩展文本提取规则，支持更多类型的文本识别。

### 3. 集成翻译服务
可以集成第三方翻译服务（如Google Translate API）实现自动翻译功能。

## 技术支持

如有问题或建议，请联系开发团队或在项目仓库中提交Issue。 