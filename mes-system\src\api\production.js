import request from '@/utils/request'

// 获取产线列表
export function getProductionLines(page = 1, pageSize = 10, includeWorkCenters = false) {
  return request({
    url: '/api/production-lines',
    method: 'get',
    timeout: 10000, // 10秒超时
    params: {
      page,
      pageSize,
      includeWorkCenters
    }
  })
}

// 根据产线ID获取工作中心列表（包含设备信息）
export function getWorkCentersByProductionLine(productionLineId, includeEquipment = true) {
  return request({
    url: `/api/work-centers/by-production-line/${productionLineId}`,
    method: 'get',
    timeout: 15000, // 15秒超时，因为数据较多
    params: {
      includeEquipment
    }
  })
}

// 创建新产线
export function createProductionLine(data) {
  return request({
    url: '/api/production-lines',
    method: 'post',
    timeout: 8000, // 8秒超时
    data
  })
}

// 更新产线信息
export function updateProductionLine(id, data) {
  return request({
    url: `/api/production-lines/${id}`,
    method: 'put',
    timeout: 8000, // 8秒超时
    data
  })
}

// 删除产线
export function deleteProductionLine(id) {
  return request({
    url: `/api/production-lines/${id}`,
    method: 'delete',
    timeout: 8000, // 8秒超时
  })
}

// 创建工作中心
export function createWorkCenter(productionLineId, data) {
  return request({
    url: `/api/work-centers/production-line/${productionLineId}`,
    method: 'post',
    timeout: 8000, // 8秒超时
    data
  })
}

// 更新工作中心
export function updateWorkCenter(id, data) {
  return request({
    url: `/api/work-centers/${id}`,
    method: 'put',
    timeout: 8000, // 8秒超时
    data
  })
}

// 删除工作中心
export function deleteWorkCenter(id) {
  return request({
    url: `/api/work-centers/${id}`,
    method: 'delete',
    timeout: 8000, // 8秒超时
  })
}

// 创建设备
export function createEquipment(workCenterId, data) {
  return request({
    url: `/api/equipment/work-center/${workCenterId}`,
    method: 'post',
    timeout: 8000, // 8秒超时
    data
  })
}

// 更新设备
export function updateEquipment(id, data) {
  return request({
    url: `/api/equipment/${id}`,
    method: 'put',
    timeout: 8000, // 8秒超时
    data
  })
}

// 删除设备
export function deleteEquipment(id) {
  return request({
    url: `/api/equipment/${id}`,
    method: 'delete',
    timeout: 8000, // 8秒超时
  })
} 