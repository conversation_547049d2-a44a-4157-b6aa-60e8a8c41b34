<template>
  <PageTemplate
    ref="pageTemplateRef"
    page-title="示例管理"
    entity-name="示例"
    add-button-text="新增示例"
    :search-fields="searchFields"
    :table-columns="tableColumns"
    :form-fields="formFields"
    :form-rules="formRules"
    :actions="['edit', 'view', 'delete']"
    @search="handleSearch"
    @add="handleAdd"
    @edit="handleEdit"
    @view="handleView"
    @delete="handleDelete"
    @submit="handleSubmit"
    @refresh="loadData"
  >
    <!-- 状态列插槽 -->
    <template #status="{ row }">
      <el-tag :type="row.status ? 'success' : 'danger'">
        {{ row.status ? '启用' : '禁用' }}
      </el-tag>
    </template>
    
    <!-- 类型列插槽 -->
    <template #type="{ row }">
      <el-tag :type="getTypeTagType(row.type)">
        {{ getTypeLabel(row.type) }}
      </el-tag>
    </template>
    
    <!-- 额外操作按钮 -->
    <template #extra-actions>
      <el-button type="warning" :icon="Download" @click="handleExport">
        导出
      </el-button>
      <el-button type="info" :icon="Upload" @click="handleImport">
        导入
      </el-button>
    </template>
    
    <!-- 自定义操作列 -->
    <template #custom-actions="{ row }">
      <el-button 
        type="warning" 
        size="small" 
        @click="handleCopy(row)"
      >
        复制
      </el-button>
    </template>
  </PageTemplate>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Upload } from '@element-plus/icons-vue'
import PageTemplate from '@/components/PageTemplate.vue'

// 页面模板引用
const pageTemplateRef = ref(null)

// 搜索字段配置
const searchFields = [
  {
    prop: 'name',
    label: '名称',
    type: 'input',
    placeholder: '请输入示例名称'
  },
  {
    prop: 'type',
    label: '类型',
    type: 'select',
    options: [
      { label: '类型A', value: 'A' },
      { label: '类型B', value: 'B' },
      { label: '类型C', value: 'C' }
    ]
  },
  {
    prop: 'status',
    label: '状态',
    type: 'select',
    options: [
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ]
  },
  {
    prop: 'createTime',
    label: '创建时间',
    type: 'daterange'
  }
]

// 表格列配置
const tableColumns = [
  {
    prop: 'id',
    label: 'ID',
    width: '80'
  },
  {
    prop: 'name',
    label: '名称',
    minWidth: '150'
  },
  {
    prop: 'code',
    label: '编码',
    minWidth: '120'
  },
  {
    prop: 'type',
    label: '类型',
    width: '100',
    slot: 'type'
  },
  {
    prop: 'status',
    label: '状态',
    width: '100',
    slot: 'status'
  },
  {
    prop: 'description',
    label: '描述',
    minWidth: '200'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: '180'
  }
]

// 表单字段配置
const formFields = [
  {
    prop: 'name',
    label: '名称',
    type: 'input',
    placeholder: '请输入示例名称'
  },
  {
    prop: 'code',
    label: '编码',
    type: 'input',
    placeholder: '请输入示例编码'
  },
  {
    prop: 'type',
    label: '类型',
    type: 'select',
    options: [
      { label: '类型A', value: 'A' },
      { label: '类型B', value: 'B' },
      { label: '类型C', value: 'C' }
    ]
  },
  {
    prop: 'priority',
    label: '优先级',
    type: 'number',
    min: 1,
    max: 10,
    defaultValue: 1
  },
  {
    prop: 'status',
    label: '状态',
    type: 'switch',
    defaultValue: true
  },
  {
    prop: 'description',
    label: '描述',
    type: 'textarea',
    placeholder: '请输入示例描述'
  }
]

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入示例名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入示例编码', trigger: 'blur' },
    { 
      pattern: /^[A-Z][A-Z0-9_]*$/, 
      message: '编码必须以大写字母开头，只能包含大写字母、数字和下划线', 
      trigger: 'blur' 
    }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ]
}

// 获取类型标签样式
const getTypeTagType = (type) => {
  const typeMap = {
    'A': 'primary',
    'B': 'success',
    'C': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取类型标签文本
const getTypeLabel = (type) => {
  const labelMap = {
    'A': '类型A',
    'B': '类型B',
    'C': '类型C'
  }
  return labelMap[type] || '未知'
}

// 模拟数据
const mockData = [
  {
    id: 1,
    name: '示例数据1',
    code: 'EXAMPLE_001',
    type: 'A',
    status: true,
    description: '这是第一个示例数据',
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    name: '示例数据2',
    code: 'EXAMPLE_002',
    type: 'B',
    status: false,
    description: '这是第二个示例数据',
    createTime: '2024-01-14 15:20:00'
  },
  {
    id: 3,
    name: '示例数据3',
    code: 'EXAMPLE_003',
    type: 'C',
    status: true,
    description: '这是第三个示例数据',
    createTime: '2024-01-13 09:45:00'
  }
]

// 加载数据
const loadData = async (params = {}) => {
  try {
    pageTemplateRef.value?.setLoading(true)
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟搜索过滤
    let filteredData = [...mockData]
    if (params.name) {
      filteredData = filteredData.filter(item => 
        item.name.includes(params.name)
      )
    }
    if (params.type) {
      filteredData = filteredData.filter(item => item.type === params.type)
    }
    if (params.status !== undefined && params.status !== '') {
      filteredData = filteredData.filter(item => item.status === params.status)
    }
    
    pageTemplateRef.value?.setDataList(filteredData)
    pageTemplateRef.value?.setPagination(1, 20, filteredData.length)
  } catch (error) {
    console.error('加载数据失败:', error)
    pageTemplateRef.value?.showError('加载数据失败')
  } finally {
    pageTemplateRef.value?.setLoading(false)
  }
}

// 搜索处理
const handleSearch = (searchParams) => {
  console.log('搜索参数:', searchParams)
  loadData(searchParams)
}

// 新增处理
const handleAdd = () => {
  console.log('新增示例')
}

// 编辑处理
const handleEdit = (row) => {
  console.log('编辑示例:', row)
}

// 查看处理
const handleView = (row) => {
  ElMessage.info(`查看示例: ${row.name}`)
}

// 删除处理
const handleDelete = async (row) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    pageTemplateRef.value?.showSuccess('删除成功')
    loadData()
  } catch (error) {
    console.error('删除失败:', error)
    pageTemplateRef.value?.showError('删除失败')
  }
}

// 表单提交处理
const handleSubmit = async ({ type, data }) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    if (type === 'add') {
      pageTemplateRef.value?.showSuccess('新增成功')
    } else {
      pageTemplateRef.value?.showSuccess('编辑成功')
    }
    
    pageTemplateRef.value?.closeDialog()
    loadData()
  } catch (error) {
    console.error('保存失败:', error)
    pageTemplateRef.value?.showError('保存失败')
  } finally {
    pageTemplateRef.value?.setSubmitting(false)
  }
}

// 复制处理
const handleCopy = (row) => {
  ElMessage.success(`复制示例: ${row.name}`)
}

// 导出处理
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 导入处理
const handleImport = () => {
  ElMessage.info('导入功能开发中...')
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
/* 自定义样式 */
</style> 