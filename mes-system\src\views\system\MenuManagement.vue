<template>
  <div class="menu-management">
    <div class="page-header">
      <h2 class="page-title">{{ $t('menuManagement.management') }}</h2>
      <div class="page-actions">
        <el-button type="primary" :icon="Plus" @click="handleAddMenu">{{ $t('menuManagement.addMenu') }}</el-button>
        <el-button type="warning" :icon="Upload" @click="handleInitializeMenus" :loading="initializing">
          {{ initializing ? $t('menuManagement.initializing') : $t('menuManagement.initializeMenus') }}
        </el-button>
        <el-button type="success" :icon="RefreshRight" @click="refreshMenuList">{{ $t('menuManagement.refresh') }}</el-button>
      </div>
    </div>

    <!-- 菜单列表 -->
    <el-table
      v-loading="loading"
      :data="menuList"
      row-key="id"
      border
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      style="width: 100%"
    >
      <el-table-column prop="name" :label="$t('menuManagement.menuName')" min-width="200">
        <template #default="{ row }">
          <div class="menu-name">
            <el-icon v-if="row.icon"><component :is="getIconComponent(row.icon)" /></el-icon>
            <span>{{ row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="menuId" :label="$t('menuManagement.menuId')" width="150" />
      <el-table-column prop="path" :label="$t('menuManagement.path')" min-width="200" />
      <el-table-column prop="type" :label="$t('menuManagement.type')" width="100">
        <template #default="{ row }">
          <el-tag :type="row.type === 'menu-item' ? 'primary' : 'success'">
            {{ row.type === 'menu-item' ? $t('menuManagement.menuItem') : $t('menuManagement.subMenu') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="sortOrder" :label="$t('menuManagement.sortOrder')" width="100" />
      <el-table-column :label="$t('menuManagement.status')" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status ? 'success' : 'danger'">
            {{ row.status ? $t('menuManagement.enabled') : $t('menuManagement.disabled') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('menuManagement.operation')" width="300" align="center" fixed="right">
        <template #default="{ row }">
          <el-button-group>
            <el-button
              type="primary"
              size="small"
              @click="handleEditMenu(row)"
            >
              {{ $t('menuManagement.edit') }}
            </el-button>
            <el-button
              v-if="row.type === 'sub-menu'"
              type="success"
              size="small"
              @click="handleAddChildMenu(row)"
            >
              {{ $t('menuManagement.addChild') }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDeleteMenu(row)"
            >
              {{ $t('menuManagement.delete') }}
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 菜单表单对话框 -->
    <el-dialog
      v-model="menuDialogVisible"
      :title="getDialogTitle()"
      width="600px"
      @closed="resetMenuForm"
    >
      <el-form
        ref="menuFormRef"
        :model="menuForm"
        :rules="menuRules"
        label-width="100px"
      >
        <el-form-item :label="$t('menuManagement.parentMenu')" v-if="menuDialogType === 'addChild'">
          <el-input v-model="parentMenuName" disabled />
        </el-form-item>
        <el-form-item :label="$t('menuManagement.menuName')" prop="name">
          <el-input v-model="menuForm.name" :placeholder="$t('menuManagement.enterMenuName')" />
        </el-form-item>
        <el-form-item :label="$t('menuManagement.menuId')" prop="menuId">
          <el-input 
            v-model="menuForm.menuId" 
            :disabled="menuDialogType === 'edit'" 
            :placeholder="$t('menuManagement.enterMenuId')" 
          />
        </el-form-item>
        <el-form-item :label="$t('menuManagement.menuType')" prop="type">
          <el-radio-group v-model="menuForm.type">
            <el-radio label="menu-item">{{ $t('menuManagement.menuItem') }}</el-radio>
            <el-radio label="sub-menu" :disabled="menuDialogType === 'addChild'">{{ $t('menuManagement.subMenu') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('menuManagement.menuPath')" prop="path" v-if="menuForm.type === 'menu-item'">
          <el-input v-model="menuForm.path" :placeholder="$t('menuManagement.pathPlaceholder')" />
        </el-form-item>
        <el-form-item :label="$t('menuManagement.menuIcon')" prop="icon">
          <el-select v-model="menuForm.icon" :placeholder="$t('menuManagement.selectIcon')" filterable>
            <el-option
              v-for="icon in availableIcons"
              :key="icon.name"
              :label="icon.label"
              :value="icon.name"
            >
              <div class="icon-option">
                <el-icon><component :is="icon.component" /></el-icon>
                <span>{{ icon.label }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('menuManagement.sortNumber')" prop="sortOrder">
          <el-input-number v-model="menuForm.sortOrder" :min="0" />
        </el-form-item>
        <el-form-item :label="$t('menuManagement.menuStatus')" prop="status">
          <el-switch v-model="menuForm.status" :active-text="$t('menuManagement.enabled')" :inactive-text="$t('menuManagement.disabled')" />
        </el-form-item>
        <el-form-item :label="$t('menuManagement.menuDescription')" prop="description">
          <el-input 
            v-model="menuForm.description" 
            type="textarea" 
            :rows="3" 
            :placeholder="$t('menuManagement.enterDescription')" 
          />
        </el-form-item>
        
        <!-- 页面生成选项 -->
        <el-form-item 
          v-if="menuForm.type === 'menu-item' && (menuDialogType === 'add' || menuDialogType === 'addChild')" 
          :label="$t('menuManagement.pageGeneration')"
        >
          <el-switch 
            v-model="generatePageFile" 
            :active-text="$t('menuManagement.autoGeneratePageFile')" 
            :inactive-text="$t('menuManagement.noGeneratePageFile')"
            @change="updatePagePreview"
          />
          <div class="generate-tip">
            <el-text type="info" size="small">
              {{ $t('menuManagement.generateTip') }}
            </el-text>
          </div>
        </el-form-item>
        
        <!-- 页面预览 -->
        <el-form-item v-if="pagePreviewData" :label="$t('menuManagement.pagePreview')">
          <el-card class="preview-card" shadow="never">
            <template #header>
              <div class="preview-header">
                <span>{{ pagePreviewData.pageName }} {{ $t('menuManagement.pagePreview') }}</span>
                <el-button type="primary" text @click="showPagePreview = true">
                  {{ $t('menuManagement.viewDetails') }}
                </el-button>
              </div>
            </template>
            <div class="preview-content">
              <div class="preview-item">
                <span class="preview-label">{{ $t('menuManagement.componentName') }}：</span>
                <el-tag>{{ pagePreviewData.componentName }}</el-tag>
              </div>
              <div class="preview-item">
                <span class="preview-label">{{ $t('menuManagement.vueFile') }}：</span>
                <el-text type="info" size="small">{{ pagePreviewData.files.vue }}</el-text>
              </div>
              <div class="preview-item">
                <span class="preview-label">{{ $t('menuManagement.pageFeatures') }}：</span>
                <div class="feature-tags">
                  <el-tag 
                    v-for="feature in pagePreviewData.features.slice(0, 3)" 
                    :key="feature" 
                    size="small" 
                    type="success"
                  >
                    {{ feature }}
                  </el-tag>
                  <el-text v-if="pagePreviewData.features.length > 3" type="info" size="small">
                    {{ $t('menuManagement.featureCount', { count: pagePreviewData.features.length }) }}
                  </el-text>
                </div>
              </div>
            </div>
          </el-card>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="menuDialogVisible = false">{{ $t('menuManagement.cancel') }}</el-button>
          <el-button type="primary" @click="handleMenuSubmit" :loading="submitting">
            {{ submitting ? $t('menuManagement.saving') : $t('menuManagement.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  
  <!-- 页面预览详情对话框 -->
  <el-dialog
    v-model="showPagePreview"
    :title="$t('menuManagement.pageGenerationDetails')"
    width="800px"
    :append-to-body="true"
  >
    <div v-if="pagePreviewData" class="page-preview-detail">
      <!-- 基本信息 -->
      <el-card class="preview-section" shadow="never">
        <template #header>
          <h3>{{ $t('menuManagement.basicInfo') }}</h3>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item :label="$t('menuManagement.pageName')">{{ pagePreviewData.pageName }}</el-descriptions-item>
          <el-descriptions-item :label="$t('menuManagement.componentName')">{{ pagePreviewData.componentName }}</el-descriptions-item>
          <el-descriptions-item :label="$t('menuManagement.vueFile')" span="2">
            <el-text type="primary">{{ pagePreviewData.files.vue }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('menuManagement.apiFile')" span="2">
            <el-text type="primary">{{ pagePreviewData.files.api }}</el-text>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 页面功能 -->
      <el-card class="preview-section" shadow="never">
        <template #header>
          <h3>{{ $t('menuManagement.pageFeatures') }}</h3>
        </template>
        <div class="feature-list">
          <el-tag 
            v-for="feature in pagePreviewData.features" 
            :key="feature" 
            class="feature-tag"
            type="success"
          >
            <el-icon><Check /></el-icon>
            {{ feature }}
          </el-tag>
        </div>
      </el-card>
      
      <!-- API接口 -->
      <el-card class="preview-section" shadow="never">
        <template #header>
          <h3>{{ $t('menuManagement.apiEndpoints') }}</h3>
        </template>
        <div class="api-list">
          <div 
            v-for="(endpoint, index) in pagePreviewData.apiEndpoints" 
            :key="index" 
            class="api-item"
          >
            <el-tag 
              :type="getApiMethodType(endpoint)" 
              size="small"
              class="api-method"
            >
              {{ getApiMethod(endpoint) }}
            </el-tag>
            <el-text class="api-path">{{ getApiPath(endpoint) }}</el-text>
            <el-text type="info" size="small" class="api-desc">{{ getApiDesc(endpoint) }}</el-text>
          </div>
        </div>
      </el-card>
      
      <!-- 使用说明 -->
      <el-card class="preview-section" shadow="never">
        <template #header>
          <h3>{{ $t('menuManagement.usageInstructions') }}</h3>
        </template>
        <div class="usage-info">
          <el-alert
            :title="$t('menuManagement.pageWithCrudFeatures')"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <ul class="usage-list">
                <li>{{ $t('menuManagement.featureDescriptions.template') }}</li>
                <li>{{ $t('menuManagement.featureDescriptions.crud') }}</li>
                <li>{{ $t('menuManagement.featureDescriptions.api') }}</li>
                <li>{{ $t('menuManagement.featureDescriptions.validation') }}</li>
                <li>{{ $t('menuManagement.featureDescriptions.responsive') }}</li>
                <li>{{ $t('menuManagement.featureDescriptions.customizable') }}</li>
              </ul>
            </template>
          </el-alert>
        </div>
      </el-card>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showPagePreview = false">{{ $t('menuManagement.close') }}</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 生成文件结果对话框 -->
  <el-dialog
    v-model="showGeneratedFiles"
    :title="$t('menuManagement.generatedFilesResult')"
    width="800px"
    :append-to-body="true"
  >
    <div v-if="generatedFilesData" class="generated-files-detail">
      <!-- 基本信息 -->
      <el-card class="preview-section" shadow="never">
        <template #header>
          <h3>{{ $t('menuManagement.basicInfo') }}</h3>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item :label="$t('menuManagement.pageName')">{{ generatedFilesData.pageName }}</el-descriptions-item>
          <el-descriptions-item :label="$t('menuManagement.componentName')">{{ generatedFilesData.componentName }}</el-descriptions-item>
          <el-descriptions-item :label="$t('menuManagement.vueFile')" span="2">
            <el-text type="primary">{{ generatedFilesData.files.vue }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('menuManagement.apiFile')" span="2">
            <el-text type="primary">{{ generatedFilesData.files.api }}</el-text>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 页面功能 -->
      <el-card class="preview-section" shadow="never">
        <template #header>
          <h3>{{ $t('menuManagement.pageFeatures') }}</h3>
        </template>
        <div class="feature-list">
          <el-tag 
            v-for="feature in generatedFilesData.features" 
            :key="feature" 
            class="feature-tag"
            type="success"
          >
            <el-icon><Check /></el-icon>
            {{ feature }}
          </el-tag>
        </div>
      </el-card>
      
      <!-- API接口 -->
      <el-card class="preview-section" shadow="never">
        <template #header>
          <h3>{{ $t('menuManagement.apiEndpoints') }}</h3>
        </template>
        <div class="api-list">
          <div 
            v-for="(endpoint, index) in generatedFilesData.apiEndpoints" 
            :key="index" 
            class="api-item"
          >
            <el-tag 
              :type="getApiMethodType(endpoint)" 
              size="small"
              class="api-method"
            >
              {{ getApiMethod(endpoint) }}
            </el-tag>
            <el-text class="api-path">{{ getApiPath(endpoint) }}</el-text>
            <el-text type="info" size="small" class="api-desc">{{ getApiDesc(endpoint) }}</el-text>
          </div>
        </div>
      </el-card>
      
      <!-- 使用说明 -->
      <el-card class="preview-section" shadow="never">
        <template #header>
          <h3>{{ $t('menuManagement.usageInstructions') }}</h3>
        </template>
        <div class="usage-info">
          <el-alert
            :title="$t('menuManagement.pageWithCrudFeatures')"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <ul class="usage-list">
                <li>{{ $t('menuManagement.featureDescriptions.template') }}</li>
                <li>{{ $t('menuManagement.featureDescriptions.crud') }}</li>
                <li>{{ $t('menuManagement.featureDescriptions.api') }}</li>
                <li>{{ $t('menuManagement.featureDescriptions.validation') }}</li>
                <li>{{ $t('menuManagement.featureDescriptions.responsive') }}</li>
                <li>{{ $t('menuManagement.featureDescriptions.customizable') }}</li>
              </ul>
            </template>
          </el-alert>
        </div>
      </el-card>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showGeneratedFiles = false">{{ $t('menuManagement.close') }}</el-button>
        <el-button type="primary" @click="handleDownloadFile('vue')" :loading="submitting">
          {{ $t('menuManagement.downloadVueFile') }}
        </el-button>
        <el-button type="primary" @click="handleDownloadFile('api')" :loading="submitting">
          {{ $t('menuManagement.downloadApiFile') }}
        </el-button>
        <el-button type="primary" @click="handleCopyCode('vue')" :loading="submitting">
          {{ $t('menuManagement.copyVueCode') }}
        </el-button>
        <el-button type="primary" @click="handleCopyCode('api')" :loading="submitting">
          {{ $t('menuManagement.copyApiCode') }}
        </el-button>
        <el-button type="primary" @click="handleCopyRouteCode()" :loading="submitting">
          {{ $t('menuManagement.copyRouteCode') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  Plus,
  RefreshRight,
  Monitor,
  Setting,
  Files,
  Operation,
  List,
  Odometer,
  Calendar,
  Document,
  VideoCamera,
  Aim,
  Check,
  DataAnalysis,
  Box,
  Goods,
  Collection,
  Tools,
  Platform,
  Service,
  UserFilled,
  User,
  Money,
  Avatar,
  Lock,
  OfficeBuilding,
  Upload,
  Notebook,
  Grid,
  Menu
} from '@element-plus/icons-vue'
import { 
  getMenuTree, 
  createMenu, 
  updateMenu, 
  deleteMenu,
  initializeMenus
} from '@/api/menu'
import eventBus from '@/utils/eventBus'
import { generatePagePreview } from '@/utils/pageTemplateGenerator'

// 初始化i18n
const { t } = useI18n()

// 可用图标列表
const availableIcons = [
  { name: 'Monitor', label: '监控器', component: Monitor },
  { name: 'Setting', label: '设置', component: Setting },
  { name: 'Files', label: '文件', component: Files },
  { name: 'Operation', label: '操作', component: Operation },
  { name: 'List', label: '列表', component: List },
  { name: 'Odometer', label: '里程表', component: Odometer },
  { name: 'Calendar', label: '日历', component: Calendar },
  { name: 'Document', label: '文档', component: Document },
  { name: 'VideoCamera', label: '摄像头', component: VideoCamera },
  { name: 'Aim', label: '目标', component: Aim },
  { name: 'Check', label: '检查', component: Check },
  { name: 'DataAnalysis', label: '数据分析', component: DataAnalysis },
  { name: 'Box', label: '盒子', component: Box },
  { name: 'Goods', label: '商品', component: Goods },
  { name: 'Collection', label: '收藏', component: Collection },
  { name: 'Tools', label: '工具', component: Tools },
  { name: 'Platform', label: '平台', component: Platform },
  { name: 'Service', label: '服务', component: Service },
  { name: 'UserFilled', label: '用户(填充)', component: UserFilled },
  { name: 'User', label: '用户', component: User },
  { name: 'Money', label: '金钱', component: Money },
  { name: 'Avatar', label: '头像', component: Avatar },
  { name: 'Lock', label: '锁', component: Lock },
  { name: 'OfficeBuilding', label: '办公楼', component: OfficeBuilding },
  { name: 'Notebook', label: '笔记本', component: Notebook },
  { name: 'Grid', label: '网格', component: Grid },
  { name: 'Menu', label: '菜单', component: Menu }
]

// 数据状态
const loading = ref(false)
const submitting = ref(false)
const initializing = ref(false)
const menuList = ref([])
const menuDialogVisible = ref(false)
const menuDialogType = ref('add') // add, edit, addChild
const menuFormRef = ref(null)
const parentMenuName = ref('')
const parentMenuId = ref(null)

// 页面生成预览相关
const showPagePreview = ref(false)
const pagePreviewData = ref(null)

// 菜单表单
const menuForm = reactive({
  menuId: '',
  name: '',
  type: 'menu-item',
  path: '',
  icon: '',
  sortOrder: 0,
  status: true,
  description: '',
  parentId: null
})

// 表单验证规则
const menuRules = reactive({
  name: [
    { required: true, message: () => t('menuManagement.menuNameRequired'), trigger: 'blur' },
    { min: 2, max: 50, message: () => t('menuManagement.menuNameLength'), trigger: 'blur' }
  ],
  menuId: [
    { required: true, message: () => t('menuManagement.menuIdRequired'), trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z][a-zA-Z0-9-_]*$/, 
      message: () => t('menuManagement.menuIdPattern'), 
      trigger: 'blur' 
    }
  ],
  type: [
    { required: true, message: () => t('menuManagement.menuTypeRequired'), trigger: 'change' }
  ],
  path: [
    { 
      validator: (rule, value, callback) => {
        if (menuForm.type === 'menu-item' && !value) {
          callback(new Error(t('menuManagement.menuPathRequired')))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  icon: [
    { required: true, message: () => t('menuManagement.menuIconRequired'), trigger: 'change' }
  ],
  sortOrder: [
    { required: true, message: () => t('menuManagement.sortOrderRequired'), trigger: 'blur' }
  ]
})

// 是否生成页面文件
const generatePageFile = ref(false)

// 监听表单字段变化，更新页面预览
const updatePagePreview = () => {
  if (menuForm.type === 'menu-item' && menuForm.path && generatePageFile.value) {
    try {
      pagePreviewData.value = generatePagePreview({
        name: menuForm.name,
        menuId: menuForm.menuId,
        path: menuForm.path,
        description: menuForm.description
      })
    } catch (error) {
      console.error('生成页面预览失败:', error)
      pagePreviewData.value = null
    }
  } else {
    pagePreviewData.value = null
  }
}

// 根据图标名称获取图标组件
const getIconComponent = (iconName) => {
  const icon = availableIcons.find(item => item.name === iconName)
  return icon ? icon.component : Setting
}

// 获取对话框标题
const getDialogTitle = () => {
  switch (menuDialogType.value) {
    case 'add':
      return t('menuManagement.addMenuDialog')
    case 'edit':
      return t('menuManagement.editMenuDialog')
    case 'addChild':
      return t('menuManagement.addChildMenuDialog')
    default:
      return t('menuManagement.menuSettings')
  }
}

// 加载菜单列表
const loadMenuList = async () => {
  loading.value = true
  try {
    const response = await getMenuTree()
    if (response.data.code === 200) {
      menuList.value = response.data.data || []
      ElMessage.success(t('menuManagement.loadMenuListSuccess'))
    } else {
      ElMessage.error(response.data.message || t('menuManagement.loadMenuListError'))
    }
  } catch (error) {
    console.error('加载菜单列表失败:', error)
    ElMessage.error(t('menuManagement.networkError'))
  } finally {
    loading.value = false
  }
}

// 刷新菜单列表
const refreshMenuList = () => {
  loadMenuList()
}

// 新增菜单
const handleAddMenu = () => {
  menuDialogType.value = 'add'
  resetMenuForm()
  menuDialogVisible.value = true
}

// 编辑菜单
const handleEditMenu = (row) => {
  menuDialogType.value = 'edit'
  Object.assign(menuForm, {
    menuId: row.menuId,
    name: row.name,
    type: row.type,
    path: row.path || '',
    icon: row.icon || '',
    sortOrder: row.sortOrder || 0,
    status: row.status !== false,
    description: row.description || '',
    parentId: row.parentId
  })
  menuDialogVisible.value = true
}

// 添加子菜单
const handleAddChildMenu = (row) => {
  menuDialogType.value = 'addChild'
  resetMenuForm()
  menuForm.type = 'menu-item'
  menuForm.parentId = row.id
  parentMenuId.value = row.id
  parentMenuName.value = row.name
  menuDialogVisible.value = true
}

// 删除菜单
const handleDeleteMenu = (row) => {
  ElMessageBox.confirm(
    t('menuManagement.confirmDeleteMenu', { name: row.name }),
    t('menuManagement.deleteConfirm'),
    {
      confirmButtonText: t('menuManagement.confirm'),
      cancelButtonText: t('menuManagement.cancel'),
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await deleteMenu(row.id)
      if (response.data.code === 200) {
        ElMessage.success(t('menuManagement.deleteSuccess'))
        await loadMenuList()
        eventBus.emit('menuUpdated')
      } else {
        ElMessage.error(response.data.message || t('menuManagement.deleteError'))
      }
    } catch (error) {
      console.error('删除菜单失败:', error)
      ElMessage.error(t('menuManagement.networkError'))
    }
  }).catch(() => {
    // 取消删除
  })
}

// 重置菜单表单
const resetMenuForm = () => {
  Object.assign(menuForm, {
    menuId: '',
    name: '',
    type: 'menu-item',
    path: '',
    icon: '',
    sortOrder: 0,
    status: true,
    description: '',
    parentId: null
  })
  parentMenuName.value = ''
  parentMenuId.value = null
  generatePageFile.value = false
  pagePreviewData.value = null
}

// 菜单表单提交
const handleMenuSubmit = async () => {
  if (!menuFormRef.value) return
  
  await menuFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const formData = {
          menuId: menuForm.menuId,
          name: menuForm.name,
          type: menuForm.type,
          path: menuForm.type === 'menu-item' ? menuForm.path : null,
          icon: menuForm.icon,
          sortOrder: menuForm.sortOrder,
          status: menuForm.status,
          description: menuForm.description,
          parentId: menuForm.parentId
        }
        
        let response
        if (menuDialogType.value === 'edit') {
          // 找到菜单的数据库ID
          const findMenuId = (items, menuId) => {
            for (const item of items) {
              if (item.menuId === menuId) {
                return item.id
              }
              if (item.children && item.children.length > 0) {
                const found = findMenuId(item.children, menuId)
                if (found) return found
              }
            }
            return null
          }
          
          const id = findMenuId(menuList.value, menuForm.menuId)
          if (!id) {
            throw new Error(t('menuManagement.menuIdNotFound'))
          }
          
          response = await updateMenu(id, formData)
        } else {
          response = await createMenu(formData)
        }
        
        if (response.data.code === 200) {
          ElMessage.success(menuDialogType.value === 'edit' ? t('menuManagement.editMenuSuccess') : t('menuManagement.addMenuSuccess'))
          
          // 如果开启了页面生成且是新增菜单项，则生成页面文件
          if (generatePageFile.value && menuForm.type === 'menu-item' && menuDialogType.value !== 'edit') {
            try {
              await generatePageFiles()
            } catch (error) {
              console.error('生成页面文件失败:', error)
              ElMessage.warning(t('menuManagement.generatePageFileError'))
            }
          }
          
          menuDialogVisible.value = false
          await loadMenuList()
          eventBus.emit('menuUpdated')
        } else {
          ElMessage.error(response.data.message || t('menuManagement.operationError'))
        }
      } catch (error) {
        console.error('菜单操作失败:', error)
        ElMessage.error(t('menuManagement.networkError'))
      } finally {
        submitting.value = false
      }
    }
  })
}

// 生成页面文件
const generatePageFiles = async () => {
  if (!pagePreviewData.value) return
  
  try {
    const { createPageFilesInBrowser, downloadFile, copyToClipboard, generateRouteCode } = await import('@/utils/pageTemplateGenerator')
    
    const menuConfig = {
      name: menuForm.name,
      menuId: menuForm.menuId,
      path: menuForm.path,
      description: menuForm.description
    }
    
    // 在浏览器环境中生成文件
    const result = await createPageFilesInBrowser(menuConfig)
    
    if (result.success) {
      // 显示生成结果对话框
      showGeneratedFiles.value = true
      generatedFilesData.value = result
      
      ElMessage.success(t('menuManagement.generatePageSuccess'))
    } else {
      ElMessage.error(t('menuManagement.generatePageError') + '：' + result.message)
    }
    
  } catch (error) {
    console.error('生成页面文件失败:', error)
    ElMessage.error(t('menuManagement.generatePageError'))
  }
}

// 下载文件
const handleDownloadFile = (fileType) => {
  if (!generatedFilesData.value) return
  
  try {
    import('@/utils/pageTemplateGenerator').then(({ downloadFile }) => {
      const file = generatedFilesData.value.files[fileType]
      if (file) {
        downloadFile(file)
        ElMessage.success(`${file.name} ${t('menuManagement.downloadSuccess')}`)
      }
    })
  } catch (error) {
    ElMessage.error(t('menuManagement.downloadError'))
  }
}

// 复制代码
const handleCopyCode = async (fileType) => {
  if (!generatedFilesData.value) return
  
  try {
    const { copyToClipboard } = await import('@/utils/pageTemplateGenerator')
    const file = generatedFilesData.value.files[fileType]
    if (file) {
      const result = await copyToClipboard(file.content)
      if (result.success) {
        ElMessage.success(`${file.name} ${t('menuManagement.copySuccess')}`)
      }
    }
  } catch (error) {
    ElMessage.error(t('menuManagement.copyError'))
  }
}

// 生成路由配置代码
const getRouteCode = () => {
  if (!generatedFilesData.value) return ''
  try {
    const { generateRouteCode } = require('@/utils/pageTemplateGenerator')
    return generateRouteCode({
      name: menuForm.name,
      menuId: menuForm.menuId,
      path: menuForm.path,
      description: menuForm.description
    })
  } catch (error) {
    return ''
  }
}

// 复制路由代码
const handleCopyRouteCode = async () => {
  try {
    const { copyToClipboard } = await import('@/utils/pageTemplateGenerator')
    const routeCode = getRouteCode()
    if (routeCode) {
      const result = await copyToClipboard(routeCode)
      if (result.success) {
        ElMessage.success(t('menuManagement.routeCodeCopied'))
      }
    }
  } catch (error) {
    ElMessage.error(t('menuManagement.copyError'))
  }
}

// 生成的文件数据
const showGeneratedFiles = ref(false)
const generatedFilesData = ref(null)

// 监听表单字段变化
const watchFormChanges = () => {
  // 监听关键字段变化，更新预览
  const watchFields = ['name', 'menuId', 'path', 'description']
  watchFields.forEach(field => {
    watch(() => menuForm[field], () => {
      if (generatePageFile.value) {
        updatePagePreview()
      }
    })
  })
  
  watch(() => menuForm.type, (newType) => {
    if (newType !== 'menu-item') {
      generatePageFile.value = false
      pagePreviewData.value = null
    }
  })
}

// 初始化菜单
const handleInitializeMenus = async () => {
  try {
    const confirmResult = await ElMessageBox.confirm(
      t('menuManagement.confirmInitializeMenus'),
      t('menuManagement.initializeConfirm'),
      {
        confirmButtonText: t('menuManagement.confirm'),
        cancelButtonText: t('menuManagement.cancel'),
        type: 'warning'
      }
    )
    
    if (confirmResult !== 'confirm') return
    
    initializing.value = true
    const response = await initializeMenus()
    if (response.data.code === 200) {
      ElMessage.success(t('menuManagement.initializeMenusSuccess'))
      await loadMenuList() // 重新加载菜单列表
      eventBus.emit('menuUpdated')
    } else {
      ElMessage.error(response.data.message || t('menuManagement.initializeMenusError'))
    }
  } catch (error) {
    if (error === 'cancel') {
      return // 用户取消操作
    }
    console.error('初始化菜单失败:', error)
    ElMessage.error(t('menuManagement.networkError'))
  } finally {
    initializing.value = false
  }
}

// API预览相关辅助方法
const getApiMethod = (endpoint) => {
  return endpoint.split(' ')[0]
}

const getApiPath = (endpoint) => {
  const parts = endpoint.split(' - ')
  return parts[0].split(' ')[1]
}

const getApiDesc = (endpoint) => {
  const parts = endpoint.split(' - ')
  return parts[1] || ''
}

const getApiMethodType = (endpoint) => {
  const method = getApiMethod(endpoint)
  const typeMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger'
  }
  return typeMap[method] || 'info'
}

// 组件挂载后初始化
onMounted(() => {
  loadMenuList()
  watchFormChanges()
})
</script>

<style scoped>
.menu-management {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: #303133;
}

.page-actions {
  display: flex;
  gap: 12px;
}

.menu-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 页面生成相关样式 */
.generate-tip {
  margin-top: 8px;
}

.preview-card {
  margin-top: 8px;
  border: 1px solid #e4e7ed;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.preview-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.feature-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  align-items: center;
}

/* 页面预览详情样式 */
.page-preview-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.preview-section {
  border: 1px solid #e4e7ed;
}

.preview-section h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.feature-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.feature-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.api-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.api-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.api-method {
  min-width: 60px;
  text-align: center;
}

.api-path {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  color: #409eff;
}

.api-desc {
  margin-left: auto;
}

.usage-info {
  padding: 0;
}

.usage-list {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.usage-list li {
  margin-bottom: 6px;
  color: #606266;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .page-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .preview-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .feature-tags {
    width: 100%;
  }

  .api-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .api-desc {
    margin-left: 0;
  }
}
</style> 
 