<template>
  <div class="data-dictionary">
    <div class="page-header">
      <h2 class="page-title">{{ $t('dataDict.management') }}</h2>
      <div class="page-actions">
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          {{ $t('dataDict.addDict') }}
        </el-button>
        <el-button type="success" :icon="Upload" @click="handleImport">
          {{ $t('dataDict.import') }}
        </el-button>
        <el-button type="info" :icon="Download" @click="handleExport">
          {{ $t('dataDict.export') }}
        </el-button>
        <el-button type="warning" :icon="RefreshRight" @click="refreshList">
          {{ $t('dataDict.refresh') }}
        </el-button>
      </div>
    </div>

    <div class="content-area">
      <el-card>
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item :label="$t('dataDict.dictName')">
              <el-input 
                v-model="searchForm.name" 
                :placeholder="$t('dataDict.searchNamePlaceholder')" 
                clearable
                @keyup.enter="handleSearch"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item :label="$t('dataDict.dictCode')">
              <el-input 
                v-model="searchForm.code" 
                :placeholder="$t('dataDict.searchCodePlaceholder')" 
                clearable
                @keyup.enter="handleSearch"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item :label="$t('dataDict.dictType')">
              <el-select 
                v-model="searchForm.type" 
                :placeholder="$t('dataDict.selectTypePlaceholder')"
                clearable
                style="width: 150px"
              >
                <el-option :label="$t('dataDict.systemConfig')" value="system" />
                <el-option :label="$t('dataDict.businessConfig')" value="business" />
                <el-option :label="$t('dataDict.otherConfig')" value="other" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('dataDict.status')">
              <el-select 
                v-model="searchForm.status" 
                :placeholder="$t('dataDict.selectStatusPlaceholder')"
                clearable
                style="width: 120px"
              >
                <el-option :label="$t('dataDict.enabled')" :value="true" />
                <el-option :label="$t('dataDict.disabled')" :value="false" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">{{ $t('dataDict.search') }}</el-button>
              <el-button @click="handleReset">{{ $t('dataDict.reset') }}</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 批量操作区域 -->
        <div class="batch-operations" v-if="selectedRows.length > 0">
          <el-alert
            :title="$t('dataDict.selectedItems', { count: selectedRows.length })"
            type="info"
            show-icon
            :closable="false"
          >
            <template #default>
              <div class="batch-buttons">
                <el-button size="small" type="success" @click="handleBatchEnable">
                  {{ $t('dataDict.batchEnable') }}
                </el-button>
                <el-button size="small" type="warning" @click="handleBatchDisable">
                  {{ $t('dataDict.batchDisable') }}
                </el-button>
                <el-button size="small" type="danger" @click="handleBatchDelete">
                  {{ $t('dataDict.batchDelete') }}
                </el-button>
              </div>
            </template>
          </el-alert>
        </div>

        <!-- 表格区域 -->
        <el-table
          v-loading="loading"
          :data="dataList"
          border
          stripe
          @selection-change="handleSelectionChange"
          style="width: 100%"
        >
          <el-table-column type="selection" width="50" />
          <el-table-column prop="id" :label="$t('dataDict.id')" width="80" sortable />
          <el-table-column prop="name" :label="$t('dataDict.name')" min-width="150" show-overflow-tooltip />
          <el-table-column prop="code" :label="$t('dataDict.code')" min-width="150" show-overflow-tooltip />
          <el-table-column prop="type" :label="$t('dataDict.type')" width="120">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.type)">
                {{ getTypeLabel(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" :label="$t('dataDict.description')" min-width="200" show-overflow-tooltip />
          <el-table-column prop="itemCount" :label="$t('dataDict.itemCount')" width="120" align="center">
            <template #default="{ row }">
              <el-tag type="info">{{ $t('dataDict.itemsCount', { count: row.itemCount || 0 }) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" :label="$t('dataDict.status')" width="80" align="center">
            <template #default="{ row }">
              <el-switch
                v-model="row.status"
                @change="handleStatusChange(row)"
                :disabled="loading"
              />
            </template>
          </el-table-column>
          <el-table-column prop="createTime" :label="$t('dataDict.createTime')" width="160" sortable />
          <el-table-column prop="updateTime" :label="$t('dataDict.updateTime')" width="160" sortable />
          <el-table-column :label="$t('dataDict.operation')" width="250" align="center" fixed="right">
            <template #default="{ row }">
              <el-button-group>
                <el-button type="primary" size="small" @click="handleEdit(row)">
                  {{ $t('dataDict.edit') }}
                </el-button>
                <el-button type="success" size="small" @click="handleViewItems(row)">
                  {{ $t('dataDict.dictItems') }} ({{ row.itemCount || 0 }})
                </el-button>
                <el-button type="info" size="small" @click="handleCopy(row)">
                  {{ $t('dataDict.copy') }}
                </el-button>
                <el-button type="danger" size="small" @click="handleDelete(row)">
                  {{ $t('dataDict.delete') }}
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="pagination-area">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? $t('dataDict.editDictDialog') : $t('dataDict.addDictDialog')"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item :label="$t('dataDict.dictName')" prop="name">
          <el-input v-model="form.name" :placeholder="$t('dataDict.namePlaceholder')" />
        </el-form-item>
        <el-form-item :label="$t('dataDict.dictCode')" prop="code">
          <el-input 
            v-model="form.code" 
            :placeholder="$t('dataDict.codePlaceholder')"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item :label="$t('dataDict.dictType')" prop="type">
          <el-select v-model="form.type" :placeholder="$t('dataDict.selectTypePlaceholder')" style="width: 100%">
            <el-option :label="$t('dataDict.systemConfig')" value="system" />
            <el-option :label="$t('dataDict.businessConfig')" value="business" />
            <el-option :label="$t('dataDict.otherConfig')" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('dataDict.description')" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            :placeholder="$t('dataDict.descriptionPlaceholder')"
          />
        </el-form-item>
        <el-form-item :label="$t('dataDict.sort')" prop="sort">
          <el-input-number v-model="form.sort" :min="0" :max="9999" />
        </el-form-item>
        <el-form-item :label="$t('dataDict.status')" prop="status">
          <el-switch v-model="form.status" :active-text="$t('dataDict.enabledText')" :inactive-text="$t('dataDict.disabledText')" />
        </el-form-item>
        <el-form-item :label="$t('dataDict.remark')">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="2"
            :placeholder="$t('dataDict.remarkPlaceholder')"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">{{ $t('dataDict.cancel') }}</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ $t('dataDict.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 字典项管理对话框 -->
    <el-dialog
      v-model="itemsDialogVisible"
      :title="`${$t('dataDict.dictItemsDialog')} - ${currentDict.name}`"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="dict-items-container">
        <div class="items-toolbar">
          <el-button type="primary" size="small" @click="handleAddItem">
            {{ $t('dataDict.addItem') }}
          </el-button>
          <el-button type="success" size="small" @click="handleItemsSort">
            {{ $t('dataDict.itemsSort') }}
          </el-button>
          <el-button type="warning" size="small" @click="refreshItems">
            {{ $t('dataDict.refresh') }}
          </el-button>
        </div>
        
        <el-table :data="itemsList" border stripe v-loading="itemsLoading">
          <el-table-column prop="label" :label="$t('dataDict.itemLabel')" min-width="150" />
          <el-table-column prop="value" :label="$t('dataDict.itemValue')" min-width="150" />
          <el-table-column prop="sort" :label="$t('dataDict.sort')" width="80" />
          <el-table-column prop="status" :label="$t('dataDict.status')" width="80" align="center">
            <template #default="{ row }">
              <el-switch v-model="row.status" size="small" @change="handleItemStatusChange(row)" />
            </template>
          </el-table-column>
          <el-table-column prop="description" :label="$t('dataDict.description')" min-width="200" show-overflow-tooltip />
          <el-table-column :label="$t('dataDict.operation')" width="150" align="center">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleEditItem(row)">
                {{ $t('dataDict.edit') }}
              </el-button>
              <el-button type="danger" size="small" @click="handleDeleteItem(row)">
                {{ $t('dataDict.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 字典项新增/编辑对话框 -->
    <el-dialog
      v-model="itemDialogVisible"
      :title="isEditItem ? $t('dataDict.editItemDialog') : $t('dataDict.addItemDialog')"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="itemFormRef"
        :model="itemForm"
        :rules="itemFormRules"
        label-width="80px"
      >
        <el-form-item :label="$t('dataDict.itemLabel')" prop="label">
          <el-input v-model="itemForm.label" :placeholder="$t('dataDict.itemLabelPlaceholder')" />
        </el-form-item>
        <el-form-item :label="$t('dataDict.itemValue')" prop="value">
          <el-input v-model="itemForm.value" :placeholder="$t('dataDict.itemValuePlaceholder')" />
        </el-form-item>
        <el-form-item :label="$t('dataDict.sort')" prop="sort">
          <el-input-number v-model="itemForm.sort" :min="0" :max="9999" />
        </el-form-item>
        <el-form-item :label="$t('dataDict.status')" prop="status">
          <el-switch v-model="itemForm.status" :active-text="$t('dataDict.enabledText')" :inactive-text="$t('dataDict.disabledText')" />
        </el-form-item>
        <el-form-item :label="$t('dataDict.description')">
          <el-input
            v-model="itemForm.description"
            type="textarea"
            :rows="3"
            :placeholder="$t('dataDict.itemDescriptionPlaceholder')"
          />
        </el-form-item>
        <el-form-item :label="$t('dataDict.extProperty1')">
          <el-input v-model="itemForm.extProperty1" :placeholder="$t('dataDict.extProperty1Placeholder')" />
        </el-form-item>
        <el-form-item :label="$t('dataDict.extProperty2')">
          <el-input v-model="itemForm.extProperty2" :placeholder="$t('dataDict.extProperty2Placeholder')" />
        </el-form-item>
        <el-form-item :label="$t('dataDict.extProperty3')">
          <el-input v-model="itemForm.extProperty3" :placeholder="$t('dataDict.extProperty3Placeholder')" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="itemDialogVisible = false">{{ $t('dataDict.cancel') }}</el-button>
          <el-button type="primary" @click="handleItemSubmit" :loading="itemSubmitLoading">
            {{ $t('dataDict.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog v-model="importDialogVisible" :title="$t('dataDict.importDialog')" width="500px">
      <el-upload
        class="upload-demo"
        drag
        action="#"
        :auto-upload="false"
        :on-change="handleFileChange"
        accept=".xlsx,.xls"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          {{ $t('dataDict.uploadText') }}<em>{{ $t('dataDict.clickUpload') }}</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            {{ $t('dataDict.uploadTip') }}
          </div>
        </template>
      </el-upload>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">{{ $t('dataDict.cancel') }}</el-button>
          <el-button type="primary" @click="handleImportSubmit">{{ $t('dataDict.confirmImport') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { Plus, RefreshRight, Upload, Download, UploadFilled } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import request from '@/utils/request'

const { t } = useI18n()

// 格式化时间函数
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  return new Date(timeStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// API 请求函数
const api = {
  // 获取字典列表
  getDictionaries: (params) => {
    const requestParams = {}
    if (params.name) requestParams.Name = params.name
    if (params.code) requestParams.Code = params.code
    if (params.type) requestParams.Type = params.type
    if (params.status !== null && params.status !== undefined && params.status !== '') {
      requestParams.Status = params.status
    }
    requestParams.Page = params.page
    requestParams.PageSize = params.pageSize
    
    return request({
      url: '/api/datadictionary',
      method: 'get',
      params: requestParams
    })
  },

  // 获取字典详情
  getDictionary: (id) => {
    return request({
      url: `/api/datadictionary/${id}`,
      method: 'get'
    })
  },

  // 创建字典
  createDictionary: (data) => {
    return request({
      url: '/api/datadictionary',
      method: 'post',
      data
    })
  },

  // 更新字典
  updateDictionary: (id, data) => {
    return request({
      url: `/api/datadictionary/${id}`,
      method: 'put',
      data
    })
  },

  // 删除字典
  deleteDictionary: (id) => {
    return request({
      url: `/api/datadictionary/${id}`,
      method: 'delete'
    })
  },

  // 批量删除字典
  batchDeleteDictionaries: (ids) => {
    return request({
      url: '/api/datadictionary/batch',
      method: 'delete',
      data: { ids }
    })
  },

  // 批量更新字典状态
  batchUpdateDictionaryStatus: (ids, status) => {
    return request({
      url: '/api/datadictionary/batch/status',
      method: 'put',
      data: { ids, status }
    })
  },

  // 获取字典项列表
  getDictionaryItems: (dictId, params = {}) => {
    return request({
      url: `/api/datadictionary/${dictId}/items`,
      method: 'get',
      params: {
        Page: params.page || 1,
        PageSize: params.pageSize || 100
      }
    })
  },

  // 创建字典项
  createDictionaryItem: (data) => {
    return request({
      url: '/api/datadictionary/items',
      method: 'post',
      data
    })
  },

  // 更新字典项
  updateDictionaryItem: (id, data) => {
    return request({
      url: `/api/datadictionary/items/${id}`,
      method: 'put',
      data
    })
  },

  // 删除字典项
  deleteDictionaryItem: (id) => {
    return request({
      url: `/api/datadictionary/items/${id}`,
      method: 'delete'
    })
  }
}

// 数据状态
const loading = ref(false)
const dataList = ref([])
const selectedRows = ref([])

// 字典项相关状态
const itemsLoading = ref(false)

// 搜索表单
const searchForm = reactive({
  name: '',
  code: '',
  type: '',
  status: null
})

// 分页配置
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 对话框状态
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)

// 表单数据
const formRef = ref()
const form = reactive({
  id: null,
  name: '',
  code: '',
  type: '',
  description: '',
  sort: 0,
  status: true,
  remark: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入字典名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入字典编码', trigger: 'blur' },
    { pattern: /^[A-Z][A-Z0-9_]*$/, message: '编码必须以大写字母开头，只能包含大写字母、数字和下划线', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择字典类型', trigger: 'change' }
  ],
  description: [
    { max: 200, message: '描述不能超过200个字符', trigger: 'blur' }
  ]
}

// 字典项相关
const itemsDialogVisible = ref(false)
const itemDialogVisible = ref(false)
const isEditItem = ref(false)
const itemSubmitLoading = ref(false)
const currentDict = ref({})
const itemsList = ref([])

const itemFormRef = ref()
const itemForm = reactive({
  id: null,
  dictId: null,
  label: '',
  value: '',
  sort: 0,
  status: true,
  description: '',
  extProperty1: '',
  extProperty2: '',
  extProperty3: ''
})

const itemFormRules = {
  label: [
    { required: true, message: '请输入字典标签', trigger: 'blur' }
  ],
  value: [
    { required: true, message: '请输入字典值', trigger: 'blur' }
  ]
}

// 导入相关  
const importDialogVisible = ref(false)
const importFile = ref(null)

// 获取类型标签样式
const getTypeTagType = (type) => {
  switch (type) {
    case 'system': return 'primary'
    case 'business': return 'success'
    case 'other': return 'info'
    default: return 'info'
  }
}

// 获取类型标签文本
const getTypeLabel = (type) => {
  switch (type) {
    case 'system': return t('dataDict.systemConfig')
    case 'business': return t('dataDict.businessConfig')
    case 'other': return t('dataDict.otherConfig')
    default: return t('dataDict.unknown')
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      name: searchForm.name,
      code: searchForm.code,
      type: searchForm.type,
      status: searchForm.status,
      page: pagination.page,
      pageSize: pagination.size
    }
    
    const response = await api.getDictionaries(params)
    
    if (response.data.code === 200) {
      const result = response.data.data
      dataList.value = result.items.map(item => ({
        ...item,
        createTime: formatTime(item.createTime),
        updateTime: formatTime(item.updateTime),
        itemCount: item.itemCount || 0
      }))
      pagination.total = result.totalCount
    } else {
      ElMessage.error(response.data.message || t('dataDict.loadFailed'))
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error(t('dataDict.loadFailed') + ': ' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    code: '',
    type: '',
    status: null
  })
  pagination.page = 1
  loadData()
}

// 刷新列表
const refreshList = () => {
  loadData()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 新增
const handleAdd = () => {
  isEdit.value = false
  Object.assign(form, {
    id: null,
    name: '',
    code: '',
    type: '',
    description: '',
    sort: 0,
    status: true,
    remark: ''
  })
  dialogVisible.value = true
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 编辑
const handleEdit = (row) => {
  isEdit.value = true
  Object.assign(form, { ...row })
  dialogVisible.value = true
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 复制
const handleCopy = (row) => {
  isEdit.value = false
  Object.assign(form, {
    ...row,
    id: null,
    name: row.name + '_副本',
    code: row.code + '_COPY'
  })
  dialogVisible.value = true
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      
      try {
        const submitData = {
          name: form.name,
          code: form.code,
          type: form.type,
          description: form.description,
          sort: form.sort,
          status: form.status,
          remark: form.remark
        }
        
        if (isEdit.value) {
          const response = await api.updateDictionary(form.id, submitData)
          if (response.data.code === 200) {
            ElMessage.success(t('dataDict.editSuccess'))
          } else {
            ElMessage.error(response.data.message || t('dataDict.editFailed'))
            return
          }
        } else {
          const response = await api.createDictionary(submitData)
          if (response.data.code === 200) {
            ElMessage.success(t('dataDict.addSuccess'))
          } else {
            ElMessage.error(response.data.message || t('dataDict.addFailed'))
            return
          }
        }
        
        dialogVisible.value = false
        loadData()
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error(t('dataDict.operationFailed') + ': ' + (error.response?.data?.message || error.message))
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(
    t('dataDict.deleteConfirm', { name: row.name }),
    t('dataDict.deleteTitle'),
    {
      confirmButtonText: t('dataDict.confirm'),
      cancelButtonText: t('dataDict.cancel'),
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await api.deleteDictionary(row.id)
      if (response.data.code === 200) {
        ElMessage.success(t('dataDict.deleteSuccess'))
        loadData()
      } else {
        ElMessage.error(response.data.message || t('dataDict.deleteFailed'))
      }
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error(t('dataDict.deleteFailed') + ': ' + (error.response?.data?.message || error.message))
    }
  }).catch(() => {
    // 取消删除
  })
}

// 状态变化
const handleStatusChange = async (row) => {
  try {
    const response = await api.updateDictionary(row.id, {
      name: row.name,
      code: row.code,
      type: row.type,
      description: row.description,
      sort: row.sort,
      status: row.status,
      remark: row.remark
    })
    
    if (response.data.code === 200) {
      const action = row.status ? '启用' : '禁用'
      ElMessage.success(`${action}成功`)
    } else {
      // 恢复原状态
      row.status = !row.status
      ElMessage.error(response.data.message || '状态更新失败')
    }
  } catch (error) {
    // 恢复原状态
    row.status = !row.status
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败: ' + (error.response?.data?.message || error.message))
  }
}

// 批量启用
const handleBatchEnable = () => {
  ElMessageBox.confirm(
    `确定要启用选中的 ${selectedRows.value.length} 个字典吗？`,
    '批量启用',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(async () => {
    try {
      const ids = selectedRows.value.map(row => row.id)
      const response = await api.batchUpdateDictionaryStatus(ids, true)
      
      if (response.data.code === 200) {
        ElMessage.success('批量启用成功')
        loadData()
      } else {
        ElMessage.error(response.data.message || '批量启用失败')
      }
    } catch (error) {
      console.error('批量启用失败:', error)
      ElMessage.error('批量启用失败: ' + (error.response?.data?.message || error.message))
    }
  })
}

// 批量禁用
const handleBatchDisable = () => {
  ElMessageBox.confirm(
    `确定要禁用选中的 ${selectedRows.value.length} 个字典吗？`,
    '批量禁用',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const ids = selectedRows.value.map(row => row.id)
      const response = await api.batchUpdateDictionaryStatus(ids, false)
      
      if (response.data.code === 200) {
        ElMessage.success('批量禁用成功')
        loadData()
      } else {
        ElMessage.error(response.data.message || '批量禁用失败')
      }
    } catch (error) {
      console.error('批量禁用失败:', error)
      ElMessage.error('批量禁用失败: ' + (error.response?.data?.message || error.message))
    }
  })
}

// 批量删除
const handleBatchDelete = () => {
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedRows.value.length} 个字典吗？此操作不可恢复！`,
    '批量删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    }
  ).then(async () => {
    try {
      const ids = selectedRows.value.map(row => row.id)
      const response = await api.batchDeleteDictionaries(ids)
      
      if (response.data.code === 200) {
        ElMessage.success('批量删除成功')
        selectedRows.value = []
        loadData()
      } else {
        ElMessage.error(response.data.message || '批量删除失败')
      }
    } catch (error) {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败: ' + (error.response?.data?.message || error.message))
    }
  })
}

// 查看字典项
const handleViewItems = async (row) => {
  currentDict.value = row
  itemsDialogVisible.value = true
  await loadItems()
}

// 加载字典项
const loadItems = async () => {
  itemsLoading.value = true
  try {
    const response = await api.getDictionaryItems(currentDict.value.id)
    
    if (response.data.code === 200) {
      itemsList.value = response.data.data.items || []
    } else {
      ElMessage.error(response.data.message || '加载字典项失败')
    }
  } catch (error) {
    console.error('加载字典项失败:', error)
    ElMessage.error('加载字典项失败: ' + (error.response?.data?.message || error.message))
  } finally {
    itemsLoading.value = false
  }
}

// 刷新字典项
const refreshItems = () => {
  loadItems()
}

// 新增字典项
const handleAddItem = () => {
  isEditItem.value = false
  Object.assign(itemForm, {
    id: null,
    dictId: currentDict.value.id,
    label: '',
    value: '',
    sort: 0,
    status: true,
    description: '',
    extProperty1: '',
    extProperty2: '',
    extProperty3: ''
  })
  itemDialogVisible.value = true
  nextTick(() => {
    itemFormRef.value?.clearValidate()
  })
}

// 编辑字典项
const handleEditItem = (row) => {
  isEditItem.value = true
  Object.assign(itemForm, { ...row })
  itemDialogVisible.value = true
  nextTick(() => {
    itemFormRef.value?.clearValidate()
  })
}

// 提交字典项
const handleItemSubmit = async () => {
  if (!itemFormRef.value) return
  
  await itemFormRef.value.validate(async (valid) => {
    if (valid) {
      itemSubmitLoading.value = true
      
      try {
        const submitData = {
          dictId: itemForm.dictId,
          label: itemForm.label,
          value: itemForm.value,
          sort: itemForm.sort,
          status: itemForm.status,
          description: itemForm.description,
          extProperty1: itemForm.extProperty1,
          extProperty2: itemForm.extProperty2,
          extProperty3: itemForm.extProperty3
        }
        
        if (isEditItem.value) {
          const response = await api.updateDictionaryItem(itemForm.id, submitData)
          if (response.data.code === 200) {
            ElMessage.success('编辑字典项成功')
          } else {
            ElMessage.error(response.data.message || '编辑字典项失败')
            return
          }
        } else {
          const response = await api.createDictionaryItem(submitData)
          if (response.data.code === 200) {
            ElMessage.success('新增字典项成功')
          } else {
            ElMessage.error(response.data.message || '新增字典项失败')
            return
          }
        }
        
        itemDialogVisible.value = false
        await loadItems()
        // 刷新主列表以更新字典项数量
        loadData()
      } catch (error) {
        console.error('字典项操作失败:', error)
        ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message))
      } finally {
        itemSubmitLoading.value = false
      }
    }
  })
}

// 删除字典项
const handleDeleteItem = (row) => {
  ElMessageBox.confirm(
    `确定要删除字典项"${row.label}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await api.deleteDictionaryItem(row.id)
      
      if (response.data.code === 200) {
        ElMessage.success('删除字典项成功')
        await loadItems()
        // 刷新主列表以更新字典项数量
        loadData()
      } else {
        ElMessage.error(response.data.message || '删除字典项失败')
      }
    } catch (error) {
      console.error('删除字典项失败:', error)
      ElMessage.error('删除字典项失败: ' + (error.response?.data?.message || error.message))
    }
  })
}

// 字典项状态变化
const handleItemStatusChange = async (row) => {
  try {
    const response = await api.updateDictionaryItem(row.id, {
      dictId: row.dictId,
      label: row.label,
      value: row.value,
      sort: row.sort,
      status: row.status,
      description: row.description,
      extProperty1: row.extProperty1,
      extProperty2: row.extProperty2,
      extProperty3: row.extProperty3
    })
    
    if (response.data.code === 200) {
      const action = row.status ? '启用' : '禁用'
      ElMessage.success(`${action}字典项成功`)
    } else {
      // 恢复原状态
      row.status = !row.status
      ElMessage.error(response.data.message || '状态更新失败')
    }
  } catch (error) {
    // 恢复原状态
    row.status = !row.status
    console.error('字典项状态更新失败:', error)
    ElMessage.error('状态更新失败: ' + (error.response?.data?.message || error.message))
  }
}

// 字典项排序
const handleItemsSort = () => {
  ElMessage.info('排序功能开发中...')
}

// 导入
const handleImport = () => {
  importDialogVisible.value = true
}

// 导出
const handleExport = () => {
  ElMessage.success('导出功能开发中...')
}

// 文件变化
const handleFileChange = (file) => {
  importFile.value = file
}

// 导入提交
const handleImportSubmit = () => {
  if (!importFile.value) {
    ElMessage.warning('请选择要导入的文件')
    return
  }
  
  ElMessage.success('导入功能开发中...')
  importDialogVisible.value = false
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  loadData()
}

// 当前页变化
const handleCurrentChange = (page) => {
  pagination.page = page
  loadData()
}

// 组件挂载后初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.data-dictionary {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.content-area {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 6px;
}

.search-form {
  margin: 0;
}

.batch-operations {
  margin-bottom: 15px;
}

.batch-buttons {
  margin-top: 10px;
}

.pagination-area {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dict-items-container {
  min-height: 400px;
}

.items-toolbar {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
}

:deep(.el-table) {
  background: #fff;
}

:deep(.el-card) {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

.upload-demo {
  text-align: center;
}

:deep(.el-upload-dragger) {
  width: 100%;
}
</style> 