using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using mes_system_server.Data;
using mes_system_server.DTOs;
using mes_system_server.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace mes_system_server.Controllers
{
    [Route("api/work-centers")]
    [ApiController]
    [AllowAnonymous]
    public class WorkCentersController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public WorkCentersController(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取指定产线的工作中心列表
        /// </summary>
        /// <param name="productionLineId">产线ID</param>
        /// <param name="includeEquipment">是否包含设备数据</param>
        /// <returns>工作中心列表</returns>
        [HttpGet("by-production-line/{productionLineId}")]
        public async Task<IActionResult> GetWorkCentersByProductionLine(
            int productionLineId, 
            [FromQuery] bool includeEquipment = true)
        {
            try
            {
                // 验证产线是否存在
                var productionLineExists = await _context.ProductionLines
                    .AnyAsync(pl => pl.Id == productionLineId && pl.IsActive);

                if (!productionLineExists)
                {
                    return NotFound(ApiResponseDto<string>.Fail("产线不存在", 404));
                }

                var query = _context.WorkCenters
                    .Where(wc => wc.ProductionLineId == productionLineId && wc.IsActive);

                var workCenters = await query
                    .OrderBy(wc => wc.SortOrder)
                    .ThenBy(wc => wc.Id)
                    .ToListAsync();

                var workCenterDtos = new List<WorkCenterDto>();

                foreach (var wc in workCenters)
                {
                    var dto = new WorkCenterDto
                    {
                        Id = wc.Id,
                        Name = wc.Name,
                        Code = wc.Code,
                        Description = wc.Description,
                        ProductionLineId = wc.ProductionLineId,
                        SortOrder = wc.SortOrder,
                        IsActive = wc.IsActive,
                        CreateTime = wc.CreateTime,
                        UpdateTime = wc.UpdateTime
                    };

                    // 如果需要包含设备数据
                    if (includeEquipment)
                    {
                        var equipment = await _context.Equipment
                            .Where(eq => eq.WorkCenterId == wc.Id && eq.IsActive)
                            .OrderBy(eq => eq.SortOrder)
                            .ThenBy(eq => eq.Id)
                            .ToListAsync();

                        dto.Equipment = equipment.Select(eq => new EquipmentDto
                        {
                            Id = eq.Id,
                            Name = eq.Name,
                            Code = eq.Code,
                            Description = eq.Description,
                            WorkCenterId = eq.WorkCenterId,
                            SortOrder = eq.SortOrder,
                            IsActive = eq.IsActive,
                            CreateTime = eq.CreateTime,
                            UpdateTime = eq.UpdateTime
                        }).ToList();
                    }

                    workCenterDtos.Add(dto);
                }

                var response = new WorkCenterResponseDto
                {
                    Total = workCenterDtos.Count,
                    List = workCenterDtos
                };

                return Ok(ApiResponseDto<WorkCenterResponseDto>.Success(response));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取工作中心列表失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 根据ID获取工作中心详情
        /// </summary>
        /// <param name="id">工作中心ID</param>
        /// <returns>工作中心详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetWorkCenter(int id)
        {
            try
            {
                var workCenter = await _context.WorkCenters
                    .FirstOrDefaultAsync(wc => wc.Id == id && wc.IsActive);

                if (workCenter == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("工作中心不存在", 404));
                }

                var workCenterDto = new WorkCenterDto
                {
                    Id = workCenter.Id,
                    Name = workCenter.Name,
                    Code = workCenter.Code,
                    Description = workCenter.Description,
                    ProductionLineId = workCenter.ProductionLineId,
                    SortOrder = workCenter.SortOrder,
                    IsActive = workCenter.IsActive,
                    CreateTime = workCenter.CreateTime,
                    UpdateTime = workCenter.UpdateTime
                };

                return Ok(ApiResponseDto<WorkCenterDto>.Success(workCenterDto));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"获取工作中心详情失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 创建工作中心
        /// </summary>
        /// <param name="productionLineId">产线ID</param>
        /// <param name="workCenterDto">工作中心信息</param>
        /// <returns>创建的工作中心</returns>
        [HttpPost("production-line/{productionLineId}")]
        public async Task<IActionResult> CreateWorkCenter(int productionLineId, [FromBody] CreateWorkCenterDto workCenterDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                // 验证产线是否存在
                var productionLineExists = await _context.ProductionLines
                    .AnyAsync(pl => pl.Id == productionLineId && pl.IsActive);

                if (!productionLineExists)
                {
                    return NotFound(ApiResponseDto<string>.Fail("产线不存在", 404));
                }

                // 检查工作中心编码在该产线内是否已存在
                var existingWorkCenter = await _context.WorkCenters
                    .FirstOrDefaultAsync(wc => wc.ProductionLineId == productionLineId && 
                                             wc.Code == workCenterDto.Code && wc.IsActive);

                if (existingWorkCenter != null)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("工作中心编码在该产线内已存在"));
                }

                var workCenter = new WorkCenter
                {
                    Name = workCenterDto.Name,
                    Code = workCenterDto.Code,
                    Description = workCenterDto.Description,
                    ProductionLineId = productionLineId,
                    SortOrder = workCenterDto.SortOrder,
                    IsActive = true,
                    CreateTime = DateTime.UtcNow,
                    UpdateTime = DateTime.UtcNow
                };

                _context.WorkCenters.Add(workCenter);
                await _context.SaveChangesAsync();

                var createdWorkCenterDto = new WorkCenterDto
                {
                    Id = workCenter.Id,
                    Name = workCenter.Name,
                    Code = workCenter.Code,
                    Description = workCenter.Description,
                    ProductionLineId = workCenter.ProductionLineId,
                    SortOrder = workCenter.SortOrder,
                    IsActive = workCenter.IsActive,
                    CreateTime = workCenter.CreateTime,
                    UpdateTime = workCenter.UpdateTime
                };

                return CreatedAtAction(nameof(GetWorkCenter), new { id = workCenter.Id }, 
                    ApiResponseDto<WorkCenterDto>.Success(createdWorkCenterDto, "添加成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"创建工作中心失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 更新工作中心
        /// </summary>
        /// <param name="id">工作中心ID</param>
        /// <param name="workCenterDto">工作中心信息</param>
        /// <returns>更新的工作中心</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateWorkCenter(int id, [FromBody] UpdateWorkCenterDto workCenterDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            try
            {
                var workCenter = await _context.WorkCenters
                    .FirstOrDefaultAsync(wc => wc.Id == id && wc.IsActive);

                if (workCenter == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("工作中心不存在", 404));
                }

                // 检查工作中心编码在该产线内是否与其他工作中心冲突
                var existingWorkCenter = await _context.WorkCenters
                    .FirstOrDefaultAsync(wc => wc.ProductionLineId == workCenter.ProductionLineId && 
                                             wc.Code == workCenterDto.Code && wc.Id != id && wc.IsActive);

                if (existingWorkCenter != null)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("工作中心编码在该产线内已存在"));
                }

                // 更新工作中心信息
                workCenter.Name = workCenterDto.Name;
                workCenter.Code = workCenterDto.Code;
                workCenter.Description = workCenterDto.Description;
                workCenter.SortOrder = workCenterDto.SortOrder;
                workCenter.IsActive = workCenterDto.IsActive;
                workCenter.UpdateTime = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                var updatedWorkCenterDto = new WorkCenterDto
                {
                    Id = workCenter.Id,
                    Name = workCenter.Name,
                    Code = workCenter.Code,
                    Description = workCenter.Description,
                    ProductionLineId = workCenter.ProductionLineId,
                    SortOrder = workCenter.SortOrder,
                    IsActive = workCenter.IsActive,
                    CreateTime = workCenter.CreateTime,
                    UpdateTime = workCenter.UpdateTime
                };

                return Ok(ApiResponseDto<WorkCenterDto>.Success(updatedWorkCenterDto, "更新成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"更新工作中心失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 删除工作中心
        /// </summary>
        /// <param name="id">工作中心ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteWorkCenter(int id)
        {
            try
            {
                var workCenter = await _context.WorkCenters
                    .FirstOrDefaultAsync(wc => wc.Id == id && wc.IsActive);

                if (workCenter == null)
                {
                    return NotFound(ApiResponseDto<string>.Fail("工作中心不存在", 404));
                }

                // 检查是否有关联的设备
                var hasEquipment = await _context.Equipment
                    .AnyAsync(eq => eq.WorkCenterId == id && eq.IsActive);

                if (hasEquipment)
                {
                    return BadRequest(ApiResponseDto<string>.Fail("该工作中心下存在设备，无法删除"));
                }

                // 软删除
                workCenter.IsActive = false;
                workCenter.UpdateTime = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(ApiResponseDto<string>.Success(null, "删除成功"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<string>.Fail($"删除工作中心失败: {ex.Message}", 500));
            }
        }

        /// <summary>
        /// 批量保存工作中心配置
        /// </summary>
        /// <param name="productionLineId">产线ID</param>
        /// <param name="batchSaveDto">批量保存数据</param>
        /// <returns>保存结果</returns>
        [HttpPost("production-line/{productionLineId}/batch-save")]
        public async Task<IActionResult> BatchSaveWorkCenters(int productionLineId, [FromBody] BatchSaveWorkCenterDto batchSaveDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseDto<string>.Fail("提供的数据无效"));
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // 验证产线是否存在
                var productionLineExists = await _context.ProductionLines
                    .AnyAsync(pl => pl.Id == productionLineId && pl.IsActive);

                if (!productionLineExists)
                {
                    return NotFound(ApiResponseDto<string>.Fail("产线不存在", 404));
                }

                var savedWorkCenters = new List<WorkCenterDto>();

                foreach (var workCenterItem in batchSaveDto.WorkCenters)
                {
                    WorkCenter workCenter;

                    if (workCenterItem.Id.HasValue)
                    {
                        // 更新现有工作中心
                        workCenter = await _context.WorkCenters
                            .FirstOrDefaultAsync(wc => wc.Id == workCenterItem.Id.Value && wc.IsActive);

                        if (workCenter == null)
                        {
                            await transaction.RollbackAsync();
                            return NotFound(ApiResponseDto<string>.Fail($"工作中心ID {workCenterItem.Id} 不存在", 404));
                        }

                        workCenter.Name = workCenterItem.Name;
                        workCenter.Code = workCenterItem.Code;
                        workCenter.Description = workCenterItem.Description;
                        workCenter.SortOrder = workCenterItem.SortOrder;
                        workCenter.UpdateTime = DateTime.UtcNow;
                    }
                    else
                    {
                        // 创建新工作中心
                        workCenter = new WorkCenter
                        {
                            Name = workCenterItem.Name,
                            Code = workCenterItem.Code,
                            Description = workCenterItem.Description,
                            ProductionLineId = productionLineId,
                            SortOrder = workCenterItem.SortOrder,
                            IsActive = true,
                            CreateTime = DateTime.UtcNow,
                            UpdateTime = DateTime.UtcNow
                        };

                        _context.WorkCenters.Add(workCenter);
                    }

                    await _context.SaveChangesAsync();

                    // 处理设备
                    foreach (var equipmentItem in workCenterItem.Equipment)
                    {
                        Equipment equipment;

                        if (equipmentItem.Id.HasValue)
                        {
                            // 更新现有设备
                            equipment = await _context.Equipment
                                .FirstOrDefaultAsync(eq => eq.Id == equipmentItem.Id.Value && eq.IsActive);

                            if (equipment == null)
                            {
                                await transaction.RollbackAsync();
                                return NotFound(ApiResponseDto<string>.Fail($"设备ID {equipmentItem.Id} 不存在", 404));
                            }

                            equipment.Name = equipmentItem.Name;
                            equipment.Code = equipmentItem.Code;
                            equipment.Description = equipmentItem.Description;
                            equipment.SortOrder = equipmentItem.SortOrder;
                            equipment.UpdateTime = DateTime.UtcNow;
                        }
                        else
                        {
                            // 创建新设备
                            equipment = new Equipment
                            {
                                Name = equipmentItem.Name,
                                Code = equipmentItem.Code,
                                Description = equipmentItem.Description,
                                WorkCenterId = workCenter.Id,
                                SortOrder = equipmentItem.SortOrder,
                                IsActive = true,
                                CreateTime = DateTime.UtcNow,
                                UpdateTime = DateTime.UtcNow
                            };

                            _context.Equipment.Add(equipment);
                        }
                    }

                    await _context.SaveChangesAsync();

                    // 构建返回数据
                    var workCenterDto = new WorkCenterDto
                    {
                        Id = workCenter.Id,
                        Name = workCenter.Name,
                        Code = workCenter.Code,
                        Description = workCenter.Description,
                        ProductionLineId = workCenter.ProductionLineId,
                        SortOrder = workCenter.SortOrder,
                        IsActive = workCenter.IsActive,
                        CreateTime = workCenter.CreateTime,
                        UpdateTime = workCenter.UpdateTime
                    };

                    savedWorkCenters.Add(workCenterDto);
                }

                await transaction.CommitAsync();

                var response = new WorkCenterResponseDto
                {
                    Total = savedWorkCenters.Count,
                    List = savedWorkCenters
                };

                return Ok(ApiResponseDto<WorkCenterResponseDto>.Success(response, "批量保存成功"));
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, ApiResponseDto<string>.Fail($"批量保存工作中心失败: {ex.Message}", 500));
            }
        }
    }
} 