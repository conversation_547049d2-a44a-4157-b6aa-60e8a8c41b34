/**
 * 页面模板生成器
 * 用于根据菜单配置自动生成标准页面文件
 */

import fs from 'fs'
import path from 'path'

/**
 * 生成Vue页面模板
 * @param {Object} menuConfig 菜单配置
 * @param {string} menuConfig.name 菜单名称
 * @param {string} menuConfig.menuId 菜单ID
 * @param {string} menuConfig.path 菜单路径
 * @param {string} menuConfig.description 菜单描述
 * @returns {string} 生成的Vue组件代码
 */
export function generatePageTemplate(menuConfig) {
  const { name, menuId, path: menuPath, description } = menuConfig
  
  // 生成组件名称（帕斯卡命名法）
  const componentName = toPascalCase(menuId)
  
  // 生成实体名称（去掉菜单相关词汇）
  const entityName = name.replace(/管理|菜单|页面/g, '')
  
  const template = `<template>
  <PageTemplate
    ref="pageTemplateRef"
    :page-title="'${name}'"
    :entity-name="'${entityName}'"
    :add-button-text="'新增${entityName}'"
    :search-fields="searchFields"
    :table-columns="tableColumns"
    :form-fields="formFields"
    :form-rules="formRules"
    :actions="['edit', 'delete']"
    @search="handleSearch"
    @add="handleAdd"
    @edit="handleEdit"
    @delete="handleDelete"
    @submit="handleSubmit"
    @refresh="loadData"
  >
    <!-- 状态列插槽 -->
    <template #status="{ row }">
      <el-tag :type="row.status ? 'success' : 'danger'">
        {{ row.status ? '启用' : '禁用' }}
      </el-tag>
    </template>
  </PageTemplate>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import PageTemplate from '@/components/PageTemplate.vue'

// 页面模板引用
const pageTemplateRef = ref(null)

// 搜索字段配置
const searchFields = [
  {
    prop: 'name',
    label: '名称',
    type: 'input',
    placeholder: '请输入${entityName}名称'
  },
  {
    prop: 'status',
    label: '状态',
    type: 'select',
    options: [
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ]
  }
]

// 表格列配置
const tableColumns = [
  {
    prop: 'id',
    label: 'ID',
    width: '80'
  },
  {
    prop: 'name',
    label: '名称',
    minWidth: '150'
  },
  {
    prop: 'code',
    label: '编码',
    minWidth: '120'
  },
  {
    prop: 'status',
    label: '状态',
    width: '100',
    slot: 'status'
  },
  {
    prop: 'description',
    label: '描述',
    minWidth: '200'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: '180'
  }
]

// 表单字段配置
const formFields = [
  {
    prop: 'name',
    label: '名称',
    type: 'input',
    placeholder: '请输入${entityName}名称'
  },
  {
    prop: 'code',
    label: '编码',
    type: 'input',
    placeholder: '请输入${entityName}编码'
  },
  {
    prop: 'status',
    label: '状态',
    type: 'switch',
    defaultValue: true
  },
  {
    prop: 'description',
    label: '描述',
    type: 'textarea',
    placeholder: '请输入${entityName}描述'
  }
]

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入${entityName}名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入${entityName}编码', trigger: 'blur' },
    { 
      pattern: /^[A-Z][A-Z0-9_]*$/, 
      message: '编码必须以大写字母开头，只能包含大写字母、数字和下划线', 
      trigger: 'blur' 
    }
  ]
}

// 加载数据
const loadData = async (params = {}) => {
  try {
    pageTemplateRef.value?.setLoading(true)
    
    // TODO: 替换为实际的API调用
    // const response = await get${componentName}List(params)
    
    // 模拟数据
    const mockData = {
      list: [
        {
          id: 1,
          name: '示例${entityName}1',
          code: 'EXAMPLE_001',
          status: true,
          description: '这是一个示例${entityName}',
          createTime: new Date().toLocaleString()
        }
      ],
      total: 1
    }
    
    pageTemplateRef.value?.setDataList(mockData.list)
    pageTemplateRef.value?.setPagination(1, 20, mockData.total)
  } catch (error) {
    console.error('加载数据失败:', error)
    pageTemplateRef.value?.showError('加载数据失败')
  } finally {
    pageTemplateRef.value?.setLoading(false)
  }
}

// 搜索处理
const handleSearch = (searchParams) => {
  loadData(searchParams)
}

// 新增处理
const handleAdd = () => {
  console.log('新增${entityName}')
}

// 编辑处理
const handleEdit = (row) => {
  console.log('编辑${entityName}:', row)
}

// 删除处理
const handleDelete = async (row) => {
  try {
    // TODO: 替换为实际的API调用
    // await delete${componentName}(row.id)
    
    pageTemplateRef.value?.showSuccess('删除成功')
    loadData()
  } catch (error) {
    console.error('删除失败:', error)
    pageTemplateRef.value?.showError('删除失败')
  }
}

// 表单提交处理
const handleSubmit = async ({ type, data }) => {
  try {
    if (type === 'add') {
      // TODO: 替换为实际的API调用
      // await create${componentName}(data)
      pageTemplateRef.value?.showSuccess('新增成功')
    } else {
      // TODO: 替换为实际的API调用
      // await update${componentName}(data.id, data)
      pageTemplateRef.value?.showSuccess('编辑成功')
    }
    
    pageTemplateRef.value?.closeDialog()
    loadData()
  } catch (error) {
    console.error('保存失败:', error)
    pageTemplateRef.value?.showError('保存失败')
  } finally {
    pageTemplateRef.value?.setSubmitting(false)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
/* 自定义样式 */
</style>`

  return template
}

/**
 * 生成API文件模板
 * @param {Object} menuConfig 菜单配置
 * @returns {string} 生成的API文件代码
 */
export function generateApiTemplate(menuConfig) {
  const { name, menuId } = menuConfig
  const componentName = toPascalCase(menuId)
  const apiPath = toKebabCase(menuId)
  
  const template = `/**
 * ${name} API
 */
import request from '@/utils/request'

/**
 * 获取${name}列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回列表数据
 */
export function get${componentName}List(params) {
  return request({
    url: '/api/${apiPath}',
    method: 'get',
    params
  })
}

/**
 * 获取${name}详情
 * @param {number} id ID
 * @returns {Promise} 返回详情数据
 */
export function get${componentName}Detail(id) {
  return request({
    url: \`/api/${apiPath}/\${id}\`,
    method: 'get'
  })
}

/**
 * 创建${name}
 * @param {Object} data 数据
 * @returns {Promise} 返回创建结果
 */
export function create${componentName}(data) {
  return request({
    url: '/api/${apiPath}',
    method: 'post',
    data
  })
}

/**
 * 更新${name}
 * @param {number} id ID
 * @param {Object} data 数据
 * @returns {Promise} 返回更新结果
 */
export function update${componentName}(id, data) {
  return request({
    url: \`/api/${apiPath}/\${id}\`,
    method: 'put',
    data
  })
}

/**
 * 删除${name}
 * @param {number} id ID
 * @returns {Promise} 返回删除结果
 */
export function delete${componentName}(id) {
  return request({
    url: \`/api/${apiPath}/\${id}\`,
    method: 'delete'
  })
}`

  return template
}

/**
 * 生成路由配置
 * @param {Object} menuConfig 菜单配置
 * @returns {Object} 路由配置对象
 */
export function generateRouteConfig(menuConfig) {
  const { name, menuId, path: menuPath } = menuConfig
  const componentName = toPascalCase(menuId)
  
  return {
    path: menuPath,
    name: componentName,
    component: `() => import('@/views/${getViewPath(menuPath)}/${componentName}.vue')`,
    meta: {
      title: name,
      requiresAuth: true
    }
  }
}

/**
 * 转换为帕斯卡命名法
 * @param {string} str 字符串
 * @returns {string} 帕斯卡命名法字符串
 */
function toPascalCase(str) {
  return str
    .split(/[-_]/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('')
}

/**
 * 转换为短横线命名法
 * @param {string} str 字符串
 * @returns {string} 短横线命名法字符串
 */
function toKebabCase(str) {
  return str
    .replace(/([A-Z])/g, '-$1')
    .toLowerCase()
    .replace(/^-/, '')
    .replace(/_/g, '-')
}

/**
 * 根据菜单路径获取视图文件路径
 * @param {string} menuPath 菜单路径
 * @returns {string} 视图文件路径
 */
function getViewPath(menuPath) {
  const pathParts = menuPath.split('/').filter(part => part !== '' && part !== 'home')
  return pathParts.join('/')
}

/**
 * 创建完整的页面文件结构
 * @param {Object} menuConfig 菜单配置
 * @param {string} basePath 基础路径
 */
export async function createPageFiles(menuConfig, basePath = 'src') {
  const { menuId, path: menuPath } = menuConfig
  const componentName = toPascalCase(menuId)
  const viewPath = getViewPath(menuPath)
  
  try {
    // 创建Vue组件文件
    const componentDir = path.join(basePath, 'views', viewPath)
    const componentFile = path.join(componentDir, `${componentName}.vue`)
    
    if (!fs.existsSync(componentDir)) {
      fs.mkdirSync(componentDir, { recursive: true })
    }
    
    const vueTemplate = generatePageTemplate(menuConfig)
    fs.writeFileSync(componentFile, vueTemplate, 'utf8')
    
    // 创建API文件
    const apiDir = path.join(basePath, 'api')
    const apiFile = path.join(apiDir, `${toKebabCase(menuId)}.js`)
    
    if (!fs.existsSync(apiDir)) {
      fs.mkdirSync(apiDir, { recursive: true })
    }
    
    const apiTemplate = generateApiTemplate(menuConfig)
    fs.writeFileSync(apiFile, apiTemplate, 'utf8')
    
    console.log(`页面文件已创建:`)
    console.log(`- Vue组件: ${componentFile}`)
    console.log(`- API文件: ${apiFile}`)
    
    return {
      componentFile,
      apiFile,
      routeConfig: generateRouteConfig(menuConfig)
    }
  } catch (error) {
    console.error('创建页面文件失败:', error)
    throw error
  }
}

/**
 * 生成页面配置预览
 * @param {Object} menuConfig 菜单配置
 * @returns {Object} 页面配置预览
 */
export function generatePagePreview(menuConfig) {
  const { name, menuId, path: menuPath, description } = menuConfig
  const componentName = toPascalCase(menuId)
  const viewPath = getViewPath(menuPath)
  
  return {
    pageName: name,
    componentName,
    files: {
      vue: `src/views/${viewPath}/${componentName}.vue`,
      api: `src/api/${toKebabCase(menuId)}.js`
    },
    features: [
      '标准CRUD操作',
      '搜索和筛选',
      '分页显示',
      '表单验证',
      '状态管理',
      '响应式布局'
    ],
    apiEndpoints: [
      `GET /api/${toKebabCase(menuId)} - 获取列表`,
      `GET /api/${toKebabCase(menuId)}/:id - 获取详情`,
      `POST /api/${toKebabCase(menuId)} - 创建数据`,
      `PUT /api/${toKebabCase(menuId)}/:id - 更新数据`,
      `DELETE /api/${toKebabCase(menuId)}/:id - 删除数据`
    ]
  }
}

/**
 * 在浏览器环境中创建并下载页面文件
 * @param {Object} menuConfig 菜单配置
 * @returns {Promise} 文件创建结果
 */
export async function createPageFilesInBrowser(menuConfig) {
  const { menuId, path: menuPath } = menuConfig
  const componentName = toPascalCase(menuId)
  const viewPath = getViewPath(menuPath)
  
  try {
    // 生成Vue组件代码
    const vueTemplate = generatePageTemplate(menuConfig)
    const apiTemplate = generateApiTemplate(menuConfig)
    
    // 创建文件下载链接
    const vueBlob = new Blob([vueTemplate], { type: 'text/plain' })
    const apiBlob = new Blob([apiTemplate], { type: 'text/plain' })
    
    // 文件路径信息
    const files = {
      vue: {
        name: `${componentName}.vue`,
        path: `src/views/${viewPath}/${componentName}.vue`,
        blob: vueBlob,
        content: vueTemplate
      },
      api: {
        name: `${toKebabCase(menuId)}.js`,
        path: `src/api/${toKebabCase(menuId)}.js`,
        blob: apiBlob,
        content: apiTemplate
      }
    }
    
    return {
      success: true,
      files,
      routeConfig: generateRouteConfig(menuConfig),
      message: '页面模板已生成，可下载文件或复制代码'
    }
  } catch (error) {
    console.error('生成页面文件失败:', error)
    return {
      success: false,
      error: error.message,
      message: '页面模板生成失败'
    }
  }
}

/**
 * 下载生成的文件
 * @param {Object} file 文件对象
 */
export function downloadFile(file) {
  const url = URL.createObjectURL(file.blob)
  const link = document.createElement('a')
  link.href = url
  link.download = file.name
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

/**
 * 复制代码到剪贴板
 * @param {string} content 代码内容
 * @returns {Promise} 复制结果
 */
export async function copyToClipboard(content) {
  try {
    await navigator.clipboard.writeText(content)
    return { success: true, message: '代码已复制到剪贴板' }
  } catch (error) {
    // 降级处理
    const textArea = document.createElement('textarea')
    textArea.value = content
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    return { success: true, message: '代码已复制到剪贴板' }
  }
}

/**
 * 生成路由注册代码
 * @param {Object} menuConfig 菜单配置
 * @returns {string} 路由代码
 */
export function generateRouteCode(menuConfig) {
  const routeConfig = generateRouteConfig(menuConfig)
  const componentName = toPascalCase(menuConfig.menuId)
  const viewPath = getViewPath(menuConfig.path)
  
  return `// 在 router/index.js 中添加以下导入和路由配置

// 导入组件
import ${componentName} from '../views/${viewPath}/${componentName}.vue'

// 在 Home 路由的 children 数组中添加以下配置
{
  path: '${routeConfig.path.replace('/home/', '')}',
  name: '${routeConfig.name}',
  component: ${componentName},
  meta: ${JSON.stringify(routeConfig.meta, null, 2)}
}`
} 