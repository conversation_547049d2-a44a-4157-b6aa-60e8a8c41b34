import request from './request'

/**
 * 多语言管理相关API
 */

// 获取所有翻译数据
export function getI18nData() {
  return request({
    url: '/api/i18n/all',
    method: 'get'
  })
}

// 保存翻译数据
export function saveI18nData(data) {
  return request({
    url: '/api/i18n/save',
    method: 'post',
    data
  })
}

// 新增翻译项
export function addI18nItem(item) {
  return request({
    url: '/api/i18n/add',
    method: 'post',
    data: item
  })
}

// 更新翻译项
export function updateI18nItem(key, item) {
  return request({
    url: `/api/i18n/update/${encodeURIComponent(key)}`,
    method: 'put',
    data: item
  })
}

// 删除翻译项
export function deleteI18nItem(key) {
  return request({
    url: `/api/i18n/delete/${encodeURIComponent(key)}`,
    method: 'delete'
  })
}

// 批量导入翻译数据
export function importI18nData(data) {
  return request({
    url: '/api/i18n/import',
    method: 'post',
    data
  })
}

// 导出翻译数据
export function exportI18nData(languages = []) {
  return request({
    url: '/api/i18n/export',
    method: 'get',
    params: { languages: languages.join(',') }
  })
}

// 提取页面文本
export function extractPageTexts(pagePath, rules = [], modulePrefix = '') {
  return request({
    url: '/api/i18n/extract',
    method: 'post',
    data: {
      pagePath,
      rules,
      modulePrefix
    }
  })
}

// 获取可用的页面列表
export function getAvailablePages() {
  return request({
    url: '/api/i18n/pages',
    method: 'get'
  })
}

// 获取翻译统计信息
export function getI18nStats() {
  return request({
    url: '/api/i18n/stats',
    method: 'get'
  })
}

// 搜索翻译项
export function searchI18nItems(keyword, filters = {}) {
  return request({
    url: '/api/i18n/search',
    method: 'get',
    params: {
      keyword,
      ...filters
    }
  })
} 