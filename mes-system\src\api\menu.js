import request from '@/utils/request'

/**
 * 获取菜单树
 * @returns {Promise} - 返回菜单树数据
 */
export function getMenuTree() {
  return request({
    url: '/api/menus/tree',
    method: 'get'
  })
}

/**
 * 获取菜单列表
 * @returns {Promise} - 返回菜单列表数据
 */
export function getMenus() {
  return request({
    url: '/api/menus',
    method: 'get'
  })
}

/**
 * 获取菜单详情
 * @param {number} id - 菜单ID
 * @returns {Promise} - 返回菜单详情
 */
export function getMenu(id) {
  return request({
    url: `/api/menus/${id}`,
    method: 'get'
  })
}

/**
 * 创建菜单
 * @param {Object} data - 菜单数据
 * @returns {Promise} - 返回创建结果
 */
export function createMenu(data) {
  return request({
    url: '/api/menus',
    method: 'post',
    data
  })
}

/**
 * 更新菜单
 * @param {number} id - 菜单ID
 * @param {Object} data - 菜单数据
 * @returns {Promise} - 返回更新结果
 */
export function updateMenu(id, data) {
  return request({
    url: `/api/menus/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除菜单
 * @param {number} id - 菜单ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteMenu(id) {
  return request({
    url: `/api/menus/${id}`,
    method: 'delete'
  })
}

/**
 * 初始化菜单数据
 * @returns {Promise} - 返回初始化结果
 */
export function initializeMenus() {
  return request({
    url: '/api/menus/initialize',
    method: 'post'
  })
}

/**
 * 刷新菜单缓存
 * @returns {Promise} - 返回刷新结果
 */
export function refreshMenuCache() {
  return request({
    url: '/api/menus/refresh',
    method: 'post'
  })
} 