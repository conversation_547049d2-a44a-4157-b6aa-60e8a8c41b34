using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using mes_system_server.Data;

var builder = WebApplication.CreateBuilder(args);

// 配置端口
builder.WebHost.UseUrls("http://0.0.0.0:5221");

// 添加服务到容器
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

var jwtKey = builder.Configuration["Jwt:Key"] ?? throw new InvalidOperationException("JWT key not configured");
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey))
        };
    });

builder.Services.AddControllers();
// 添加路由配置，设置为不区分大小写
builder.Services.Configure<RouteOptions>(options => {
    options.LowercaseUrls = true;
    options.LowercaseQueryStrings = true;
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();

// 配置 Swagger
var swaggerConfig = builder.Configuration.GetSection("Swagger");
if (swaggerConfig.GetValue<bool>("Enabled"))
{
    builder.Services.AddSwaggerGen(c =>
    {
        var version = swaggerConfig["Version"] ?? "v1";
        var title = swaggerConfig["Title"] ?? "MES System API";
        var description = swaggerConfig["Description"] ?? "MES System API DocumentationV1.0.2";
        var contactName = swaggerConfig.GetSection("Contact:Name").Value ?? "MES System Team";
        var contactEmail = swaggerConfig.GetSection("Contact:Email").Value ?? "<EMAIL>";
        var licenseName = swaggerConfig.GetSection("License:Name").Value ?? "MIT";
        var licenseUrl = swaggerConfig.GetSection("License:Url").Value ?? "https://opensource.org/licenses/MIT";

        c.SwaggerDoc(version, new Microsoft.OpenApi.Models.OpenApiInfo
        {
            Title = title,
            Version = version,
            Description = description,
            Contact = new Microsoft.OpenApi.Models.OpenApiContact
            {
                Name = contactName,
                Email = contactEmail
            },
            License = new Microsoft.OpenApi.Models.OpenApiLicense
            {
                Name = licenseName,
                Url = new Uri(licenseUrl)
            }
        });
    });
}

// 配置 CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("Production", policyBuilder =>
    {
        policyBuilder
            .WithOrigins(builder.Configuration.GetSection("Cors:AllowedOrigins").Get<string[]>() ?? Array.Empty<string>())
            .WithMethods(builder.Configuration.GetSection("Cors:AllowedMethods").Get<string[]>() ?? new[] { "GET", "POST", "PUT", "DELETE" })
            .WithHeaders(builder.Configuration.GetSection("Cors:AllowedHeaders").Get<string[]>() ?? new[] { "Content-Type", "Authorization" })
            .WithExposedHeaders(builder.Configuration.GetSection("Cors:ExposedHeaders").Get<string[]>() ?? new[] { "X-Total-Count", "X-Total-Pages" })
            .AllowCredentials();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (builder.Configuration.GetSection("Swagger").GetValue<bool>("Enabled"))
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        var version = builder.Configuration.GetSection("Swagger:Version").Value ?? "v1";
        var title = builder.Configuration.GetSection("Swagger:Title").Value ?? "MES System API";
        c.SwaggerEndpoint($"/swagger/{version}/swagger.json", title);
    });
}

// 启用 HTTPS 重定向
if (builder.Configuration.GetValue<bool>("Security:RequireHttps"))
{
    app.UseHttpsRedirection();
}

// 配置 HSTS
if (builder.Configuration.GetValue<bool>("Security:HstsEnabled"))
{
    app.UseHsts();
}

app.UseCors("Production");

// 启用静态文件服务 - 允许直接访问wwwroot下的文件
app.UseStaticFiles();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

var summaries = new[]
{
    "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "Balmy", "Hot", "Sweltering", "Scorching"
};

app.MapGet("/weatherforecast", () =>
{
    var forecast =  Enumerable.Range(1, 5).Select(index =>
        new WeatherForecast
        (
            DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
            Random.Shared.Next(-20, 55),
            summaries[Random.Shared.Next(summaries.Length)]
        ))
        .ToArray();
    return forecast;
})
.WithName("GetWeatherForecast")
.WithOpenApi();

app.Run();

record WeatherForecast(DateOnly Date, int TemperatureC, string? Summary)
{
    public int TemperatureF => 32 + (int)(TemperatureC / 0.5556);
}
