<template>
  <div class="salary-management">
    <div class="operation-bar">
      <div class="search-area">
        <el-date-picker
          v-model="month"
          type="month"
          placeholder="选择月份"
          @change="handleMonthChange"
        />

        <el-input
          v-model="searchQuery"
          placeholder="请输入员工姓名/工号"
          class="search-input"
          clearable
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
          </template>
        </el-input>

        <el-select v-model="departmentFilter" clearable placeholder="部门" class="filter-select" @change="handleSearch">
          <el-option label="研发部" value="研发部" />
          <el-option label="生产部" value="生产部" />
          <el-option label="质检部" value="质检部" />
          <el-option label="仓储部" value="仓储部" />
        </el-select>

        <el-button type="primary" :icon="Plus" @click="handleAdd">录入薪资</el-button>
        <el-button type="success" :icon="Upload">导入</el-button>
        <el-button type="warning" :icon="Download">导出</el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="salaryList"
      border
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="month" label="月份" width="100" align="center" />
      <el-table-column prop="employeeId" label="工号" width="100" align="center" />
      <el-table-column prop="name" label="姓名" width="100" align="center" />
      <el-table-column prop="department" label="部门" width="100" align="center" />
      <el-table-column prop="position" label="职位" width="100" align="center" />
      <el-table-column prop="baseSalary" label="基本工资" width="120" align="center">
        <template #default="{ row }">
          {{ formatCurrency(row.baseSalary) }}
        </template>
      </el-table-column>
      <el-table-column prop="bonus" label="奖金" width="100" align="center">
        <template #default="{ row }">
          {{ formatCurrency(row.bonus) }}
        </template>
      </el-table-column>
      <el-table-column prop="overtime" label="加班费" width="100" align="center">
        <template #default="{ row }">
          {{ formatCurrency(row.overtime) }}
        </template>
      </el-table-column>
      <el-table-column prop="deduction" label="扣款" width="100" align="center">
        <template #default="{ row }">
          {{ formatCurrency(row.deduction) }}
        </template>
      </el-table-column>
      <el-table-column prop="total" label="实发工资" width="120" align="center">
        <template #default="{ row }">
          {{ formatCurrency(row.total) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 'paid' ? 'success' : 'warning'">
            {{ row.status === 'paid' ? '已发放' : '未发放' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" min-width="180" align="center" />
      <el-table-column label="操作" width="150" align="center">
        <template #default="{ row }">
          <el-button-group>
            <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 薪资录入对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '录入薪资' : '编辑薪资'"
      width="600px"
    >
      <el-form
        ref="salaryFormRef"
        :model="salaryForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="月份" prop="month">
          <el-date-picker
            v-model="salaryForm.month"
            type="month"
            placeholder="选择月份"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="员工" prop="employeeId">
          <el-select v-model="salaryForm.employeeId" placeholder="请选择员工" style="width: 100%">
            <el-option
              v-for="employee in employeeOptions"
              :key="employee.id"
              :label="employee.name"
              :value="employee.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="基本工资" prop="baseSalary">
          <el-input-number
            v-model="salaryForm.baseSalary"
            :min="0"
            :precision="2"
            :step="100"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="奖金" prop="bonus">
          <el-input-number
            v-model="salaryForm.bonus"
            :min="0"
            :precision="2"
            :step="100"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="加班费" prop="overtime">
          <el-input-number
            v-model="salaryForm.overtime"
            :min="0"
            :precision="2"
            :step="100"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="扣款" prop="deduction">
          <el-input-number
            v-model="salaryForm.deduction"
            :min="0"
            :precision="2"
            :step="100"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="salaryForm.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="已发放" value="paid" />
            <el-option label="未发放" value="unpaid" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="salaryForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Upload, Download } from '@element-plus/icons-vue'

// 搜索和筛选条件
const month = ref('')
const searchQuery = ref('')
const departmentFilter = ref('')

// 表格数据
const loading = ref(false)
const salaryList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 员工选项
const employeeOptions = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' }
])

// 表单对话框
const dialogVisible = ref(false)
const dialogType = ref('add')
const salaryFormRef = ref(null)
const salaryForm = reactive({
  month: '',
  employeeId: '',
  baseSalary: 0,
  bonus: 0,
  overtime: 0,
  deduction: 0,
  status: 'unpaid',
  remark: ''
})

// 表单验证规则
const rules = {
  month: [
    { required: true, message: '请选择月份', trigger: 'change' }
  ],
  employeeId: [
    { required: true, message: '请选择员工', trigger: 'change' }
  ],
  baseSalary: [
    { required: true, message: '请输入基本工资', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 格式化货币
const formatCurrency = (value) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(value)
}

// 月份变更
const handleMonthChange = () => {
  handleSearch()
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchSalaryList()
}

// 获取薪资列表
const fetchSalaryList = async () => {
  loading.value = true
  try {
    // TODO: 调用后端API获取薪资列表
    loading.value = false
  } catch (error) {
    console.error('获取薪资列表失败:', error)
    ElMessage.error('获取薪资列表失败，请稍后重试')
    loading.value = false
  }
}

// 新增薪资
const handleAdd = () => {
  dialogType.value = 'add'
  Object.keys(salaryForm).forEach(key => {
    salaryForm[key] = key.includes('Salary') || key.includes('bonus') || key.includes('overtime') || key.includes('deduction') ? 0 : ''
  })
  salaryForm.status = 'unpaid'
  dialogVisible.value = true
}

// 编辑薪资
const handleEdit = (row) => {
  dialogType.value = 'edit'
  Object.keys(salaryForm).forEach(key => {
    salaryForm[key] = row[key]
  })
  dialogVisible.value = true
}

// 删除薪资
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除该薪资记录吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    // TODO: 调用后端API删除薪资记录
    ElMessage.success('删除成功')
    fetchSalaryList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除薪资记录失败:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!salaryFormRef.value) return
  
  try {
    await salaryFormRef.value.validate()
    // TODO: 调用后端API保存薪资信息
    ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
    dialogVisible.value = false
    fetchSalaryList()
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchSalaryList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchSalaryList()
}

// 初始化
onMounted(() => {
  fetchSalaryList()
})
</script>

<style scoped>
.salary-management {
  padding: 20px;
}

.operation-bar {
  margin-bottom: 20px;
}

.search-area {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input {
  width: 200px;
}

.filter-select {
  width: 120px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 