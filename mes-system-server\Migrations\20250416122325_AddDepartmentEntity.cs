﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace mes_system_server.Migrations
{
    /// <inheritdoc />
    public partial class AddDepartmentEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Departments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Manager = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ParentId = table.Column<int>(type: "int", nullable: true),
                    Level = table.Column<string>(type: "nvarchar(1)", maxLength: 1, nullable: false),
                    EmployeeCount = table.Column<int>(type: "int", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Departments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Departments_Departments_ParentId",
                        column: x => x.ParentId,
                        principalTable: "Departments",
                        principalColumn: "Id");
                });

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(1953), new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(1953) });

            migrationBuilder.InsertData(
                table: "Departments",
                columns: new[] { "Id", "Code", "CreateTime", "Description", "EmployeeCount", "Level", "Manager", "Name", "ParentId", "UpdateTime" },
                values: new object[,]
                {
                    { 1, "RD", new DateTime(2023, 1, 1, 8, 0, 0, 0, DateTimeKind.Unspecified), "负责产品研发", 20, "1", "张三", "研发部", null, new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2124) },
                    { 2, "PROD", new DateTime(2023, 1, 2, 9, 0, 0, 0, DateTimeKind.Unspecified), "负责产品生产", 50, "1", "李四", "生产部", null, new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2363) },
                    { 4, "WH", new DateTime(2023, 1, 4, 11, 0, 0, 0, DateTimeKind.Unspecified), "负责仓库管理", 10, "1", "赵六", "仓储部", null, new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2392) },
                    { 5, "HR", new DateTime(2023, 1, 5, 12, 0, 0, 0, DateTimeKind.Unspecified), "负责人力资源管理", 8, "1", "钱七", "人力资源部", null, new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2411) },
                    { 3, "QA", new DateTime(2023, 1, 3, 10, 0, 0, 0, DateTimeKind.Unspecified), "负责质量检测", 15, "2", "王五", "质检部", 2, new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2384) },
                    { 6, "FE", new DateTime(2023, 1, 6, 13, 0, 0, 0, DateTimeKind.Unspecified), "负责前端开发", 12, "2", "孙八", "前端开发组", 1, new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2420) },
                    { 7, "BE", new DateTime(2023, 1, 7, 14, 0, 0, 0, DateTimeKind.Unspecified), "负责后端开发", 8, "2", "周九", "后端开发组", 1, new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2428) },
                    { 8, "UI", new DateTime(2023, 1, 8, 15, 0, 0, 0, DateTimeKind.Unspecified), "负责UI设计", 5, "3", "吴十", "UI设计团队", 6, new DateTime(2025, 4, 16, 12, 23, 24, 885, DateTimeKind.Utc).AddTicks(2435) }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Departments_ParentId",
                table: "Departments",
                column: "ParentId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Departments");

            migrationBuilder.UpdateData(
                table: "Companies",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 4, 15, 12, 47, 47, 848, DateTimeKind.Utc).AddTicks(5541), new DateTime(2025, 4, 15, 12, 47, 47, 848, DateTimeKind.Utc).AddTicks(5541) });
        }
    }
}
