# MES智能制造执行系统

> 基于Vue 3 + Element Plus的现代化制造执行管理平台

## 🚀 快速开始

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build
```

## 📚 文档中心

完整的文档和使用指南请查看：[docs/README.md](./docs/README.md)

### 核心文档
- [项目概述](./docs/project-overview.md) - 系统介绍和技术栈
- [菜单系统](./docs/menu-system.md) - 动态菜单配置指南
- [PageTemplate](./docs/page-template.md) - 通用页面模板使用说明
- [添加新菜单示例](./docs/add-menu-example.md) - 完整的开发示例

## 🎯 核心特性

- 🔧 **PageTemplate组件** - 快速生成标准CRUD页面
- 🎛️ **动态菜单系统** - 可视化菜单配置和权限管理  
- 👥 **角色权限管理** - 细粒度权限控制
- 📱 **响应式设计** - 支持PC、平板、手机多端访问
- ⚡ **现代化技术栈** - Vue 3 + Vite + Element Plus

## 🏗️ 项目结构

```
mes-system/
├── docs/           # 📚 文档中心
├── src/            # 💻 源代码
│   ├── components/ # 🧩 通用组件
│   ├── views/      # 📄 页面组件
│   ├── api/        # 🔌 API接口
│   └── utils/      # 🛠️ 工具函数
└── public/         # 📁 静态资源
```

## 💡 快速上手

1. **查看演示页面**: 访问 `/home/<USER>/page-template-demo`
2. **菜单管理**: 在 `/home/<USER>/menus` 中配置菜单
3. **生成页面**: 使用PageTemplate快速创建CRUD页面

---

📖 **详细文档**: [docs/README.md](./docs/README.md) | 🐛 **问题反馈**: [Issues](./issues)
