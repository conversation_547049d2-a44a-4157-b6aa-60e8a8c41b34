﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using mes_system_server.Data;

#nullable disable

namespace mes_system_server.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250712012018_AddMaterialModels")]
    partial class AddMaterialModels
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.15")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("mes_system_server.Models.Company", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CompanyType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreditCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LegalRepresentative")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("RegisteredCapital")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("RegistrationDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Companies");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Address = "上海市浦东新区张江高科技园区",
                            CompanyType = "LLC",
                            CreatedAt = new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2245),
                            CreditCode = "91310000XXXXXXXXXX",
                            Description = "丰信科技是一家专注于智能制造领域的高新技术企业，致力于为工业企业提供先进的智能制造解决方案和服务。",
                            Email = "<EMAIL>",
                            LegalRepresentative = "张三",
                            Name = "丰信科技有限公司",
                            Phone = "021-12345678",
                            RegisteredCapital = "1000万元",
                            RegistrationDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            UpdatedAt = new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2245)
                        });
                });

            modelBuilder.Entity("mes_system_server.Models.DataDictionary", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("CreateBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Remark")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("UpdateBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_DataDictionaries_Status");

                    b.HasIndex("Type", "Sort")
                        .HasDatabaseName("IX_DataDictionaries_Type_Sort");

                    b.ToTable("DataDictionaries");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "USER_STATUS",
                            CreateTime = new DateTime(2024, 1, 1, 8, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "用户账号状态配置",
                            Name = "用户状态",
                            Remark = "系统核心配置",
                            Sort = 1,
                            Status = true,
                            Type = "system"
                        },
                        new
                        {
                            Id = 2,
                            Code = "DEPT_TYPE",
                            CreateTime = new DateTime(2024, 1, 1, 9, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "部门分类配置",
                            Name = "部门类型",
                            Remark = "业务配置",
                            Sort = 2,
                            Status = true,
                            Type = "business"
                        },
                        new
                        {
                            Id = 3,
                            Code = "GENDER",
                            CreateTime = new DateTime(2024, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "性别分类",
                            Name = "性别",
                            Remark = "基础数据",
                            Sort = 3,
                            Status = true,
                            Type = "system"
                        },
                        new
                        {
                            Id = 4,
                            Code = "EDUCATION",
                            CreateTime = new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "学历分类",
                            Name = "学历",
                            Remark = "基础数据",
                            Sort = 4,
                            Status = true,
                            Type = "system"
                        });
                });

            modelBuilder.Entity("mes_system_server.Models.DataDictionaryItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("CreateBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("DictId")
                        .HasColumnType("int");

                    b.Property<string>("ExtProperty1")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ExtProperty2")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ExtProperty3")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Label")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<int?>("UpdateBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("DictId", "Sort")
                        .HasDatabaseName("IX_DataDictionaryItems_DictId_Sort");

                    b.HasIndex("DictId", "Status")
                        .HasDatabaseName("IX_DataDictionaryItems_DictId_Status");

                    b.HasIndex("DictId", "Value")
                        .IsUnique();

                    b.ToTable("DataDictionaryItems");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreateTime = new DateTime(2024, 1, 1, 8, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "用户正常状态",
                            DictId = 1,
                            Label = "正常",
                            Sort = 1,
                            Status = true,
                            Value = "1"
                        },
                        new
                        {
                            Id = 2,
                            CreateTime = new DateTime(2024, 1, 1, 8, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "用户被禁用",
                            DictId = 1,
                            Label = "禁用",
                            Sort = 2,
                            Status = true,
                            Value = "0"
                        },
                        new
                        {
                            Id = 3,
                            CreateTime = new DateTime(2024, 1, 1, 8, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "用户被锁定",
                            DictId = 1,
                            Label = "锁定",
                            Sort = 3,
                            Status = true,
                            Value = "2"
                        },
                        new
                        {
                            Id = 4,
                            CreateTime = new DateTime(2024, 1, 1, 9, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "技术研发部门",
                            DictId = 2,
                            Label = "技术部门",
                            Sort = 1,
                            Status = true,
                            Value = "tech"
                        },
                        new
                        {
                            Id = 5,
                            CreateTime = new DateTime(2024, 1, 1, 9, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "销售业务部门",
                            DictId = 2,
                            Label = "销售部门",
                            Sort = 2,
                            Status = true,
                            Value = "sales"
                        },
                        new
                        {
                            Id = 6,
                            CreateTime = new DateTime(2024, 1, 1, 9, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "人力资源部门",
                            DictId = 2,
                            Label = "人事部门",
                            Sort = 3,
                            Status = true,
                            Value = "hr"
                        },
                        new
                        {
                            Id = 7,
                            CreateTime = new DateTime(2024, 1, 1, 9, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "财务管理部门",
                            DictId = 2,
                            Label = "财务部门",
                            Sort = 4,
                            Status = true,
                            Value = "finance"
                        },
                        new
                        {
                            Id = 8,
                            CreateTime = new DateTime(2024, 1, 1, 9, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "行政管理部门",
                            DictId = 2,
                            Label = "行政部门",
                            Sort = 5,
                            Status = true,
                            Value = "admin"
                        },
                        new
                        {
                            Id = 9,
                            CreateTime = new DateTime(2024, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "男性",
                            DictId = 3,
                            Label = "男",
                            Sort = 1,
                            Status = true,
                            Value = "male"
                        },
                        new
                        {
                            Id = 10,
                            CreateTime = new DateTime(2024, 1, 1, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "女性",
                            DictId = 3,
                            Label = "女",
                            Sort = 2,
                            Status = true,
                            Value = "female"
                        },
                        new
                        {
                            Id = 11,
                            CreateTime = new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "小学学历",
                            DictId = 4,
                            Label = "小学",
                            Sort = 1,
                            Status = true,
                            Value = "primary"
                        },
                        new
                        {
                            Id = 12,
                            CreateTime = new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "初中学历",
                            DictId = 4,
                            Label = "初中",
                            Sort = 2,
                            Status = true,
                            Value = "junior"
                        },
                        new
                        {
                            Id = 13,
                            CreateTime = new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "高中学历",
                            DictId = 4,
                            Label = "高中",
                            Sort = 3,
                            Status = true,
                            Value = "senior"
                        },
                        new
                        {
                            Id = 14,
                            CreateTime = new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "大专学历",
                            DictId = 4,
                            Label = "大专",
                            Sort = 4,
                            Status = true,
                            Value = "college"
                        },
                        new
                        {
                            Id = 15,
                            CreateTime = new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "本科学历",
                            DictId = 4,
                            Label = "本科",
                            Sort = 5,
                            Status = true,
                            Value = "bachelor"
                        },
                        new
                        {
                            Id = 16,
                            CreateTime = new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "硕士学历",
                            DictId = 4,
                            Label = "硕士",
                            Sort = 6,
                            Status = true,
                            Value = "master"
                        },
                        new
                        {
                            Id = 17,
                            CreateTime = new DateTime(2024, 1, 1, 11, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "博士学历",
                            DictId = 4,
                            Label = "博士",
                            Sort = 7,
                            Status = true,
                            Value = "doctor"
                        });
                });

            modelBuilder.Entity("mes_system_server.Models.Department", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("EmployeeCount")
                        .HasColumnType("int");

                    b.Property<string>("Level")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("nvarchar(1)");

                    b.Property<string>("Manager")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.ToTable("Departments");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "RD",
                            CreateTime = new DateTime(2023, 1, 1, 8, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责产品研发",
                            EmployeeCount = 20,
                            Level = "1",
                            Manager = "张三",
                            Name = "研发部",
                            UpdateTime = new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2263)
                        },
                        new
                        {
                            Id = 2,
                            Code = "PROD",
                            CreateTime = new DateTime(2023, 1, 2, 9, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责产品生产",
                            EmployeeCount = 50,
                            Level = "1",
                            Manager = "李四",
                            Name = "生产部",
                            UpdateTime = new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2274)
                        },
                        new
                        {
                            Id = 3,
                            Code = "QA",
                            CreateTime = new DateTime(2023, 1, 3, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责质量检测",
                            EmployeeCount = 15,
                            Level = "2",
                            Manager = "王五",
                            Name = "质检部",
                            ParentId = 2,
                            UpdateTime = new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2279)
                        },
                        new
                        {
                            Id = 4,
                            Code = "WH",
                            CreateTime = new DateTime(2023, 1, 4, 11, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责仓库管理",
                            EmployeeCount = 10,
                            Level = "1",
                            Manager = "赵六",
                            Name = "仓储部",
                            UpdateTime = new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2285)
                        },
                        new
                        {
                            Id = 5,
                            Code = "HR",
                            CreateTime = new DateTime(2023, 1, 5, 12, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责人力资源管理",
                            EmployeeCount = 8,
                            Level = "1",
                            Manager = "钱七",
                            Name = "人力资源部",
                            UpdateTime = new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2290)
                        },
                        new
                        {
                            Id = 6,
                            Code = "FE",
                            CreateTime = new DateTime(2023, 1, 6, 13, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责前端开发",
                            EmployeeCount = 12,
                            Level = "2",
                            Manager = "孙八",
                            Name = "前端开发组",
                            ParentId = 1,
                            UpdateTime = new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2295)
                        },
                        new
                        {
                            Id = 7,
                            Code = "BE",
                            CreateTime = new DateTime(2023, 1, 7, 14, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责后端开发",
                            EmployeeCount = 8,
                            Level = "2",
                            Manager = "周九",
                            Name = "后端开发组",
                            ParentId = 1,
                            UpdateTime = new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2300)
                        },
                        new
                        {
                            Id = 8,
                            Code = "UI",
                            CreateTime = new DateTime(2023, 1, 8, 15, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责UI设计",
                            EmployeeCount = 5,
                            Level = "3",
                            Manager = "吴十",
                            Name = "UI设计团队",
                            ParentId = 6,
                            UpdateTime = new DateTime(2025, 7, 12, 1, 20, 17, 800, DateTimeKind.Utc).AddTicks(2305)
                        });
                });

            modelBuilder.Entity("mes_system_server.Models.Employee", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("Age")
                        .HasColumnType("int");

                    b.Property<string>("BaseSalary")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime?>("Birthday")
                        .HasMaxLength(20)
                        .HasColumnType("datetime2");

                    b.Property<string>("ContractNo")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("int");

                    b.Property<string>("Education")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("EntryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FactoryAddress")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FactoryContact")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FactoryName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FactoryPhone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Gender")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("IdCard")
                        .IsRequired()
                        .HasMaxLength(18)
                        .HasColumnType("nvarchar(18)");

                    b.Property<DateTime?>("IdCardIssueDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IdCardIssuePlace")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Level")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PerformanceLevel")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Position")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("SalaryType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("SkillLevel")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Team")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("WorkYears")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("EmployeeId")
                        .IsUnique();

                    b.ToTable("Employees");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Age = 33,
                            BaseSalary = "15000",
                            Birthday = new DateTime(1990, 5, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ContractNo = "HT2022001",
                            CreateTime = new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2355),
                            DepartmentId = 1,
                            Education = "本科",
                            Email = "<EMAIL>",
                            EmployeeId = "EMP001",
                            EntryDate = new DateTime(2022, 1, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            FactoryAddress = "上海市浦东新区张江高科技园区",
                            FactoryContact = "王经理",
                            FactoryName = "总部研发中心",
                            FactoryPhone = "021-12345678",
                            Gender = "男",
                            IdCard = "310101199005103215",
                            IdCardIssuePlace = "",
                            Level = "3",
                            Name = "张大明",
                            PerformanceLevel = "A",
                            Phone = "13800138001",
                            Position = "软件工程师",
                            SalaryType = "月薪",
                            SkillLevel = "高级",
                            Status = "active",
                            Team = "后端开发组",
                            UpdateTime = new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2355),
                            WorkYears = 8
                        },
                        new
                        {
                            Id = 2,
                            Age = 31,
                            BaseSalary = "12000",
                            Birthday = new DateTime(1992, 8, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ContractNo = "HT2022015",
                            CreateTime = new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2368),
                            DepartmentId = 6,
                            Education = "本科",
                            Email = "<EMAIL>",
                            EmployeeId = "EMP002",
                            EntryDate = new DateTime(2022, 3, 20, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            FactoryAddress = "上海市浦东新区张江高科技园区",
                            FactoryContact = "王经理",
                            FactoryName = "总部研发中心",
                            FactoryPhone = "021-12345678",
                            Gender = "女",
                            IdCard = "310101199208153624",
                            IdCardIssuePlace = "",
                            Level = "2",
                            Name = "李小红",
                            PerformanceLevel = "B+",
                            Phone = "13900139002",
                            Position = "前端开发工程师",
                            SalaryType = "月薪",
                            SkillLevel = "中级",
                            Status = "active",
                            Team = "前端开发组",
                            UpdateTime = new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2369),
                            WorkYears = 5
                        },
                        new
                        {
                            Id = 3,
                            Age = 38,
                            BaseSalary = "10000",
                            Birthday = new DateTime(1985, 12, 20, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ContractNo = "HT2021052",
                            CreateTime = new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2379),
                            DepartmentId = 2,
                            Education = "大专",
                            Email = "<EMAIL>",
                            EmployeeId = "EMP003",
                            EntryDate = new DateTime(2021, 5, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            FactoryAddress = "上海市松江区新桥镇新茸路888号",
                            FactoryContact = "赵经理",
                            FactoryName = "松江生产基地",
                            FactoryPhone = "021-87654321",
                            Gender = "男",
                            IdCard = "310101198512204871",
                            IdCardIssuePlace = "",
                            Level = "2",
                            Name = "王强",
                            PerformanceLevel = "A",
                            Phone = "13700137003",
                            Position = "生产主管",
                            SalaryType = "月薪",
                            SkillLevel = "中级",
                            Status = "active",
                            Team = "生产一组",
                            UpdateTime = new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2379),
                            WorkYears = 10
                        },
                        new
                        {
                            Id = 4,
                            Age = 30,
                            BaseSalary = "11000",
                            Birthday = new DateTime(1993, 3, 25, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ContractNo = "HT2022089",
                            CreateTime = new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2389),
                            DepartmentId = 5,
                            Education = "硕士",
                            Email = "<EMAIL>",
                            EmployeeId = "EMP004",
                            EntryDate = new DateTime(2022, 8, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            FactoryAddress = "上海市浦东新区张江高科技园区",
                            FactoryContact = "钱经理",
                            FactoryName = "总部",
                            FactoryPhone = "021-12345678",
                            Gender = "女",
                            IdCard = "310101199303251234",
                            IdCardIssuePlace = "",
                            Level = "2",
                            Name = "赵静",
                            PerformanceLevel = "A-",
                            Phone = "13600136004",
                            Position = "人力资源专员",
                            SalaryType = "月薪",
                            SkillLevel = "中级",
                            Status = "active",
                            Team = "招聘组",
                            UpdateTime = new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2389),
                            WorkYears = 3
                        },
                        new
                        {
                            Id = 5,
                            Age = 35,
                            BaseSalary = "8000",
                            Birthday = new DateTime(1988, 7, 8, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ContractNo = "HT2022035",
                            CreateTime = new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2400),
                            DepartmentId = 3,
                            Education = "大专",
                            Email = "<EMAIL>",
                            EmployeeId = "EMP005",
                            EntryDate = new DateTime(2022, 4, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            FactoryAddress = "上海市松江区新桥镇新茸路888号",
                            FactoryContact = "赵经理",
                            FactoryName = "松江生产基地",
                            FactoryPhone = "021-87654321",
                            Gender = "男",
                            IdCard = "310101198807085678",
                            IdCardIssuePlace = "",
                            Level = "1",
                            Name = "陈明",
                            PerformanceLevel = "B",
                            Phone = "13500135005",
                            Position = "质检专员",
                            SalaryType = "月薪",
                            SkillLevel = "初级",
                            Status = "active",
                            Team = "质检一组",
                            UpdateTime = new DateTime(2025, 7, 12, 9, 20, 17, 800, DateTimeKind.Local).AddTicks(2400),
                            WorkYears = 5
                        });
                });

            modelBuilder.Entity("mes_system_server.Models.Equipment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("WorkCenterId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("WorkCenterId", "Code")
                        .IsUnique();

                    b.ToTable("Equipment");
                });

            modelBuilder.Entity("mes_system_server.Models.MaterialModel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AbcCategory")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("AccountSubject")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal?>("AverageCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BatchSize")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("CostCenter")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("CreateBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("CycleTime")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("DemandSource")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("DemandTimeFence")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("ExportTaxRefund")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ForecastMethod")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("HsCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Image")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<decimal?>("ImportTaxRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("InspectionRequired")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDangerous")
                        .HasColumnType("bit");

                    b.Property<string>("LeadTime")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("LicenseRequirement")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("LotSizeRule")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Material")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal?>("MaxOrderQty")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MaxStock")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MinOrderQty")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MinPurchaseQty")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("OriginCountry")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PackingRequirement")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Planner")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PlanningCycle")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PlanningNote")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("PlanningStrategy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ProductionLeadTime")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ProductionType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PurchaseLeadTime")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PurchaseNote")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<decimal?>("PurchasePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PurchaseUnit")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("QualityLevel")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("QualityStandard")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<decimal?>("ReorderPoint")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("SafetyStock")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SafetyStockDays")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<decimal?>("SalesPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SalesUnit")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("SetupTime")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ShelfLife")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Size")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Specification")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<decimal?>("StandardCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("StorageCondition")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("StorageLocation")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Supplier")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("SupplyTimeFence")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("TaxRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TransportMode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int?>("UpdateBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("ValuationMethod")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Warranty")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("WorkCenter")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_MaterialModels_Name");

                    b.HasIndex("Type", "Code")
                        .IsUnique();

                    b.HasIndex("Type", "Category", "IsActive")
                        .HasDatabaseName("IX_MaterialModels_Type_Category_IsActive");

                    b.HasIndex("Type", "CreateTime", "IsActive")
                        .HasDatabaseName("IX_MaterialModels_Type_CreateTime_IsActive");

                    b.HasIndex("Type", "Status", "IsActive")
                        .HasDatabaseName("IX_MaterialModels_Type_Status_IsActive");

                    b.HasIndex("Type", "Name", "Code", "IsActive")
                        .HasDatabaseName("IX_MaterialModels_Type_Name_Code_IsActive");

                    b.ToTable("MaterialModels");
                });

            modelBuilder.Entity("mes_system_server.Models.Menu", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Icon")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("MenuId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<string>("Path")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("MenuId")
                        .IsUnique();

                    b.HasIndex("ParentId");

                    b.ToTable("Menus");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "仓储管理模块",
                            MenuId = "warehouse",
                            Name = "仓储管理",
                            SortOrder = 1,
                            Status = true,
                            Type = "sub-menu"
                        },
                        new
                        {
                            Id = 2,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "生产管理模块",
                            MenuId = "production",
                            Name = "生产管理",
                            SortOrder = 2,
                            Status = true,
                            Type = "sub-menu"
                        },
                        new
                        {
                            Id = 3,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "工程管理模块",
                            MenuId = "engineering",
                            Name = "工程管理",
                            SortOrder = 3,
                            Status = true,
                            Type = "sub-menu"
                        },
                        new
                        {
                            Id = 4,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "生产线管理",
                            MenuId = "production-line",
                            Name = "生产管理",
                            SortOrder = 4,
                            Status = true,
                            Type = "sub-menu"
                        },
                        new
                        {
                            Id = 5,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "质量管理模块",
                            MenuId = "quality",
                            Name = "质量管理",
                            SortOrder = 5,
                            Status = true,
                            Type = "sub-menu"
                        },
                        new
                        {
                            Id = 6,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "设备管理模块",
                            MenuId = "equipment",
                            Name = "设备管理",
                            SortOrder = 6,
                            Status = true,
                            Type = "sub-menu"
                        },
                        new
                        {
                            Id = 7,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "系统管理模块",
                            MenuId = "system",
                            Name = "系统管理",
                            SortOrder = 7,
                            Status = true,
                            Type = "sub-menu"
                        },
                        new
                        {
                            Id = 8,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "warehouse-in",
                            Name = "入库管理",
                            ParentId = 1,
                            Path = "/home/<USER>/in",
                            SortOrder = 1,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 9,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "warehouse-out",
                            Name = "出库管理",
                            ParentId = 1,
                            Path = "/home/<USER>/out",
                            SortOrder = 2,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 10,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "warehouse-transfer",
                            Name = "库存调拨",
                            ParentId = 1,
                            Path = "/home/<USER>/transfer",
                            SortOrder = 3,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 11,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "warehouse-check",
                            Name = "库存盘点",
                            ParentId = 1,
                            Path = "/home/<USER>/check",
                            SortOrder = 4,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 12,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "production-plan",
                            Name = "生产计划",
                            ParentId = 2,
                            Path = "/home/<USER>/plan",
                            SortOrder = 1,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 13,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "production-order",
                            Name = "生产订单",
                            ParentId = 2,
                            Path = "/home/<USER>/order",
                            SortOrder = 2,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 14,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "production-schedule",
                            Name = "排产管理",
                            ParentId = 2,
                            Path = "/home/<USER>/schedule",
                            SortOrder = 3,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 15,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "engineering-process",
                            Name = "工艺管理",
                            ParentId = 3,
                            Path = "/home/<USER>/process",
                            SortOrder = 1,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 16,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "engineering-bom",
                            Name = "BOM管理",
                            ParentId = 3,
                            Path = "/home/<USER>/bom",
                            SortOrder = 2,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 17,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "engineering-model",
                            Name = "型号管理",
                            ParentId = 3,
                            Path = "/home/<USER>/models",
                            SortOrder = 3,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 18,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "production-monitor",
                            Name = "生产监控",
                            ParentId = 4,
                            Path = "/home/<USER>/monitor",
                            SortOrder = 1,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 19,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "production-task",
                            Name = "生产任务",
                            ParentId = 4,
                            Path = "/home/<USER>/task",
                            SortOrder = 2,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 20,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "quality-inspection",
                            Name = "质量检验",
                            ParentId = 5,
                            Path = "/home/<USER>/inspection",
                            SortOrder = 1,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 21,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "quality-report",
                            Name = "质量报告",
                            ParentId = 5,
                            Path = "/home/<USER>/report",
                            SortOrder = 2,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 22,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "equipment-list",
                            Name = "设备台账",
                            ParentId = 6,
                            Path = "/home/<USER>/list",
                            SortOrder = 1,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 23,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "system-users",
                            Name = "用户管理",
                            ParentId = 7,
                            Path = "/home/<USER>/users",
                            SortOrder = 1,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 24,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "system-roles",
                            Name = "角色权限",
                            ParentId = 7,
                            Path = "/home/<USER>/roles",
                            SortOrder = 2,
                            Status = true,
                            Type = "menu-item"
                        },
                        new
                        {
                            Id = 25,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MenuId = "system-menus",
                            Name = "菜单管理",
                            ParentId = 7,
                            Path = "/home/<USER>/menus",
                            SortOrder = 3,
                            Status = true,
                            Type = "menu-item"
                        });
                });

            modelBuilder.Entity("mes_system_server.Models.Position", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Level")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.ToTable("Positions");
                });

            modelBuilder.Entity("mes_system_server.Models.ProductionLine", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("ProductionLines");
                });

            modelBuilder.Entity("mes_system_server.Models.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("RoleCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("RoleCode")
                        .IsUnique();

                    b.ToTable("Roles");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreateTime = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "系统管理员，拥有所有权限",
                            RoleCode = "admin",
                            RoleName = "系统管理员",
                            Status = true
                        },
                        new
                        {
                            Id = 2,
                            CreateTime = new DateTime(2024, 1, 2, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责生产管理相关工作",
                            RoleCode = "production_manager",
                            RoleName = "生产经理",
                            Status = true
                        },
                        new
                        {
                            Id = 3,
                            CreateTime = new DateTime(2024, 1, 3, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "负责产品质量检验工作",
                            RoleCode = "quality_inspector",
                            RoleName = "质量检验员",
                            Status = true
                        });
                });

            modelBuilder.Entity("mes_system_server.Models.RoleMenu", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("MenuId")
                        .HasColumnType("int");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MenuId");

                    b.HasIndex("RoleId", "MenuId")
                        .IsUnique();

                    b.ToTable("RoleMenus");
                });

            modelBuilder.Entity("mes_system_server.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("RoleId")
                        .HasColumnType("int");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasFilter("[Email] IS NOT NULL");

                    b.HasIndex("RoleId");

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("mes_system_server.Models.WisPDFBookmark", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FilteredBookmarkName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ImageBase64")
                        .HasColumnType("text");

                    b.Property<int?>("ImageHeight")
                        .HasColumnType("int");

                    b.Property<string>("ImagePath")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<long?>("ImageSize")
                        .HasColumnType("bigint");

                    b.Property<int?>("ImageWidth")
                        .HasColumnType("int");

                    b.Property<string>("OriginalBookmarkName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("PageNumber")
                        .HasColumnType("int");

                    b.Property<string>("Remarks")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("FileName")
                        .HasDatabaseName("IX_WisPDFBookmarks_FileName");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_WisPDFBookmarks_Status");

                    b.HasIndex("CreateTime", "Status")
                        .HasDatabaseName("IX_WisPDFBookmarks_CreateTime_Status");

                    b.HasIndex("OriginalBookmarkName", "Status")
                        .HasDatabaseName("IX_WisPDFBookmarks_OriginalBookmarkName_Status");

                    b.HasIndex("FileName", "PageNumber", "Status")
                        .HasDatabaseName("IX_WisPDFBookmarks_FileName_PageNumber_Status");

                    b.HasIndex("FileName", "Status", "PageNumber")
                        .HasDatabaseName("IX_WisPDFBookmarks_FileName_Status_PageNumber");

                    b.ToTable("WisPDFBookmarks");
                });

            modelBuilder.Entity("mes_system_server.Models.WorkCenter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("ProductionLineId")
                        .HasColumnType("int");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ProductionLineId", "Code")
                        .IsUnique();

                    b.ToTable("WorkCenters");
                });

            modelBuilder.Entity("mes_system_server.Models.DataDictionaryItem", b =>
                {
                    b.HasOne("mes_system_server.Models.DataDictionary", "DataDictionary")
                        .WithMany("Items")
                        .HasForeignKey("DictId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataDictionary");
                });

            modelBuilder.Entity("mes_system_server.Models.Department", b =>
                {
                    b.HasOne("mes_system_server.Models.Department", "ParentDepartment")
                        .WithMany()
                        .HasForeignKey("ParentId");

                    b.Navigation("ParentDepartment");
                });

            modelBuilder.Entity("mes_system_server.Models.Employee", b =>
                {
                    b.HasOne("mes_system_server.Models.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("mes_system_server.Models.Equipment", b =>
                {
                    b.HasOne("mes_system_server.Models.WorkCenter", "WorkCenter")
                        .WithMany("Equipment")
                        .HasForeignKey("WorkCenterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkCenter");
                });

            modelBuilder.Entity("mes_system_server.Models.Menu", b =>
                {
                    b.HasOne("mes_system_server.Models.Menu", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("mes_system_server.Models.Position", b =>
                {
                    b.HasOne("mes_system_server.Models.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("mes_system_server.Models.RoleMenu", b =>
                {
                    b.HasOne("mes_system_server.Models.Menu", "Menu")
                        .WithMany("RoleMenus")
                        .HasForeignKey("MenuId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("mes_system_server.Models.Role", "Role")
                        .WithMany("RoleMenus")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Menu");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("mes_system_server.Models.User", b =>
                {
                    b.HasOne("mes_system_server.Models.Role", "UserRole")
                        .WithMany("Users")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("UserRole");
                });

            modelBuilder.Entity("mes_system_server.Models.WorkCenter", b =>
                {
                    b.HasOne("mes_system_server.Models.ProductionLine", "ProductionLine")
                        .WithMany("WorkCenters")
                        .HasForeignKey("ProductionLineId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProductionLine");
                });

            modelBuilder.Entity("mes_system_server.Models.DataDictionary", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("mes_system_server.Models.Menu", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("RoleMenus");
                });

            modelBuilder.Entity("mes_system_server.Models.ProductionLine", b =>
                {
                    b.Navigation("WorkCenters");
                });

            modelBuilder.Entity("mes_system_server.Models.Role", b =>
                {
                    b.Navigation("RoleMenus");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("mes_system_server.Models.WorkCenter", b =>
                {
                    b.Navigation("Equipment");
                });
#pragma warning restore 612, 618
        }
    }
}
